{"version": 3, "sources": ["file:///E:/M2Game/Client/assets/scripts/GameOld/world/player/PlayerSystem.ts"], "names": ["_decorator", "Vec3", "System", "RegisterTypeID", "ccclass", "PlayerState", "PlayerSystem", "_players", "Map", "_currentPlayerId", "_inputBuffer", "_maxInputBufferSize", "_nextPlayerId", "_invulnerabilityTime", "_maxPlayers", "getSystemName", "onInit", "console", "log", "onUnInit", "for<PERSON>ach", "player", "_destroyPlayer", "clear", "length", "onUpdate", "deltaTime", "_processInputBuffer", "_updatePlayer", "onLateUpdate", "_deltaTime", "_updatePlayerVisuals", "createPlayer", "startPosition", "initialStats", "size", "warn", "playerId", "defaultStats", "maxHealth", "currentHealth", "attackPower", "moveSpeed", "defense", "experience", "level", "stats", "id", "position", "velocity", "rotation", "state", "IDLE", "inventory", "abilities", "statusEffects", "lastDamageTime", "invulnerabilityDuration", "node", "undefined", "set", "removePlayer", "get", "delete", "keys", "next", "value", "getPlayer", "getCurrentPlayer", "setCurrentPlayer", "has", "damagePlayer", "damage", "_damageSource", "DEAD", "actualDamage", "Math", "max", "Date", "now", "TAKING_DAMAGE", "healPlayer", "healAmount", "oldHealth", "min", "actualHealing", "addInput", "input", "push", "shift", "getAllPlayers", "Array", "from", "values", "getPlayerCount", "duration", "effect", "newDuration", "add", "multiplyScalar", "pop", "_applyInputToPlayer", "moveDirection", "moveForce", "normalize", "MOVING", "isAttacking", "ATTACKING", "<PERSON><PERSON><PERSON><PERSON>", "setPosition", "setRotationFromEuler", "destroy"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;AAASA,MAAAA,U,OAAAA,U;AAAYC,MAAAA,I,OAAAA,I;;AACZC,MAAAA,M,iBAAAA,M;;AACAC,MAAAA,c,iBAAAA,c;;;;;;;;;OACH;AAAEC,QAAAA;AAAF,O,GAAcJ,U;AAEpB;AACA;AACA;;6BACYK,W,0BAAAA,W;AAAAA,QAAAA,W;AAAAA,QAAAA,W;AAAAA,QAAAA,W;AAAAA,QAAAA,W;AAAAA,QAAAA,W;AAAAA,QAAAA,W;eAAAA,W;;AASZ;AACA;AACA;;AAWA;AACA;AACA;;AASA;AACA;AACA;;;AAgBA;AACA;AACA;8BAGaC,Y,WAFZF,OAAO,CAAC,cAAD,C;;yEAAR,MAEaE,YAFb;AAAA;AAAA,4BAEyC;AAAA;AAAA;AAAA,eAE7BC,QAF6B,GAEO,IAAIC,GAAJ,EAFP;AAAA,eAG7BC,gBAH6B,GAGK,IAHL;AAAA,eAI7BC,YAJ6B,GAIC,EAJD;AAAA,eAK7BC,mBAL6B,GAKC,EALD;AAAA,eAM7BC,aAN6B,GAML,CANK;AAMF;AAEnC;AARqC,eAS7BC,oBAT6B,GASE,GATF;AASO;AATP,eAU7BC,WAV6B,GAUP,CAVO;AAAA;;AAUJ;;AAEjC;AACJ;AACA;AACWC,QAAAA,aAAa,GAAW;AAC3B,iBAAO,cAAP;AACH;AAED;AACJ;AACA;;;AACcC,QAAAA,MAAM,GAAS;AACrBC,UAAAA,OAAO,CAACC,GAAR,CAAY,0CAAZ,EADqB,CAGrB;;AACA,eAAKR,YAAL,GAAoB,EAApB;AAEAO,UAAAA,OAAO,CAACC,GAAR,kDAA2D,KAAKJ,WAAhE;AACH;AAED;AACJ;AACA;;;AACcK,QAAAA,QAAQ,GAAS;AACvBF,UAAAA,OAAO,CAACC,GAAR,CAAY,yCAAZ,EADuB,CAGvB;;AACA,eAAKX,QAAL,CAAca,OAAd,CAAsBC,MAAM,IAAI;AAC5B,iBAAKC,cAAL,CAAoBD,MAApB;AACH,WAFD;;AAIA,eAAKd,QAAL,CAAcgB,KAAd;;AACA,eAAKd,gBAAL,GAAwB,IAAxB;AACA,eAAKC,YAAL,CAAkBc,MAAlB,GAA2B,CAA3B;AACA,eAAKZ,aAAL,GAAqB,CAArB;AAEAK,UAAAA,OAAO,CAACC,GAAR,CAAY,gCAAZ;AACH;AAED;AACJ;AACA;;;AACcO,QAAAA,QAAQ,CAACC,SAAD,EAA0B;AACxC;AACA,eAAKC,mBAAL,GAFwC,CAIxC;;;AACA,eAAKpB,QAAL,CAAca,OAAd,CAAsBC,MAAM,IAAI;AAC5B,iBAAKO,aAAL,CAAmBP,MAAnB,EAA2BK,SAA3B;AACH,WAFD;AAGH;AAED;AACJ;AACA;;;AACcG,QAAAA,YAAY,CAACC,UAAD,EAA2B;AAC7C;AACA,eAAKvB,QAAL,CAAca,OAAd,CAAsBC,MAAM,IAAI;AAC5B,iBAAKU,oBAAL,CAA0BV,MAA1B;AACH,WAFD;AAGH;AAED;AACJ;AACA;AACA;AACA;AACA;;;AACWW,QAAAA,YAAY,CAACC,aAAD,EAAsBC,YAAtB,EAA8E;AAAA,cAAxDA,YAAwD;AAAxDA,YAAAA,YAAwD,GAAnB,EAAmB;AAAA;;AAC7F,cAAI,KAAK3B,QAAL,CAAc4B,IAAd,IAAsB,KAAKrB,WAA/B,EAA4C;AACxCG,YAAAA,OAAO,CAACmB,IAAR,CAAa,0DAAb;AACA,mBAAO,IAAP;AACH,WAJ4F,CAM7F;;;AACA,cAAMC,QAAQ,GAAG,KAAKzB,aAAL,EAAjB,CAP6F,CAS7F;;AACA,cAAM0B,YAAyB,GAAG;AAC9BC,YAAAA,SAAS,EAAE,GADmB;AAE9BC,YAAAA,aAAa,EAAE,GAFe;AAG9BC,YAAAA,WAAW,EAAE,EAHiB;AAI9BC,YAAAA,SAAS,EAAE,GAJmB;AAK9BC,YAAAA,OAAO,EAAE,CALqB;AAM9BC,YAAAA,UAAU,EAAE,CANkB;AAO9BC,YAAAA,KAAK,EAAE;AAPuB,WAAlC,CAV6F,CAoB7F;;AACA,cAAMC,KAAK,gBAAQR,YAAR,EAAyBJ,YAAzB,CAAX;;AACAY,UAAAA,KAAK,CAACN,aAAN,GAAsBM,KAAK,CAACP,SAA5B,CAtB6F,CAsBtD;AAEvC;;AACA,cAAMlB,MAAkB,GAAG;AACvB0B,YAAAA,EAAE,EAAEV,QADmB;AAEvBW,YAAAA,QAAQ,EAAE,IAAI/C,IAAJ,CAASgC,aAAT,CAFa;AAGvBgB,YAAAA,QAAQ,EAAE,IAAIhD,IAAJ,EAHa;AAIvBiD,YAAAA,QAAQ,EAAE,CAJa;AAKvBC,YAAAA,KAAK,EAAE9C,WAAW,CAAC+C,IALI;AAMvBN,YAAAA,KAAK,EAAEA,KANgB;AAOvBO,YAAAA,SAAS,EAAE,EAPY;AAQvBC,YAAAA,SAAS,EAAE,EARY;AASvBC,YAAAA,aAAa,EAAE,IAAI/C,GAAJ,EATQ;AAUvBgD,YAAAA,cAAc,EAAE,CAVO;AAWvBC,YAAAA,uBAAuB,EAAE,CAXF;AAYvBC,YAAAA,IAAI,EAAEC;AAZiB,WAA3B;;AAeA,eAAKpD,QAAL,CAAcqD,GAAd,CAAkBvB,QAAlB,EAA4BhB,MAA5B,EAxC6F,CA0C7F;;;AACA,cAAI,KAAKZ,gBAAL,KAA0B,IAA9B,EAAoC;AAChC,iBAAKA,gBAAL,GAAwB4B,QAAxB;AACH;;AAEDpB,UAAAA,OAAO,CAACC,GAAR,mCAA4CmB,QAA5C;AACA,iBAAOA,QAAP;AACH;AAED;AACJ;AACA;AACA;AACA;;;AACWwB,QAAAA,YAAY,CAACxB,QAAD,EAA4B;AAC3C,cAAMhB,MAAM,GAAG,KAAKd,QAAL,CAAcuD,GAAd,CAAkBzB,QAAlB,CAAf;;AACA,cAAI,CAAChB,MAAL,EAAa;AACT,mBAAO,KAAP;AACH;;AAED,eAAKC,cAAL,CAAoBD,MAApB;;AACA,eAAKd,QAAL,CAAcwD,MAAd,CAAqB1B,QAArB,EAP2C,CAS3C;;;AACA,cAAI,KAAK5B,gBAAL,KAA0B4B,QAA9B,EAAwC;AACpC,iBAAK5B,gBAAL,GAAwB,KAAKF,QAAL,CAAc4B,IAAd,GAAqB,CAArB,GAAyB,KAAK5B,QAAL,CAAcyD,IAAd,GAAqBC,IAArB,GAA4BC,KAArD,GAA6D,IAArF;AACH;;AAEDjD,UAAAA,OAAO,CAACC,GAAR,mCAA4CmB,QAA5C;AACA,iBAAO,IAAP;AACH;AAED;AACJ;AACA;AACA;AACA;;;AACW8B,QAAAA,SAAS,CAAC9B,QAAD,EAAsC;AAClD,iBAAO,KAAK9B,QAAL,CAAcuD,GAAd,CAAkBzB,QAAlB,KAA+B,IAAtC;AACH;AAED;AACJ;AACA;AACA;;;AACW+B,QAAAA,gBAAgB,GAAsB;AACzC,iBAAO,KAAK3D,gBAAL,KAA0B,IAA1B,GAAiC,KAAKF,QAAL,CAAcuD,GAAd,CAAkB,KAAKrD,gBAAvB,KAA4C,IAA7E,GAAoF,IAA3F;AACH;AAED;AACJ;AACA;AACA;AACA;;;AACW4D,QAAAA,gBAAgB,CAAChC,QAAD,EAA4B;AAC/C,cAAI,CAAC,KAAK9B,QAAL,CAAc+D,GAAd,CAAkBjC,QAAlB,CAAL,EAAkC;AAC9B,mBAAO,KAAP;AACH;;AAED,eAAK5B,gBAAL,GAAwB4B,QAAxB;AACApB,UAAAA,OAAO,CAACC,GAAR,0CAAmDmB,QAAnD;AACA,iBAAO,IAAP;AACH;AAED;AACJ;AACA;AACA;AACA;AACA;AACA;;;AACWkC,QAAAA,YAAY,CAAClC,QAAD,EAAmBmC,MAAnB,EAAmCC,aAAnC,EAAoE;AACnF,cAAMpD,MAAM,GAAG,KAAKd,QAAL,CAAcuD,GAAd,CAAkBzB,QAAlB,CAAf;;AACA,cAAI,CAAChB,MAAD,IAAWA,MAAM,CAAC8B,KAAP,KAAiB9C,WAAW,CAACqE,IAA5C,EAAkD;AAC9C,mBAAO,KAAP;AACH,WAJkF,CAMnF;;;AACA,cAAIrD,MAAM,CAACoC,uBAAP,GAAiC,CAArC,EAAwC;AACpCxC,YAAAA,OAAO,CAACC,GAAR,2BAAoCmB,QAApC;AACA,mBAAO,KAAP;AACH,WAVkF,CAYnF;;;AACA,cAAMsC,YAAY,GAAGC,IAAI,CAACC,GAAL,CAAS,CAAT,EAAYL,MAAM,GAAGnD,MAAM,CAACyB,KAAP,CAAaH,OAAlC,CAArB,CAbmF,CAenF;;AACAtB,UAAAA,MAAM,CAACyB,KAAP,CAAaN,aAAb,GAA6BoC,IAAI,CAACC,GAAL,CAAS,CAAT,EAAYxD,MAAM,CAACyB,KAAP,CAAaN,aAAb,GAA6BmC,YAAzC,CAA7B;AACAtD,UAAAA,MAAM,CAACmC,cAAP,GAAwBsB,IAAI,CAACC,GAAL,EAAxB;AACA1D,UAAAA,MAAM,CAACoC,uBAAP,GAAiC,KAAK5C,oBAAtC,CAlBmF,CAoBnF;;AACA,cAAIQ,MAAM,CAACyB,KAAP,CAAaN,aAAb,IAA8B,CAAlC,EAAqC;AACjCnB,YAAAA,MAAM,CAAC8B,KAAP,GAAe9C,WAAW,CAACqE,IAA3B;AACAzD,YAAAA,OAAO,CAACC,GAAR,2BAAoCmB,QAApC;AACH,WAHD,MAGO;AACHhB,YAAAA,MAAM,CAAC8B,KAAP,GAAe9C,WAAW,CAAC2E,aAA3B;AACH;;AAED/D,UAAAA,OAAO,CAACC,GAAR,2BAAoCmB,QAApC,cAAqDsC,YAArD,iBAA6EtD,MAAM,CAACyB,KAAP,CAAaN,aAA1F,SAA2GnB,MAAM,CAACyB,KAAP,CAAaP,SAAxH;AACA,iBAAO,IAAP;AACH;AAED;AACJ;AACA;AACA;AACA;AACA;;;AACW0C,QAAAA,UAAU,CAAC5C,QAAD,EAAmB6C,UAAnB,EAAgD;AAC7D,cAAM7D,MAAM,GAAG,KAAKd,QAAL,CAAcuD,GAAd,CAAkBzB,QAAlB,CAAf;;AACA,cAAI,CAAChB,MAAD,IAAWA,MAAM,CAAC8B,KAAP,KAAiB9C,WAAW,CAACqE,IAA5C,EAAkD;AAC9C,mBAAO,KAAP;AACH;;AAED,cAAMS,SAAS,GAAG9D,MAAM,CAACyB,KAAP,CAAaN,aAA/B;AACAnB,UAAAA,MAAM,CAACyB,KAAP,CAAaN,aAAb,GAA6BoC,IAAI,CAACQ,GAAL,CAAS/D,MAAM,CAACyB,KAAP,CAAaP,SAAtB,EAAiClB,MAAM,CAACyB,KAAP,CAAaN,aAAb,GAA6B0C,UAA9D,CAA7B;AAEA,cAAMG,aAAa,GAAGhE,MAAM,CAACyB,KAAP,CAAaN,aAAb,GAA6B2C,SAAnD;;AACA,cAAIE,aAAa,GAAG,CAApB,EAAuB;AACnBpE,YAAAA,OAAO,CAACC,GAAR,2BAAoCmB,QAApC,oBAA2DgD,aAA3D;AACA,mBAAO,IAAP;AACH;;AAED,iBAAO,KAAP;AACH;AAED;AACJ;AACA;AACA;;;AACWC,QAAAA,QAAQ,CAACC,KAAD,EAA2B;AACtC,eAAK7E,YAAL,CAAkB8E,IAAlB,CAAuBD,KAAvB,EADsC,CAGtC;;;AACA,cAAI,KAAK7E,YAAL,CAAkBc,MAAlB,GAA2B,KAAKb,mBAApC,EAAyD;AACrD,iBAAKD,YAAL,CAAkB+E,KAAlB;AACH;AACJ;AAED;AACJ;AACA;AACA;;;AACWC,QAAAA,aAAa,GAAiB;AACjC,iBAAOC,KAAK,CAACC,IAAN,CAAW,KAAKrF,QAAL,CAAcsF,MAAd,EAAX,CAAP;AACH;AAED;AACJ;AACA;AACA;;;AACWC,QAAAA,cAAc,GAAW;AAC5B,iBAAO,KAAKvF,QAAL,CAAc4B,IAArB;AACH;AAED;AACJ;AACA;;;AACYP,QAAAA,aAAa,CAACP,MAAD,EAAqBK,SAArB,EAA8C;AAC/D;AACA,cAAIL,MAAM,CAACoC,uBAAP,GAAiC,CAArC,EAAwC;AACpCpC,YAAAA,MAAM,CAACoC,uBAAP,IAAkC/B,SAAlC;;AACA,gBAAIL,MAAM,CAACoC,uBAAP,IAAkC,CAAtC,EAAyC;AACrCpC,cAAAA,MAAM,CAACoC,uBAAP,GAAiC,CAAjC;;AACA,kBAAIpC,MAAM,CAAC8B,KAAP,KAAiB9C,WAAW,CAAC2E,aAAjC,EAAgD;AAC5C3D,gBAAAA,MAAM,CAAC8B,KAAP,GAAe9C,WAAW,CAAC+C,IAA3B;AACH;AACJ;AACJ,WAV8D,CAY/D;;;AACA/B,UAAAA,MAAM,CAACkC,aAAP,CAAqBnC,OAArB,CAA6B,CAAC2E,QAAD,EAAWC,MAAX,KAAsB;AAC/C,gBAAMC,WAAW,GAAGF,QAAQ,GAAGrE,SAA/B;;AACA,gBAAIuE,WAAW,IAAI,CAAnB,EAAsB;AAClB5E,cAAAA,MAAM,CAACkC,aAAP,CAAqBQ,MAArB,CAA4BiC,MAA5B;AACA/E,cAAAA,OAAO,CAACC,GAAR,kCAA2C8E,MAA3C,4BAAwE3E,MAAM,CAAC0B,EAA/E;AACH,aAHD,MAGO;AACH1B,cAAAA,MAAM,CAACkC,aAAP,CAAqBK,GAArB,CAAyBoC,MAAzB,EAAiCC,WAAjC;AACH;AACJ,WARD,EAb+D,CAuB/D;;AACA5E,UAAAA,MAAM,CAAC2B,QAAP,CAAgBkD,GAAhB,CAAoBjG,IAAI,CAACkG,cAAL,CAAoB,IAAIlG,IAAJ,EAApB,EAAgCoB,MAAM,CAAC4B,QAAvC,EAAiDvB,SAAjD,CAApB,EAxB+D,CA0B/D;;AACAL,UAAAA,MAAM,CAAC4B,QAAP,CAAgBkD,cAAhB,CAA+B,GAA/B;AACH;AAED;AACJ;AACA;;;AACYxE,QAAAA,mBAAmB,GAAS;AAChC,cAAI,KAAKjB,YAAL,CAAkBc,MAAlB,KAA6B,CAA7B,IAAkC,CAAC,KAAKf,gBAA5C,EAA8D;AAC1D;AACH;;AAED,cAAMY,MAAM,GAAG,KAAKd,QAAL,CAAcuD,GAAd,CAAkB,KAAKrD,gBAAvB,CAAf;;AACA,cAAI,CAACY,MAAD,IAAWA,MAAM,CAAC8B,KAAP,KAAiB9C,WAAW,CAACqE,IAA5C,EAAkD;AAC9C,iBAAKhE,YAAL,CAAkBc,MAAlB,GAA2B,CAA3B,CAD8C,CAChB;;AAC9B;AACH,WAT+B,CAWhC;;;AACA,cAAM+D,KAAK,GAAG,KAAK7E,YAAL,CAAkB0F,GAAlB,EAAd;;AACA,cAAIb,KAAJ,EAAW;AACP,iBAAKc,mBAAL,CAAyBhF,MAAzB,EAAiCkE,KAAjC;AACH,WAf+B,CAiBhC;;;AACA,eAAK7E,YAAL,CAAkBc,MAAlB,GAA2B,CAA3B;AACH;AAED;AACJ;AACA;;;AACY6E,QAAAA,mBAAmB,CAAChF,MAAD,EAAqBkE,KAArB,EAA+C;AACtE;AACA,cAAIA,KAAK,CAACe,aAAN,CAAoB9E,MAApB,KAA+B,CAAnC,EAAsC;AAClC,gBAAM+E,SAAS,GAAGtG,IAAI,CAACkG,cAAL,CAAoB,IAAIlG,IAAJ,EAApB,EAAgCsF,KAAK,CAACe,aAAN,CAAoBE,SAApB,EAAhC,EAAiEnF,MAAM,CAACyB,KAAP,CAAaJ,SAA9E,CAAlB;AACArB,YAAAA,MAAM,CAAC4B,QAAP,CAAgBiD,GAAhB,CAAoBK,SAApB;AACAlF,YAAAA,MAAM,CAAC8B,KAAP,GAAe9C,WAAW,CAACoG,MAA3B;AACH,WAJD,MAIO,IAAIpF,MAAM,CAAC8B,KAAP,KAAiB9C,WAAW,CAACoG,MAAjC,EAAyC;AAC5CpF,YAAAA,MAAM,CAAC8B,KAAP,GAAe9C,WAAW,CAAC+C,IAA3B;AACH,WARqE,CAUtE;;;AACA,cAAImC,KAAK,CAACmB,WAAN,IAAqBrF,MAAM,CAAC8B,KAAP,KAAiB9C,WAAW,CAACsG,SAAlD,IAA+DtF,MAAM,CAAC8B,KAAP,KAAiB9C,WAAW,CAAC2E,aAAhG,EAA+G;AAC3G3D,YAAAA,MAAM,CAAC8B,KAAP,GAAe9C,WAAW,CAACsG,SAA3B,CAD2G,CAE3G;AACH;AACJ;AAED;AACJ;AACA;;;AACY5E,QAAAA,oBAAoB,CAACV,MAAD,EAA2B;AACnD,cAAIA,MAAM,CAACqC,IAAP,IAAerC,MAAM,CAACqC,IAAP,CAAYkD,OAA/B,EAAwC;AACpCvF,YAAAA,MAAM,CAACqC,IAAP,CAAYmD,WAAZ,CAAwBxF,MAAM,CAAC2B,QAA/B;AACA3B,YAAAA,MAAM,CAACqC,IAAP,CAAYoD,oBAAZ,CAAiC,CAAjC,EAAoC,CAApC,EAAuCzF,MAAM,CAAC6B,QAA9C;AACH;AACJ;AAED;AACJ;AACA;;;AACY5B,QAAAA,cAAc,CAACD,MAAD,EAA2B;AAC7C,cAAIA,MAAM,CAACqC,IAAP,IAAerC,MAAM,CAACqC,IAAP,CAAYkD,OAA/B,EAAwC;AACpCvF,YAAAA,MAAM,CAACqC,IAAP,CAAYqD,OAAZ;AACA1F,YAAAA,MAAM,CAACqC,IAAP,GAAcC,SAAd;AACH;AACJ;;AApXoC,O", "sourcesContent": ["import { _decorator, Vec3, Node } from \"cc\";\nimport { System } from \"../base/System\";\nimport { RegisterTypeID } from \"../base/TypeID\";\nconst { ccclass } = _decorator;\n\n/**\n * Player state enumeration\n */\nexport enum PlayerState {\n    IDLE = \"idle\",\n    MOVING = \"moving\",\n    ATTACKING = \"attacking\",\n    TAKING_DAMAGE = \"taking_damage\",\n    DEAD = \"dead\",\n    INVULNERABLE = \"invulnerable\"\n}\n\n/**\n * Player statistics\n */\nexport interface PlayerStats {\n    maxHealth: number;\n    currentHealth: number;\n    attackPower: number;\n    moveSpeed: number;\n    defense: number;\n    experience: number;\n    level: number;\n}\n\n/**\n * Player input data\n */\nexport interface PlayerInput {\n    moveDirection: Vec3;\n    isAttacking: boolean;\n    isJumping: boolean;\n    mousePosition: Vec3;\n    keyStates: { [key: string]: boolean };\n}\n\n/**\n * Player data\n */\nexport interface PlayerData {\n    id: number;\n    position: Vec3;\n    velocity: Vec3;\n    rotation: number;\n    state: PlayerState;\n    stats: PlayerStats;\n    inventory: string[];\n    abilities: string[];\n    statusEffects: Map<string, number>; // effect name -> remaining time\n    lastDamageTime: number;\n    invulnerabilityDuration: number;\n    node?: Node;\n}\n\n/**\n * PlayerSystem - manages player state, movement, combat, and progression\n */\n@ccclass(\"PlayerSystem\")\n@RegisterTypeID\nexport class PlayerSystem extends System {\n\n    private _players: Map<number, PlayerData> = new Map();\n    private _currentPlayerId: number | null = null;\n    private _inputBuffer: PlayerInput[] = [];\n    private _maxInputBufferSize: number = 10;\n    private _nextPlayerId: number = 1; // Auto-incrementing player ID\n\n    // Configuration\n    private _invulnerabilityTime: number = 2.0; // seconds\n    private _maxPlayers: number = 4; // for multiplayer support\n    \n    /**\n     * Get the system name\n     */\n    public getSystemName(): string {\n        return \"PlayerSystem\";\n    }\n    \n    /**\n     * Initialize the player system\n     */\n    protected onInit(): void {\n        console.log(\"PlayerSystem: Initializing player system\");\n        \n        // Initialize input buffer\n        this._inputBuffer = [];\n        \n        console.log(`PlayerSystem: Initialized with max players: ${this._maxPlayers}`);\n    }\n    \n    /**\n     * Cleanup the player system\n     */\n    protected onUnInit(): void {\n        console.log(\"PlayerSystem: Cleaning up player system\");\n        \n        // Cleanup all players\n        this._players.forEach(player => {\n            this._destroyPlayer(player);\n        });\n        \n        this._players.clear();\n        this._currentPlayerId = null;\n        this._inputBuffer.length = 0;\n        this._nextPlayerId = 1;\n        \n        console.log(\"PlayerSystem: Cleanup complete\");\n    }\n    \n    /**\n     * Update the player system\n     */\n    protected onUpdate(deltaTime: number): void {\n        // Process input buffer\n        this._processInputBuffer();\n        \n        // Update all players\n        this._players.forEach(player => {\n            this._updatePlayer(player, deltaTime);\n        });\n    }\n    \n    /**\n     * Late update - handle any post-update logic\n     */\n    protected onLateUpdate(_deltaTime: number): void {\n        // Update visual representations\n        this._players.forEach(player => {\n            this._updatePlayerVisuals(player);\n        });\n    }\n    \n    /**\n     * Create a new player\n     * @param startPosition Starting position for the player\n     * @param initialStats Initial player statistics\n     * @returns The player ID if creation was successful, or null if failed\n     */\n    public createPlayer(startPosition: Vec3, initialStats: Partial<PlayerStats> = {}): number | null {\n        if (this._players.size >= this._maxPlayers) {\n            console.warn(\"PlayerSystem: Cannot create player - max players reached\");\n            return null;\n        }\n\n        // Generate new player ID\n        const playerId = this._nextPlayerId++;\n\n        // Create default stats\n        const defaultStats: PlayerStats = {\n            maxHealth: 100,\n            currentHealth: 100,\n            attackPower: 10,\n            moveSpeed: 300,\n            defense: 5,\n            experience: 0,\n            level: 1\n        };\n\n        // Merge with provided stats\n        const stats = { ...defaultStats, ...initialStats };\n        stats.currentHealth = stats.maxHealth; // Ensure current health doesn't exceed max\n\n        // Create player data\n        const player: PlayerData = {\n            id: playerId,\n            position: new Vec3(startPosition),\n            velocity: new Vec3(),\n            rotation: 0,\n            state: PlayerState.IDLE,\n            stats: stats,\n            inventory: [],\n            abilities: [],\n            statusEffects: new Map(),\n            lastDamageTime: 0,\n            invulnerabilityDuration: 0,\n            node: undefined\n        };\n\n        this._players.set(playerId, player);\n\n        // Set as current player if it's the first one\n        if (this._currentPlayerId === null) {\n            this._currentPlayerId = playerId;\n        }\n\n        console.log(`PlayerSystem: Created player ${playerId}`);\n        return playerId;\n    }\n    \n    /**\n     * Remove a player\n     * @param playerId The ID of the player to remove\n     * @returns true if the player was removed\n     */\n    public removePlayer(playerId: number): boolean {\n        const player = this._players.get(playerId);\n        if (!player) {\n            return false;\n        }\n\n        this._destroyPlayer(player);\n        this._players.delete(playerId);\n\n        // Update current player if necessary\n        if (this._currentPlayerId === playerId) {\n            this._currentPlayerId = this._players.size > 0 ? this._players.keys().next().value : null;\n        }\n\n        console.log(`PlayerSystem: Removed player ${playerId}`);\n        return true;\n    }\n\n    /**\n     * Get a player by ID\n     * @param playerId The ID of the player to get\n     * @returns The player data or null if not found\n     */\n    public getPlayer(playerId: number): PlayerData | null {\n        return this._players.get(playerId) || null;\n    }\n\n    /**\n     * Get the current active player\n     * @returns The current player data or null if no current player\n     */\n    public getCurrentPlayer(): PlayerData | null {\n        return this._currentPlayerId !== null ? this._players.get(this._currentPlayerId) || null : null;\n    }\n\n    /**\n     * Set the current active player\n     * @param playerId The ID of the player to set as current\n     * @returns true if the player was set as current\n     */\n    public setCurrentPlayer(playerId: number): boolean {\n        if (!this._players.has(playerId)) {\n            return false;\n        }\n\n        this._currentPlayerId = playerId;\n        console.log(`PlayerSystem: Set current player to ${playerId}`);\n        return true;\n    }\n    \n    /**\n     * Apply damage to a player\n     * @param playerId The ID of the player to damage\n     * @param damage The amount of damage to apply\n     * @param _damageSource Optional source of the damage (unused parameter)\n     * @returns true if damage was applied\n     */\n    public damagePlayer(playerId: number, damage: number, _damageSource?: string): boolean {\n        const player = this._players.get(playerId);\n        if (!player || player.state === PlayerState.DEAD) {\n            return false;\n        }\n\n        // Check invulnerability\n        if (player.invulnerabilityDuration > 0) {\n            console.log(`PlayerSystem: Player ${playerId} is invulnerable, damage ignored`);\n            return false;\n        }\n\n        // Calculate actual damage (apply defense)\n        const actualDamage = Math.max(1, damage - player.stats.defense);\n\n        // Apply damage\n        player.stats.currentHealth = Math.max(0, player.stats.currentHealth - actualDamage);\n        player.lastDamageTime = Date.now();\n        player.invulnerabilityDuration = this._invulnerabilityTime;\n\n        // Update state\n        if (player.stats.currentHealth <= 0) {\n            player.state = PlayerState.DEAD;\n            console.log(`PlayerSystem: Player ${playerId} died`);\n        } else {\n            player.state = PlayerState.TAKING_DAMAGE;\n        }\n\n        console.log(`PlayerSystem: Player ${playerId} took ${actualDamage} damage (${player.stats.currentHealth}/${player.stats.maxHealth} HP remaining)`);\n        return true;\n    }\n\n    /**\n     * Heal a player\n     * @param playerId The ID of the player to heal\n     * @param healAmount The amount of health to restore\n     * @returns true if healing was applied\n     */\n    public healPlayer(playerId: number, healAmount: number): boolean {\n        const player = this._players.get(playerId);\n        if (!player || player.state === PlayerState.DEAD) {\n            return false;\n        }\n\n        const oldHealth = player.stats.currentHealth;\n        player.stats.currentHealth = Math.min(player.stats.maxHealth, player.stats.currentHealth + healAmount);\n\n        const actualHealing = player.stats.currentHealth - oldHealth;\n        if (actualHealing > 0) {\n            console.log(`PlayerSystem: Player ${playerId} healed for ${actualHealing} HP`);\n            return true;\n        }\n\n        return false;\n    }\n    \n    /**\n     * Add input to the input buffer\n     * @param input The input data to add\n     */\n    public addInput(input: PlayerInput): void {\n        this._inputBuffer.push(input);\n        \n        // Limit buffer size\n        if (this._inputBuffer.length > this._maxInputBufferSize) {\n            this._inputBuffer.shift();\n        }\n    }\n    \n    /**\n     * Get all players\n     * @returns Array of all player data\n     */\n    public getAllPlayers(): PlayerData[] {\n        return Array.from(this._players.values());\n    }\n    \n    /**\n     * Get the number of active players\n     * @returns The number of active players\n     */\n    public getPlayerCount(): number {\n        return this._players.size;\n    }\n    \n    /**\n     * Update a single player\n     */\n    private _updatePlayer(player: PlayerData, deltaTime: number): void {\n        // Update invulnerability\n        if (player.invulnerabilityDuration > 0) {\n            player.invulnerabilityDuration -= deltaTime;\n            if (player.invulnerabilityDuration <= 0) {\n                player.invulnerabilityDuration = 0;\n                if (player.state === PlayerState.TAKING_DAMAGE) {\n                    player.state = PlayerState.IDLE;\n                }\n            }\n        }\n        \n        // Update status effects\n        player.statusEffects.forEach((duration, effect) => {\n            const newDuration = duration - deltaTime;\n            if (newDuration <= 0) {\n                player.statusEffects.delete(effect);\n                console.log(`PlayerSystem: Status effect ${effect} expired for player ${player.id}`);\n            } else {\n                player.statusEffects.set(effect, newDuration);\n            }\n        });\n        \n        // Update position based on velocity\n        player.position.add(Vec3.multiplyScalar(new Vec3(), player.velocity, deltaTime));\n        \n        // Apply friction to velocity\n        player.velocity.multiplyScalar(0.9);\n    }\n    \n    /**\n     * Process the input buffer\n     */\n    private _processInputBuffer(): void {\n        if (this._inputBuffer.length === 0 || !this._currentPlayerId) {\n            return;\n        }\n        \n        const player = this._players.get(this._currentPlayerId);\n        if (!player || player.state === PlayerState.DEAD) {\n            this._inputBuffer.length = 0; // Clear buffer\n            return;\n        }\n        \n        // Process the most recent input\n        const input = this._inputBuffer.pop();\n        if (input) {\n            this._applyInputToPlayer(player, input);\n        }\n        \n        // Clear remaining inputs (we only process the latest)\n        this._inputBuffer.length = 0;\n    }\n    \n    /**\n     * Apply input to a player\n     */\n    private _applyInputToPlayer(player: PlayerData, input: PlayerInput): void {\n        // Apply movement\n        if (input.moveDirection.length() > 0) {\n            const moveForce = Vec3.multiplyScalar(new Vec3(), input.moveDirection.normalize(), player.stats.moveSpeed);\n            player.velocity.add(moveForce);\n            player.state = PlayerState.MOVING;\n        } else if (player.state === PlayerState.MOVING) {\n            player.state = PlayerState.IDLE;\n        }\n        \n        // Apply attack\n        if (input.isAttacking && player.state !== PlayerState.ATTACKING && player.state !== PlayerState.TAKING_DAMAGE) {\n            player.state = PlayerState.ATTACKING;\n            // Attack logic would go here\n        }\n    }\n    \n    /**\n     * Update player visual representation\n     */\n    private _updatePlayerVisuals(player: PlayerData): void {\n        if (player.node && player.node.isValid) {\n            player.node.setPosition(player.position);\n            player.node.setRotationFromEuler(0, 0, player.rotation);\n        }\n    }\n    \n    /**\n     * Destroy a player's visual representation\n     */\n    private _destroyPlayer(player: PlayerData): void {\n        if (player.node && player.node.isValid) {\n            player.node.destroy();\n            player.node = undefined;\n        }\n    }\n}\n"]}