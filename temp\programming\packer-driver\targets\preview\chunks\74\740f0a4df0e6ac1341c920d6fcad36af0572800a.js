System.register(["__unresolved_0", "cc", "__unresolved_1"], function (_export, _context) {
  "use strict";

  var _reporterNs, _cclegacy, __checkObsolete__, __checkObsoleteInNamespace__, _decorator, Input, Node, UITransform, view, BaseUI, UILayer, UIMgr, _dec, _class, _crd, ccclass, property, ClickControlUI;

  function asyncGeneratorStep(gen, resolve, reject, _next, _throw, key, arg) { try { var info = gen[key](arg); var value = info.value; } catch (error) { reject(error); return; } if (info.done) { resolve(value); } else { Promise.resolve(value).then(_next, _throw); } }

  function _asyncToGenerator(fn) { return function () { var self = this, args = arguments; return new Promise(function (resolve, reject) { var gen = fn.apply(self, args); function _next(value) { asyncGeneratorStep(gen, resolve, reject, _next, _throw, "next", value); } function _throw(err) { asyncGeneratorStep(gen, resolve, reject, _next, _throw, "throw", err); } _next(undefined); }); }; }

  function _reportPossibleCrUseOfBaseUI(extras) {
    _reporterNs.report("BaseUI", "../UIMgr", _context.meta, extras);
  }

  function _reportPossibleCrUseOfUILayer(extras) {
    _reporterNs.report("UILayer", "../UIMgr", _context.meta, extras);
  }

  function _reportPossibleCrUseOfUIMgr(extras) {
    _reporterNs.report("UIMgr", "../UIMgr", _context.meta, extras);
  }

  return {
    setters: [function (_unresolved_) {
      _reporterNs = _unresolved_;
    }, function (_cc) {
      _cclegacy = _cc.cclegacy;
      __checkObsolete__ = _cc.__checkObsolete__;
      __checkObsoleteInNamespace__ = _cc.__checkObsoleteInNamespace__;
      _decorator = _cc._decorator;
      Input = _cc.Input;
      Node = _cc.Node;
      UITransform = _cc.UITransform;
      view = _cc.view;
    }, function (_unresolved_2) {
      BaseUI = _unresolved_2.BaseUI;
      UILayer = _unresolved_2.UILayer;
      UIMgr = _unresolved_2.UIMgr;
    }],
    execute: function () {
      _crd = true;

      _cclegacy._RF.push({}, "ba78dptDzlImqmuWejTbbym", "ClickControlUI", undefined);

      __checkObsolete__(['_decorator', 'EventTouch', 'Input', 'math', 'Node', 'UITransform', 'view']);

      ({
        ccclass,
        property
      } = _decorator);
      /**
       * 点击控制UI的配置参数
       */

      /**
       * 点击控制UI组件
       * 功能：创建一个全屏透明遮罩，用于控制特定区域的点击事件
       * 当点击在指定节点外时触发取消回调，点击在节点内时触发点击回调
       */
      _export("ClickControlUI", ClickControlUI = (_dec = ccclass('ClickControlUI'), _dec(_class = class ClickControlUI extends (_crd && BaseUI === void 0 ? (_reportPossibleCrUseOfBaseUI({
        error: Error()
      }), BaseUI) : BaseUI) {
        constructor() {
          super(...arguments);
          this._touchNodes = [];
          this._onCancel = null;
          this._onClick = null;
          this._target = null;
          this._clickContinue = false;
          this._onlyHideOnTargetClick = false;
        }

        static getUrl() {
          return "ui/common/ClickControlUI";
        }

        static getLayer() {
          return (_crd && UILayer === void 0 ? (_reportPossibleCrUseOfUILayer({
            error: Error()
          }), UILayer) : UILayer).Top;
        }

        isTouchControl() {
          return this._touchNodes.length > 0;
        }

        onLoad() {
          var trans = this.getComponent(UITransform);
          trans.width = view.getVisibleSize().width;
          trans.height = view.getVisibleSize().height;
          this.node.on(Input.EventType.TOUCH_CANCEL, this.onTouchCancel, this);
          this.node.on(Input.EventType.TOUCH_END, this.onTouchEnd, this);
        }

        _isTouchOnTarget(touchPos, target) {
          var targetUITrans = target.getComponent(UITransform);
          if (!targetUITrans) return false;
          var rect = targetUITrans.getBoundingBoxToWorld();
          return rect.contains(touchPos);
        }

        onShow(config) {
          var _this = this;

          return _asyncToGenerator(function* () {
            if (_this.isTouchControl()) return;
            _this._onCancel = config.onClickMiss || null;
            _this._onClick = config.onClickHit || null;
            _this._target = config.target || null;
            _this._touchNodes = config.touchNodes instanceof Node ? [config.touchNodes] : config.touchNodes;
            _this._clickContinue = config.clickContinue || false;
            _this._onlyHideOnTargetClick = config.onlyHideOnTargetClick || false;
          })();
        }

        onTouchCancel() {
          if (!this._onlyHideOnTargetClick) {
            (_crd && UIMgr === void 0 ? (_reportPossibleCrUseOfUIMgr({
              error: Error()
            }), UIMgr) : UIMgr).hideUI(ClickControlUI);
          }
        }

        onTouchEnd(event) {
          var touchPos = event.getUILocation();

          var touchNode = this._touchNodes.find(nd => this._isTouchOnTarget(touchPos, nd));

          if (touchNode) {
            var _this$_onClick;

            // 点击目标节点时触发点击回调
            (_this$_onClick = this._onClick) == null || _this$_onClick.call(this._target, touchNode);

            if (this._clickContinue) {
              this._touchNodes.splice(this._touchNodes.indexOf(touchNode), 1);

              return;
            }

            (_crd && UIMgr === void 0 ? (_reportPossibleCrUseOfUIMgr({
              error: Error()
            }), UIMgr) : UIMgr).hideUI(ClickControlUI);
          } else {
            // 点击非目标区域时触发取消回调
            if (!this._onlyHideOnTargetClick) {
              var _this$_onCancel;

              (_this$_onCancel = this._onCancel) == null || _this$_onCancel.call(this._target);
              (_crd && UIMgr === void 0 ? (_reportPossibleCrUseOfUIMgr({
                error: Error()
              }), UIMgr) : UIMgr).hideUI(ClickControlUI);
            }
          }
        }

        onHide() {
          var _this2 = this;

          return _asyncToGenerator(function* () {
            _this2._touchNodes = [];
            _this2._onCancel = null;
            _this2._onClick = null;
            _this2._target = null;
            _this2._clickContinue = false;
            _this2._onlyHideOnTargetClick = false;
          })();
        }

        onClose() {
          return _asyncToGenerator(function* () {})();
        }

      }) || _class));

      _cclegacy._RF.pop();

      _crd = false;
    }
  };
});
//# sourceMappingURL=740f0a4df0e6ac1341c920d6fcad36af0572800a.js.map