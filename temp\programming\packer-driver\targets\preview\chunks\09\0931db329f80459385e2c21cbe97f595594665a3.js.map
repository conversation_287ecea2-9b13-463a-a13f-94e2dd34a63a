{"version": 3, "sources": ["file:///E:/M2Game/Client/assets/scripts/Game/ui/plane/boss/BossEntity.ts"], "names": ["_decorator", "BossBase", "ccclass", "property", "BossEntity", "_datas", "_data", "_atkPointsPool", "_attacks", "_uiNode", "_topAnimNode", "_idleName", "_units", "Map", "_colUnitsIndex", "_colUnits", "_deadUnitIds", "_atkUnits", "_atkUnitSounds", "_atkUnitSoundIds", "_formIndex", "_formNum", "_nextForm", "_prePosX", "_prePosY", "_posX", "_posY", "_trackCom", "_curTrackType", "_curTrack", "_trackTime", "_trackOffX", "_trackOffY", "_moveToX", "_moveToY", "_moveSpeed", "_bArriveDes", "_transFormMove", "_nextWayPointTime", "_nextWayPointX", "_nextWayPointY", "_nextWayPointInterval", "_nextWaySpeed", "_shootAble", "_atkActions", "_nextAttackInterval", "_nextAttackTime", "_bOrde<PERSON><PERSON><PERSON>ck", "_orderIndex", "_attackID", "_atkPointDatas", "_attackPoints", "_orderAtkArr", "_action", "_bDamageable", "_b<PERSON><PERSON>ckMove", "_bFirstWayPoint", "transformBattle", "_bRemoveable", "_shadow", "wingmanPlanes", "_cloakeAnim", "init", "datas", "length", "active", "setShadow", "shadow", "isDamageable"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAASA,MAAAA,U,OAAAA,U;;AACFC,MAAAA,Q;;;;;;;;;OAKD;AAAEC,QAAAA,OAAF;AAAWC,QAAAA;AAAX,O,GAAwBH,U;;yBAGTI,U,WADpBF,OAAO,CAAC,YAAD,C,gBAAR,MACqBE,UADrB;AAAA;AAAA,gCACiD;AAAA;AAAA;AAAA,eAC7CC,MAD6C,GAC7B,EAD6B;AAAA,eAE7CC,KAF6C,GAEhC,IAFgC;AAAA,eAG7CC,cAH6C,GAGb,EAHa;AAAA,eAI7CC,QAJ6C,GAIxB,EAJwB;AAAA,eAK7CC,OAL6C,GAK7B,IAL6B;AAAA,eAM7CC,YAN6C,GAMxB,IANwB;AAAA,eAO7CC,SAP6C,GAOzB,OAPyB;AAAA,eAQ7CC,MAR6C,GAQb,IAAIC,GAAJ,EARa;AAAA,eAS7CC,cAT6C,GASpB,CAToB;AAAA,eAU7CC,SAV6C,GAUrB,EAVqB;AAAA,eAW7CC,YAX6C,GAWpB,EAXoB;AAAA,eAY7CC,SAZ6C,GAYrB,EAZqB;AAAA,eAa7CC,cAb6C,GAaP,IAAIL,GAAJ,EAbO;AAAA,eAc7CM,gBAd6C,GAcL,IAAIN,GAAJ,EAdK;AAAA,eAe7CO,UAf6C,GAexB,CAAC,CAfuB;AAAA,eAgB7CC,QAhB6C,GAgB1B,CAhB0B;AAAA,eAiB7CC,SAjB6C,GAiBxB,KAjBwB;AAAA,eAkB7CC,QAlB6C,GAkB1B,CAlB0B;AAAA,eAmB7CC,QAnB6C,GAmB1B,CAnB0B;AAAA,eAoB7CC,KApB6C,GAoB7B,CApB6B;AAAA,eAqB7CC,KArB6C,GAqB7B,CArB6B;AAAA,eAsB7CC,SAtB6C,GAsBjB,IAtBiB;AAAA,eAuB7CC,aAvB6C,GAuBrB,CAAC,CAvBoB;AAAA,eAwB7CC,SAxB6C,GAwB5B,IAxB4B;AAAA,eAyB7CC,UAzB6C,GAyBxB,CAzBwB;AAAA,eA0B7CC,UA1B6C,GA0BxB,CA1BwB;AAAA,eA2B7CC,UA3B6C,GA2BxB,CA3BwB;AAAA,eA4B7CC,QA5B6C,GA4B1B,CA5B0B;AAAA,eA6B7CC,QA7B6C,GA6B1B,CA7B0B;AAAA,eA8B7CC,UA9B6C,GA8BxB,CA9BwB;AAAA,eA+B7CC,WA/B6C,GA+BtB,KA/BsB;AAAA,eAgC7CC,cAhC6C,GAgCnB,KAhCmB;AAAA,eAiC7CC,iBAjC6C,GAiCjB,CAjCiB;AAAA,eAkC7CC,cAlC6C,GAkCpB,CAlCoB;AAAA,eAmC7CC,cAnC6C,GAmCpB,CAnCoB;AAAA,eAoC7CC,qBApC6C,GAoCb,CApCa;AAAA,eAqC7CC,aArC6C,GAqCrB,CArCqB;AAAA,eAsC7CC,UAtC6C,GAsCvB,IAtCuB;AAAA,eAuC7CC,WAvC6C,GAuCxB,EAvCwB;AAAA,eAwC7CC,mBAxC6C,GAwCf,CAxCe;AAAA,eAyC7CC,eAzC6C,GAyCnB,CAzCmB;AAAA,eA0C7CC,aA1C6C,GA0CpB,KA1CoB;AAAA,eA2C7CC,WA3C6C,GA2CvB,CA3CuB;AAAA,eA4C7CC,SA5C6C,GA4CzB,CA5CyB;AAAA,eA6C7CC,cA7C6C,GA6CrB,EA7CqB;AAAA,eA8C7CC,aA9C6C,GA8Cd,EA9Cc;AAAA,eA+C7CC,YA/C6C,GA+CpB,EA/CoB;AAAA,eAgD7CC,OAhD6C,GAgD3B,CAAC,CAhD0B;AAAA,eAiD7CC,YAjD6C,GAiDrB,KAjDqB;AAAA,eAkD7CC,YAlD6C,GAkDrB,KAlDqB;AAAA,eAmD7CC,eAnD6C,GAmDlB,KAnDkB;AAAA,eAoD7CC,eApD6C,GAoDlB,IApDkB;AAAA,eAqD7CC,YArD6C,GAqDrB,KArDqB;AAAA,eAsD7CC,OAtD6C,GAsD9B,IAtD8B;AAAA,eAuD7CC,aAvD6C,GAuDtB,EAvDsB;AAAA,eAwD7CC,WAxD6C,GAwDlB,IAxDkB;AAAA;;AA2D7C;AACJ;AACA;AACA;AACIC,QAAAA,IAAI,CAACC,KAAD,EAAe;AACf,eAAK1D,MAAL,GAAc0D,KAAd;AACA,eAAK1C,QAAL,GAAgB,KAAKhB,MAAL,CAAY2D,MAA5B;AACA,eAAKR,eAAL,GAAuB,IAAvB,CAHe,CAIf;AACA;AACA;AACA;;AACA,eAAKS,MAAL,GAAc,IAAd;AACH;AAED;AACJ;AACA;AACA;;;AACIC,QAAAA,SAAS,CAACC,MAAD,EAAc;AACnB,eAAKR,OAAL,GAAeQ,MAAf;AACH,SAhF4C,CAkFj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pB,iBAAO,KAAKd,YAAZ;AACH,SAtiB4C,CAwiBj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mCiD,O", "sourcesContent": ["import { _decorator, Component } from 'cc';\r\nimport BossBase from './BossBase';\r\nimport AttackPoint from '../../base/AttackPoint';\r\nimport BossUnit from './BossUnit';\r\nimport TrackComponent from '../../base/TrackComponent';\r\nimport PfFrameAnim from '../../base/PfFrameAnim';\r\nconst { ccclass, property } = _decorator;\r\n\r\n@ccclass(\"BossEntity\")\r\nexport default class BossEntity extends BossBase {\r\n    _datas: any[] = [];\r\n    _data: any = null;\r\n    _atkPointsPool: AttackPoint[] = [];\r\n    _attacks: number[] = [];\r\n    _uiNode: Node = null;\r\n    _topAnimNode: Node = null;\r\n    _idleName: string = \"idle1\";\r\n    _units: Map<number, BossUnit> = new Map();\r\n    _colUnitsIndex: number = 0;\r\n    _colUnits: BossUnit[] = [];\r\n    _deadUnitIds: number[] = [];\r\n    _atkUnits: BossUnit[] = [];\r\n    _atkUnitSounds: Map<number, string> = new Map();\r\n    _atkUnitSoundIds: Map<number, number> = new Map();\r\n    _formIndex: number = -1;\r\n    _formNum: number = 0;\r\n    _nextForm: boolean = false;\r\n    _prePosX: number = 0;\r\n    _prePosY: number = 0;\r\n    _posX: number = 0;\r\n    _posY: number = 0;\r\n    _trackCom: TrackComponent = null;\r\n    _curTrackType: number = -1;\r\n    _curTrack: any = null;\r\n    _trackTime: number = 0;\r\n    _trackOffX: number = 0;\r\n    _trackOffY: number = 0;\r\n    _moveToX: number = 0;\r\n    _moveToY: number = 0;\r\n    _moveSpeed: number = 0;\r\n    _bArriveDes: boolean = false;\r\n    _transFormMove: boolean = false;\r\n    _nextWayPointTime: number = 0;\r\n    _nextWayPointX: number = 0;\r\n    _nextWayPointY: number = 0;\r\n    _nextWayPointInterval: number = 0;\r\n    _nextWaySpeed: number = 0;\r\n    _shootAble: boolean = true;\r\n    _atkActions: any[] = [];\r\n    _nextAttackInterval: number = 0;\r\n    _nextAttackTime: number = 0;\r\n    _bOrderAttack: boolean = false;\r\n    _orderIndex: number = 0;\r\n    _attackID: number = 0;\r\n    _atkPointDatas: any[] = [];\r\n    _attackPoints: AttackPoint[] = [];\r\n    _orderAtkArr: number[] = [];\r\n    _action: number = -1;\r\n    _bDamageable: boolean = false;\r\n    _bAttackMove: boolean = false;\r\n    _bFirstWayPoint: boolean = false;\r\n    transformBattle: boolean = true;\r\n    _bRemoveable: boolean = false;\r\n    _shadow: any = null;\r\n    wingmanPlanes: any[] = [];\r\n    _cloakeAnim: PfFrameAnim = null;\r\n\r\n\r\n    /**\r\n     * 初始化 Boss 数据\r\n     * @param datas Boss 数据数组\r\n     */\r\n    init(datas: any[]) {\r\n        this._datas = datas;\r\n        this._formNum = this._datas.length;\r\n        this._bFirstWayPoint = true;\r\n        // this._initUI();\r\n        // this._initProperty();\r\n        // this._initTrack();\r\n        // this.setFormIndex(0);\r\n        this.active = true;\r\n    }\r\n\r\n    /**\r\n     * 设置影子\r\n     * @param shadow 影子对象\r\n     */\r\n    setShadow(shadow: any) {\r\n        this._shadow = shadow;\r\n    }\r\n\r\n//     /**\r\n//      * 设置形态索引\r\n//      * @param index 形态索引\r\n//      */\r\n//     setFormIndex(index: number) {\r\n//         if (this._formIndex !== index) {\r\n//             this._formIndex = index;\r\n//             this._bOrderAttack = true;\r\n//             this._orderIndex = 0;\r\n//             this._data = this._datas[this._formIndex];\r\n//             this._idleName = `idle${this._formIndex + 1}`;\r\n//             this._collideAtk = this._data.collideAttack;\r\n\r\n//             if (index === 0) {\r\n//                 this._initUnits();\r\n//                 this.setAction(BossAction.Appear);\r\n//             } else {\r\n//                 this._units.forEach((unit) => {\r\n//                     unit.init(BossManager.getUnitData(unit.id), this);\r\n//                     unit.setCollideAtk(this._data.collideAttack);\r\n//                     unit.setPropertyRate(this.propertyRate);\r\n//                 });\r\n//                 this.setAction(BossAction.Transform);\r\n//             }\r\n\r\n//             this._orderAtkArr = [];\r\n//             for (let i = 0; i < this._data.attackActions.length; i++) {\r\n//                 this._orderAtkArr.push(i);\r\n//             }\r\n\r\n//             this._atkPointDatas = [];\r\n//             for (const point of this._data.attackPoints) {\r\n//                 const data = [point.bAvailable, point];\r\n//                 this._atkPointDatas.push(data);\r\n//             }\r\n\r\n//             this._atkActions = [...this._data.attackActions];\r\n//             this._colUnitsIndex = 0;\r\n//             this._colUnits = [];\r\n//             this._deadUnitIds = [];\r\n//             this.unitArr = [];\r\n//         }\r\n//     }\r\n\r\n//     /**\r\n//      * 进入下一形态\r\n//      */\r\n//     enterNextForm() {\r\n//         if (this._formIndex < this._datas.length - 1) {\r\n//             this._formIndex++;\r\n//             this.setFormIndex(this._formIndex);\r\n//         }\r\n//     }\r\n\r\n//     /**\r\n//      * 设置 Boss 的行为\r\n//      * @param action 行为类型\r\n//      */\r\n//     setAction(action: BossAction) {\r\n//         if (this._action !== action) {\r\n//             this._action = action;\r\n\r\n//             switch (this._action) {\r\n//                 case BossAction.Normal:\r\n//                     this._playSkel(this._idleName, true);\r\n//                     this.setDamangeable(true);\r\n//                     break;\r\n\r\n//                 case BossAction.Appear:\r\n//                     this._playSkel(`enter${this._formIndex + 1}`, true);\r\n//                     this.setDamangeable(false);\r\n//                     this._startAppearTrack();\r\n//                     break;\r\n\r\n//                 case BossAction.Transform:\r\n//                     this._playSkel(`ready${this._formIndex + 1}`, false, () => {\r\n//                         this.transformBattle && this.transformEnd();\r\n//                     });\r\n//                     this.setDamangeable(false);\r\n//                     break;\r\n\r\n//                 case BossAction.AttackPrepare:\r\n//                     this._checkAtkAnim() || this.scheduleOnce(() => {\r\n//                         this.setAction(BossAction.AttackIng);\r\n//                     });\r\n//                     this.setDamangeable(true);\r\n//                     break;\r\n\r\n//                 case BossAction.AttackIng:\r\n//                 case BossAction.AttackOver:\r\n//                     this.setDamangeable(true);\r\n//                     break;\r\n\r\n//                 case BossAction.Blast:\r\n//                     this.setDamangeable(false);\r\n//                     break;\r\n\r\n//                 default:\r\n//                     this.setDamangeable(true);\r\n//             }\r\n//         }\r\n//     }\r\n\r\n//     // ...前面的代码...\r\n\r\n//     /**\r\n//      * 检查并生成 Boss 的僚机\r\n//      */\r\n//     checkBossWingman() {\r\n//         if (this._data.enemyId > 0 && this._data.enemyPos.length > 0) {\r\n//             for (let i = 0; i < this._data.enemyPos.length; i++) {\r\n//                 const wingman = EnemyManager.EnemyMgr.addPlane(this._data.enemyId);\r\n//                 wingman.setExp(0);\r\n//                 wingman.setScaleType(EnemyScale.None);\r\n//                 wingman.attack = this._data.attack;\r\n//                 wingman.node.position = this._data.enemyPos[i];\r\n\r\n//                 const trackGroup = i % 2 === 0 ? this._data.enemyTrackGroup1 : this._data.enemyTrackGroup2;\r\n//                 wingman.initTrack(trackGroup, [-1, 0, 0], wingman.node.position.x, wingman.node.position.y);\r\n//                 wingman.startBattle();\r\n//                 wingman.collideAble = false;\r\n\r\n//                 this.wingmanPlanes.push(wingman);\r\n//             }\r\n//         }\r\n//     }\r\n\r\n//     /**\r\n//      * 开始战斗\r\n//      */\r\n//     startBattle() {\r\n//         this.active = true;\r\n//         this._startNormalTrack();\r\n//         this.setAction(BossAction.Normal);\r\n//         this._checkNextCollideUnits();\r\n//         this.checkBossWingman();\r\n//     }\r\n\r\n//     /**\r\n//      * 检查下一个碰撞单元\r\n//      */\r\n//     _checkNextCollideUnits(): boolean {\r\n//         const currentUnits = this._data.unitsOrder[this._colUnitsIndex];\r\n//         let hasAliveUnits = false;\r\n\r\n//         for (const unitId of currentUnits) {\r\n//             if (!Tools.arrContain(this._deadUnitIds, unitId)) {\r\n//                 hasAliveUnits = true;\r\n//                 break;\r\n//             }\r\n//         }\r\n\r\n//         if (hasAliveUnits) {\r\n//             if (this._colUnits.length === 0) {\r\n//                 for (const unitId of currentUnits) {\r\n//                     const unit = this._units.get(unitId);\r\n//                     unit.setCollideAble(true);\r\n//                     this._colUnits.push(unit);\r\n//                     this.unitArr.push(unit);\r\n//                 }\r\n//             }\r\n//         } else {\r\n//             this._colUnitsIndex++;\r\n//             if (this._colUnitsIndex >= this._data.unitsOrder.length) {\r\n//                 return false;\r\n//             }\r\n\r\n//             const nextUnits = this._data.unitsOrder[this._colUnitsIndex];\r\n//             this._colUnits = [];\r\n//             this.unitArr = [];\r\n\r\n//             for (const unitId of nextUnits) {\r\n//                 const unit = this._units.get(unitId);\r\n//                 unit.setCollideAble(true);\r\n//                 this._colUnits.push(unit);\r\n//                 this.unitArr.push(unit);\r\n//             }\r\n//         }\r\n\r\n//         return true;\r\n//     }\r\n\r\n//     /**\r\n//      * 更新游戏逻辑\r\n//      * @param deltaTime 每帧时间\r\n//      */\r\n//     updateGameLogic(deltaTime: number) {\r\n//         if (this.active && !this.isDead && !this._nextForm) {\r\n//             this.wingmanPlanes.forEach((wingman) => {\r\n//                 wingman.node.angle += this._data.enemyRotate * deltaTime;\r\n//             });\r\n\r\n//             switch (this._action) {\r\n//                 case BossAction.Normal:\r\n//                     this._processNextWayPoint(deltaTime);\r\n//                     this._updateMove(deltaTime);\r\n//                     this._processNextAttack(deltaTime);\r\n//                     break;\r\n\r\n//                 case BossAction.Appear:\r\n//                     this._updateMove(deltaTime);\r\n//                     if (this._bArriveDes) {\r\n//                         this.setAction(BossAction.Transform);\r\n//                     }\r\n//                     break;\r\n\r\n//                 case BossAction.Transform:\r\n//                     if (this._transFormMove) {\r\n//                         this._updateMove(deltaTime);\r\n//                     }\r\n//                     break;\r\n\r\n//                 case BossAction.AttackPrepare:\r\n//                     this._processNextWayPoint(deltaTime);\r\n//                     if (this._bAttackMove) {\r\n//                         this._updateMove(deltaTime);\r\n//                     }\r\n//                     break;\r\n\r\n//                 case BossAction.AttackIng:\r\n//                     this._processNextWayPoint(deltaTime);\r\n//                     if (this._bAttackMove) {\r\n//                         this._updateMove(deltaTime);\r\n//                     }\r\n//                     this._udpateShoot(deltaTime);\r\n//                     break;\r\n\r\n//                 case BossAction.AttackOver:\r\n//                     this._processNextWayPoint(deltaTime);\r\n//                     if (this._bAttackMove) {\r\n//                         this._updateMove(deltaTime);\r\n//                     }\r\n//                     this.setAction(BossAction.Normal);\r\n//                     break;\r\n\r\n//                 case BossAction.Blast:\r\n//                     break;\r\n//             }\r\n\r\n//             this._units.forEach((unit) => {\r\n//                 unit.updateGameLogic(deltaTime);\r\n//             });\r\n//         }\r\n//     }\r\n\r\n//     /**\r\n//      * Boss 死亡逻辑\r\n//      */\r\n//     toDie() {\r\n//         if (!this.isDead) {\r\n//             this.isDead = true;\r\n//             this.setAction(BossAction.Blast);\r\n//             this._playDieAnim();\r\n\r\n//             if (this._data.id >= 250 && this._data.id < 300) {\r\n//                 let allBossesDead = true;\r\n//                 for (const boss of BossManager.BossMgr.bosses) {\r\n//                     if (boss !== this && !boss.isDead) {\r\n//                         allBossesDead = false;\r\n//                         break;\r\n//                     }\r\n//                 }\r\n\r\n//                 if (allBossesDead) {\r\n//                     this.checkLoot();\r\n//                 }\r\n//             } else if (this._data.nextBoss.length > 0 && GameIns.battleManager.isGameType(GameType.Boss)) {\r\n//                 // Do nothing, next boss will be handled\r\n//             } else {\r\n//                 this.checkLoot();\r\n//             }\r\n\r\n//             this.onDie();\r\n//             GameIns.loadManager.addExp(this._data.exp);\r\n//         }\r\n//     }\r\n\r\n//     /**\r\n//      * 单元销毁逻辑\r\n//      * @param unit 被销毁的单元\r\n//      */\r\n//     unitDestroyed(unit: any) {\r\n//         this._deadUnitIds.push(unit.unitId);\r\n//         Tools.arrRemove(this._colUnits, unit);\r\n\r\n//         let allUnitsDead = true;\r\n//         this._units.forEach((unit) => {\r\n//             if (!unit.isDead) {\r\n//                 allUnitsDead = false;\r\n//             }\r\n//         });\r\n\r\n//         for (let i = 0; i < this._atkActions.length; i++) {\r\n//             let hasActivePoints = false;\r\n//             for (const pointId of this._atkActions[i].atkPointId) {\r\n//                 const pointData = this._atkPointDatas[pointId];\r\n//                 if (pointData[0]) {\r\n//                     if (unit.unitId === pointData[1].atkUnitId) {\r\n//                         pointData[0] = false;\r\n//                     } else {\r\n//                         hasActivePoints = true;\r\n//                     }\r\n//                 }\r\n//             }\r\n\r\n//             if (!hasActivePoints) {\r\n//                 this._atkActions.splice(i, 1);\r\n//                 i--;\r\n//             }\r\n//         }\r\n\r\n//         for (let i = 0; i < this._attackPoints.length; i++) {\r\n//             if (this._attackPoints[i].getAtkUnitId() === unit.unitId) {\r\n//                 this._attackPoints.splice(i, 1);\r\n//                 i--;\r\n//             }\r\n//         }\r\n\r\n//         const soundKey = this._atkUnitSounds.get(unit.unitId);\r\n//         if (soundKey) {\r\n//             this._atkUnitSounds.delete(unit.unitId);\r\n//         }\r\n\r\n//         const soundId = this._atkUnitSoundIds.get(unit.unitId);\r\n//         if (soundId !== null) {\r\n//             this._atkUnitSoundIds.delete(unit.unitId);\r\n//             GameIns.default.audioManager.stopEffect(soundId);\r\n//         }\r\n\r\n//         this.setBodySkin(this._deadUnitIds.length);\r\n\r\n//         if (allUnitsDead) {\r\n//             this._formIndex++;\r\n//             if (this._checkNextForm()) {\r\n//                 this._nextForm = true;\r\n//             } else {\r\n//                 this._formIndex--;\r\n//                 this.toDie();\r\n//             }\r\n//         } else {\r\n//             this._checkNextCollideUnits();\r\n//         }\r\n//     }\r\n\r\n//     /**\r\n//      * 单元销毁动画结束\r\n//      * @param unit 被销毁的单元\r\n//      */\r\n//     unitDestroyAnimEnd(unit: any) {\r\n//         if (this._nextForm) {\r\n//             this._nextForm = false;\r\n//             this.setFormIndex(this._formIndex);\r\n//         }\r\n//     }\r\n\r\n//     /**\r\n//      * 检查是否有下一形态\r\n//      */\r\n//     _checkNextForm(): boolean {\r\n//         return this._formIndex < this._datas.length;\r\n//     }\r\n\r\n//     // ...继续按照文件中的顺序还原剩余函数...\r\n//     /**\r\n//      * 初始化属性\r\n//      */\r\n//     _initProperty() {\r\n//         for (let i = 0; i < this._datas.length; i++) {\r\n//             const data = this._datas[i];\r\n//             this._attacks.push(data.attack);\r\n//         }\r\n//     }\r\n\r\n//     /**\r\n//      * 设置属性倍率\r\n//      * @param rates 属性倍率数组\r\n//      * @param updateHp 是否更新血量\r\n//      */\r\n//     setPropertyRate(rates: number[], updateHp: boolean = false) {\r\n//         super.setPropertyRate(rates);\r\n\r\n//         this.m_totalHp = 0;\r\n//         this.m_curHp = 0;\r\n\r\n//         if (rates.length > 1) {\r\n//             for (let i = 0; i < this._attacks.length; i++) {\r\n//                 this._attacks[i] *= rates[1];\r\n//             }\r\n//         }\r\n\r\n//         if (rates.length > 2) {\r\n//             this._collideAtk *= rates[2];\r\n//         }\r\n\r\n//         this._units.forEach((unit) => {\r\n//             unit.setPropertyRate(this.propertyRate);\r\n//             this.m_totalHp += unit.maxHp;\r\n//             this.m_curHp += unit.maxHp;\r\n//         });\r\n\r\n//         if (this._data.nextBoss.length > 0) {\r\n//             for (let i = 0; i < this._data.nextBoss.length; i++) {\r\n//                 const bossData = BossManager.getBossDatas(this._data.nextBoss[i]);\r\n//                 if (bossData.length > 0) {\r\n//                     for (const unitId of bossData[0].units) {\r\n//                         const unitData = BossManager.getUnitData(unitId);\r\n//                         if (unitData) {\r\n//                             this.m_totalHp += unitData.hp * (this.propertyRate[0] || 1);\r\n//                             this.m_curHp += unitData.hp * (this.propertyRate[0] || 1);\r\n//                         }\r\n//                     }\r\n//                 }\r\n//             }\r\n//         }\r\n\r\n//         if (!updateHp) {\r\n//             BossBattleManager.setCurHp(this.m_curHp);\r\n//             BossBattleManager.setTotalHp(this.m_totalHp);\r\n//         }\r\n//     }\r\n\r\n//     /**\r\n//      * 获取形态数量\r\n//      */\r\n//     get formNum(): number {\r\n//         return this._formNum;\r\n//     }\r\n\r\n//     /**\r\n//      * 获取当前形态索引\r\n//      */\r\n//     get formIndex(): number {\r\n//         return this._formIndex;\r\n//     }\r\n\r\n//     /**\r\n//      * 获取当前形态的攻击力\r\n//      */\r\n//     get attack(): number {\r\n//         return this._attacks[this._formIndex];\r\n//     }\r\n\r\n//     /**\r\n//      * 获取碰撞攻击力\r\n//      */\r\n//     getColliderAtk(): number {\r\n//         return this._collideAtk;\r\n//     }\r\n\r\n//     /**\r\n//      * 获取 X 坐标\r\n//      */\r\n//     get posX(): number {\r\n//         return this._posX;\r\n//     }\r\n\r\n//     /**\r\n//      * 获取 Y 坐标\r\n//      */\r\n//     get posY(): number {\r\n//         return this._posY;\r\n//     }\r\n\r\n    /**\r\n     * 是否可被攻击\r\n     */\r\n    isDamageable(): boolean {\r\n        return this._bDamageable;\r\n    }\r\n\r\n//     /**\r\n//      * 是否可移除\r\n//      */\r\n//     get removeAble(): boolean {\r\n//         return this._bRemoveable;\r\n//     }\r\n\r\n//     set removeAble(value: boolean) {\r\n//         this._bRemoveable = value;\r\n//     }\r\n\r\n//     /**\r\n//      * 获取所有碰撞单元\r\n//      */\r\n//     getAllColUnits(): any[] {\r\n//         return this._colUnits;\r\n//     }\r\n\r\n//     /**\r\n//      * 设置是否可被攻击\r\n//      * @param damageable 是否可被攻击\r\n//      */\r\n//     setDamangeable(damageable: boolean) {\r\n//         this._bDamageable = damageable;\r\n//         this._units.forEach((unit) => {\r\n//             if (!unit.isDead) {\r\n//                 unit.damageable = damageable;\r\n//             }\r\n//         });\r\n//     }\r\n\r\n//     /**\r\n//      * 初始化 UI\r\n//      */\r\n//     _initUI() {\r\n//         this._uiNode = new Node();\r\n//         this.node.addChild(this._uiNode);\r\n\r\n//         this._topAnimNode = new Node();\r\n//         this.node.addChild(this._topAnimNode);\r\n//     }\r\n\r\n//     /**\r\n//      * 初始化单元\r\n//      */\r\n//     _initUnits() {\r\n//         Tools.removeChildByName(this._uiNode, \"units\");\r\n\r\n//         const unitsNode = new Node();\r\n//         this._uiNode.addChild(unitsNode);\r\n//         unitsNode.name = \"units\";\r\n\r\n//         for (const unitId of this._data.units) {\r\n//             const unitData = BossManager.getUnitData(unitId);\r\n//             if (unitData) {\r\n//                 const unitNode = new Node();\r\n//                 unitsNode.addChild(unitNode);\r\n\r\n//                 const unit = unitNode.addComponent(BossUnit);\r\n//                 unit.init(unitData, this);\r\n//                 unit.setCollideAtk(this._data.collideAttack);\r\n//                 unit.setPropertyRate(this.propertyRate);\r\n\r\n//                 this._units.set(unitData.uId, unit);\r\n//             }\r\n//         }\r\n\r\n//         for (let i = this._atkPointsPool.length; i < this._atkPointDatas.length; i++) {\r\n//             const pointNode = new Node();\r\n//             this.node.addChild(pointNode);\r\n//             pointNode.angle = -180;\r\n\r\n//             const attackPoint = pointNode.addComponent(AttackPoint);\r\n//             this._atkPointsPool.push(attackPoint);\r\n//         }\r\n//     }\r\n\r\n//     /**\r\n//      * 设置身体皮肤\r\n//      * @param skinIndex 皮肤索引\r\n//      */\r\n//     setBodySkin(skinIndex: number) {\r\n//         this._units.forEach((unit) => {\r\n//             if (unit.isBody()) {\r\n//                 unit.setSkin(skinIndex);\r\n//             }\r\n//         });\r\n//     }\r\n\r\n//     // ...继续按照文件中的顺序还原剩余函数...\r\n//     /**\r\n//  * 初始化轨迹\r\n//  */\r\n//     _initTrack() {\r\n//         this._trackCom = Tools.addScript(this.node, TrackComponent);\r\n//         this._trackCom.setTrackGroupStartCall((trackGroup, trackIndex, trackType) => { });\r\n//         this._trackCom.setTrackGroupOverCall((trackGroup) => {\r\n//             if (this._action === BossAction.Appear) {\r\n//                 this._trackCom.setTrackAble(false);\r\n//                 this.setAction(BossAction.Transform);\r\n//             }\r\n//         });\r\n//         this._trackCom.setTrackOverCall(() => { });\r\n//         this._trackCom.setTrackLeaveCall(() => { });\r\n//         this._trackCom.setTrackStartCall((track) => {\r\n//             this.setTrackType(track);\r\n//         });\r\n//     }\r\n\r\n//     /**\r\n//      * 开始出现轨迹\r\n//      */\r\n//     _startAppearTrack() {\r\n//         const trackGroup = new EnemyWave.TrackGroup();\r\n//         trackGroup.loopNum = 1;\r\n//         trackGroup.trackIDs = [this._data.appearParam[2]];\r\n//         trackGroup.speeds = [this._data.appearParam[3]];\r\n//         trackGroup.trackIntervals = [0];\r\n\r\n//         this._trackCom.init(this, [trackGroup], [], this._data.appearParam[0], this._data.appearParam[1]);\r\n//         this._trackCom.setTrackAble(true);\r\n//         this._trackCom.startTrack();\r\n//     }\r\n\r\n//     /**\r\n//      * 开始正常轨迹\r\n//      */\r\n//     _startNormalTrack() {\r\n//         this._trackCom.init(this, this._data.trackGroups, [], this.node.x, this.node.y);\r\n//         this._trackCom.setTrackAble(true);\r\n//         this._trackCom.startTrack();\r\n//         this.setAction(BossAction.Normal);\r\n//     }\r\n\r\n//     /**\r\n//      * 设置轨迹类型\r\n//      * @param track 当前轨迹\r\n//      */\r\n//     setTrackType(track: any) {\r\n//         if (track) {\r\n//             switch (track.type) {\r\n//                 case 4:\r\n//                 case 5:\r\n//                     if (this._curTrackType !== 4 && this._curTrackType !== 5) {\r\n//                         this._trackCom.setTrackAble(false);\r\n//                         this._shootAble = false;\r\n//                         this._colUnits.forEach((unit) => unit.setCollideAble(false));\r\n//                         this._playCloakeHideAnim(() => {\r\n//                             this._trackCom.setTrackAble(true);\r\n//                         });\r\n//                     }\r\n//                     break;\r\n\r\n//                 default:\r\n//                     if (this._curTrackType === 4 || this._curTrackType === 5) {\r\n//                         this._shootAble = false;\r\n//                         this._trackCom.setTrackAble(false);\r\n//                         this._playCloakeShowAnim(() => {\r\n//                             this._shootAble = true;\r\n//                             this._trackCom.setTrackAble(true);\r\n//                             this._colUnits.forEach((unit) => unit.setCollideAble(true));\r\n//                         });\r\n//                         this._playSkel(\"cloake\", true);\r\n//                     }\r\n//             }\r\n//             this._curTrackType = track.type;\r\n//         }\r\n//     }\r\n\r\n//     /**\r\n//      * 移动到指定位置\r\n//      * @param x X 坐标\r\n//      * @param y Y 坐标\r\n//      * @param speed 移动速度\r\n//      * @param transformMove 是否为变形移动\r\n//      */\r\n//     moveToPos(x: number, y: number, speed: number, transformMove: boolean = false) {\r\n//         this._moveToX = x;\r\n//         this._moveToY = y;\r\n//         this._moveSpeed = speed;\r\n//         this._bArriveDes = false;\r\n//         this._transFormMove = transformMove;\r\n//     }\r\n\r\n//     /**\r\n//      * 设置位置\r\n//      * @param pos 位置\r\n//      * @param arrived 是否到达目标\r\n//      */\r\n//     setPosition(pos: Vec2, arrived: boolean = false) {\r\n//         this._posX = pos.x;\r\n//         this._posY = pos.y;\r\n//         this.setPos(this._posX, this._posY);\r\n//         this._bArriveDes = arrived;\r\n//     }\r\n\r\n//     /**\r\n//      * 设置位置\r\n//      * @param x X 坐标\r\n//      * @param y Y 坐标\r\n//      * @param update 是否更新\r\n//      */\r\n//     setPos(x: number, y: number, update: boolean = true) {\r\n//         super.setPos(x, y, update);\r\n//         this._posX = x;\r\n//         this._posY = y;\r\n//     }\r\n\r\n//     /**\r\n//      * 处理下一个路径点\r\n//      * @param deltaTime 每帧时间\r\n//      */\r\n//     _processNextWayPoint(deltaTime: number) {\r\n//         if (this._bArriveDes && this._data.trackGroups.length === 0) {\r\n//             this._nextWayPointTime += deltaTime;\r\n//             if (this._nextWayPointTime > this._nextWayPointInterval) {\r\n//                 this._nextWayPointInterval = Tools.getRandomInArray(this._data.wayPointIntervals);\r\n//                 this._nextWayPointTime = 0;\r\n\r\n//                 if (this._bFirstWayPoint) {\r\n//                     this._bFirstWayPoint = false;\r\n//                 } else {\r\n//                     const index = Tools.random_int(0, this._data.wayPointXs.length - 1);\r\n//                     this._nextWayPointX = this._data.wayPointXs[index];\r\n//                     this._nextWayPointY = this._data.wayPointYs[index];\r\n//                     this._nextWaySpeed = Tools.getRandomInArray(this._data.speeds);\r\n//                     this.moveToPos(this._nextWayPointX, this._nextWayPointY, this._nextWaySpeed);\r\n//                 }\r\n//             }\r\n//         }\r\n//     }\r\n\r\n//     /**\r\n//      * 更新移动逻辑\r\n//      * @param deltaTime 每帧时间\r\n//      */\r\n//     _updateMove(deltaTime: number) {\r\n//         if (this._action === BossAction.Appear || this._data.trackGroups.length > 0) {\r\n//             this._trackCom.updateGameLogic(deltaTime);\r\n//         } else if (!this._bArriveDes) {\r\n//             this._prePosX = this._posX;\r\n//             this._prePosY = this._posY;\r\n\r\n//             const dx = this._moveToX - this._posX;\r\n//             const dy = this._moveToY - this._posY;\r\n//             const distance = Math.sqrt(dx * dx + dy * dy);\r\n\r\n//             const moveDistance = Math.min(this._moveSpeed * deltaTime, distance);\r\n//             const moveX = (dx / distance) * moveDistance;\r\n//             const moveY = (dy / distance) * moveDistance;\r\n\r\n//             this._posX += moveX;\r\n//             this._posY += moveY;\r\n\r\n//             this.setPos(this._posX, this._posY);\r\n\r\n//             this._bArriveDes = distance <= 0.5;\r\n//         }\r\n//     }\r\n\r\n//     // ...继续按照文件中的顺序还原剩余函数...\r\n//     /**\r\n//      * 处理下一次攻击\r\n//      * @param deltaTime 每帧时间\r\n//      */\r\n//     _processNextAttack(deltaTime: number) {\r\n//         if (this._shootAble && this._action === BossAction.Normal) {\r\n//             this._nextAttackTime += deltaTime;\r\n//             if (this._nextAttackTime > this._nextAttackInterval) {\r\n//                 this._nextAttackInterval = Tools.getRandomInArray(this._data.attackIntervals);\r\n//                 this._nextAttackTime = 0;\r\n\r\n//                 let attackAction = null;\r\n//                 if (this._bOrderAttack) {\r\n//                     const randomIndex = Tools.getRandomInArray(this._orderAtkArr);\r\n//                     Tools.arrRemove(this._orderAtkArr, randomIndex);\r\n//                     attackAction = this._atkActions[randomIndex];\r\n//                     this._orderIndex++;\r\n//                     if (this._orderIndex > this._atkActions.length - 1) {\r\n//                         this._bOrderAttack = false;\r\n//                     }\r\n//                 } else {\r\n//                     attackAction = Tools.getRandomInArray(this._atkActions);\r\n//                 }\r\n\r\n//                 if (attackAction) {\r\n//                     this._bAttackMove = attackAction.bAtkMove;\r\n//                     this._attackID = attackAction.atkActId;\r\n//                     this._attackPoints.splice(0);\r\n\r\n//                     for (const pointId of attackAction.atkPointId) {\r\n//                         const pointData = this._atkPointDatas[pointId];\r\n//                         if (pointData[0]) {\r\n//                             const attackPoint = this._atkPointsPool[pointId] || new AttackPoint();\r\n//                             attackPoint.initForBoss(pointData[1], this);\r\n//                             this._attackPoints.push(attackPoint);\r\n//                         }\r\n//                     }\r\n\r\n//                     if (this._attackPoints.length > 0) {\r\n//                         this.setAction(BossAction.AttackPrepare);\r\n//                     }\r\n//                 }\r\n//             }\r\n//         }\r\n//     }\r\n\r\n//     /**\r\n//      * 更新射击逻辑\r\n//      * @param deltaTime 每帧时间\r\n//      */\r\n//     _udpateShoot(deltaTime: number) {\r\n//         if (this._shootAble) {\r\n//             let allAttacksOver = true;\r\n\r\n//             for (const attackPoint of this._attackPoints) {\r\n//                 attackPoint.updateGameLogic(deltaTime);\r\n//                 if (!attackPoint.isAttackOver()) {\r\n//                     allAttacksOver = false;\r\n//                 }\r\n//             }\r\n\r\n//             if (allAttacksOver) {\r\n//                 this.setAction(BossAction.AttackOver);\r\n//             }\r\n//         }\r\n//     }\r\n\r\n//     /**\r\n//      * 检查攻击动画\r\n//      */\r\n//     _checkAtkAnim(): boolean {\r\n//         let hasAnimation = false;\r\n\r\n//         for (const attackPoint of this._attackPoints) {\r\n//             for (const anim of attackPoint.getAtkAnims()) {\r\n//                 const unit = this._units.get(anim[0]);\r\n//                 if (unit && !Tools.arrContain(this._deadUnitIds, unit.unitId)) {\r\n//                     hasAnimation = true;\r\n//                     unit.playSkel(anim[1], false, () => {\r\n//                         this.setAction(BossAction.AttackIng);\r\n//                     });\r\n//                 }\r\n//             }\r\n//         }\r\n\r\n//         return hasAnimation;\r\n//     }\r\n\r\n//     /**\r\n//      * 播放骨骼动画\r\n//      * @param animName 动画名称\r\n//      * @param loop 是否循环\r\n//      * @param callback 动画结束回调\r\n//      * @param unitId 单元 ID\r\n//      */\r\n//     _playSkel(animName: string, loop: boolean, callback: Function = null, unitId: number = -1) {\r\n//         this._units.forEach((unit, id) => {\r\n//             if (unitId === -1 || id === unitId) {\r\n//                 unit.playSkel(animName, loop, callback);\r\n//             }\r\n//         });\r\n//     }\r\n\r\n//     /**\r\n//      * 播放死亡动画\r\n//      */\r\n//     _playDieAnim() {\r\n//         const frameTime = GameConfig.ActionFrameTime;\r\n\r\n//         const playBlastEffects = () => {\r\n//             for (const blast of this._data.blastParam) {\r\n//                 const delay = blast[3] * frameTime;\r\n//                 this.scheduleOnce(() => {\r\n//                     EnemyEffectLayer.addBlastEffect(this, blast[2], {\r\n//                         x: blast[0],\r\n//                         y: blast[1],\r\n//                         scale: blast[4],\r\n//                         angle: blast[5],\r\n//                     });\r\n//                 }, delay);\r\n//             }\r\n//         };\r\n\r\n//         if (this._data.nextBoss.length > 0) {\r\n//             this._checkNextBoss();\r\n//         } else {\r\n//             this._bRemoveable = true;\r\n//         }\r\n\r\n//         playBlastEffects();\r\n//     }\r\n\r\n//     /**\r\n//      * 播放白屏死亡动画\r\n//      */\r\n//     playDieWhiteAnim() {\r\n//         this.scheduleOnce(() => {\r\n//             EffectLayer.showWhiteScreen(4 * GameConfig.ActionFrameTime, 255);\r\n//             this._uiNode.opacity = 0;\r\n//             this._units.forEach((unit) => {\r\n//                 unit.hideSmoke();\r\n//             });\r\n//         }, 41 * GameConfig.ActionFrameTime);\r\n//     }\r\n\r\n//     /**\r\n//      * 播放坠落动画\r\n//      */\r\n//     _playFallAnim() {\r\n//         const frameTime = GameConfig.ActionFrameTime;\r\n\r\n//         const fallSequence = sequence(\r\n//             moveTo(2 * frameTime, v2(-1, 6)),\r\n//             moveTo(frameTime, v2(3, -2))\r\n//         );\r\n\r\n//         this._uiNode.runAction(repeatForever(fallSequence));\r\n//         this._uiNode.runAction(scaleTo(60 * frameTime, 0.5));\r\n//     }\r\n\r\n//     /**\r\n//      * 播放震动动画\r\n//      */\r\n//     _playShakeAnim() {\r\n//         const frameTime = GameConfig.ActionFrameTime;\r\n\r\n//         tween(this._uiNode)\r\n//             .to(2 * frameTime, { position: v2(-3, -2) })\r\n//             .to(2 * frameTime, { position: v2(11, -14), angle: 1 })\r\n//             .to(2 * frameTime, { position: v2(7, 4) })\r\n//             .to(2 * frameTime, { position: v2(20, -9), angle: 0 })\r\n//             .to(2 * frameTime, { position: v2(29, 7) })\r\n//             .to(frameTime, { position: v2(13, -5) })\r\n//             .to(frameTime, { position: v2(17, 2) })\r\n//             .to(frameTime, { position: v2(4, -6) })\r\n//             .to(frameTime, { position: v2(14, 4) })\r\n//             .to(frameTime, { position: v2(-1, -4) })\r\n//             .to(frameTime, { position: v2(5, 6) })\r\n//             .to(frameTime, { position: v2(-3, -5) })\r\n//             .to(frameTime, { position: v2(1, 3) })\r\n//             .to(frameTime, { position: v2(-7, -6) })\r\n//             .to(frameTime, { position: v2(0, 2) })\r\n//             .to(frameTime, { position: v2(-3, -4) })\r\n//             .delay(frameTime)\r\n//             .to(frameTime, { position: v2(0, 0) })\r\n//             .start();\r\n//     }\r\n\r\n//     /**\r\n//      * 播放坠落震动动画\r\n//      */\r\n//     _playFallShake() {\r\n//         const frameTime = GameConfig.ActionFrameTime;\r\n\r\n//         const fallShakeSequence = sequence(\r\n//             moveTo(2 * frameTime, v2(-1, 6)),\r\n//             moveTo(frameTime, v2(3, -2))\r\n//         );\r\n\r\n//         this._uiNode.runAction(repeatForever(fallShakeSequence));\r\n//     }\r\n\r\n//     /**\r\n//      * 播放隐身动画\r\n//      */\r\n//     _playCloakeAnim() {\r\n//         if (!this._cloakeAnim) {\r\n//             const animNode = instantiate(GameConst.frameAnim);\r\n//             this.node.addChild(animNode, 11);\r\n\r\n//             this._cloakeAnim = animNode.getComponent(PfFrameAnim);\r\n//             this._cloakeAnim.init(\r\n//                 EnemyManager.enemyAtlas,\r\n//                 \"a_\",\r\n//                 12,\r\n//                 GameConfig.ActionFrameTime\r\n//             );\r\n//             animNode.active = false;\r\n//         }\r\n\r\n//         this._cloakeAnim.node.scale = 1.3;\r\n//         this._cloakeAnim.node.active = true;\r\n//         this._cloakeAnim.reset(1);\r\n//     }\r\n\r\n//     /**\r\n//      * 播放隐身消失动画\r\n//      * @param callback 动画结束回调\r\n//      */\r\n//     _playCloakeHideAnim(callback: Function = null) {\r\n//         const frameTime = GameConfig.ActionFrameTime;\r\n\r\n//         GameIns.audioManager.playEffect(\"cloake\");\r\n//         this._playCloakeAnim();\r\n\r\n//         tween(this.node)\r\n//             .to(5 * frameTime, { opacity: 90 })\r\n//             .to(2 * frameTime, { opacity: 0 })\r\n//             .call(() => {\r\n//                 this._playSkel(\"cloake\", true);\r\n//             })\r\n//             .to(6 * frameTime, { opacity: 255 })\r\n//             .call(() => {\r\n//                 if (callback) callback();\r\n//             })\r\n//             .start();\r\n//     }\r\n\r\n//     /**\r\n//      * 播放隐身显现动画\r\n//      * @param callback 动画结束回调\r\n//      */\r\n//     _playCloakeShowAnim(callback: Function = null) {\r\n//         const frameTime = GameConfig.ActionFrameTime;\r\n\r\n//         tween(this.node)\r\n//             .to(4 * frameTime, { opacity: 102 })\r\n//             .to(2 * frameTime, { opacity: 255 })\r\n//             .to(4 * frameTime, { opacity: 102 })\r\n//             .to(2 * frameTime, { opacity: 255 })\r\n//             .to(3 * frameTime, { opacity: 102 })\r\n//             .to(frameTime, { opacity: 0 })\r\n//             .call(() => {\r\n//                 this._playSkel(this._idleName, true);\r\n//                 this._playCloakeAnim();\r\n//             })\r\n//             .to(7 * frameTime, { opacity: 255 })\r\n//             .call(() => {\r\n//                 if (callback) callback();\r\n//             })\r\n//             .start();\r\n//     }\r\n//     /**\r\n//      * 检查并生成下一个 Boss\r\n//      */\r\n//     _checkNextBoss() {\r\n//         if (this._data.id === 200) {\r\n//             this._playSkel(\"next\", false, () => {\r\n//                 for (const nextBossId of this._data.nextBoss) {\r\n//                     const bossData = BossManager.getBossDatas(nextBossId)[0];\r\n//                     const boss = BossManager.createBossById(nextBossId);\r\n//                     boss.setPosition(this.node.position, true);\r\n//                     boss.active = false;\r\n//                     boss.setPropertyRate(this.propertyRate, true);\r\n\r\n//                     tween(boss.node)\r\n//                         .to(1, { position: v2(bossData.appearParam[0], bossData.appearParam[1]) })\r\n//                         .call(() => {\r\n//                             boss.setPosition(boss.node.position, true);\r\n//                             boss.startBattle();\r\n//                         })\r\n//                         .start();\r\n//                 }\r\n\r\n//                 this.node.active = false;\r\n//                 this._bRemoveable = true;\r\n//             });\r\n//         }\r\n//     }\r\n\r\n//     /**\r\n//      * 改变血量\r\n//      * @param delta 血量变化值\r\n//      */\r\n//     hpChange(delta: number) {\r\n//         this.m_curHp += delta;\r\n//         if (this.m_curHp < 0) {\r\n//             this.m_curHp = 0;\r\n//         }\r\n//         BossBattleManager.hpChange(delta, this.node);\r\n//     }\r\n\r\n//     /**\r\n//      * 获取血量百分比\r\n//      */\r\n//     getHpPercent(): number {\r\n//         return this.m_curHp / this.m_totalHp;\r\n//     }\r\n\r\n\r\n}"]}