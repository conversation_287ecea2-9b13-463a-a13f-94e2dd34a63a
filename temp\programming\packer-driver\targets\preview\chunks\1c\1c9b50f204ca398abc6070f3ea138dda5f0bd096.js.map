{"version": 3, "sources": ["file:///E:/M2Game/Client/assets/scripts/Game/ui/plane/boss/BossUnit.ts"], "names": ["_decorator", "Node", "Sprite", "tween", "Color", "Enum", "Vec2", "sp", "v2", "v3", "UITransform", "GameConfig", "ColliderComp", "Bullet", "EnemyEffectLayer", "MainPlane", "GameEnum", "BossHurt", "Tools", "GameIns", "ccclass", "property", "BossUnit", "_data", "_boss<PERSON><PERSON>", "_collideComp", "_unitType", "_curHp", "_maxHp", "_hpBar", "_hpWhite", "_hpWhiteTween", "defence", "_hpStage", "_hpStageIndex", "_action", "BossAction", "Normal", "_damaged", "_damageable", "_bSmoking", "_whiteNode", "_winkCount", "_bW<PERSON><PERSON><PERSON>e", "_winkAct", "_skel", "_curAnim", "_skelCallMap", "Map", "_skelEventCallMap", "_smokeSkelPool", "_smokePosArr", "_smokeSkelArr", "_smokeBoneArr", "onLoad", "init", "data", "boss<PERSON><PERSON>", "reset", "type", "EnemyType", "initData", "_initUI", "_refreshHpBar", "<PERSON><PERSON><PERSON>", "isDead", "_initCollide", "_checkHpStage", "removeSmoke", "hp", "addComp", "m_comps", "for<PERSON>ach", "comp", "colliderData", "collider", "setData", "x", "y", "width", "height", "node", "position", "enabled", "pos", "skelNode", "addComponent", "<PERSON><PERSON><PERSON><PERSON>", "setSiblingIndex", "UnitZIndex", "Skel", "Skeleton", "skeletonData", "boss<PERSON><PERSON><PERSON>", "skelDataMap", "get", "anim", "premultipliedAlpha", "addSpine", "mixArr", "mix", "setMix", "error", "log", "setCompleteListener", "trackEntry", "animationName", "animation", "name", "callback", "key", "setEventListener", "event", "eventName", "to", "color", "hurtColor", "getR", "getG", "getB", "WHITE", "opacity", "ActionFrameTime", "hpParam", "hpNode", "getChildByName", "uId", "parent", "setBossFrame", "whiteNode", "Type", "FILLED", "fillType", "FillType", "HORIZONTAL", "fill<PERSON><PERSON><PERSON>", "barNode", "posX", "posY", "scaleX", "setPosition", "setScale", "getScale", "active", "updateGameLogic", "deltaTime", "smoke", "index", "boneName", "bone", "findBone", "add", "worldX", "worldY", "update", "onCollide", "damageable", "entity", "damage", "getAttack", "hurtEffectManager", "createHurtNumByType", "console", "Math", "max", "warAttackManager", "playerWARatio", "hurt", "hpChange", "_checkHp", "_wink<PERSON><PERSON>e", "amount", "change", "newHp", "_playStageAnim", "_die", "hpStage", "length", "stop", "_playDieAnim", "unitDestroyed", "fillDifference", "abs", "call", "start", "playSkel", "loop", "set", "setAnimation", "setEventCallback", "skinIndex", "formIndex", "setAction", "action", "clone", "_playShakeAnim", "actionFrameTime", "angle", "hideSmoke", "push", "removeSpine", "id", "unitId", "isBody", "getUnitType", "setPropertyRate", "rates", "attack", "_collideAtk", "value", "boss<PERSON><PERSON><PERSON>", "curHp", "maxHp", "stageIndex", "blastParams", "blastParam", "param", "scheduleOnce", "effectData", "scale", "_onStageAnimEnd", "me", "addBlastEffect", "shakeParams", "blastShake", "smokeParams", "blastSmoke", "smokeAnim", "_getSmokeAnim", "zIndex", "SmokeTop", "SmokeBottom", "unitDestroyAnimEnd", "pop", "smokeNode", "smokeSkelData"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAASA,MAAAA,U,OAAAA,U;AAAuBC,MAAAA,I,OAAAA,I;AAAMC,MAAAA,M,OAAAA,M;AAAQC,MAAAA,K,OAAAA,K;AAAOC,MAAAA,K,OAAAA,K;AAAOC,MAAAA,I,OAAAA,I;AAAMC,MAAAA,I,OAAAA,I;AAAMC,MAAAA,E,OAAAA,E;AAAIC,MAAAA,E,OAAAA,E;AAAIC,MAAAA,E,OAAAA,E;AAAIC,MAAAA,W,OAAAA,W;;AAC7EC,MAAAA,U;;AAEEC,MAAAA,Y,iBAAAA,Y;;AACFC,MAAAA,M;;AACAC,MAAAA,gB;;AACEC,MAAAA,S,iBAAAA,S;;AACFC,MAAAA,Q;;AACAC,MAAAA,Q;;AACEC,MAAAA,K,iBAAAA,K;;AACAC,MAAAA,O,kBAAAA,O;;;;;;;;;OACH;AAAEC,QAAAA,OAAF;AAAWC,QAAAA;AAAX,O,GAAwBrB,U;;yBAGTsB,Q,WADpBF,OAAO,CAAC,UAAD,C,2BAAR,MACqBE,QADrB;AAAA;AAAA,gCAC+C;AAAA;AAAA;AAAA,eAO3CC,KAP2C,GAO9B,IAP8B;AAAA,eAQ3CC,UAR2C,GAQzB,IARyB;AAAA,eAS3CC,YAT2C,GASP,IATO;AAAA,eAU3CC,SAV2C,GAUvB,CAVuB;AAAA,eAW3CC,MAX2C,GAW1B,CAX0B;AAAA,eAY3CC,MAZ2C,GAY1B,CAZ0B;AAAA,eAa3CC,MAb2C,GAanB,IAbmB;AAAA,eAc3CC,QAd2C,GAcjB,IAdiB;AAAA,eAe3CC,aAf2C,GAetB,IAfsB;AAAA,eAgB3CC,OAhB2C,GAgBzB,CAhByB;AAAA,eAiB3CC,QAjB2C,GAiBxB,CAjBwB;AAAA,eAkB3CC,aAlB2C,GAkBnB,CAlBmB;AAAA,eAmB3CC,OAnB2C,GAmBzB;AAAA;AAAA,oCAASC,UAAT,CAAoBC,MAnBK;AAAA,eAoB3CC,QApB2C,GAoBvB,KApBuB;AAAA,eAqB3CC,WArB2C,GAqBpB,KArBoB;AAAA,eAsB3CC,SAtB2C,GAsBtB,KAtBsB;AAAA,eAuB3CC,UAvB2C,GAuBjB,IAvBiB;AAAA,eAwB3CC,UAxB2C,GAwBtB,CAxBsB;AAAA,eAyB3CC,WAzB2C,GAyBpB,KAzBoB;AAAA,eA0B3CC,QA1B2C,GA0B3B,IA1B2B;AAAA,eA2B3CC,KA3B2C,GA2B9B,IA3B8B;AAAA,eA4B3CC,QA5B2C,GA4BxB,EA5BwB;AAAA,eA6B3CC,YA7B2C,GA6BL,IAAIC,GAAJ,EA7BK;AAAA,eA8B3CC,iBA9B2C,GA8BA,IAAID,GAAJ,EA9BA;AAAA,eA+B3CE,cA/B2C,GA+BnB,EA/BmB;AAAA,eAgC3CC,YAhC2C,GAgCpB,EAhCoB;AAAA,eAiC3CC,aAjC2C,GAiCpB,EAjCoB;AAAA,eAkC3CC,aAlC2C,GAkCjB,EAlCiB;AAAA;;AAoC3CC,QAAAA,MAAM,GAAS,CACX;AACH;;AAEDC,QAAAA,IAAI,CAACC,IAAD,EAAYC,SAAZ,EAAkC;AAClC,eAAKC,KAAL;AACA,eAAKC,IAAL,GAAY;AAAA;AAAA,oCAASC,SAAT,CAAmBtC,QAA/B;AACA,eAAKC,KAAL,GAAaiC,IAAb;AACA,eAAKhC,UAAL,GAAkBiC,SAAlB;AAEA,eAAKI,QAAL;;AACA,eAAKC,OAAL;;AACA,eAAKC,aAAL;;AACA,eAAKC,OAAL,CAAa,CAAb;;AAEA,cAAI,KAAKrC,MAAL,GAAc,CAAlB,EAAqB;AACjB,iBAAKD,SAAL,GAAiB,CAAjB;AACA,iBAAKuC,MAAL,GAAc,KAAd;;AACA,iBAAKC,YAAL;;AACA,iBAAKC,aAAL;AACH,WALD,MAKO;AACH,iBAAKzC,SAAL,GAAiB,CAAjB;AACA,iBAAKuC,MAAL,GAAc,IAAd;AACH;AACJ;;AAEDP,QAAAA,KAAK,GAAS;AACV,eAAKZ,QAAL,GAAgB,EAAhB;AACA,eAAKb,QAAL,GAAgB,CAAhB;AACA,eAAKC,aAAL,GAAqB,CAArB;AACA,eAAKC,OAAL,GAAe;AAAA;AAAA,oCAASC,UAAT,CAAoBC,MAAnC;AACA,eAAK+B,WAAL;AACH;;AAEDP,QAAAA,QAAQ,GAAS;AACb,eAAKlC,MAAL,GAAc,KAAKJ,KAAL,CAAW8C,EAAzB;AACA,eAAKzC,MAAL,GAAc,KAAKD,MAAnB;AACH;;AAEDuC,QAAAA,YAAY,GAAS;AACjB,cAAI,CAAC,KAAKzC,YAAV,EAAwB;AACpB,iBAAKA,YAAL,GAAoB,KAAK6C,OAAL;AAAA;AAAA,8CAA2B;AAAA;AAAA,+CAA3B,CAApB;AACA,iBAAKC,OAAL,CAAaC,OAAb,CAAsBC,IAAD,IAAU;AAC3BA,cAAAA,IAAI,CAAClB,IAAL,CAAU,IAAV;AACH,aAFD;AAGH;;AAED,cAAMmB,YAAY,GAAG,KAAKnD,KAAL,CAAWoD,QAAhC;;AACA,eAAKlD,YAAL,CAAkBmD,OAAlB,CACI,CAACF,YAAY,CAACf,IAAd,EAAoBe,YAAY,CAACG,CAAjC,EAAoCH,YAAY,CAACI,CAAjD,EAAoDJ,YAAY,CAACK,KAAjE,EAAwEL,YAAY,CAACM,MAArF,CADJ,EAEI,KAAKxD,UAFT,EAGIhB,EAAE,CAAC,KAAKyE,IAAL,CAAUC,QAAV,CAAmBL,CAApB,EAAuB,KAAKI,IAAL,CAAUC,QAAV,CAAmBJ,CAA1C,CAHN;;AAKA,eAAKrD,YAAL,CAAkB0D,OAAlB,GAA4B,KAA5B;AACH;;AAEDrB,QAAAA,OAAO,GAAS;AACZ,eAAKmB,IAAL,CAAUC,QAAV,GAAqB,KAAK3D,KAAL,CAAW6D,GAAhC;;AAEA,kBAAQ,KAAK7D,KAAL,CAAWoC,IAAnB;AACI,iBAAK,CAAL;AACI,kBAAI,CAAC,KAAKd,KAAV,EAAiB;AACb,oBAAMwC,SAAQ,GAAG,IAAIpF,IAAJ,EAAjB;;AACAoF,gBAAAA,SAAQ,CAACC,YAAT,CAAsB5E,WAAtB;;AACA,qBAAKuE,IAAL,CAAUM,QAAV,CAAmBF,SAAnB;AACA,qBAAKJ,IAAL,CAAUO,eAAV,CAA0BlE,QAAQ,CAACmE,UAAT,CAAoBC,IAA9C;AACA,qBAAK7C,KAAL,GAAawC,SAAQ,CAACC,YAAT,CAAsB/E,EAAE,CAACoF,QAAzB,CAAb;AACA,qBAAK9C,KAAL,CAAW+C,YAAX,GAA0B;AAAA;AAAA,wCAAQC,WAAR,CAAoBC,WAApB,CAAgCC,GAAhC,CAAoC,KAAKxE,KAAL,CAAWyE,IAA/C,CAA1B;AACA,qBAAKnD,KAAL,CAAWoD,kBAAX,GAAgC,KAAhC;;AACA,qBAAKzE,UAAL,CAAgB0E,QAAhB,CAAyB,KAAKrD,KAA9B;;AAEA,qBAAKtB,KAAL,CAAW4E,MAAX,CAAkB3B,OAAlB,CAA2B4B,GAAD,IAA2B;AACjD,sBAAI;AACA,yBAAKvD,KAAL,CAAWwD,MAAX,CAAkBD,GAAG,CAAC,CAAD,CAArB,EAA0BA,GAAG,CAAC,CAAD,CAA7B,EAAkC,GAAlC;AACH,mBAFD,CAEE,OAAOE,KAAP,EAAc;AACZ;AAAA;AAAA,wCAAMC,GAAN,CAAU,sBAAV,EAAkCH,GAAlC;AACH;AACJ,iBAND;;AAQA,qBAAKvD,KAAL,CAAW2D,mBAAX,CAAgCC,UAAD,IAAqB;AAChD,sBAAMC,aAAa,GAAGD,UAAU,CAACE,SAAX,GAAuBF,UAAU,CAACE,SAAX,CAAqBC,IAA5C,GAAmD,EAAzE;;AACA,uBAAK7D,YAAL,CAAkByB,OAAlB,CAA0B,CAACqC,QAAD,EAAWC,GAAX,KAAmB;AACzC,wBAAIJ,aAAa,KAAKI,GAAlB,IAAyBD,QAA7B,EAAuC;AACnCA,sBAAAA,QAAQ;AACX;AACJ,mBAJD;AAKH,iBAPD;;AASA,qBAAKhE,KAAL,CAAWkE,gBAAX,CAA4B,CAACN,UAAD,EAAkBO,KAAlB,KAAiC;AACzD,sBAAMC,SAAS,GAAGD,KAAK,CAACxD,IAAN,CAAWoD,IAA7B;;AACA,uBAAK3D,iBAAL,CAAuBuB,OAAvB,CAA+B,CAACqC,QAAD,EAAWC,GAAX,KAAmB;AAC9C,wBAAIG,SAAS,KAAKH,GAAd,IAAqBD,QAAzB,EAAmC;AAC/BA,sBAAAA,QAAQ;AACX;AACJ,mBAJD;AAKH,iBAPD;AAQH;;AAED,mBAAKjE,QAAL,GAAgBzC,KAAK,GAChB+G,EADW,CACR,CADQ,EACL;AAAEC,gBAAAA,KAAK,EAAE,IAAI/G,KAAJ,CAAU,KAAKmB,KAAL,CAAW6F,SAAX,CAAqBC,IAArB,EAAV,EAAuC,KAAK9F,KAAL,CAAW6F,SAAX,CAAqBE,IAArB,EAAvC,EAAoE,KAAK/F,KAAL,CAAW6F,SAAX,CAAqBG,IAArB,EAApE;AAAT,eADK,EAEXL,EAFW,CAER,GAFQ,EAEH;AAAEC,gBAAAA,KAAK,EAAE/G,KAAK,CAACoH;AAAf,eAFG,CAAhB;AAGA;;AAEJ,iBAAK,CAAL;AACI,kBAAMnC,QAAQ,GAAG,IAAIpF,IAAJ,EAAjB;AACAoF,cAAAA,QAAQ,CAACC,YAAT,CAAsB5E,WAAtB;AACA,mBAAKuE,IAAL,CAAUM,QAAV,CAAmBF,QAAnB;AACA,mBAAKJ,IAAL,CAAUO,eAAV,CAA0BlE,QAAQ,CAACmE,UAAT,CAAoBC,IAA9C;AACA,mBAAK9C,QAAL,GAAgBzC,KAAK,GAChB+G,EADW,CACR,CADQ,EACL;AAAEO,gBAAAA,OAAO,EAAE;AAAX,eADK,EAEXP,EAFW,CAER,IAAI;AAAA;AAAA,4CAAWQ,eAFP,EAEwB;AAAED,gBAAAA,OAAO,EAAE;AAAX,eAFxB,CAAhB;AAGA;AApDR;;AAuDA,iBAAO,KAAKlG,KAAL,CAAWoG,OAAX,CAAmB,CAAnB,MAA0B,CAAjC,EAAoC;AAChC,gBAAIC,MAAW,GAAG,KAAKpG,UAAL,CAAgByD,IAAhB,CAAqB4C,cAArB,WAA4C,KAAKtG,KAAL,CAAWuG,GAAvD,CAAlB;;AACA,gBAAI,CAACF,MAAL,EAAa;AACTA,cAAAA,MAAM,GAAG,IAAI3H,IAAJ,EAAT;AACA2H,cAAAA,MAAM,CAACtC,YAAP,CAAoB5E,WAApB;AACAkH,cAAAA,MAAM,CAAChB,IAAP,aAAsB,KAAKrF,KAAL,CAAWuG,GAAjC;AACAF,cAAAA,MAAM,CAACG,MAAP,GAAgB,KAAKvG,UAAL,CAAgByD,IAAhC;AACA2C,cAAAA,MAAM,CAACpC,eAAP,CAAuB,EAAvB;AAEA;AAAA;AAAA,sCAAQK,WAAR,CAAoBmC,YAApB,CAAiCJ,MAAM,CAACtC,YAAP,CAAoBpF,MAApB,CAAjC,EAA8D,MAA9D;AAEA,kBAAM+H,SAAS,GAAG,IAAIhI,IAAJ,EAAlB;AACAgI,cAAAA,SAAS,CAAC3C,YAAV,CAAuB5E,WAAvB;AACAkH,cAAAA,MAAM,CAACrC,QAAP,CAAgB0C,SAAhB;AACA,mBAAKnG,QAAL,GAAgBmG,SAAS,CAAC3C,YAAV,CAAuBpF,MAAvB,CAAhB;AACA;AAAA;AAAA,sCAAQ2F,WAAR,CAAoBmC,YAApB,CAAiC,KAAKlG,QAAtC,EAAgD,MAAhD;AACA,mBAAKA,QAAL,CAAc6B,IAAd,GAAqBzD,MAAM,CAACgI,IAAP,CAAYC,MAAjC;AACA,mBAAKrG,QAAL,CAAcsG,QAAd,GAAyBlI,MAAM,CAACmI,QAAP,CAAgBC,UAAzC;AACA,mBAAKxG,QAAL,CAAcyG,SAAd,GAA0B,CAA1B;AAEA,kBAAMC,OAAO,GAAG,IAAIvI,IAAJ,EAAhB;AACAuI,cAAAA,OAAO,CAAClD,YAAR,CAAqB5E,WAArB;AACAkH,cAAAA,MAAM,CAACrC,QAAP,CAAgBiD,OAAhB;AACA,mBAAK3G,MAAL,GAAc2G,OAAO,CAAClD,YAAR,CAAqBpF,MAArB,CAAd;AACA;AAAA;AAAA,sCAAQ2F,WAAR,CAAoBmC,YAApB,CAAiC,KAAKnG,MAAtC,EAA8C,MAA9C;AACA,mBAAKA,MAAL,CAAY8B,IAAZ,GAAmBzD,MAAM,CAACgI,IAAP,CAAYC,MAA/B;AACA,mBAAKtG,MAAL,CAAYuG,QAAZ,GAAuBlI,MAAM,CAACmI,QAAP,CAAgBC,UAAvC;AACA,mBAAKzG,MAAL,CAAY0G,SAAZ,GAAwB,CAAxB;AACH;;AAED,gBAAIE,IAAI,GAAG,KAAKlH,KAAL,CAAWoG,OAAX,CAAmB,CAAnB,IAAwB,KAAK1C,IAAL,CAAUC,QAAV,CAAmBL,CAAtD;AACA,gBAAI6D,IAAI,GAAI,KAAKnH,KAAL,CAAWoG,OAAX,CAAmB,CAAnB,IAAwB,KAAK1C,IAAL,CAAUC,QAAV,CAAmBJ,CAAvD;AACA,gBAAI6D,MAAM,GAAG,KAAKpH,KAAL,CAAWoG,OAAX,CAAmB,CAAnB,CAAb;AACAC,YAAAA,MAAM,CAACgB,WAAP,CAAmBH,IAAnB,EAAyBC,IAAzB;AACAd,YAAAA,MAAM,CAACiB,QAAP,CAAgBF,MAAM,GAACf,MAAM,CAACkB,QAAP,GAAkBjE,CAAzC,EAA2C+C,MAAM,CAACkB,QAAP,GAAkBhE,CAA7D;AACA8C,YAAAA,MAAM,CAACmB,MAAP,GAAgB,KAAhB;AACH;AACJ;;AAEDC,QAAAA,eAAe,CAACC,SAAD,EAA0B;AACrC,eAAK7F,aAAL,CAAmBoB,OAAnB,CAA2B,CAAC0E,KAAD,EAAQC,KAAR,KAAkB;AACzC,gBAAMC,QAAQ,GAAG,KAAK/F,aAAL,CAAmB8F,KAAnB,CAAjB;;AACA,gBAAME,IAAI,GAAG,KAAKxG,KAAL,CAAWyG,QAAX,CAAoBF,QAApB,CAAb;;AACA,gBAAIC,IAAJ,EAAU;AACNH,cAAAA,KAAK,CAACjE,IAAN,CAAWC,QAAX,GAAsB,KAAK/B,YAAL,CAAkBgG,KAAlB,EAAyBI,GAAzB,CAA6B,IAAIjJ,IAAJ,CAAS+I,IAAI,CAACG,MAAd,EAAsBH,IAAI,CAACI,MAA3B,CAA7B,CAAtB;AACH;AACJ,WAND;;AAQA,cAAI,CAAC,KAAKxF,MAAV,EAAkB;AACd,gBAAI,KAAKtB,WAAT,EAAsB;AAClB,mBAAKD,UAAL;;AACA,kBAAI,KAAKA,UAAL,GAAkB,CAAtB,EAAyB;AACrB,qBAAKA,UAAL,GAAkB,CAAlB;AACA,qBAAKC,WAAL,GAAmB,KAAnB;AACH;AACJ;;AAED,iBAAK4B,OAAL,CAAaC,OAAb,CAAsBC,IAAD,IAAU;AAC3BA,cAAAA,IAAI,CAACiF,MAAL,CAAYT,SAAZ;AACH,aAFD;AAGH;AACJ;;AACDU,QAAAA,SAAS,CAAChF,QAAD,EAA+B;AACpC,cAAI,CAAC,KAAKV,MAAN,IAAgB,KAAK2F,UAAzB,EAAqC;AACjC,gBAAIjF,QAAQ,CAACkF,MAAT;AAAA;AAAA,iCAAJ,EAAuC;AACnC,kBAAIC,MAAM,GAAGnF,QAAQ,CAACkF,MAAT,CAAgBE,SAAhB,CAA0B,IAA1B,CAAb;;AACA,kBAAI;AACA;AAAA;AAAA,wCAAQC,iBAAR,CAA0BC,mBAA1B,CAA8C,KAAKxI,YAAnD,EAAiEkD,QAAQ,CAACkF,MAA1E,EAAkFC,MAAlF,EAA0F;AAAEjF,kBAAAA,CAAC,EAAE,CAAL;AAAQC,kBAAAA,CAAC,EAAE;AAAX,iBAA1F;AACH,eAFD,CAEE,OAAOwB,KAAP,EAAc;AACZ4D,gBAAAA,OAAO,CAAC5D,KAAR,CAAcA,KAAd;AACH;;AACDwD,cAAAA,MAAM,GAAGK,IAAI,CAACC,GAAL,CAASN,MAAM,GAAG,EAAlB,EAAsBA,MAAM,GAAG,KAAK9H,OAApC,IAA+C;AAAA;AAAA,sCAAQqI,gBAAR,CAAyBC,aAAjF;AACA,mBAAKC,IAAL,CAAUT,MAAV;AACH,aATD,MASO,IAAInF,QAAQ,CAACkF,MAAT;AAAA;AAAA,uCAAJ,EAA0C,CAC7C;AACH;AACJ;AACJ;;AAEDU,QAAAA,IAAI,CAACT,MAAD,EAA0B;AAC1B,cAAI,KAAK7F,MAAL,IAAe,CAAC,KAAK2F,UAAzB,EAAqC;AACjC,mBAAO,KAAP;AACH;;AACD,eAAKY,QAAL,CAAc,CAACV,MAAf;;AACA,eAAKW,QAAL;;AACA,cAAI,CAAC,KAAKxG,MAAV,EAAkB;AACd,iBAAKyG,UAAL;AACH;;AACD,iBAAO,IAAP;AACH;;AAEDF,QAAAA,QAAQ,CAACG,MAAD,EAAuB;AAC3B,cAAIC,MAAM,GAAGD,MAAb;AACA,cAAIE,KAAK,GAAG,KAAKlJ,MAAL,GAAcgJ,MAA1B;;AAEA,cAAIE,KAAK,GAAG,CAAZ,EAAe;AACXD,YAAAA,MAAM,GAAG,CAAC,KAAKjJ,MAAf;AACH;;AAED,eAAKA,MAAL,GAAckJ,KAAd;;AACA,cAAI,KAAKlJ,MAAL,GAAc,CAAlB,EAAqB;AACjB,iBAAKA,MAAL,GAAc,CAAd;AACH;;AAED,cAAI,KAAKH,UAAT,EAAqB;AACjB,iBAAKA,UAAL,CAAgBgJ,QAAhB,CAAyBI,MAAzB;AACH;;AAED,eAAK7G,aAAL;AACH;;AAED0G,QAAAA,QAAQ,GAAS;AACb,cAAI,KAAK9I,MAAL,IAAe,KAAKM,QAAxB,EAAkC;AAC9B,iBAAKK,QAAL,GAAgB,IAAhB;;AACA,iBAAKwI,cAAL,CAAoB,KAAK5I,aAAL,GAAqB,CAAzC;;AACA,iBAAKA,aAAL;;AACA,iBAAKiC,aAAL;;AAEA,gBAAI,KAAKlC,QAAL,GAAgB,CAApB,EAAuB;AACnB,mBAAK8I,IAAL;AACH;AACJ;AACJ;;AAED5G,QAAAA,aAAa,GAAS;AAClB,cAAI,KAAKjC,aAAL,GAAqB,KAAKX,KAAL,CAAWyJ,OAAX,CAAmBC,MAA5C,EAAoD;AAChD,iBAAKhJ,QAAL,GAAgB,KAAKV,KAAL,CAAWyJ,OAAX,CAAmB,KAAK9I,aAAxB,CAAhB;AACH,WAFD,MAEO;AACH,iBAAKD,QAAL,GAAgB,CAAC,CAAjB;AACH;AACJ;;AAED8I,QAAAA,IAAI,GAAS;AACT,eAAK9G,MAAL,GAAc,IAAd;;AACA,cAAI;AACA,gBAAI,KAAKpC,MAAT,EAAiB;AACb,mBAAKA,MAAL,CAAYoD,IAAZ,CAAiB8C,MAAjB,CAAyBgB,MAAzB,GAAkC,KAAlC;AACH;AACJ,WAJD,CAIE,OAAOzC,KAAP,EAAc;AACZ4D,YAAAA,OAAO,CAAC5D,KAAR,CAAcA,KAAd;AACH;;AAED,cAAI,KAAKvE,aAAT,EAAwB;AACpB,iBAAKA,aAAL,CAAmBmJ,IAAnB;;AACA,iBAAKnJ,aAAL,GAAqB,IAArB;AACH;;AAED,cAAI,KAAKN,YAAT,EAAuB;AACnB,iBAAKA,YAAL,CAAkB0D,OAAlB,GAA4B,KAA5B;AACH;;AAED,eAAKgG,YAAL;;AACA,cAAI,KAAK3J,UAAT,EAAqB;AACjB,iBAAKA,UAAL,CAAgB4J,aAAhB,CAA8B,IAA9B;AACH;AACJ;;AAEDrH,QAAAA,aAAa,GAAS;AAClB,cAAI,KAAKlC,MAAT,EAAiB;AACb,gBAAI,KAAKE,aAAT,EAAwB;AACpB,mBAAKA,aAAL,CAAmBmJ,IAAnB;;AACA,mBAAKnJ,aAAL,GAAqB,IAArB;AACH;;AAED,iBAAKF,MAAL,CAAY0G,SAAZ,GAAwB,KAAK5G,MAAL,GAAc,KAAKC,MAA3C;AAEA,gBAAMyJ,cAAc,GAAGlB,IAAI,CAACmB,GAAL,CAAS,KAAKxJ,QAAL,CAAeyG,SAAf,GAA2B,KAAK1G,MAAL,CAAY0G,SAAhD,CAAvB;AACA,iBAAKxG,aAAL,GAAqB5B,KAAK,CAAC,KAAK2B,QAAN,CAAL,CAChBoF,EADgB,CACbmE,cADa,EACG;AAAE9C,cAAAA,SAAS,EAAE,KAAK1G,MAAL,CAAY0G;AAAzB,aADH,EAEhBgD,IAFgB,CAEX,MAAM;AACR,mBAAKxJ,aAAL,GAAqB,IAArB;AACH,aAJgB,EAKhByJ,KALgB,EAArB;AAMH;AACJ;;AAEDC,QAAAA,QAAQ,CAAC/E,aAAD,EAAwBgF,IAAxB,EAAuC7E,QAAvC,EAAsF;AAAA,cAA/CA,QAA+C;AAA/CA,YAAAA,QAA+C,GAAf,IAAe;AAAA;;AAC1F,cAAI,CAAC,KAAKhE,KAAN,IAAe,KAAKC,QAAL,KAAkB4D,aAArC,EAAoD;AAChD,mBAAO,KAAP;AACH;;AACD,eAAK5D,QAAL,GAAgB4D,aAAhB;;AACA,eAAK3D,YAAL,CAAkB4I,GAAlB,CAAsBjF,aAAtB,EAAqCG,QAArC;;AACA,eAAKhE,KAAL,CAAW+I,YAAX,CAAwB,CAAxB,EAA2BlF,aAA3B,EAA0CgF,IAA1C;;AACA,iBAAO,IAAP;AACH;;AAEDG,QAAAA,gBAAgB,CAAC5E,SAAD,EAAoBJ,QAApB,EAA8C;AAC1D,eAAK5D,iBAAL,CAAuB0I,GAAvB,CAA2B1E,SAA3B,EAAsCJ,QAAtC;AACH;;AAED7C,QAAAA,OAAO,CAAC8H,SAAD,EAA0B;AAC7B,cAAI,KAAKjJ,KAAT,EAAgB;AACZ,iBAAKA,KAAL,CAAWmB,OAAX,QAAuB,KAAKxC,UAAL,CAAgBuK,SAAhB,GAA4B,CAAnD,UAAwDD,SAAxD;AACH;AACJ;;AAEDE,QAAAA,SAAS,CAACC,MAAD,EAAuB;AAC5B,cAAI,CAAC,KAAKhI,MAAV,EAAkB;AACd,iBAAK9B,OAAL,GAAe8J,MAAf;AACH;AACJ;;AAEDvB,QAAAA,UAAU,GAAS;AACf,cAAI,CAAC,KAAK/H,WAAN,IAAqB,KAAKR,OAAL,GAAe,CAAxC,EAA2C;AAAE;AACzC,iBAAKQ,WAAL,GAAmB,IAAnB;;AACA,gBAAI,KAAKE,KAAL,IAAc,KAAKA,KAAL,CAAWoC,IAA7B,EAAmC;AAC/B,mBAAKrC,QAAL,CAAcsJ,KAAd,CAAoB,KAAKrJ,KAAL,CAAWoC,IAA/B,EAAqCuG,KAArC;AACH;AACJ;AACJ;;AAEDW,QAAAA,cAAc,GAAS;AACnB,cAAMC,eAAe,GAAG,KAAxB,CADmB,CACY;;AAC/BjM,UAAAA,KAAK,CAAC,KAAK8E,IAAN,CAAL,CACKiC,EADL,CACQkF,eADR,EACyB;AAAElH,YAAAA,QAAQ,EAAEzE,EAAE,CAAC,EAAD,EAAK,CAAC,EAAN,CAAd;AAAyB4L,YAAAA,KAAK,EAAE,CAAC;AAAjC,WADzB,EAEKnF,EAFL,CAEQ,IAAIkF,eAFZ,EAE6B;AAAElH,YAAAA,QAAQ,EAAEzE,EAAE,CAAC,CAAD,EAAI,CAAJ,CAAd;AAAsB4L,YAAAA,KAAK,EAAE;AAA7B,WAF7B,EAGKnF,EAHL,CAGQ,IAAIkF,eAHZ,EAG6B;AAAElH,YAAAA,QAAQ,EAAEzE,EAAE,CAAC,EAAD,EAAK,CAAC,EAAN,CAAd;AAAyB4L,YAAAA,KAAK,EAAE;AAAhC,WAH7B,EAIKnF,EAJL,CAIQ,IAAIkF,eAJZ,EAI6B;AAAElH,YAAAA,QAAQ,EAAEzE,EAAE,CAAC,EAAD,EAAK,CAAL;AAAd,WAJ7B,EAKKyG,EALL,CAKQ,IAAIkF,eALZ,EAK6B;AAAElH,YAAAA,QAAQ,EAAEzE,EAAE,CAAC,EAAD,EAAK,CAAC,CAAN;AAAd,WAL7B,EAMKyG,EANL,CAMQkF,eANR,EAMyB;AAAElH,YAAAA,QAAQ,EAAEzE,EAAE,CAAC,EAAD,EAAK,CAAL;AAAd,WANzB,EAOKyG,EAPL,CAOQkF,eAPR,EAOyB;AAAElH,YAAAA,QAAQ,EAAEzE,EAAE,CAAC,CAAD,EAAI,CAAC,CAAL;AAAd,WAPzB,EAQKyG,EARL,CAQQkF,eARR,EAQyB;AAAElH,YAAAA,QAAQ,EAAEzE,EAAE,CAAC,EAAD,EAAK,CAAL;AAAd,WARzB,EASKyG,EATL,CASQkF,eATR,EASyB;AAAElH,YAAAA,QAAQ,EAAEzE,EAAE,CAAC,CAAC,CAAF,EAAK,CAAC,CAAN;AAAd,WATzB,EAUKyG,EAVL,CAUQkF,eAVR,EAUyB;AAAElH,YAAAA,QAAQ,EAAEzE,EAAE,CAAC,CAAD,EAAI,CAAJ;AAAd,WAVzB,EAWKyG,EAXL,CAWQkF,eAXR,EAWyB;AAAElH,YAAAA,QAAQ,EAAEzE,EAAE,CAAC,CAAC,CAAF,EAAK,CAAC,CAAN;AAAd,WAXzB,EAYKyG,EAZL,CAYQkF,eAZR,EAYyB;AAAElH,YAAAA,QAAQ,EAAEzE,EAAE,CAAC,CAAD,EAAI,CAAJ;AAAd,WAZzB,EAaKyG,EAbL,CAaQkF,eAbR,EAayB;AAAElH,YAAAA,QAAQ,EAAEzE,EAAE,CAAC,CAAD,EAAI,CAAJ;AAAd,WAbzB,EAcK+K,KAdL;AAeH;;AAEDc,QAAAA,SAAS,GAAS;AACd,eAAK,IAAMpD,KAAX,IAAoB,KAAK9F,aAAzB,EAAwC;AACpCjD,YAAAA,KAAK,CAAC+I,KAAK,CAACjE,IAAP,CAAL,CACKiC,EADL,CACQ,IAAI,KADZ,EACmB;AAAEO,cAAAA,OAAO,EAAE;AAAX,aADnB,EACmC;AADnC,aAEK+D,KAFL;AAGH;AACJ;;AAEDpH,QAAAA,WAAW,GAAS;AAChB,eAAK,IAAM8E,KAAX,IAAoB,KAAK9F,aAAzB,EAAwC;AACpC8F,YAAAA,KAAK,CAACjE,IAAN,CAAW8D,MAAX,GAAoB,KAApB;;AACA,iBAAK7F,cAAL,CAAoBqJ,IAApB,CAAyBrD,KAAzB;;AACA,iBAAK1H,UAAL,CAAgBgL,WAAhB,CAA4BtD,KAA5B;AACH;;AACD,eAAK9F,aAAL,CAAmB6H,MAAnB,GAA4B,CAA5B;AACA,eAAK9H,YAAL,CAAkB8H,MAAlB,GAA2B,CAA3B;AACA,eAAK5H,aAAL,CAAmB4H,MAAnB,GAA4B,CAA5B;AACH;;AAEK,YAAFwB,EAAE,GAAW;AACb,iBAAO,KAAKlL,KAAL,CAAWkL,EAAlB;AACH;;AAES,YAANC,MAAM,GAAW;AACjB,iBAAO,KAAKnL,KAAL,CAAWuG,GAAlB;AACH;;AAED6E,QAAAA,MAAM,GAAY;AACd,iBAAO,KAAKjL,SAAL,KAAmB,CAA1B;AACH;;AAEDkL,QAAAA,WAAW,GAAW;AAClB,iBAAO,KAAKrL,KAAL,CAAWoC,IAAlB;AACH;;AAEDkJ,QAAAA,eAAe,CAACC,KAAD,EAAwB;AACnC,cAAIA,KAAK,CAAC7B,MAAN,GAAe,CAAnB,EAAsB;AAClB,iBAAKtJ,MAAL,IAAemL,KAAK,CAAC,CAAD,CAApB;AACA,iBAAKlL,MAAL,GAAc,KAAKD,MAAnB;AACA,iBAAKoL,MAAL,IAAeD,KAAK,CAAC,CAAD,CAApB;AACA,iBAAKE,WAAL,IAAoBF,KAAK,CAAC,CAAD,CAAzB;AACH;AACJ;;AAEa,YAAVlD,UAAU,GAAY;AACtB,iBAAO,KAAKrH,WAAZ;AACH;;AAEa,YAAVqH,UAAU,CAACqD,KAAD,EAAiB;AAC3B,eAAK1K,WAAL,GAAmB0K,KAAnB;AACH;;AAEa,YAAVC,UAAU,GAAQ;AAClB,iBAAO,KAAK1L,UAAZ;AACH;;AAEQ,YAAL2L,KAAK,GAAW;AAChB,iBAAO,KAAKxL,MAAZ;AACH;;AAEQ,YAALyL,KAAK,GAAW;AAChB,iBAAO,KAAKxL,MAAZ;AACH;;AACDkJ,QAAAA,cAAc,CAACuC,UAAD,EAA2B;AACrC,cAAMjB,eAAe,GAAG;AAAA;AAAA,wCAAW1E,eAAnC;;AAEA,cAAI;AACA,gBAAM4F,WAAW,GAAG,KAAK/L,KAAL,CAAWgM,UAAX,CAAsB,KAAKrL,aAA3B,CAApB;;AACA,gBAAIoL,WAAJ,EAAiB;AACbA,cAAAA,WAAW,CAAC9I,OAAZ,CAAoB,CAACgJ,KAAD,EAAarE,KAAb,KAA+B;AAC/C,qBAAKsE,YAAL,CAAkB,MAAM;AACpB,sBAAMC,UAAU,GAAG;AACf7I,oBAAAA,CAAC,EAAE2I,KAAK,CAAC,CAAD,CAAL,GAAW,KAAKvI,IAAL,CAAUC,QAAV,CAAmBL,CADlB;AAEfC,oBAAAA,CAAC,EAAE0I,KAAK,CAAC,CAAD,CAAL,GAAW,KAAKvI,IAAL,CAAUC,QAAV,CAAmBJ,CAFlB;AAGf6I,oBAAAA,KAAK,EAAEH,KAAK,CAAC,CAAD,CAHG;AAIfnB,oBAAAA,KAAK,EAAEmB,KAAK,CAAC,CAAD;AAJG,mBAAnB;AAMA,sBAAM3G,QAAQ,GAAGsC,KAAK,KAAKmE,WAAW,CAACrC,MAAZ,GAAqB,CAA/B,GAAmC,MAAM,KAAK2C,eAAL,EAAzC,GAAkE,IAAnF;AACA;AAAA;AAAA,4DAAiBC,EAAjB,CAAoBC,cAApB,CAAmC,KAAKtM,UAAxC,EAAoDgM,KAAK,CAAC,CAAD,CAAzD,EAA8DE,UAA9D,EAA0E7G,QAA1E;;AACA,sBAAI2G,KAAK,CAAC,CAAD,CAAL,GAAW,CAAf,EAAkB,CACd;AACH;AACJ,iBAZD,EAYGA,KAAK,CAAC,CAAD,CAAL,GAAWpB,eAZd;AAaH,eAdD;AAeH;AACJ,WAnBD,CAmBE,OAAO9F,KAAP,EAAc;AACZ;AAAA;AAAA,gCAAMC,GAAN,CAAU,mBAAV,EAA+BD,KAA/B;AACH;;AAED,cAAI;AACA,gBAAMyH,WAAW,GAAG,KAAKxM,KAAL,CAAWyM,UAAX,CAAsB,KAAK9L,aAA3B,CAApB;;AACA,gBAAI6L,WAAJ,EAAiB;AACbA,cAAAA,WAAW,CAACvJ,OAAZ,CAAqBgJ,KAAD,IAAgB;AAChC,qBAAKC,YAAL,CAAkB,MAAM,CACpB;AACH,iBAFD,EAEGD,KAAK,CAAC,CAAD,CAAL,GAAWpB,eAFd;AAGH,eAJD;AAKH;AACJ,WATD,CASE,OAAO9F,KAAP,EAAc;AACZ;AAAA;AAAA,gCAAMC,GAAN,CAAU,mBAAV,EAA+BD,KAA/B;AACH;;AAED,cAAI;AACA,iBAAKlC,WAAL;AACA,gBAAM6J,WAAW,GAAG,KAAK1M,KAAL,CAAW2M,UAAX,CAAsB,KAAKhM,aAA3B,CAApB;;AACA,gBAAI+L,WAAJ,EAAiB;AACbA,cAAAA,WAAW,CAACzJ,OAAZ,CAAqBgJ,KAAD,IAAgB;AAChC,qBAAKC,YAAL,CAAkB,MAAM;AACpB,sBAAMU,SAAS,GAAG,KAAKC,aAAL,EAAlB;;AACAD,kBAAAA,SAAS,CAACvC,YAAV,CAAuB,CAAvB,WAAiC4B,KAAK,CAAC,CAAD,CAAtC,EAA6C,IAA7C;;AACA,uBAAKpK,aAAL,CAAmBmJ,IAAnB,CAAwB4B,SAAxB;;AACA,uBAAK3M,UAAL,CAAgB0E,QAAhB,CAAyBiI,SAAzB;;AACA,uBAAK9K,aAAL,CAAmBkJ,IAAnB,OAA4BiB,KAAK,CAAC,CAAD,CAAjC;;AACA,uBAAKrK,YAAL,CAAkBoJ,IAAlB,CAAuB,IAAIjM,IAAJ,CAASkN,KAAK,CAAC,CAAD,CAAd,EAAmBA,KAAK,CAAC,CAAD,CAAxB,CAAvB;;AACAW,kBAAAA,SAAS,CAAClJ,IAAV,CAAeoJ,MAAf,GAAwBb,KAAK,CAAC,CAAD,CAAL,GAAW,CAAX,GAAelM,QAAQ,CAACmE,UAAT,CAAoB6I,QAAnC,GAA8ChN,QAAQ,CAACmE,UAAT,CAAoB8I,WAA1F;AACH,iBARD,EAQGf,KAAK,CAAC,CAAD,CAAL,GAAWpB,eARd;AASH,eAVD;AAWH;AACJ,WAhBD,CAgBE,OAAO9F,KAAP,EAAc;AACZ;AAAA;AAAA,gCAAMC,GAAN,CAAU,mBAAV,EAA+BD,KAA/B;AACH;;AAED,eAAKtC,OAAL,CAAaqJ,UAAb;AACH;;AAEDO,QAAAA,eAAe,GAAS;AACpB,cAAI;AACA,gBAAI,KAAKpM,UAAT,EAAqB;AACjB,mBAAKA,UAAL,CAAgBgN,kBAAhB,CAAmC,IAAnC;AACH;;AACD,gBAAI,KAAKvK,MAAT,EAAiB;AACb,mBAAKwH,QAAL,WAAqB,KAAKjK,UAAL,CAAgBuK,SAAhB,GAA4B,CAAjD,GAAsD,IAAtD;AACH;AACJ,WAPD,CAOE,OAAOzF,KAAP,EAAc;AACZ4D,YAAAA,OAAO,CAAC5D,KAAR,CAAcA,KAAd;AACH;AACJ;;AAED8H,QAAAA,aAAa,GAAQ;AACjB,cAAID,SAAS,GAAG,KAAKjL,cAAL,CAAoBuL,GAApB,EAAhB;;AACA,cAAI,CAACN,SAAL,EAAgB;AACZ,gBAAMO,SAAS,GAAG,IAAIzO,IAAJ,EAAlB;AACAyO,YAAAA,SAAS,CAACpJ,YAAV,CAAuB5E,WAAvB;AACA,iBAAKuE,IAAL,CAAUM,QAAV,CAAmBmJ,SAAnB;AACAP,YAAAA,SAAS,GAAGO,SAAS,CAACpJ,YAAV,CAAuB/E,EAAE,CAACoF,QAA1B,CAAZ;AACAwI,YAAAA,SAAS,CAACvI,YAAV,GAAyB;AAAA;AAAA,oCAAQC,WAAR,CAAoB8I,aAA7C;AACAR,YAAAA,SAAS,CAAClI,kBAAV,GAA+B,KAA/B;;AACA,iBAAK/C,cAAL,CAAoBqJ,IAApB,CAAyB4B,SAAzB;AACH;;AACDA,UAAAA,SAAS,CAAClJ,IAAV,CAAe8D,MAAf,GAAwB,IAAxB;AACA,iBAAOoF,SAAP;AACH;;AAEDhD,QAAAA,YAAY,GAAS;AACjB,cAAI,KAAK5J,KAAL,CAAWkL,EAAX,IAAiB,GAAjB,IAAwB,KAAKlL,KAAL,CAAWkL,EAAX,GAAgB,GAA5C,EAAiD;AAC7C;AACH;;AACD,eAAK5J,KAAL,CAAW+I,YAAX,CAAwB,CAAxB,aAAmC,KAAKpK,UAAL,CAAgBuK,SAAhB,GAA4B,CAA/D,GAAoE,KAApE;AACH;;AA1hB0C,O,UACpCtG,U,GAAapF,IAAI,CAAC;AACrBkO,QAAAA,WAAW,EAAE,CAAC,EADO;AAErB7I,QAAAA,IAAI,EAAE,CAAC,CAFc;AAGrB4I,QAAAA,QAAQ,EAAE,CAAC;AAHU,OAAD,C", "sourcesContent": ["import { _decorator, Component, Node, Sprite, tween, Color, Enum, Vec2, sp, v2, v3, UITransform } from 'cc';\r\nimport GameConfig from '../../../const/GameConfig';\r\nimport { HurtEffectManager } from '../../../manager/HurtEffectManager';\r\nimport { ColliderComp } from '../../base/ColliderComp';\r\nimport Bullet from '../../bullet/Bullet';\r\nimport EnemyEffectLayer from '../enemy/EnemyEffectLayer';\r\nimport { MainPlane } from '../mainPlane/MainPlane';\r\nimport GameEnum from '../../../const/GameEnum';\r\nimport BossHurt from './BossHurt';\r\nimport { Tools } from '../../../utils/Tools';\r\nimport { GameIns } from '../../../GameIns';\r\nconst { ccclass, property } = _decorator;\r\n\r\n@ccclass('BossUnit')\r\nexport default class BossUnit extends BossHurt {\r\n    static UnitZIndex = Enum({\r\n        SmokeBottom: -10,\r\n        Skel: -9,\r\n        SmokeTop: -8,\r\n    });\r\n\r\n    _data: any = null;\r\n    _bossPlane: any = null;\r\n    _collideComp: ColliderComp | null = null;\r\n    _unitType: number = 0;\r\n    _curHp: number = 0;\r\n    _maxHp: number = 0;\r\n    _hpBar: Sprite | null = null;\r\n    _hpWhite: Sprite | null = null;\r\n    _hpWhiteTween: any = null;\r\n    defence: number = 0;\r\n    _hpStage: number = 0;\r\n    _hpStageIndex: number = 0;\r\n    _action: number = GameEnum.BossAction.Normal;\r\n    _damaged: boolean = false;\r\n    _damageable: boolean = false;\r\n    _bSmoking: boolean = false;\r\n    _whiteNode: Node | null = null;\r\n    _winkCount: number = 0;\r\n    _bWinkWhite: boolean = false;\r\n    _winkAct: any = null;\r\n    _skel: any = null;\r\n    _curAnim: string = '';\r\n    _skelCallMap: Map<string, Function> = new Map();\r\n    _skelEventCallMap: Map<string, Function> = new Map();\r\n    _smokeSkelPool: any[] = [];\r\n    _smokePosArr: Vec2[] = [];\r\n    _smokeSkelArr: any[] = [];\r\n    _smokeBoneArr: string[] = [];\r\n\r\n    onLoad(): void {\r\n        // 初始化逻辑\r\n    }\r\n\r\n    init(data: any, bossPlane: any): void {\r\n        this.reset();\r\n        this.type = GameEnum.EnemyType.BossUnit;\r\n        this._data = data;\r\n        this._bossPlane = bossPlane;\r\n\r\n        this.initData();\r\n        this._initUI();\r\n        this._refreshHpBar();\r\n        this.setSkin(0);\r\n\r\n        if (this._curHp > 0) {\r\n            this._unitType = 1;\r\n            this.isDead = false;\r\n            this._initCollide();\r\n            this._checkHpStage();\r\n        } else {\r\n            this._unitType = 0;\r\n            this.isDead = true;\r\n        }\r\n    }\r\n\r\n    reset(): void {\r\n        this._curAnim = '';\r\n        this._hpStage = 0;\r\n        this._hpStageIndex = 0;\r\n        this._action = GameEnum.BossAction.Normal;\r\n        this.removeSmoke();\r\n    }\r\n\r\n    initData(): void {\r\n        this._curHp = this._data.hp;\r\n        this._maxHp = this._curHp;\r\n    }\r\n\r\n    _initCollide(): void {\r\n        if (!this._collideComp) {\r\n            this._collideComp = this.addComp(ColliderComp, new ColliderComp());\r\n            this.m_comps.forEach((comp) => {\r\n                comp.init(this);\r\n            });\r\n        }\r\n\r\n        const colliderData = this._data.collider;\r\n        this._collideComp.setData(\r\n            [colliderData.type, colliderData.x, colliderData.y, colliderData.width, colliderData.height],\r\n            this._bossPlane,\r\n            v2(this.node.position.x, this.node.position.y)\r\n        );\r\n        this._collideComp.enabled = false;\r\n    }\r\n\r\n    _initUI(): void {\r\n        this.node.position = this._data.pos;\r\n\r\n        switch (this._data.type) {\r\n            case 0:\r\n                if (!this._skel) {\r\n                    const skelNode = new Node();\r\n                    skelNode.addComponent(UITransform);\r\n                    this.node.addChild(skelNode);\r\n                    this.node.setSiblingIndex(BossUnit.UnitZIndex.Skel)\r\n                    this._skel = skelNode.addComponent(sp.Skeleton);\r\n                    this._skel.skeletonData = GameIns.bossManager.skelDataMap.get(this._data.anim);\r\n                    this._skel.premultipliedAlpha = false;\r\n                    this._bossPlane.addSpine(this._skel);\r\n\r\n                    this._data.mixArr.forEach((mix: [string, string]) => {\r\n                        try {\r\n                            this._skel.setMix(mix[0], mix[1], 0.5);\r\n                        } catch (error) {\r\n                            Tools.log('Boss unit mix error:', mix);\r\n                        }\r\n                    });\r\n\r\n                    this._skel.setCompleteListener((trackEntry: any) => {\r\n                        const animationName = trackEntry.animation ? trackEntry.animation.name : '';\r\n                        this._skelCallMap.forEach((callback, key) => {\r\n                            if (animationName === key && callback) {\r\n                                callback();\r\n                            }\r\n                        });\r\n                    });\r\n\r\n                    this._skel.setEventListener((trackEntry: any, event: any) => {\r\n                        const eventName = event.data.name;\r\n                        this._skelEventCallMap.forEach((callback, key) => {\r\n                            if (eventName === key && callback) {\r\n                                callback();\r\n                            }\r\n                        });\r\n                    });\r\n                }\r\n\r\n                this._winkAct = tween()\r\n                    .to(0, { color: new Color(this._data.hurtColor.getR(), this._data.hurtColor.getG(), this._data.hurtColor.getB()) })\r\n                    .to(0.1, { color: Color.WHITE });\r\n                break;\r\n\r\n            case 1:\r\n                const skelNode = new Node();\r\n                skelNode.addComponent(UITransform);\r\n                this.node.addChild(skelNode);\r\n                this.node.setSiblingIndex(BossUnit.UnitZIndex.Skel)\r\n                this._winkAct = tween()\r\n                    .to(0, { opacity: 180 })\r\n                    .to(3 * GameConfig.ActionFrameTime, { opacity: 0 });\r\n                break;\r\n        }\r\n\r\n        while (this._data.hpParam[0] !== 0) {\r\n            let hpNode:Node = this._bossPlane.node.getChildByName(`blood${this._data.uId}`);\r\n            if (!hpNode) {\r\n                hpNode = new Node();\r\n                hpNode.addComponent(UITransform);\r\n                hpNode.name = `blood${this._data.uId}`;\r\n                hpNode.parent = this._bossPlane.node;\r\n                hpNode.setSiblingIndex(10);\r\n\r\n                GameIns.bossManager.setBossFrame(hpNode.addComponent(Sprite), 'hp_0');\r\n\r\n                const whiteNode = new Node();\r\n                whiteNode.addComponent(UITransform);\r\n                hpNode.addChild(whiteNode);\r\n                this._hpWhite = whiteNode.addComponent(Sprite);\r\n                GameIns.bossManager.setBossFrame(this._hpWhite, 'hp_2');\r\n                this._hpWhite.type = Sprite.Type.FILLED;\r\n                this._hpWhite.fillType = Sprite.FillType.HORIZONTAL;\r\n                this._hpWhite.fillRange = 1;\r\n\r\n                const barNode = new Node();\r\n                barNode.addComponent(UITransform);\r\n                hpNode.addChild(barNode);\r\n                this._hpBar = barNode.addComponent(Sprite);\r\n                GameIns.bossManager.setBossFrame(this._hpBar, 'hp_1');\r\n                this._hpBar.type = Sprite.Type.FILLED;\r\n                this._hpBar.fillType = Sprite.FillType.HORIZONTAL;\r\n                this._hpBar.fillRange = 1;\r\n            }\r\n\r\n            let posX = this._data.hpParam[1] + this.node.position.x;\r\n            let posY  = this._data.hpParam[2] + this.node.position.y;\r\n            let scaleX = this._data.hpParam[3];\r\n            hpNode.setPosition(posX, posY);\r\n            hpNode.setScale(scaleX*hpNode.getScale().x,hpNode.getScale().y)\r\n            hpNode.active = false;\r\n        }\r\n    }\r\n\r\n    updateGameLogic(deltaTime: number): void {\r\n        this._smokeSkelArr.forEach((smoke, index) => {\r\n            const boneName = this._smokeBoneArr[index];\r\n            const bone = this._skel.findBone(boneName);\r\n            if (bone) {\r\n                smoke.node.position = this._smokePosArr[index].add(new Vec2(bone.worldX, bone.worldY));\r\n            }\r\n        });\r\n\r\n        if (!this.isDead) {\r\n            if (this._bWinkWhite) {\r\n                this._winkCount++;\r\n                if (this._winkCount > 8) {\r\n                    this._winkCount = 0;\r\n                    this._bWinkWhite = false;\r\n                }\r\n            }\r\n\r\n            this.m_comps.forEach((comp) => {\r\n                comp.update(deltaTime);\r\n            });\r\n        }\r\n    }\r\n    onCollide(collider: ColliderComp): void {\r\n        if (!this.isDead && this.damageable) {\r\n            if (collider.entity instanceof Bullet) {\r\n                let damage = collider.entity.getAttack(this);\r\n                try {\r\n                    GameIns.hurtEffectManager.createHurtNumByType(this._collideComp, collider.entity, damage, { x: 0, y: 0 });\r\n                } catch (error) {\r\n                    console.error(error);\r\n                }\r\n                damage = Math.max(damage / 10, damage - this.defence) * GameIns.warAttackManager.playerWARatio;\r\n                this.hurt(damage);\r\n            } else if (collider.entity instanceof MainPlane) {\r\n                // Handle collision with the main plane\r\n            }\r\n        }\r\n    }\r\n\r\n    hurt(damage: number): boolean {\r\n        if (this.isDead || !this.damageable) {\r\n            return false;\r\n        }\r\n        this.hpChange(-damage);\r\n        this._checkHp();\r\n        if (!this.isDead) {\r\n            this._winkWhite();\r\n        }\r\n        return true;\r\n    }\r\n\r\n    hpChange(amount: number): void {\r\n        let change = amount;\r\n        let newHp = this._curHp + amount;\r\n\r\n        if (newHp < 0) {\r\n            change = -this._curHp;\r\n        }\r\n\r\n        this._curHp = newHp;\r\n        if (this._curHp < 0) {\r\n            this._curHp = 0;\r\n        }\r\n\r\n        if (this._bossPlane) {\r\n            this._bossPlane.hpChange(change);\r\n        }\r\n\r\n        this._refreshHpBar();\r\n    }\r\n\r\n    _checkHp(): void {\r\n        if (this._curHp <= this._hpStage) {\r\n            this._damaged = true;\r\n            this._playStageAnim(this._hpStageIndex + 1);\r\n            this._hpStageIndex++;\r\n            this._checkHpStage();\r\n\r\n            if (this._hpStage < 0) {\r\n                this._die();\r\n            }\r\n        }\r\n    }\r\n\r\n    _checkHpStage(): void {\r\n        if (this._hpStageIndex < this._data.hpStage.length) {\r\n            this._hpStage = this._data.hpStage[this._hpStageIndex];\r\n        } else {\r\n            this._hpStage = -1;\r\n        }\r\n    }\r\n\r\n    _die(): void {\r\n        this.isDead = true;\r\n        try {\r\n            if (this._hpBar) {\r\n                this._hpBar.node.parent!.active = false;\r\n            }\r\n        } catch (error) {\r\n            console.error(error);\r\n        }\r\n\r\n        if (this._hpWhiteTween) {\r\n            this._hpWhiteTween.stop();\r\n            this._hpWhiteTween = null;\r\n        }\r\n\r\n        if (this._collideComp) {\r\n            this._collideComp.enabled = false;\r\n        }\r\n\r\n        this._playDieAnim();\r\n        if (this._bossPlane) {\r\n            this._bossPlane.unitDestroyed(this);\r\n        }\r\n    }\r\n\r\n    _refreshHpBar(): void {\r\n        if (this._hpBar) {\r\n            if (this._hpWhiteTween) {\r\n                this._hpWhiteTween.stop();\r\n                this._hpWhiteTween = null;\r\n            }\r\n\r\n            this._hpBar.fillRange = this._curHp / this._maxHp;\r\n\r\n            const fillDifference = Math.abs(this._hpWhite!.fillRange - this._hpBar.fillRange);\r\n            this._hpWhiteTween = tween(this._hpWhite)\r\n                .to(fillDifference, { fillRange: this._hpBar.fillRange })\r\n                .call(() => {\r\n                    this._hpWhiteTween = null;\r\n                })\r\n                .start();\r\n        }\r\n    }\r\n\r\n    playSkel(animationName: string, loop: boolean, callback: (() => void) | null = null): boolean {\r\n        if (!this._skel || this._curAnim === animationName) {\r\n            return false;\r\n        }\r\n        this._curAnim = animationName;\r\n        this._skelCallMap.set(animationName, callback);\r\n        this._skel.setAnimation(0, animationName, loop);\r\n        return true;\r\n    }\r\n\r\n    setEventCallback(eventName: string, callback: Function): void {\r\n        this._skelEventCallMap.set(eventName, callback);\r\n    }\r\n\r\n    setSkin(skinIndex: number): void {\r\n        if (this._skel) {\r\n            this._skel.setSkin(`s${this._bossPlane.formIndex + 1}_${skinIndex}`);\r\n        }\r\n    }\r\n\r\n    setAction(action: number): void {\r\n        if (!this.isDead) {\r\n            this._action = action;\r\n        }\r\n    }\r\n\r\n    _winkWhite(): void {\r\n        if (!this._bWinkWhite && this._action < 2) { // Assuming 2 is the threshold for attack actions\r\n            this._bWinkWhite = true;\r\n            if (this._skel && this._skel.node) {\r\n                this._winkAct.clone(this._skel.node).start();\r\n            }\r\n        }\r\n    }\r\n\r\n    _playShakeAnim(): void {\r\n        const actionFrameTime = 0.016; // Assuming 60 FPS\r\n        tween(this.node)\r\n            .to(actionFrameTime, { position: v3(11, -16), angle: -1 })\r\n            .to(2 * actionFrameTime, { position: v3(7, 2), angle: 1 })\r\n            .to(2 * actionFrameTime, { position: v3(20, -11), angle: 0 })\r\n            .to(2 * actionFrameTime, { position: v3(28, 5) })\r\n            .to(2 * actionFrameTime, { position: v3(13, -7) })\r\n            .to(actionFrameTime, { position: v3(17, 1) })\r\n            .to(actionFrameTime, { position: v3(4, -8) })\r\n            .to(actionFrameTime, { position: v3(14, 2) })\r\n            .to(actionFrameTime, { position: v3(-1, -6) })\r\n            .to(actionFrameTime, { position: v3(5, 4) })\r\n            .to(actionFrameTime, { position: v3(-3, -7) })\r\n            .to(actionFrameTime, { position: v3(2, 1) })\r\n            .to(actionFrameTime, { position: v3(0, 0) })\r\n            .start();\r\n    }\r\n\r\n    hideSmoke(): void {\r\n        for (const smoke of this._smokeSkelArr) {\r\n            tween(smoke.node)\r\n                .to(5 * 0.016, { opacity: 0 }) // Assuming 60 FPS\r\n                .start();\r\n        }\r\n    }\r\n\r\n    removeSmoke(): void {\r\n        for (const smoke of this._smokeSkelArr) {\r\n            smoke.node.active = false;\r\n            this._smokeSkelPool.push(smoke);\r\n            this._bossPlane.removeSpine(smoke);\r\n        }\r\n        this._smokeSkelArr.length = 0;\r\n        this._smokePosArr.length = 0;\r\n        this._smokeBoneArr.length = 0;\r\n    }\r\n\r\n    get id(): number {\r\n        return this._data.id;\r\n    }\r\n\r\n    get unitId(): number {\r\n        return this._data.uId;\r\n    }\r\n\r\n    isBody(): boolean {\r\n        return this._unitType === 0;\r\n    }\r\n\r\n    getUnitType(): number {\r\n        return this._data.type;\r\n    }\r\n\r\n    setPropertyRate(rates: number[]): void {\r\n        if (rates.length > 2) {\r\n            this._curHp *= rates[0];\r\n            this._maxHp = this._curHp;\r\n            this.attack *= rates[1];\r\n            this._collideAtk *= rates[2];\r\n        }\r\n    }\r\n\r\n    get damageable(): boolean {\r\n        return this._damageable;\r\n    }\r\n\r\n    set damageable(value: boolean) {\r\n        this._damageable = value;\r\n    }\r\n\r\n    get bossEntity(): any {\r\n        return this._bossPlane;\r\n    }\r\n\r\n    get curHp(): number {\r\n        return this._curHp;\r\n    }\r\n\r\n    get maxHp(): number {\r\n        return this._maxHp;\r\n    }\r\n    _playStageAnim(stageIndex: number): void {\r\n        const actionFrameTime = GameConfig.ActionFrameTime;\r\n\r\n        try {\r\n            const blastParams = this._data.blastParam[this._hpStageIndex];\r\n            if (blastParams) {\r\n                blastParams.forEach((param: any, index: number) => {\r\n                    this.scheduleOnce(() => {\r\n                        const effectData = {\r\n                            x: param[0] + this.node.position.x,\r\n                            y: param[1] + this.node.position.y,\r\n                            scale: param[4],\r\n                            angle: param[5],\r\n                        };\r\n                        const callback = index === blastParams.length - 1 ? () => this._onStageAnimEnd() : null;\r\n                        EnemyEffectLayer.me.addBlastEffect(this._bossPlane, param[2], effectData, callback);\r\n                        if (param[6] > 0) {\r\n                            // BossMgr.audioManager.playEffect(`blast${param[6]}`);\r\n                        }\r\n                    }, param[3] * actionFrameTime);\r\n                });\r\n            }\r\n        } catch (error) {\r\n            Tools.log('Unit blast error:', error);\r\n        }\r\n\r\n        try {\r\n            const shakeParams = this._data.blastShake[this._hpStageIndex];\r\n            if (shakeParams) {\r\n                shakeParams.forEach((param: any) => {\r\n                    this.scheduleOnce(() => {\r\n                        // MainCamera.me.shake1(EnemyMgr.shakeParam[param[1]]);\r\n                    }, param[0] * actionFrameTime);\r\n                });\r\n            }\r\n        } catch (error) {\r\n            Tools.log('Unit shake error:', error);\r\n        }\r\n\r\n        try {\r\n            this.removeSmoke();\r\n            const smokeParams = this._data.blastSmoke[this._hpStageIndex];\r\n            if (smokeParams) {\r\n                smokeParams.forEach((param: any) => {\r\n                    this.scheduleOnce(() => {\r\n                        const smokeAnim = this._getSmokeAnim();\r\n                        smokeAnim.setAnimation(0, `idle${param[4]}`, true);\r\n                        this._smokeSkelArr.push(smokeAnim);\r\n                        this._bossPlane.addSpine(smokeAnim);\r\n                        this._smokeBoneArr.push(`b${param[0]}`);\r\n                        this._smokePosArr.push(new Vec2(param[1], param[2]));\r\n                        smokeAnim.node.zIndex = param[5] > 0 ? BossUnit.UnitZIndex.SmokeTop : BossUnit.UnitZIndex.SmokeBottom;\r\n                    }, param[3] * actionFrameTime);\r\n                });\r\n            }\r\n        } catch (error) {\r\n            Tools.log('Unit smoke error:', error);\r\n        }\r\n\r\n        this.setSkin(stageIndex);\r\n    }\r\n\r\n    _onStageAnimEnd(): void {\r\n        try {\r\n            if (this._bossPlane) {\r\n                this._bossPlane.unitDestroyAnimEnd(this);\r\n            }\r\n            if (this.isDead) {\r\n                this.playSkel(`idle${this._bossPlane.formIndex + 1}`, true);\r\n            }\r\n        } catch (error) {\r\n            console.error(error);\r\n        }\r\n    }\r\n\r\n    _getSmokeAnim(): any {\r\n        let smokeAnim = this._smokeSkelPool.pop();\r\n        if (!smokeAnim) {\r\n            const smokeNode = new Node();\r\n            smokeNode.addComponent(UITransform);\r\n            this.node.addChild(smokeNode);\r\n            smokeAnim = smokeNode.addComponent(sp.Skeleton);\r\n            smokeAnim.skeletonData = GameIns.bossManager.smokeSkelData;\r\n            smokeAnim.premultipliedAlpha = false;\r\n            this._smokeSkelPool.push(smokeAnim);\r\n        }\r\n        smokeAnim.node.active = true;\r\n        return smokeAnim;\r\n    }\r\n\r\n    _playDieAnim(): void {\r\n        if (this._data.id >= 200 && this._data.id < 250) {\r\n            return;\r\n        }\r\n        this._skel.setAnimation(0, `shake${this._bossPlane.formIndex + 1}`, false);\r\n    }\r\n}"]}