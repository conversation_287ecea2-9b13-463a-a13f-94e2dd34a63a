System.register(["cc"], function (_export, _context) {
  "use strict";

  var _cclegacy, __checkObsolete__, __checkObsoleteInNamespace__, LevelData, LevelDataTerrain, LevelDataLayer, _crd;

  _export({
    LevelData: void 0,
    LevelDataTerrain: void 0,
    LevelDataLayer: void 0
  });

  return {
    setters: [function (_cc) {
      _cclegacy = _cc.cclegacy;
      __checkObsolete__ = _cc.__checkObsolete__;
      __checkObsoleteInNamespace__ = _cc.__checkObsoleteInNamespace__;
    }],
    execute: function () {
      _crd = true;

      _cclegacy._RF.push({}, "dc341cRMY1GOZDoBSe4O0s0", "leveldata", undefined);

      __checkObsolete__(['Vec2']);

      _export("LevelData", LevelData = class LevelData {
        constructor() {
          this.name = "";
          this.totalTime = 60;
          this.layers = [];
        }

      });

      _export("LevelDataTerrain", LevelDataTerrain = class LevelDataTerrain {
        constructor() {
          this.uuid = "";
          this.position = void 0;
          this.scale = void 0;
          this.rotation = void 0;
        }

      });

      _export("LevelDataLayer", LevelDataLayer = class LevelDataLayer {
        constructor() {
          this.speed = 200;
          this.backgrounds = [];
          this.terrains = [];
        }

      });

      _cclegacy._RF.pop();

      _crd = false;
    }
  };
});
//# sourceMappingURL=91314500322d3fe5f8aaaf223dbfd9a7fc9c35fe.js.map