{"version": 3, "sources": ["file:///E:/M2Game/Client/assets/scripts/GameOld/world/level/bg_layer/BackgroundLayer.ts"], "names": ["_decorator", "Component", "Sprite", "Node", "Vec3", "Global", "ccclass", "property", "<PERSON><PERSON><PERSON><PERSON>", "type", "displayName", "tooltip", "currentData", "currentSpriteIndex", "scrollProgress", "positionA", "positionB", "spriteFrames", "onLoad", "initializeNodes", "nodeA", "nodeB", "console", "warn", "set", "setPosition", "HEIGHT", "initialize", "data", "initializeSprites", "tick", "deltaTime", "length", "scrollSpeed", "scrollDuration", "swapNodes", "updateCurrentSprite", "offsetY", "tempNode", "spriteComponentA", "getComponent", "spriteFrame", "nextSpriteIndex", "spriteComponentB", "changeBackground", "resetPosition", "getCurrentSpriteIndex", "getScrollProgress"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;AAASA,MAAAA,U,OAAAA,U;AAAYC,MAAAA,S,OAAAA,S;AAAWC,MAAAA,M,OAAAA,M;AAAqBC,MAAAA,I,OAAAA,I;AAAYC,MAAAA,I,OAAAA,I;;AACxDC,MAAAA,M,iBAAAA,M;;;;;;;;;OAEH;AAAEC,QAAAA,OAAF;AAAWC,QAAAA;AAAX,O,GAAwBP,U;;iCAIjBQ,e,WADZF,OAAO,CAAC,iBAAD,C,UAIHC,QAAQ,CAAC;AAAEE,QAAAA,IAAI,EAACN,IAAP;AAAaO,QAAAA,WAAW,EAAE,kBAA1B;AAA8CC,QAAAA,OAAO,EAAE;AAAvD,OAAD,C,UAGRJ,QAAQ,CAAC;AAAEE,QAAAA,IAAI,EAACN,IAAP;AAAaO,QAAAA,WAAW,EAAE,cAA1B;AAA0CC,QAAAA,OAAO,EAAE;AAAnD,OAAD,C,2BAPb,MACaH,eADb,SACqCP,SADrC,CAC+C;AAAA;AAAA;;AAE3C;AAF2C;;AAAA;;AAAA,eASnCW,WATmC,GASA,IATA;AAAA,eAUnCC,kBAVmC,GAUN,CAVM;AAAA,eAWnCC,cAXmC,GAWV,CAXU;AAWP;AAXO,eAYnCC,SAZmC,GAYjB,IAAIX,IAAJ,EAZiB;AAAA,eAanCY,SAbmC,GAajB,IAAIZ,IAAJ,EAbiB;AAAA;;AAenB,YAAZa,YAAY,GAAG;AACvB,iBAAO,KAAKL,WAAL,IAAoB,KAAKA,WAAL,CAAiBK,YAA5C;AACH;;AAESC,QAAAA,MAAM,GAAS;AACrB,eAAKC,eAAL;AACH;;AAEOA,QAAAA,eAAe,GAAS;AAC5B,cAAI,CAAC,KAAKC,KAAN,IAAe,CAAC,KAAKC,KAAzB,EAAgC;AAC5BC,YAAAA,OAAO,CAACC,IAAR,CAAa,oDAAb;AACA;AACH,WAJ2B,CAM5B;;;AACA,eAAKR,SAAL,CAAeS,GAAf,CAAmB,CAAnB,EAAsB,CAAtB,EAAyB,CAAzB;AACA,eAAKJ,KAAL,CAAWK,WAAX,CAAuB,KAAKV,SAA5B,EAR4B,CAU5B;;AACA,eAAKC,SAAL,CAAeQ,GAAf,CAAmB,CAAnB,EAAsB;AAAA;AAAA,gCAAOE,MAA7B,EAAqC,CAArC;AACA,eAAKL,KAAL,CAAWI,WAAX,CAAuB,KAAKT,SAA5B;AACH;;AAEMW,QAAAA,UAAU,CAACC,IAAD,EAAiC;AAC9C,eAAKhB,WAAL,GAAmBgB,IAAnB;AACA,eAAKC,iBAAL;AACH;;AAEDC,QAAAA,IAAI,CAACC,SAAD,EAAoB;AACpB,cAAI,CAAC,KAAKnB,WAAN,IAAqB,KAAKA,WAAL,CAAiBK,YAAjB,CAA8Be,MAA9B,KAAyC,CAA9D,IAAmE,CAAC,KAAKZ,KAAzE,IAAkF,CAAC,KAAKC,KAA5F,EAAmG;AAC/F;AACH,WAHmB,CAKpB;AACA;;;AACA,cAAMY,WAAW,GAAG,MAAM,KAAKrB,WAAL,CAAiBsB,cAA3C,CAPoB,CAOuC;;AAC3D,eAAKpB,cAAL,IAAuBmB,WAAW,GAAGF,SAArC,CARoB,CAUpB;;AACA,cAAI,KAAKjB,cAAL,IAAuB,GAA3B,EAAgC;AAC5B,iBAAKA,cAAL,GAAsB,GAAtB;AACA,iBAAKD,kBAAL,GAA0B,CAAC,KAAKA,kBAAL,GAA0B,CAA3B,IAAgC,KAAKI,YAAL,CAAkBe,MAA5E;AACA,iBAAKG,SAAL;AACA,iBAAKC,mBAAL;AACH,WAhBmB,CAkBpB;AACA;AACA;;;AACA,cAAMC,OAAO,GAAG;AAAA;AAAA,gCAAOX,MAAP,GAAgB,KAAKZ,cAArC;AAEA,eAAKC,SAAL,CAAeS,GAAf,CAAmB,CAAnB,EAAsB,CAACa,OAAvB,EAAgC,CAAhC;AACA,eAAKjB,KAAL,CAAWK,WAAX,CAAuB,KAAKV,SAA5B;AAEA,eAAKC,SAAL,CAAeQ,GAAf,CAAmB,CAAnB,EAAsB;AAAA;AAAA,gCAAOE,MAAP,GAAgBW,OAAtC,EAA+C,CAA/C;AACA,eAAKhB,KAAL,CAAWI,WAAX,CAAuB,KAAKT,SAA5B;AACH;;AAEOmB,QAAAA,SAAS,GAAS;AACtB;AACA,cAAMG,QAAQ,GAAG,KAAKlB,KAAtB;AACA,eAAKA,KAAL,GAAa,KAAKC,KAAlB;AACA,eAAKA,KAAL,GAAaiB,QAAb,CAJsB,CAMtB;;AACA,eAAKvB,SAAL,CAAeS,GAAf,CAAmB,CAAnB,EAAsB,CAAtB,EAAyB,CAAzB;AACA,eAAKJ,KAAL,CAAWK,WAAX,CAAuB,KAAKV,SAA5B;AAEA,eAAKC,SAAL,CAAeQ,GAAf,CAAmB,CAAnB,EAAsB;AAAA;AAAA,gCAAOE,MAA7B,EAAqC,CAArC;AACA,eAAKL,KAAL,CAAWI,WAAX,CAAuB,KAAKT,SAA5B;AACH;;AAEOa,QAAAA,iBAAiB,GAAS;AAC9B,cAAI,KAAKZ,YAAL,IAAqB,KAAKA,YAAL,CAAkBe,MAAlB,GAA2B,CAApD,EAAuD;AACnD,iBAAKnB,kBAAL,GAA0B,CAA1B;AACA,iBAAKuB,mBAAL;AACH;AACJ;;AAEOA,QAAAA,mBAAmB,GAAS;AAChC,cAAI,CAAC,KAAKnB,YAAN,IAAsB,KAAKA,YAAL,CAAkBe,MAAlB,KAA6B,CAAnD,IAAwD,CAAC,KAAKZ,KAA9D,IAAuE,CAAC,KAAKC,KAAjF,EAAwF;AACpF;AACH,WAH+B,CAKhC;;;AACA,cAAMkB,gBAAgB,GAAG,KAAKnB,KAAL,CAAWoB,YAAX,CAAwBtC,MAAxB,CAAzB;;AACA,cAAIqC,gBAAgB,IAAI,KAAKtB,YAAL,CAAkB,KAAKJ,kBAAvB,CAAxB,EAAoE;AAChE0B,YAAAA,gBAAgB,CAACE,WAAjB,GAA+B,KAAKxB,YAAL,CAAkB,KAAKJ,kBAAvB,CAA/B;AACH,WAT+B,CAWhC;;;AACA,cAAM6B,eAAe,GAAG,CAAC,KAAK7B,kBAAL,GAA0B,CAA3B,IAAgC,KAAKI,YAAL,CAAkBe,MAA1E;AACA,cAAMW,gBAAgB,GAAG,KAAKtB,KAAL,CAAWmB,YAAX,CAAwBtC,MAAxB,CAAzB;;AACA,cAAIyC,gBAAgB,IAAI,KAAK1B,YAAL,CAAkByB,eAAlB,CAAxB,EAA4D;AACxDC,YAAAA,gBAAgB,CAACF,WAAjB,GAA+B,KAAKxB,YAAL,CAAkByB,eAAlB,CAA/B;AACH;AACJ;;AAEME,QAAAA,gBAAgB,CAAChB,IAAD,EAA4B;AAC/C,eAAKhB,WAAL,GAAmBgB,IAAnB;AACA,eAAKf,kBAAL,GAA0B,CAA1B;AACA,eAAKC,cAAL,GAAsB,GAAtB;AACA,eAAKsB,mBAAL;AACH;AAED;AACJ;AACA;;;AACWS,QAAAA,aAAa,GAAS;AACzB,eAAKhC,kBAAL,GAA0B,CAA1B;AACA,eAAKC,cAAL,GAAsB,GAAtB;AACA,eAAKK,eAAL;AACA,eAAKiB,mBAAL;AACH;AAED;AACJ;AACA;;;AACWU,QAAAA,qBAAqB,GAAW;AACnC,iBAAO,KAAKjC,kBAAZ;AACH;AAED;AACJ;AACA;;;AACWkC,QAAAA,iBAAiB,GAAW;AAC/B,iBAAO,KAAKjC,cAAZ;AACH;;AA9I0C,O;;;;;iBAI7B,I;;;;;;;iBAGA,I", "sourcesContent": ["import { _decorator, Component, Sprite, Sprite<PERSON><PERSON>e, Node, Enum, Vec3 } from 'cc';\r\nimport { Global } from '../../../Global';\r\nimport { BackgroundLayerData } from './Background'\r\nconst { ccclass, property } = _decorator;\r\n\r\n\r\n@ccclass('BackgroundLayer')\r\nexport class BackgroundLayer extends Component {\r\n\r\n    // we use 2 nodes for scrolling, each with a sprite component\r\n    @property({ type:Node, displayName: \"Node A (Visible)\", tooltip: \"First node that starts visible\" })\r\n    nodeA: Node = null;\r\n\r\n    @property({ type:Node, displayName: \"Node B (Top)\", tooltip: \"Second node that starts at the top\" })\r\n    nodeB: Node = null;\r\n\r\n    private currentData: BackgroundLayerData = null;\r\n    private currentSpriteIndex: number = 0;\r\n    private scrollProgress: number = 0; // Progress from 0 to 1 for current sprite\r\n    private positionA: Vec3 = new Vec3();\r\n    private positionB: Vec3 = new Vec3();\r\n\r\n    private get spriteFrames() {\r\n        return this.currentData && this.currentData.spriteFrames;\r\n    }\r\n\r\n    protected onLoad(): void {\r\n        this.initializeNodes();\r\n    }\r\n\r\n    private initializeNodes(): void {\r\n        if (!this.nodeA || !this.nodeB) {\r\n            console.warn(\"BackgroundLayer: nodeA and nodeB must be assigned!\");\r\n            return;\r\n        }\r\n\r\n        // NodeA starts at the visible area (y = 0)\r\n        this.positionA.set(0, 0, 0);\r\n        this.nodeA.setPosition(this.positionA);\r\n\r\n        // NodeB starts at the top (y = Global.HEIGHT)\r\n        this.positionB.set(0, Global.HEIGHT, 0);\r\n        this.nodeB.setPosition(this.positionB);\r\n    }\r\n\r\n    public initialize(data: BackgroundLayerData):void {\r\n        this.currentData = data;\r\n        this.initializeSprites();\r\n    }\r\n\r\n    tick(deltaTime: number) {\r\n        if (!this.currentData || this.currentData.spriteFrames.length === 0 || !this.nodeA || !this.nodeB) {\r\n            return;\r\n        }\r\n\r\n        // Calculate scroll progress based on scrollDuration\r\n        // scrollDuration is the time to complete one full scroll cycle\r\n        const scrollSpeed = 1.0 / this.currentData.scrollDuration; // Progress per second\r\n        this.scrollProgress += scrollSpeed * deltaTime;\r\n\r\n        // Handle looping within spriteFrames array and swap nodes\r\n        if (this.scrollProgress >= 1.0) {\r\n            this.scrollProgress = 0.0;\r\n            this.currentSpriteIndex = (this.currentSpriteIndex + 1) % this.spriteFrames.length;\r\n            this.swapNodes();\r\n            this.updateCurrentSprite();\r\n        }\r\n\r\n        // Calculate positions based on scroll progress\r\n        // NodeA moves from 0 to -Global.HEIGHT\r\n        // NodeB moves from Global.HEIGHT to 0\r\n        const offsetY = Global.HEIGHT * this.scrollProgress;\r\n\r\n        this.positionA.set(0, -offsetY, 0);\r\n        this.nodeA.setPosition(this.positionA);\r\n\r\n        this.positionB.set(0, Global.HEIGHT - offsetY, 0);\r\n        this.nodeB.setPosition(this.positionB);\r\n    }\r\n\r\n    private swapNodes(): void {\r\n        // Swap the nodes so the one that was at the top becomes the visible one\r\n        const tempNode = this.nodeA;\r\n        this.nodeA = this.nodeB;\r\n        this.nodeB = tempNode;\r\n\r\n        // Reset positions after swap\r\n        this.positionA.set(0, 0, 0);\r\n        this.nodeA.setPosition(this.positionA);\r\n\r\n        this.positionB.set(0, Global.HEIGHT, 0);\r\n        this.nodeB.setPosition(this.positionB);\r\n    }\r\n\r\n    private initializeSprites(): void {\r\n        if (this.spriteFrames && this.spriteFrames.length > 0) {\r\n            this.currentSpriteIndex = 0;\r\n            this.updateCurrentSprite();\r\n        }\r\n    }\r\n\r\n    private updateCurrentSprite(): void {\r\n        if (!this.spriteFrames || this.spriteFrames.length === 0 || !this.nodeA || !this.nodeB) {\r\n            return;\r\n        }\r\n\r\n        // Update nodeA with current sprite\r\n        const spriteComponentA = this.nodeA.getComponent(Sprite);\r\n        if (spriteComponentA && this.spriteFrames[this.currentSpriteIndex]) {\r\n            spriteComponentA.spriteFrame = this.spriteFrames[this.currentSpriteIndex];\r\n        }\r\n\r\n        // Update nodeB with next sprite (for seamless transition)\r\n        const nextSpriteIndex = (this.currentSpriteIndex + 1) % this.spriteFrames.length;\r\n        const spriteComponentB = this.nodeB.getComponent(Sprite);\r\n        if (spriteComponentB && this.spriteFrames[nextSpriteIndex]) {\r\n            spriteComponentB.spriteFrame = this.spriteFrames[nextSpriteIndex];\r\n        }\r\n    }\r\n\r\n    public changeBackground(data: BackgroundLayerData) {\r\n        this.currentData = data;\r\n        this.currentSpriteIndex = 0;\r\n        this.scrollProgress = 0.0;\r\n        this.updateCurrentSprite();\r\n    }\r\n\r\n    /**\r\n     * Reset the layer to its initial state\r\n     */\r\n    public resetPosition(): void {\r\n        this.currentSpriteIndex = 0;\r\n        this.scrollProgress = 0.0;\r\n        this.initializeNodes();\r\n        this.updateCurrentSprite();\r\n    }\r\n\r\n    /**\r\n     * Get the current sprite index\r\n     */\r\n    public getCurrentSpriteIndex(): number {\r\n        return this.currentSpriteIndex;\r\n    }\r\n\r\n    /**\r\n     * Get the current scroll progress (0-1)\r\n     */\r\n    public getScrollProgress(): number {\r\n        return this.scrollProgress;\r\n    }\r\n}\r\n\r\n\r\n"]}