System.register(["cc"], function (_export, _context) {
  "use strict";

  var _cclegacy, __checkObsolete__, __checkObsoleteInNamespace__, _decorator, Component, _dec, _class, _crd, ccclass, property, Weapon;

  return {
    setters: [function (_cc) {
      _cclegacy = _cc.cclegacy;
      __checkObsolete__ = _cc.__checkObsolete__;
      __checkObsoleteInNamespace__ = _cc.__checkObsoleteInNamespace__;
      _decorator = _cc._decorator;
      Component = _cc.Component;
    }],
    execute: function () {
      _crd = true;

      _cclegacy._RF.push({}, "e5b0edKuXxOzaQxHWU9ur73", "Weapon", undefined);

      __checkObsolete__(['_decorator', 'Node', 'Component']);

      ({
        ccclass,
        property
      } = _decorator);

      _export("Weapon", Weapon = (_dec = ccclass('Weapon'), _dec(_class = class Weapon extends Component {
        /**
         * Check if the weapon can be used
         * @returns true if the weapon can be used, false otherwise
         */
        canUse() {
          return true;
        }
        /**
         * Use the weapon
         * Base implementation that should be overridden by subclasses
         */


        use() {}

      }) || _class));

      _cclegacy._RF.pop();

      _crd = false;
    }
  };
});
//# sourceMappingURL=b6872983c50ec1c7715d5dcdd74b55e62460d590.js.map