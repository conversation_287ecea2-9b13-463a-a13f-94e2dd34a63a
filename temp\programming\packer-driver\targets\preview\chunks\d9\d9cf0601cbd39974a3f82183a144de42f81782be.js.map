{"version": 3, "sources": ["file:///E:/M2Game/Client/assets/scripts/Game/ui/base/ScaleComp.ts"], "names": ["_decorator", "BaseComp", "GameConst", "ccclass", "ScaleComp", "m_initScale", "m_endScale", "m_scaleSpeed", "m_curScale", "m_tag", "m_able", "m_collide", "m_width", "m_height", "m_nodeScale", "onInit", "setData", "data", "collide", "para", "Math", "abs", "width", "height", "entity", "node", "scale", "setScale", "opacity", "scheduleOnce", "update", "deltaTime", "GameAble", "newScale", "scaleRatio", "setSize"], "mappings": ";;;;;;;;;;;;;;;;;;;;AAASA,MAAAA,U,OAAAA,U;;AACFC,MAAAA,Q;;AACEC,MAAAA,S,iBAAAA,S;;;;;;;;;OAEH;AAAEC,QAAAA;AAAF,O,GAAcH,U;;yBAGCI,S,WADpBD,OAAO,CAAC,WAAD,C,gBAAR,MACqBC,SADrB;AAAA;AAAA,gCACgD;AAAA;AAAA;AAAA,eAC5CC,WAD4C,GACtB,CADsB;AAAA,eAE5CC,UAF4C,GAEvB,CAFuB;AAAA,eAG5CC,YAH4C,GAGrB,CAHqB;AAAA,eAI5CC,UAJ4C,GAIvB,CAJuB;AAAA,eAK5CC,KAL4C,GAK5B,CAL4B;AAAA,eAM5CC,MAN4C,GAM1B,KAN0B;AAAA,eAO5CC,SAP4C,GAO3B,IAP2B;AAAA,eAQ5CC,OAR4C,GAQ1B,CAR0B;AAAA,eAS5CC,QAT4C,GASzB,CATyB;AAAA,eAU5CC,WAV4C,GAUtB,CAVsB;AAAA;;AAY5CC,QAAAA,MAAM,GAAS,CACX;AACH;;AAEDC,QAAAA,OAAO,CAACC,IAAD,EAAYC,OAAZ,EAAgC;AACnC,eAAKb,WAAL,GAAmBY,IAAI,CAACE,IAAL,CAAU,CAAV,KAAgB,CAAnC;AACA,eAAKb,UAAL,GAAkBW,IAAI,CAACE,IAAL,CAAU,CAAV,KAAgB,CAAlC;AACA,eAAKZ,YAAL,GAAoBU,IAAI,CAACE,IAAL,CAAU,CAAV,KAAgB,CAApC;AACA,eAAKX,UAAL,GAAkB,KAAKH,WAAvB;AACA,eAAKI,KAAL,GAAa,KAAKJ,WAAL,GAAmB,KAAKC,UAAxB,GAAqC,CAArC,GAAyC,CAAtD;AACA,eAAKC,YAAL,GAAoB,KAAKE,KAAL,KAAe,CAAf,GAAmB,CAACW,IAAI,CAACC,GAAL,CAAS,KAAKd,YAAd,CAApB,GAAkDa,IAAI,CAACC,GAAL,CAAS,KAAKd,YAAd,CAAtE;AACA,eAAKG,MAAL,GAAc,IAAd;AACA,eAAKC,SAAL,GAAiBO,OAAjB;AACA,eAAKN,OAAL,GAAeM,OAAO,CAACD,IAAR,CAAaK,KAA5B;AACA,eAAKT,QAAL,GAAgBK,OAAO,CAACD,IAAR,CAAaM,MAA7B;AACA,eAAKT,WAAL,GAAmB,KAAKU,MAAL,CAAYC,IAAZ,CAAiBC,KAApC;AACA,eAAKF,MAAL,CAAYC,IAAZ,CAAiBE,QAAjB,CAA0B,KAAKb,WAAL,GAAmB,KAAKT,WAAlD,EAA8D,KAAKS,WAAL,GAAmB,KAAKT,WAAtF;AACA,eAAKmB,MAAL,CAAYC,IAAZ,CAAiBG,OAAjB,GAA2B,CAA3B;AAEA,eAAKJ,MAAL,CAAYK,YAAZ,CAAyB,MAAM;AAC3B,iBAAKL,MAAL,CAAYC,IAAZ,CAAiBG,OAAjB,GAA2B,GAA3B;AACH,WAFD;AAGH;;AAEDE,QAAAA,MAAM,CAACC,SAAD,EAA0B;AAC5B,cAAI;AAAA;AAAA,sCAAUC,QAAV,IAAsB,KAAKtB,MAA/B,EAAuC;AACnC,gBAAIuB,QAAQ,GAAG,KAAKzB,UAAL,GAAkB,KAAKD,YAAL,IAAqBwB,SAAS,GAAG,GAAZ,GAAkB,iBAAlB,GAAsCA,SAA3D,CAAjC;;AAEA,gBAAI,KAAKtB,KAAL,KAAe,CAAnB,EAAsB;AAClB,kBAAIwB,QAAQ,GAAG,KAAK3B,UAApB,EAAgC;AAC5B2B,gBAAAA,QAAQ,GAAG,KAAK3B,UAAhB;AACA,qBAAKI,MAAL,GAAc,KAAd;AACH;AACJ,aALD,MAKO;AACH,kBAAIuB,QAAQ,GAAG,KAAK3B,UAApB,EAAgC;AAC5B2B,gBAAAA,QAAQ,GAAG,KAAK3B,UAAhB;AACA,qBAAKI,MAAL,GAAc,KAAd;AACH;AACJ;;AAED,iBAAKF,UAAL,GAAkByB,QAAlB;AACA,iBAAKT,MAAL,CAAYC,IAAZ,CAAiBE,QAAjB,CAA0BM,QAAQ,GAAG,KAAKnB,WAA1C,EAAsDmB,QAAQ,GAAG,KAAKnB,WAAtE;AAEA,gBAAMoB,UAAU,GAAG,KAAKV,MAAL,CAAYC,IAAZ,CAAiBC,KAAjB,GAAyB,KAAKZ,WAAjD;AACA,iBAAKH,SAAL,CAAewB,OAAf,CAAuB,KAAKvB,OAAL,GAAesB,UAAtC,EAAkD,KAAKrB,QAAL,GAAgBqB,UAAlE;AACH;AACJ;;AA1D2C,O", "sourcesContent": ["import { _decorator, Component } from 'cc';\r\nimport BaseComp from './BaseComp';\r\nimport { GameConst } from '../../const/GameConst';\r\n\r\nconst { ccclass } = _decorator;\r\n\r\n@ccclass('ScaleComp')\r\nexport default class ScaleComp extends BaseComp {\r\n    m_initScale: number = 1;\r\n    m_endScale: number = 1;\r\n    m_scaleSpeed: number = 1;\r\n    m_curScale: number = 1;\r\n    m_tag: number = 0;\r\n    m_able: boolean = false;\r\n    m_collide: any = null;\r\n    m_width: number = 0;\r\n    m_height: number = 0;\r\n    m_nodeScale: number = 1;\r\n\r\n    onInit(): void {\r\n        // 初始化逻辑\r\n    }\r\n\r\n    setData(data: any, collide: any): void {\r\n        this.m_initScale = data.para[4] || 1;\r\n        this.m_endScale = data.para[5] || 1;\r\n        this.m_scaleSpeed = data.para[6] || 1;\r\n        this.m_curScale = this.m_initScale;\r\n        this.m_tag = this.m_initScale > this.m_endScale ? 0 : 1;\r\n        this.m_scaleSpeed = this.m_tag === 0 ? -Math.abs(this.m_scaleSpeed) : Math.abs(this.m_scaleSpeed);\r\n        this.m_able = true;\r\n        this.m_collide = collide;\r\n        this.m_width = collide.data.width;\r\n        this.m_height = collide.data.height;\r\n        this.m_nodeScale = this.entity.node.scale;\r\n        this.entity.node.setScale(this.m_nodeScale * this.m_initScale,this.m_nodeScale * this.m_initScale);\r\n        this.entity.node.opacity = 0;\r\n\r\n        this.entity.scheduleOnce(() => {\r\n            this.entity.node.opacity = 255;\r\n        });\r\n    }\r\n\r\n    update(deltaTime: number): void {\r\n        if (GameConst.GameAble && this.m_able) {\r\n            let newScale = this.m_curScale + this.m_scaleSpeed * (deltaTime > 0.2 ? 0.016666666666667 : deltaTime);\r\n\r\n            if (this.m_tag === 0) {\r\n                if (newScale < this.m_endScale) {\r\n                    newScale = this.m_endScale;\r\n                    this.m_able = false;\r\n                }\r\n            } else {\r\n                if (newScale > this.m_endScale) {\r\n                    newScale = this.m_endScale;\r\n                    this.m_able = false;\r\n                }\r\n            }\r\n\r\n            this.m_curScale = newScale;\r\n            this.entity.node.setScale(newScale * this.m_nodeScale,newScale * this.m_nodeScale);\r\n\r\n            const scaleRatio = this.entity.node.scale / this.m_nodeScale;\r\n            this.m_collide.setSize(this.m_width * scaleRatio, this.m_height * scaleRatio);\r\n        }\r\n    }\r\n}"]}