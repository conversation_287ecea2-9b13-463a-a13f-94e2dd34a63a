System.register(["__unresolved_0", "cc", "__unresolved_1", "__unresolved_2"], function (_export, _context) {
  "use strict";

  var _reporterNs, _cclegacy, __checkObsolete__, __checkObsoleteInNamespace__, _decorator, Component, find, Vec3, Global, GamePersistNode, _dec, _class, _crd, ccclass, property, MainGame;

  function _reportPossibleCrUseOfGameFactory(extras) {
    _reporterNs.report("GameFactory", "./factroy/GameFactory", _context.meta, extras);
  }

  function _reportPossibleCrUseOfGlobal(extras) {
    _reporterNs.report("Global", "./Global", _context.meta, extras);
  }

  function _reportPossibleCrUseOfGamePersistNode(extras) {
    _reporterNs.report("GamePersistNode", "./GamePersistNode", _context.meta, extras);
  }

  return {
    setters: [function (_unresolved_) {
      _reporterNs = _unresolved_;
    }, function (_cc) {
      _cclegacy = _cc.cclegacy;
      __checkObsolete__ = _cc.__checkObsolete__;
      __checkObsoleteInNamespace__ = _cc.__checkObsoleteInNamespace__;
      _decorator = _cc._decorator;
      Component = _cc.Component;
      find = _cc.find;
      Vec3 = _cc.Vec3;
    }, function (_unresolved_2) {
      Global = _unresolved_2.Global;
    }, function (_unresolved_3) {
      GamePersistNode = _unresolved_3.GamePersistNode;
    }],
    execute: function () {
      _crd = true;

      _cclegacy._RF.push({}, "226beFKtB1DyIayvugKZ+uE", "MainGame", undefined);

      __checkObsolete__(['_decorator', 'Component', 'Node', 'find', 'Vec3']);

      ({
        ccclass,
        property
      } = _decorator);

      _export("MainGame", MainGame = (_dec = ccclass('MainGame'), _dec(_class = class MainGame extends Component {
        constructor() {
          super(...arguments);
          this.enemyFactory = null;
          //敌机工厂
          this.goodsFactory = null;
          //物资工厂
          this.persistNode = null;
          //持久节点
          this.enemy1Timer = 0;
          //产生敌机1的定时器
          this.enemy2Timer = 0;
          //产生敌机2的定时器
          this.bloodGoodsTimer = 0;
          //加血物资的定时器
          this.lightGoodsTimer = 0;
          //激光物资的定时器
          this.missileGoodsTimer = 0;
          //激光物资的定时器
          this.enemy1MinProduceTime = 0;
          //enemy1出现时间的间隔的下限
          this.enemy1MaxProduceTime = 0;
          //enemy1出现时间的间隔的上限
          this.enemy2MinProduceTime = 0;
          //enemy2出现时间的间隔的下限
          this.enemy2MaxProduceTime = 0;
          //enemy2出现时间的间隔的上限
          this.bloodGoodsMinProduceTime = 0;
          //加血物资出现时间的间隔的下限
          this.bloodGoodsMaxProduceTime = 0;
          //加血物资出现时间的间隔的上限
          this.lightGoodsMinProduceTime = 0;
          //激光物资出现时间的间隔的下限
          this.lightGoodsMaxProduceTime = 0;
          //激光物资出现时间的间隔的上限
          this.missileGoodsMinProduceTime = 0;
          //导弹物资出现时间的间隔的下限
          this.missileGoodsMaxProduceTime = 0;
          //导弹物资出现时间的间隔的上限
          this.enemy1Random = 0;
          //enemy1出现具体时间（随机的）
          this.enemy2Random = 0;
          //enemy2出现具体时间（随机的）
          this.bloodGoodsRandom = 0;
          //加血物资出现具体时间（随机的）
          this.lightGoodsRandom = 0;
          //加血物资出现具体时间（随机的）
          this.missileGoodsRandom = 0;
        }

        //加血物资出现具体时间（随机的）
        onLoad() {
          this.persistNode = find("GamePersistNode");
          this.enemyFactory = this.persistNode.getComponent(_crd && GamePersistNode === void 0 ? (_reportPossibleCrUseOfGamePersistNode({
            error: Error()
          }), GamePersistNode) : GamePersistNode).enemyFactory;
          this.goodsFactory = this.persistNode.getComponent(_crd && GamePersistNode === void 0 ? (_reportPossibleCrUseOfGamePersistNode({
            error: Error()
          }), GamePersistNode) : GamePersistNode).goodsFactory; //从persist node面板上获取实际的值

          this.enemy1MinProduceTime = this.persistNode.getComponent(_crd && GamePersistNode === void 0 ? (_reportPossibleCrUseOfGamePersistNode({
            error: Error()
          }), GamePersistNode) : GamePersistNode).enemy1MinProduceTime;
          this.enemy1MaxProduceTime = this.persistNode.getComponent(_crd && GamePersistNode === void 0 ? (_reportPossibleCrUseOfGamePersistNode({
            error: Error()
          }), GamePersistNode) : GamePersistNode).enemy1MaxProduceTime; //从persist node面板上获取实际的值

          this.enemy2MinProduceTime = this.persistNode.getComponent(_crd && GamePersistNode === void 0 ? (_reportPossibleCrUseOfGamePersistNode({
            error: Error()
          }), GamePersistNode) : GamePersistNode).enemy2MinProduceTime;
          this.enemy2MaxProduceTime = this.persistNode.getComponent(_crd && GamePersistNode === void 0 ? (_reportPossibleCrUseOfGamePersistNode({
            error: Error()
          }), GamePersistNode) : GamePersistNode).enemy2MaxProduceTime; //从persist node面板上获取实际的值

          this.bloodGoodsMinProduceTime = this.persistNode.getComponent(_crd && GamePersistNode === void 0 ? (_reportPossibleCrUseOfGamePersistNode({
            error: Error()
          }), GamePersistNode) : GamePersistNode).bloodGoodsMinProduceTime;
          this.bloodGoodsMaxProduceTime = this.persistNode.getComponent(_crd && GamePersistNode === void 0 ? (_reportPossibleCrUseOfGamePersistNode({
            error: Error()
          }), GamePersistNode) : GamePersistNode).bloodGoodsMaxProduceTime;
          this.lightGoodsMinProduceTime = this.persistNode.getComponent(_crd && GamePersistNode === void 0 ? (_reportPossibleCrUseOfGamePersistNode({
            error: Error()
          }), GamePersistNode) : GamePersistNode).lightGoodsMinProduceTime;
          this.lightGoodsMaxProduceTime = this.persistNode.getComponent(_crd && GamePersistNode === void 0 ? (_reportPossibleCrUseOfGamePersistNode({
            error: Error()
          }), GamePersistNode) : GamePersistNode).lightGoodsMaxProduceTime;
          this.missileGoodsMinProduceTime = this.persistNode.getComponent(_crd && GamePersistNode === void 0 ? (_reportPossibleCrUseOfGamePersistNode({
            error: Error()
          }), GamePersistNode) : GamePersistNode).missileGoodsMinProduceTime;
          this.missileGoodsMaxProduceTime = this.persistNode.getComponent(_crd && GamePersistNode === void 0 ? (_reportPossibleCrUseOfGamePersistNode({
            error: Error()
          }), GamePersistNode) : GamePersistNode).missileGoodsMaxProduceTime; //初始化各种随机数

          this.enemy1Random = this.enemy1MinProduceTime + Math.random() * (this.enemy1MaxProduceTime - this.enemy1MinProduceTime);
          this.enemy2Random = this.enemy2MinProduceTime + Math.random() * (this.enemy2MaxProduceTime - this.enemy2MinProduceTime);
          this.bloodGoodsRandom = this.bloodGoodsMinProduceTime + Math.random() * (this.bloodGoodsMaxProduceTime - this.bloodGoodsMinProduceTime);
          this.lightGoodsRandom = this.lightGoodsMinProduceTime + Math.random() * (this.missileGoodsMaxProduceTime - this.lightGoodsMinProduceTime);
          this.missileGoodsRandom = this.missileGoodsMinProduceTime + Math.random() * (this.missileGoodsMaxProduceTime - this.missileGoodsMinProduceTime);
        }

        update(deltaTime) {
          //产生敌机1
          this.enemy1Timer += deltaTime;

          if (this.enemy1Timer > this.enemy1Random) {
            this.produceEnemy1();
            this.enemy1Timer = 0;
            this.enemy1Random = this.enemy1MinProduceTime + Math.random() * (this.enemy1MaxProduceTime - this.enemy1MinProduceTime);
          } //产生敌机2


          this.enemy2Timer += deltaTime;

          if (this.enemy2Timer > this.enemy2Random) {
            this.produceEnemy2();
            this.enemy2Timer = 0;
            this.enemy2Random = this.enemy2MinProduceTime + Math.random() * (this.enemy2MaxProduceTime - this.enemy2MinProduceTime);
          } //产生血量物资


          this.bloodGoodsTimer += deltaTime;

          if (this.bloodGoodsTimer > this.bloodGoodsRandom) {
            this.produceBloodGoods();
            this.bloodGoodsTimer = 0;
            this.bloodGoodsRandom = this.bloodGoodsMinProduceTime + Math.random() * (this.bloodGoodsMaxProduceTime - this.bloodGoodsMinProduceTime);
          } //产生激光物资


          this.lightGoodsTimer += deltaTime;

          if (this.lightGoodsTimer > this.lightGoodsRandom) {
            this.produceLightGoods();
            this.lightGoodsTimer = 0;
            this.lightGoodsRandom = this.lightGoodsMinProduceTime + Math.random() * (this.missileGoodsMaxProduceTime - this.lightGoodsMinProduceTime);
          } //产生导弹物资


          this.missileGoodsTimer += deltaTime;

          if (this.missileGoodsTimer > this.missileGoodsRandom) {
            this.produceMissileGoods();
            this.missileGoodsTimer = 0;
            this.missileGoodsRandom = this.missileGoodsMinProduceTime + Math.random() * (this.missileGoodsMaxProduceTime - this.missileGoodsMinProduceTime);
          }
        } //产生导弹物资


        produceMissileGoods() {
          var posBegin = new Vec3();
          var goodsTemp = null;
          goodsTemp = this.goodsFactory.createProduct((_crd && Global === void 0 ? (_reportPossibleCrUseOfGlobal({
            error: Error()
          }), Global) : Global).MISSILE_GOODS);
          this.node.addChild(goodsTemp);
          posBegin.x = (Math.random() - 0.5) * 2 * (_crd && Global === void 0 ? (_reportPossibleCrUseOfGlobal({
            error: Error()
          }), Global) : Global).WIDTH / 2;
          posBegin.y = (_crd && Global === void 0 ? (_reportPossibleCrUseOfGlobal({
            error: Error()
          }), Global) : Global).HEIGHT / 2;
          goodsTemp.setPosition(posBegin);
        } //产生激光物资


        produceLightGoods() {
          var posBegin = new Vec3();
          var goodsTemp = null;
          goodsTemp = this.goodsFactory.createProduct((_crd && Global === void 0 ? (_reportPossibleCrUseOfGlobal({
            error: Error()
          }), Global) : Global).LIGHT_GOODS);
          this.node.addChild(goodsTemp);
          posBegin.x = (Math.random() - 0.5) * 2 * (_crd && Global === void 0 ? (_reportPossibleCrUseOfGlobal({
            error: Error()
          }), Global) : Global).WIDTH / 2;
          posBegin.y = (_crd && Global === void 0 ? (_reportPossibleCrUseOfGlobal({
            error: Error()
          }), Global) : Global).HEIGHT / 2;
          goodsTemp.setPosition(posBegin);
        } //产生血量物资


        produceBloodGoods() {
          var posBegin = new Vec3();
          var goodsTemp = null;
          goodsTemp = this.goodsFactory.createProduct((_crd && Global === void 0 ? (_reportPossibleCrUseOfGlobal({
            error: Error()
          }), Global) : Global).BLOOD_GOODS);
          this.node.addChild(goodsTemp);
          posBegin.x = (Math.random() - 0.5) * 2 * (_crd && Global === void 0 ? (_reportPossibleCrUseOfGlobal({
            error: Error()
          }), Global) : Global).WIDTH / 2;
          posBegin.y = (_crd && Global === void 0 ? (_reportPossibleCrUseOfGlobal({
            error: Error()
          }), Global) : Global).HEIGHT / 2;
          goodsTemp.setPosition(posBegin);
        }
        /**
         * //产生敌机1
         */


        produceEnemy1() {
          var posBegin = new Vec3();
          var enemyTemp = null;
          enemyTemp = this.enemyFactory.createProduct((_crd && Global === void 0 ? (_reportPossibleCrUseOfGlobal({
            error: Error()
          }), Global) : Global).ENEMY_1);
          this.node.addChild(enemyTemp);
          posBegin.x = (Math.random() - 0.5) * 2 * (_crd && Global === void 0 ? (_reportPossibleCrUseOfGlobal({
            error: Error()
          }), Global) : Global).WIDTH / 2;
          posBegin.y = (_crd && Global === void 0 ? (_reportPossibleCrUseOfGlobal({
            error: Error()
          }), Global) : Global).HEIGHT / 2;
          enemyTemp.setPosition(posBegin);
        }
        /**
         * //产生敌机2
         */


        produceEnemy2() {
          var posBegin = new Vec3();
          var enemyTemp = null;
          enemyTemp = this.enemyFactory.createProduct((_crd && Global === void 0 ? (_reportPossibleCrUseOfGlobal({
            error: Error()
          }), Global) : Global).ENEMY_2);
          this.node.addChild(enemyTemp);
          posBegin.x = (Math.random() - 0.5) * 2 * (_crd && Global === void 0 ? (_reportPossibleCrUseOfGlobal({
            error: Error()
          }), Global) : Global).WIDTH / 2;
          posBegin.y = (_crd && Global === void 0 ? (_reportPossibleCrUseOfGlobal({
            error: Error()
          }), Global) : Global).HEIGHT / 2;
          enemyTemp.setPosition(posBegin);
        }

      }) || _class));

      _cclegacy._RF.pop();

      _crd = false;
    }
  };
});
//# sourceMappingURL=96d023f1b8c5298f2a01413669e02f6cd4aceeef.js.map