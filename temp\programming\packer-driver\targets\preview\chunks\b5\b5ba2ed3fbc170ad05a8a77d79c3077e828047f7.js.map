{"version": 3, "sources": ["file:///E:/M2Game/Client/assets/scripts/ui/components/common/list/ListItem.ts"], "names": ["Node", "Component", "Enum", "Sprite", "SpriteFrame", "tween", "_decorator", "EventHandler", "Tween", "<PERSON><PERSON>", "UITransform", "Vec3", "DEV", "ccclass", "property", "disallowMultiple", "menu", "executionOrder", "SelectedType", "ListItem", "type", "tooltip", "visible", "selectedMode", "NONE", "SWITCH", "_unselectedSpriteFrame", "_selected", "_btnCom", "list", "_eventReg", "listId", "selected", "val", "selectedFlag", "TOGGLE", "active", "sp", "getComponent", "spriteFrame", "selectedSpriteFrame", "btnCom", "node", "onLoad", "com", "onDestroy", "off", "EventType", "SIZE_CHANGED", "_onSizeChange", "_registerEvent", "clickEvents", "unshift", "createEvt", "adaptiveSize", "on", "_onItemAdaptive", "component", "handler<PERSON>ame", "<PERSON><PERSON><PERSON><PERSON>", "name", "match", "pop", "replace", "evt", "target", "handler", "showAni", "aniType", "callFunc", "del", "t", "twe", "ut", "to", "scale", "by", "position", "height", "width", "call", "_delSingleItem", "n", "displayData", "length", "id", "splice", "start", "onClickThis", "selectedId"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;AASSA,MAAAA,I,OAAAA,I;AAAMC,MAAAA,S,OAAAA,S;AAAWC,MAAAA,I,OAAAA,I;AAAMC,MAAAA,M,OAAAA,M;AAAQC,MAAAA,W,OAAAA,W;AAAaC,MAAAA,K,OAAAA,K;AAAOC,MAAAA,U,OAAAA,U;AAAYC,MAAAA,Y,OAAAA,Y;AAAcC,MAAAA,K,OAAAA,K;AAAOC,MAAAA,M,OAAAA,M;AAAQC,MAAAA,W,OAAAA,W;AAAaC,MAAAA,I,OAAAA,I;;AACzGC,MAAAA,G,UAAAA,G;;;;;;;AAVT;AACA;AACA;AACA;AACA;AACA;AACA;AACA;OACM;AAAEC,QAAAA,OAAF;AAAWC,QAAAA,QAAX;AAAqBC,QAAAA,gBAArB;AAAuCC,QAAAA,IAAvC;AAA6CC,QAAAA;AAA7C,O,GAAgEX,U;;;;AAKjEY,MAAAA,Y,0BAAAA,Y;AAAAA,QAAAA,Y,CAAAA,Y;AAAAA,QAAAA,Y,CAAAA,Y;AAAAA,QAAAA,Y,CAAAA,Y;eAAAA,Y;QAAAA,Y;;yBAUgBC,Q,WAHpBJ,gBAAgB,E,UAChBC,IAAI,CAAC,WAAD,C,UACJC,cAAc,CAAC,CAAC,IAAF,C,UAGVH,QAAQ,CAAC;AAAEM,QAAAA,IAAI,EAAEjB,MAAR;AAAgBkB,QAAAA,OAAO,EAAET,GAAG,IAAI;AAAhC,OAAD,C,UAGRE,QAAQ,CAAC;AAAEM,QAAAA,IAAI,EAAEpB,IAAR;AAAcqB,QAAAA,OAAO,EAAET,GAAG,IAAI;AAA9B,OAAD,C,UAGRE,QAAQ,CAAC;AACNM,QAAAA,IAAI,EAAElB,IAAI,CAACgB,YAAD,CADJ;AAENG,QAAAA,OAAO,EAAET,GAAG,IAAI;AAFV,OAAD,C,UAMRE,QAAQ,CAAC;AACNM,QAAAA,IAAI,EAAEpB,IADA;AACMqB,QAAAA,OAAO,EAAET,GAAG,IAAI,MADtB;;AAENU,QAAAA,OAAO,GAAG;AAAE,iBAAO,KAAKC,YAAL,GAAoBL,YAAY,CAACM,IAAxC;AAA8C;;AAFpD,OAAD,C,UAMRV,QAAQ,CAAC;AACNM,QAAAA,IAAI,EAAEhB,WADA;AACaiB,QAAAA,OAAO,EAAET,GAAG,IAAI,iBAD7B;;AAENU,QAAAA,OAAO,GAAG;AAAE,iBAAO,KAAKC,YAAL,IAAqBL,YAAY,CAACO,MAAzC;AAAiD;;AAFvD,OAAD,C,UAQRX,QAAQ,CAAC;AACNO,QAAAA,OAAO,EAAET,GAAG,IAAI;AADV,OAAD,C,EAhCZC,O,iEAAD,MAIqBM,QAJrB,SAIsClB,SAJtC,CAIgD;AAAA;AAAA;;AAC5C;AAD4C;;AAI5C;AAJ4C;;AAO5C;AAP4C;;AAa5C;AAb4C;;AAmB5C;AAnB4C;;AAyB5C;AAzB4C,eA0B5CyB,sBA1B4C,GA0BN,IA1BM;;AA2B5C;AA3B4C;;AAgC5C;AAhC4C,eAiC5CC,SAjC4C,GAiCvB,KAjCuB;AAsD5C;AAtD4C,eAuDpCC,OAvDoC;AA6D5C;AA7D4C,eA8DrCC,IA9DqC;AA+D5C;AA/D4C,eAgEpCC,SAhEoC,GAgExB,KAhEwB;AAiE5C;AAjE4C,eAkErCC,MAlEqC;AAAA;;AAkChC,YAARC,QAAQ,CAACC,GAAD,EAAe;AACvB,eAAKN,SAAL,GAAiBM,GAAjB;AACAzB,UAAAA,KAAK;AACL,cAAI,CAAC,KAAK0B,YAAV,EACI;;AACJ,kBAAQ,KAAKX,YAAb;AACI,iBAAKL,YAAY,CAACiB,MAAlB;AACI,mBAAKD,YAAL,CAAkBE,MAAlB,GAA2BH,GAA3B;AACA;;AACJ,iBAAKf,YAAY,CAACO,MAAlB;AACI,kBAAIY,EAAU,GAAG,KAAKH,YAAL,CAAkBI,YAAlB,CAA+BnC,MAA/B,CAAjB;;AACA,kBAAIkC,EAAJ,EAAQ;AACJA,gBAAAA,EAAE,CAACE,WAAH,GAAiBN,GAAG,GAAG,KAAKO,mBAAR,GAA8B,KAAKd,sBAAvD;AACH;;AACD;AATR;AAWH;;AACW,YAARM,QAAQ,GAAG;AACX,iBAAO,KAAKL,SAAZ;AACH;;AAGS,YAANc,MAAM,GAAG;AACT,cAAI,CAAC,KAAKb,OAAV,EACI,KAAKA,OAAL,GAAe,KAAKc,IAAL,CAAUJ,YAAV,CAAuB7B,MAAvB,CAAf;AACJ,iBAAO,KAAKmB,OAAZ;AACH;;AAQDe,QAAAA,MAAM,GAAG;AACL;AACA;AACA;AACA;AACA,cAAI,KAAKpB,YAAL,IAAqBL,YAAY,CAACO,MAAtC,EAA8C;AAC1C,gBAAImB,GAAW,GAAG,KAAKV,YAAL,CAAkBI,YAAlB,CAA+BnC,MAA/B,CAAlB;AACA,iBAAKuB,sBAAL,GAA8BkB,GAAG,CAACL,WAAlC;AACH;AACJ;;AAEDM,QAAAA,SAAS,GAAG;AACR,eAAKH,IAAL,CAAUI,GAAV,CAAc9C,IAAI,CAAC+C,SAAL,CAAeC,YAA7B,EAA2C,KAAKC,aAAhD,EAA+D,IAA/D;AACH;;AAEDC,QAAAA,cAAc,GAAG;AACb,cAAI,CAAC,KAAKpB,SAAV,EAAqB;AACjB,gBAAI,KAAKW,MAAL,IAAe,KAAKZ,IAAL,CAAUN,YAAV,GAAyB,CAA5C,EAA+C;AAC3C,mBAAKkB,MAAL,CAAYU,WAAZ,CAAwBC,OAAxB,CAAgC,KAAKC,SAAL,CAAe,IAAf,EAAqB,aAArB,CAAhC;AACH;;AACD,gBAAI,KAAKC,YAAT,EAAuB;AACnB,mBAAKZ,IAAL,CAAUa,EAAV,CAAavD,IAAI,CAAC+C,SAAL,CAAeC,YAA5B,EAA0C,KAAKC,aAA/C,EAA8D,IAA9D;AACH;;AACD,iBAAKnB,SAAL,GAAiB,IAAjB;AACH;AACJ;;AAEDmB,QAAAA,aAAa,GAAG;AACZ,eAAKpB,IAAL,CAAU2B,eAAV,CAA0B,KAAKd,IAA/B;AACH;AACD;AACJ;AACA;AACA;AACA;AACA;AACA;;;AACIW,QAAAA,SAAS,CAACI,SAAD,EAAuBC,WAAvB,EAA4ChB,IAA5C,EAA+D;AAAA,cAAnBA,IAAmB;AAAnBA,YAAAA,IAAmB,GAAN,IAAM;AAAA;;AACpE,cAAI,CAACe,SAAS,CAACE,OAAf,EACI,OAFgE,CAEzD;;AACXF,UAAAA,SAAS,CAAC,SAAD,CAAT,GAAuBA,SAAS,CAAC,SAAD,CAAT,IAAwBA,SAAS,CAACG,IAAV,CAAeC,KAAf,CAAqB,YAArB,EAAmCC,GAAnC,GAAyCC,OAAzC,CAAiD,OAAjD,EAA0D,EAA1D,CAA/C;AACA,cAAIC,GAAG,GAAG,IAAIzD,YAAJ,EAAV;AACAyD,UAAAA,GAAG,CAACC,MAAJ,GAAavB,IAAI,IAAIe,SAAS,CAACf,IAA/B;AACAsB,UAAAA,GAAG,CAACP,SAAJ,GAAgBA,SAAS,CAAC,SAAD,CAAzB;AACAO,UAAAA,GAAG,CAACE,OAAJ,GAAcR,WAAd;AACA,iBAAOM,GAAP;AACH;;AAEDG,QAAAA,OAAO,CAACC,OAAD,EAAkBC,QAAlB,EAAsCC,GAAtC,EAAoD;AACvD,cAAIC,CAAM,GAAG,IAAb;AACA,cAAIC,GAAJ;AACA,cAAIC,EAAe,GAAGF,CAAC,CAAC7B,IAAF,CAAOJ,YAAP,CAAoB5B,WAApB,CAAtB;;AACA,kBAAQ0D,OAAR;AACI,iBAAK,CAAL;AAAQ;AACJI,cAAAA,GAAG,GAAGnE,KAAK,CAACkE,CAAC,CAAC7B,IAAH,CAAL,CACDgC,EADC,CACE,EADF,EACM;AAAEC,gBAAAA,KAAK,EAAE,IAAIhE,IAAJ,CAAS,EAAT,EAAa,EAAb;AAAT,eADN,EAEDiE,EAFC,CAEE,EAFF,EAEM;AAAEC,gBAAAA,QAAQ,EAAE,IAAIlE,IAAJ,CAAS,CAAT,EAAY8D,EAAE,CAACK,MAAH,GAAY,CAAxB;AAAZ,eAFN,CAAN;AAGA;;AACJ,iBAAK,CAAL;AAAQ;AACJN,cAAAA,GAAG,GAAGnE,KAAK,CAACkE,CAAC,CAAC7B,IAAH,CAAL,CACDgC,EADC,CACE,EADF,EACM;AAAEC,gBAAAA,KAAK,EAAE,IAAIhE,IAAJ,CAAS,EAAT,EAAa,EAAb;AAAT,eADN,EAEDiE,EAFC,CAEE,EAFF,EAEM;AAAEC,gBAAAA,QAAQ,EAAE,IAAIlE,IAAJ,CAAS8D,EAAE,CAACM,KAAH,GAAW,CAApB,EAAuB,CAAvB;AAAZ,eAFN,CAAN;AAGA;;AACJ,iBAAK,CAAL;AAAQ;AACJP,cAAAA,GAAG,GAAGnE,KAAK,CAACkE,CAAC,CAAC7B,IAAH,CAAL,CACDgC,EADC,CACE,EADF,EACM;AAAEC,gBAAAA,KAAK,EAAE,IAAIhE,IAAJ,CAAS,EAAT,EAAa,EAAb;AAAT,eADN,EAEDiE,EAFC,CAEE,EAFF,EAEM;AAAEC,gBAAAA,QAAQ,EAAE,IAAIlE,IAAJ,CAAS,CAAT,EAAY8D,EAAE,CAACK,MAAH,GAAY,CAAC,CAAzB;AAAZ,eAFN,CAAN;AAGA;;AACJ,iBAAK,CAAL;AAAQ;AACJN,cAAAA,GAAG,GAAGnE,KAAK,CAACkE,CAAC,CAAC7B,IAAH,CAAL,CACDgC,EADC,CACE,EADF,EACM;AAAEC,gBAAAA,KAAK,EAAE,IAAIhE,IAAJ,CAAS,EAAT,EAAa,EAAb;AAAT,eADN,EAEDiE,EAFC,CAEE,EAFF,EAEM;AAAEC,gBAAAA,QAAQ,EAAE,IAAIlE,IAAJ,CAAS8D,EAAE,CAACM,KAAH,GAAW,CAAC,CAArB,EAAwB,CAAxB;AAAZ,eAFN,CAAN;AAGA;;AACJ;AAAS;AACLP,cAAAA,GAAG,GAAGnE,KAAK,CAACkE,CAAC,CAAC7B,IAAH,CAAL,CACDgC,EADC,CACE,EADF,EACM;AAAEC,gBAAAA,KAAK,EAAE,IAAIhE,IAAJ,CAAS,EAAT,EAAa,EAAb;AAAT,eADN,CAAN;AAEA;AAxBR;;AA2BA,cAAI0D,QAAQ,IAAIC,GAAhB,EAAqB;AACjBE,YAAAA,GAAG,CAACQ,IAAJ,CAAS,MAAM;AACX,kBAAIV,GAAJ,EAAS;AACLC,gBAAAA,CAAC,CAAC1C,IAAF,CAAOoD,cAAP,CAAsBV,CAAC,CAAC7B,IAAxB;;AACA,qBAAK,IAAIwC,CAAS,GAAGX,CAAC,CAAC1C,IAAF,CAAOsD,WAAP,CAAmBC,MAAnB,GAA4B,CAAjD,EAAoDF,CAAC,IAAI,CAAzD,EAA4DA,CAAC,EAA7D,EAAiE;AAC7D,sBAAIX,CAAC,CAAC1C,IAAF,CAAOsD,WAAP,CAAmBD,CAAnB,EAAsBG,EAAtB,IAA4Bd,CAAC,CAACxC,MAAlC,EAA0C;AACtCwC,oBAAAA,CAAC,CAAC1C,IAAF,CAAOsD,WAAP,CAAmBG,MAAnB,CAA0BJ,CAA1B,EAA6B,CAA7B;AACA;AACH;AACJ;AACJ;;AACDb,cAAAA,QAAQ;AACX,aAXD;AAYH;;AACDG,UAAAA,GAAG,CAACe,KAAJ;AACH;;AAEDC,QAAAA,WAAW,GAAG;AACV,eAAK3D,IAAL,CAAU4D,UAAV,GAAuB,KAAK1D,MAA5B;AACH;;AAtK2C,O;;;;;iBAG7B,I;;;;;;;iBAGD,I;;;;;;;iBAMeb,YAAY,CAACM,I;;;;;;;iBAMrB,I;;;;;;;iBAMc,I;;;;;;;iBAOX,K", "sourcesContent": ["/******************************************\n * <AUTHOR> <<EMAIL>>\n * @date 2019/12/9\n * @doc 列表Item组件.\n * 说明：\n *      1、此组件须配合List组件使用。（配套的配套的..）\n * @end\n ******************************************/\nconst { ccclass, property, disallowMultiple, menu, executionOrder } = _decorator;\nimport { Node, Component, Enum, Sprite, SpriteFrame, tween, _decorator, EventHandler, Tween, Button, UITransform, Vec3 } from 'cc';\nimport { DEV } from 'cc/env';\nimport List from './List';\n\nenum SelectedType {\n    NONE = 0,\n    TOGGLE = 1,\n    SWITCH = 2,\n}\n\n@ccclass\n@disallowMultiple()\n@menu('List Item')\n@executionOrder(-5001)          //先于List\nexport default class ListItem extends Component {\n    //图标\n    @property({ type: Sprite, tooltip: DEV && '图标' })\n    icon: Sprite = null;\n    //标题\n    @property({ type: Node, tooltip: DEV && '标题' })\n    title: Node = null;\n    //选择模式\n    @property({\n        type: Enum(SelectedType),\n        tooltip: DEV && '选择模式'\n    })\n    selectedMode: SelectedType = SelectedType.NONE;\n    //被选标志\n    @property({\n        type: Node, tooltip: DEV && '被选标识',\n        visible() { return this.selectedMode > SelectedType.NONE }\n    })\n    selectedFlag: Node = null;\n    //被选择的SpriteFrame\n    @property({\n        type: SpriteFrame, tooltip: DEV && '被选择的SpriteFrame',\n        visible() { return this.selectedMode == SelectedType.SWITCH }\n    })\n    selectedSpriteFrame: SpriteFrame = null;\n    //未被选择的SpriteFrame\n    _unselectedSpriteFrame: SpriteFrame = null;\n    //自适应尺寸\n    @property({\n        tooltip: DEV && '自适应尺寸（宽或高）',\n    })\n    adaptiveSize: boolean = false;\n    //选择\n    _selected: boolean = false;\n    set selected(val: boolean) {\n        this._selected = val;\n        Tween\n        if (!this.selectedFlag)\n            return;\n        switch (this.selectedMode) {\n            case SelectedType.TOGGLE:\n                this.selectedFlag.active = val;\n                break;\n            case SelectedType.SWITCH:\n                let sp: Sprite = this.selectedFlag.getComponent(Sprite);\n                if (sp) {\n                    sp.spriteFrame = val ? this.selectedSpriteFrame : this._unselectedSpriteFrame;\n                }\n                break;\n        }\n    }\n    get selected() {\n        return this._selected;\n    }\n    //按钮组件\n    private _btnCom: any;\n    get btnCom() {\n        if (!this._btnCom)\n            this._btnCom = this.node.getComponent(Button);\n        return this._btnCom;\n    }\n    //依赖的List组件\n    public list: List;\n    //是否已经注册过事件\n    private _eventReg = false;\n    //序列id\n    public listId: number;\n\n    onLoad() {\n        // //没有按钮组件的话，selectedFlag无效\n        // if (!this.btnCom)\n        //     this.selectedMode == SelectedType.NONE;\n        //有选择模式时，保存相应的东西\n        if (this.selectedMode == SelectedType.SWITCH) {\n            let com: Sprite = this.selectedFlag.getComponent(Sprite);\n            this._unselectedSpriteFrame = com.spriteFrame;\n        }\n    }\n\n    onDestroy() {\n        this.node.off(Node.EventType.SIZE_CHANGED, this._onSizeChange, this);\n    }\n\n    _registerEvent() {\n        if (!this._eventReg) {\n            if (this.btnCom && this.list.selectedMode > 0) {\n                this.btnCom.clickEvents.unshift(this.createEvt(this, 'onClickThis'));\n            }\n            if (this.adaptiveSize) {\n                this.node.on(Node.EventType.SIZE_CHANGED, this._onSizeChange, this);\n            }\n            this._eventReg = true;\n        }\n    }\n\n    _onSizeChange() {\n        this.list._onItemAdaptive(this.node);\n    }\n    /**\n     * 创建事件\n     * @param {cc.Component} component 组件脚本\n     * @param {string} handlerName 触发函数名称\n     * @param {cc.Node} node 组件所在node（不传的情况下取component.node）\n     * @returns cc.Component.EventHandler\n     */\n    createEvt(component: Component, handlerName: string, node: Node = null) {\n        if (!component.isValid)\n            return;//有些异步加载的，节点以及销毁了。\n        component['comName'] = component['comName'] || component.name.match(/\\<(.*?)\\>/g).pop().replace(/\\<|>/g, '');\n        let evt = new EventHandler();\n        evt.target = node || component.node;\n        evt.component = component['comName'];\n        evt.handler = handlerName;\n        return evt;\n    }\n\n    showAni(aniType: number, callFunc: Function, del: boolean) {\n        let t: any = this;\n        let twe: Tween<Node>;\n        let ut: UITransform = t.node.getComponent(UITransform);\n        switch (aniType) {\n            case 0: //向上消失\n                twe = tween(t.node)\n                    .to(.2, { scale: new Vec3(.7, .7) })\n                    .by(.3, { position: new Vec3(0, ut.height * 2) });\n                break;\n            case 1: //向右消失\n                twe = tween(t.node)\n                    .to(.2, { scale: new Vec3(.7, .7) })\n                    .by(.3, { position: new Vec3(ut.width * 2, 0) });\n                break;\n            case 2: //向下消失\n                twe = tween(t.node)\n                    .to(.2, { scale: new Vec3(.7, .7) })\n                    .by(.3, { position: new Vec3(0, ut.height * -2) });\n                break;\n            case 3: //向左消失\n                twe = tween(t.node)\n                    .to(.2, { scale: new Vec3(.7, .7) })\n                    .by(.3, { position: new Vec3(ut.width * -2, 0) });\n                break;\n            default: //默认：缩小消失\n                twe = tween(t.node)\n                    .to(.3, { scale: new Vec3(.1, .1) });\n                break;\n        }\n\n        if (callFunc || del) {\n            twe.call(() => {\n                if (del) {\n                    t.list._delSingleItem(t.node);\n                    for (let n: number = t.list.displayData.length - 1; n >= 0; n--) {\n                        if (t.list.displayData[n].id == t.listId) {\n                            t.list.displayData.splice(n, 1);\n                            break;\n                        }\n                    }\n                }\n                callFunc();\n            });\n        }\n        twe.start();\n    }\n\n    onClickThis() {\n        this.list.selectedId = this.listId;\n    }\n\n}\n"]}