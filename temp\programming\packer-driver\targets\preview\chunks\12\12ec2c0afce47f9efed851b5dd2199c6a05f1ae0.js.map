{"version": 3, "sources": ["file:///E:/M2Game/Client/assets/scripts/GameOld/world/level/event_group/LevelTimeEventCondition.ts"], "names": ["_decorator", "EventCondition", "LevelSystem", "ccclass", "property", "LevelTimeEventCondition", "displayName", "tooltip", "canTrigger", "world", "levelSys", "getSystem", "getCurrentLevel", "levelElapsedTime", "targetTime"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;AAASA,MAAAA,U,OAAAA,U;;AACAC,MAAAA,c,iBAAAA,c;;AACAC,MAAAA,W,iBAAAA,W;;;;;;;;;OACH;AAAEC,QAAAA,OAAF;AAAWC,QAAAA;AAAX,O,GAAwBJ,U;;yCAGjBK,uB,WADZF,OAAO,CAAC,yBAAD,C,UAGHC,QAAQ,CAAC;AAAEE,QAAAA,WAAW,EAAE,aAAf;AAA8BC,QAAAA,OAAO,EAAE;AAAvC,OAAD,C,2BAHb,MACaF,uBADb;AAAA;AAAA,4CAC4D;AAAA;AAAA;;AAAA;AAAA;;AAKjDG,QAAAA,UAAU,GAAY;AACzB,cAAI,KAAKC,KAAL,IAAc,IAAlB,EAAwB;AACpB,mBAAO,KAAP;AACH;;AACD,cAAIC,QAAQ,GAAG,KAAKD,KAAL,CAAWE,SAAX;AAAA;AAAA,yCAAf;;AACA,cAAID,QAAJ,EAAc;AAAA;;AACV,mBAAO,0BAAAA,QAAQ,CAACE,eAAT,6CAA4BC,gBAA5B,KAAgD,KAAKC,UAA5D;AACH;;AACD,iBAAO,IAAP;AACH;;AAduD,O;;;;;iBAGnC,C", "sourcesContent": ["import { _decorator, Component, Node } from 'cc'; \r\nimport { EventCondition } from '../../base/EventCondition';\r\nimport { LevelSystem } from '../LevelSystem';\r\nconst { ccclass, property } = _decorator;\r\n\r\n@ccclass('LevelTimeEventCondition')\r\nexport class LevelTimeEventCondition extends EventCondition {\r\n    \r\n    @property({ displayName: \"Target Time\", tooltip: \"Target time in ms\" })\r\n    targetTime: number = 0;\r\n    \r\n    public canTrigger(): boolean {\r\n        if (this.world == null) {\r\n            return false;\r\n        }\r\n        let levelSys = this.world.getSystem(LevelSystem);\r\n        if (levelSys) {\r\n            return levelSys.getCurrentLevel()?.levelElapsedTime >= this.targetTime;\r\n        }\r\n        return true;\r\n    }\r\n}"]}