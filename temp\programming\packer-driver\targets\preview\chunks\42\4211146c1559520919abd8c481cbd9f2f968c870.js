System.register(["__unresolved_0", "cc", "__unresolved_1", "__unresolved_2", "__unresolved_3", "__unresolved_4", "__unresolved_5", "__unresolved_6", "__unresolved_7", "__unresolved_8"], function (_export, _context) {
  "use strict";

  var _reporterNs, _cclegacy, __checkObsolete__, __checkObsoleteInNamespace__, _decorator, Sprite, Label, Vec2, Color, Tween, instantiate, tween, v2, UIOpacity, GameEnum, GameConst, Tools, GameConfig, GameIns, PfFrameAnim, BattleLayer, Entity, _dec, _dec2, _dec3, _dec4, _class, _class2, _descriptor, _descriptor2, _descriptor3, _crd, ccclass, property, PropEntity;

  function _initializerDefineProperty(target, property, descriptor, context) { if (!descriptor) return; Object.defineProperty(target, property, { enumerable: descriptor.enumerable, configurable: descriptor.configurable, writable: descriptor.writable, value: descriptor.initializer ? descriptor.initializer.call(context) : void 0 }); }

  function _applyDecoratedDescriptor(target, property, decorators, descriptor, context) { var desc = {}; Object.keys(descriptor).forEach(function (key) { desc[key] = descriptor[key]; }); desc.enumerable = !!desc.enumerable; desc.configurable = !!desc.configurable; if ('value' in desc || desc.initializer) { desc.writable = true; } desc = decorators.slice().reverse().reduce(function (desc, decorator) { return decorator(target, property, desc) || desc; }, desc); if (context && desc.initializer !== void 0) { desc.value = desc.initializer ? desc.initializer.call(context) : void 0; desc.initializer = undefined; } if (desc.initializer === void 0) { Object.defineProperty(target, property, desc); desc = null; } return desc; }

  function _initializerWarningHelper(descriptor, context) { throw new Error('Decorating class property failed. Please ensure that ' + 'transform-class-properties is enabled and runs after the decorators transform.'); }

  function V3(x, y) {
    throw new Error('Function not implemented.');
  }

  function _reportPossibleCrUseOfGameEnum(extras) {
    _reporterNs.report("GameEnum", "../../const/GameEnum", _context.meta, extras);
  }

  function _reportPossibleCrUseOfGameConst(extras) {
    _reporterNs.report("GameConst", "../../const/GameConst", _context.meta, extras);
  }

  function _reportPossibleCrUseOfTools(extras) {
    _reporterNs.report("Tools", "../../utils/Tools", _context.meta, extras);
  }

  function _reportPossibleCrUseOfGameConfig(extras) {
    _reporterNs.report("GameConfig", "../../const/GameConfig", _context.meta, extras);
  }

  function _reportPossibleCrUseOfGameIns(extras) {
    _reporterNs.report("GameIns", "../../GameIns", _context.meta, extras);
  }

  function _reportPossibleCrUseOfPfFrameAnim(extras) {
    _reporterNs.report("PfFrameAnim", "../base/PfFrameAnim", _context.meta, extras);
  }

  function _reportPossibleCrUseOfBattleLayer(extras) {
    _reporterNs.report("BattleLayer", "../layer/BattleLayer", _context.meta, extras);
  }

  function _reportPossibleCrUseOfEntity(extras) {
    _reporterNs.report("Entity", "../base/Entity", _context.meta, extras);
  }

  return {
    setters: [function (_unresolved_) {
      _reporterNs = _unresolved_;
    }, function (_cc) {
      _cclegacy = _cc.cclegacy;
      __checkObsolete__ = _cc.__checkObsolete__;
      __checkObsoleteInNamespace__ = _cc.__checkObsoleteInNamespace__;
      _decorator = _cc._decorator;
      Sprite = _cc.Sprite;
      Label = _cc.Label;
      Vec2 = _cc.Vec2;
      Color = _cc.Color;
      Tween = _cc.Tween;
      instantiate = _cc.instantiate;
      tween = _cc.tween;
      v2 = _cc.v2;
      UIOpacity = _cc.UIOpacity;
    }, function (_unresolved_2) {
      GameEnum = _unresolved_2.default;
    }, function (_unresolved_3) {
      GameConst = _unresolved_3.GameConst;
    }, function (_unresolved_4) {
      Tools = _unresolved_4.Tools;
    }, function (_unresolved_5) {
      GameConfig = _unresolved_5.default;
    }, function (_unresolved_6) {
      GameIns = _unresolved_6.GameIns;
    }, function (_unresolved_7) {
      PfFrameAnim = _unresolved_7.default;
    }, function (_unresolved_8) {
      BattleLayer = _unresolved_8.default;
    }, function (_unresolved_9) {
      Entity = _unresolved_9.default;
    }],
    execute: function () {
      _crd = true;

      _cclegacy._RF.push({}, "93fdfds8mVCG6lJEri5wkMb", "PropEntity", undefined);

      __checkObsolete__(['_decorator', 'Sprite', 'Label', 'Vec2', 'Color', 'Tween', 'instantiate', 'tween', 'Vec3', 'v2', 'UIOpacity', 'SpriteFrame']);

      ({
        ccclass,
        property
      } = _decorator);

      _export("default", PropEntity = (_dec = ccclass('PropEntity'), _dec2 = property(Sprite), _dec3 = property(Sprite), _dec4 = property(Label), _dec(_class = (_class2 = class PropEntity extends (_crd && Entity === void 0 ? (_reportPossibleCrUseOfEntity({
        error: Error()
      }), Entity) : Entity) {
        constructor() {
          super(...arguments);

          _initializerDefineProperty(this, "icon", _descriptor, this);

          _initializerDefineProperty(this, "light", _descriptor2, this);

          _initializerDefineProperty(this, "lab_num", _descriptor3, this);

          this._data = null;
          this._param = [];
          this._count = 0;
          this._frameAnim = null;
          this._bAttract = false;
          this._moveSpeed = 0;
          this._moveDir = Vec2.ZERO;
          this._freeAcd = -800;
          this._attractAcd = 2000;
          this._actEndCall = null;
          this._autoAttract = false;
        }

        init(data, count, param, actEndCall) {
          if (param === void 0) {
            param = [];
          }

          if (actEndCall === void 0) {
            actEndCall = null;
          }

          this._data = data;
          this._count = count;
          this._param = param;
          this._autoAttract = false;
          this._actEndCall = actEndCall;
          this._bAttract = false;
          this._moveSpeed = 0;
          this._moveDir = Vec2.ZERO;
          this.node.setScale(1, 1);
          this.lab_num.node.active = false;

          this._initSelf();
        }

        setAppearParam(speed, angle, freeAcd) {
          this._moveSpeed = speed;
          this._moveDir = new Vec2(0, -1).rotate(angle * (Math.PI / 180));
          this._freeAcd = freeAcd;
        }

        updateGameLogic(dt) {
          if (this._moveSpeed > 0) {
            if (this._bAttract) {
              this._moveSpeed += this._attractAcd * dt;
            } else {
              this._moveSpeed += this._freeAcd * dt;

              if (this._moveSpeed < 0) {
                this._moveSpeed = 0;

                if (this._actEndCall) {
                  this._actEndCall(this);

                  this._actEndCall = null;
                }
              }
            }

            var pos = this.node.position;
            var pos2 = v2(pos.x, pos.y).add(this._moveDir.multiplyScalar(this._moveSpeed * dt));
            this.node.setPosition(pos2.x, pos2.y);
          }
        }

        stopSpread() {
          this._moveSpeed = 0;

          if (this._actEndCall) {
            this._actEndCall(this);

            this._actEndCall = null;
          }
        }

        _initSelf() {
          this.icon.spriteFrame = null;
          this.light.spriteFrame = null;
          Tween.stopAllByTarget(this.light.node);
          var zIndex = 0;

          if (this.mainType === (_crd && GameEnum === void 0 ? (_reportPossibleCrUseOfGameEnum({
            error: Error()
          }), GameEnum) : GameEnum).ItemType.Coin) {
            zIndex = this.node.parent === (_crd && BattleLayer === void 0 ? (_reportPossibleCrUseOfBattleLayer({
              error: Error()
            }), BattleLayer) : BattleLayer).me.enemyPlaneLayer ? 10 : 0;
            this.lab_num.node.getComponent(UIOpacity).opacity = 0;

            if (!this._frameAnim) {
              var frameAnimNode = instantiate((_crd && GameConst === void 0 ? (_reportPossibleCrUseOfGameConst({
                error: Error()
              }), GameConst) : GameConst).frameAnim);
              this.node.addChild(frameAnimNode);
              this._frameAnim = frameAnimNode.getComponent(_crd && PfFrameAnim === void 0 ? (_reportPossibleCrUseOfPfFrameAnim({
                error: Error()
              }), PfFrameAnim) : PfFrameAnim);

              this._frameAnim.init((_crd && GameConst === void 0 ? (_reportPossibleCrUseOfGameConst({
                error: Error()
              }), GameConst) : GameConst).CommonAtlas, 'ag_', 6, 2 * (_crd && GameConfig === void 0 ? (_reportPossibleCrUseOfGameConfig({
                error: Error()
              }), GameConfig) : GameConfig).ActionFrameTime);
            }

            this._frameAnim.reset();
          } else {
            zIndex = 1;
            var count = Math.round(this._count);

            if (this.mainType !== (_crd && GameEnum === void 0 ? (_reportPossibleCrUseOfGameEnum({
              error: Error()
            }), GameEnum) : GameEnum).ItemType.Coin && count > 1) {
              this.lab_num.string = 'x' + count.toString();
              this.lab_num.node.getComponent(UIOpacity).opacity = 255;
            }

            if (this._frameAnim) {
              this._frameAnim.stop();
            }

            if (this.mainType === (_crd && GameEnum === void 0 ? (_reportPossibleCrUseOfGameEnum({
              error: Error()
            }), GameEnum) : GameEnum).ItemType.Box) {
              this.icon.spriteFrame = (_crd && GameConst === void 0 ? (_reportPossibleCrUseOfGameConst({
                error: Error()
              }), GameConst) : GameConst).CommonAtlas.getSpriteFrame((_crd && GameIns === void 0 ? (_reportPossibleCrUseOfGameIns({
                error: Error()
              }), GameIns) : GameIns).itemManager.getItemRecord(this._data.id).image);
            } else {
              this.icon.spriteFrame = (_crd && GameIns === void 0 ? (_reportPossibleCrUseOfGameIns({
                error: Error()
              }), GameIns) : GameIns).itemManager.getIcon(this._data.id);
              this.icon.node.setScale(0.65, 0.65);

              if ((_crd && GameIns === void 0 ? (_reportPossibleCrUseOfGameIns({
                error: Error()
              }), GameIns) : GameIns).itemManager.isEquip(this._data.id)) {
                this.icon.node.setScale(0.5, 0.5);
                zIndex = 2;
              }

              if (!this.light.spriteFrame) {
                this.light.spriteFrame = (_crd && GameIns === void 0 ? (_reportPossibleCrUseOfGameIns({
                  error: Error()
                }), GameIns) : GameIns).loadManager.getImage('light6', 'itemImage');
              }

              this.icon.node.setScale(0.75, 0.75);

              if (this.light.spriteFrame) {
                tween(this.light.node).repeatForever(new Tween().by(1, {
                  angle: -60
                })).start();
              }
            }
          }

          if (this.node.getSiblingIndex() !== zIndex) {
            this.node.setSiblingIndex(zIndex);
          }
        }

        _playShowAnim(callback) {
          if (callback === void 0) {
            callback = null;
          }

          var actionFrameTime = (_crd && GameConfig === void 0 ? (_reportPossibleCrUseOfGameConfig({
            error: Error()
          }), GameConfig) : GameConfig).ActionFrameTime;
          var position = this.node.position;
          var randomVec1 = position.x < 0 ? (_crd && Tools === void 0 ? (_reportPossibleCrUseOfTools({
            error: Error()
          }), Tools) : Tools).getRandomVec2(new Vec2(-40, 0), new Vec2(1, 20)) : (_crd && Tools === void 0 ? (_reportPossibleCrUseOfTools({
            error: Error()
          }), Tools) : Tools).getRandomVec2(new Vec2(0, 40), new Vec2(1, 20));
          var randomVec2 = position.x < 0 ? (_crd && Tools === void 0 ? (_reportPossibleCrUseOfTools({
            error: Error()
          }), Tools) : Tools).getRandomVec2(new Vec2(-100, -50), new Vec2(25, 60)) : (_crd && Tools === void 0 ? (_reportPossibleCrUseOfTools({
            error: Error()
          }), Tools) : Tools).getRandomVec2(new Vec2(50, 100), new Vec2(25, 60));
          var randomVec3 = new Vec2(randomVec2.x, (_crd && Tools === void 0 ? (_reportPossibleCrUseOfTools({
            error: Error()
          }), Tools) : Tools).random_int(-10, 10));
          tween(this.node).to(2 * actionFrameTime, {
            position: position.add(V3(randomVec1.x, randomVec1.y))
          }).to(3 * actionFrameTime, {
            position: position.add(V3(randomVec2.x, randomVec2.y))
          }).to(4 * actionFrameTime, {
            position: position.add(V3(randomVec3.x, randomVec3.y))
          }).call(() => {
            if (callback) callback(this);
          }).start();
        }

        remove() {
          Tween.stopAllByTarget(this.node);
        }

        _getLightColor() {
          var color = Color.WHITE;

          switch (this._data.itemtype) {
            case (_crd && GameEnum === void 0 ? (_reportPossibleCrUseOfGameEnum({
              error: Error()
            }), GameEnum) : GameEnum).ItemType.Box:
              switch (this._data.type) {
                case 1:
                  color = new Color(100, 243, 92);
                  break;

                case 2:
                  color = new Color(19, 192, 236);
                  break;

                case 3:
                  color = new Color(238, 227, 32);
                  break;
              }

              break;

            case (_crd && GameEnum === void 0 ? (_reportPossibleCrUseOfGameEnum({
              error: Error()
            }), GameEnum) : GameEnum).ItemType.Fragment:
              color = new Color(255, 255, 0);
              break;

            case (_crd && GameEnum === void 0 ? (_reportPossibleCrUseOfGameEnum({
              error: Error()
            }), GameEnum) : GameEnum).ItemType.Upgrade:
              color = new Color(0, 255, 255);
              break;
          }

          return color;
        }

        get itemId() {
          return this.mainType !== (_crd && GameEnum === void 0 ? (_reportPossibleCrUseOfGameEnum({
            error: Error()
          }), GameEnum) : GameEnum).ItemType.Box ? this._data.id : this._param[0];
        }

        get mainType() {
          return this._data.itemtype;
        }

        get subType() {
          return this._data.type;
        }

        get count() {
          return this._count;
        }

        get autoAttract() {
          return this._autoAttract;
        }

        set autoAttract(value) {
          this._autoAttract = value;
        }

        get bAttract() {
          return this._bAttract;
        }

        set bAttract(value) {
          this._bAttract = value;
        }

        set moveDir(value) {
          this._moveDir = value;
        }

        set moveSpeed(value) {
          this._moveSpeed = value;
        }

        set scale(value) {
          if (this.node.scale > value) {
            this.node.setScale(value, value);
          }
        }

      }, (_descriptor = _applyDecoratedDescriptor(_class2.prototype, "icon", [_dec2], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: null
      }), _descriptor2 = _applyDecoratedDescriptor(_class2.prototype, "light", [_dec3], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: null
      }), _descriptor3 = _applyDecoratedDescriptor(_class2.prototype, "lab_num", [_dec4], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: null
      })), _class2)) || _class));

      _cclegacy._RF.pop();

      _crd = false;
    }
  };
});
//# sourceMappingURL=4211146c1559520919abd8c481cbd9f2f968c870.js.map