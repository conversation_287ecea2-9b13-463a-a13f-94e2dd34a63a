{"version": 3, "sources": ["file:///E:/M2Game/Client/assets/scripts/Game/ui/plane/Plane.ts"], "names": ["_decorator", "Entity", "ccclass", "property", "Plane", "enemy", "isDead"], "mappings": ";;;;;;;;;;;;;;;;AAASA,MAAAA,U,OAAAA,U;;AACFC,MAAAA,M;;;;;;;;;OAED;AAAEC,QAAAA,OAAF;AAAWC,QAAAA;AAAX,O,GAAwBH,U;;yBAGTI,K,WADpBF,OAAO,CAAC,OAAD,C,gBAAR,MACqBE,KADrB;AAAA;AAAA,4BAC0C;AAAA;AAAA;AAAA,eAEtCC,KAFsC,GAE9B,IAF8B;AAExB;AAFwB,eAGtCC,MAHsC,GAG7B,KAH6B;AAAA,UAGtB;;;AAHsB,O", "sourcesContent": ["import { _decorator } from 'cc';\r\nimport Entity from '../base/Entity';\r\n\r\nconst { ccclass, property } = _decorator;\r\n\r\n@ccclass('Plane')\r\nexport default class Plane extends Entity {\r\n\r\n    enemy = true; // 是否为敌机\r\n    isDead = false; // 是否死亡\r\n}"]}