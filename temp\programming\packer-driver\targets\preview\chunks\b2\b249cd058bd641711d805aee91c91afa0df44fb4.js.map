{"version": 3, "sources": ["file:///E:/M2Game/Client/assets/scripts/ui/main/BottomUI.ts"], "names": ["_decorator", "Layout", "tween", "UITransform", "v3", "ButtonPlus", "BaseUI", "<PERSON><PERSON><PERSON><PERSON>", "UIMgr", "BattleUI", "PlaneUI", "ShopUI", "SkyIslandUI", "TalentUI", "ccclass", "property", "BottomUI", "_moduleBtns", "_ModuleUI", "_clickIndex", "_originSize", "getUrl", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "onLoad", "moduleBtns", "bottomLayer", "node", "getComponentsInChildren", "i", "length", "element", "addClick", "onClick", "push", "getComponent", "contentSize", "updateClickState", "updateLayout", "immediate", "for<PERSON>ach", "btn", "isSelected", "targetScale", "setScale", "to", "scale", "easing", "start", "scheduleOnce", "v", "ui", "get", "hideUI", "event", "console", "log", "target", "name", "index", "findIndex", "openUI", "onShow", "onHide", "onClose", "update", "dt"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAASA,MAAAA,U,OAAAA,U;AAAwBC,MAAAA,M,OAAAA,M;AAAcC,MAAAA,K,OAAAA,K;AAAOC,MAAAA,W,OAAAA,W;AAAaC,MAAAA,E,OAAAA,E;;AAE1DC,MAAAA,U,iBAAAA,U;;AACAC,MAAAA,M,iBAAAA,M;AAAQC,MAAAA,O,iBAAAA,O;AAASC,MAAAA,K,iBAAAA,K;;AACjBC,MAAAA,Q,iBAAAA,Q;;AACAC,MAAAA,O,iBAAAA,O;;AACAC,MAAAA,M,iBAAAA,M;;AACAC,MAAAA,W,iBAAAA,W;;AACAC,MAAAA,Q,iBAAAA,Q;;;;;;;;;OAEH;AAAEC,QAAAA,OAAF;AAAWC,QAAAA;AAAX,O,GAAwBf,U;;0BAGjBgB,Q,WADZF,OAAO,CAAC,UAAD,C,UAIHC,QAAQ,CAACd,MAAD,C,2BAJb,MACae,QADb;AAAA;AAAA,4BACqC;AAAA;AAAA;;AAAA;;AAAA,eAMzBC,WANyB,GAMG,EANH;AAAA,eAOzBC,SAPyB,GAON;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,yCAPM;AAAA,eAQzBC,WARyB,GAQH,CARG;AAAA,eASzBC,WATyB;AAAA;;AACb,eAANC,MAAM,GAAW;AAAE,iBAAO,kBAAP;AAA4B;;AACvC,eAARC,QAAQ,GAAY;AAAE,iBAAO;AAAA;AAAA,kCAAQC,OAAf;AAAwB;;AASlDC,QAAAA,MAAM,GAAS;AACrB,cAAMC,UAAU,GAAG,KAAKC,WAAL,CAAiBC,IAAjB,CAAsBC,uBAAtB;AAAA;AAAA,uCAAnB;;AACA,eAAK,IAAIC,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAGJ,UAAU,CAACK,MAA/B,EAAuCD,CAAC,EAAxC,EAA4C;AACxC,gBAAME,OAAO,GAAGN,UAAU,CAACI,CAAD,CAA1B;AACAE,YAAAA,OAAO,CAACC,QAAR,CAAiB,KAAKC,OAAtB,EAA+B,IAA/B;;AACA,iBAAKhB,WAAL,CAAiBiB,IAAjB,CAAsBH,OAAtB;AACH;;AACD,eAAKX,WAAL,GAAmB,KAAKH,WAAL,CAAiB,CAAjB,EAAoBU,IAApB,CAAyBQ,YAAzB,CAAsChC,WAAtC,EAAmDiC,WAAtE;AACA,eAAKC,gBAAL;AACH;;AAEDC,QAAAA,YAAY,CAACC,SAAD,EAAoB;AAAA,cAAnBA,SAAmB;AAAnBA,YAAAA,SAAmB,GAAP,KAAO;AAAA;;AAC5B,eAAKtB,WAAL,CAAiBuB,OAAjB,CAAyB,CAACC,GAAD,EAAMZ,CAAN,KAAY;AACjC,gBAAMa,UAAU,GAAGb,CAAC,KAAK,KAAKV,WAA9B;AACA,gBAAMwB,WAAW,GAAGD,UAAU,GAAG,GAAH,GAAS,GAAvC;;AACA,gBAAIH,SAAJ,EAAe;AACXE,cAAAA,GAAG,CAACd,IAAJ,CAASiB,QAAT,CAAkBxC,EAAE,CAACuC,WAAD,EAAcA,WAAd,EAA2B,CAA3B,CAApB;AACH,aAFD,MAEO;AACHzC,cAAAA,KAAK,CAACuC,GAAG,CAACd,IAAL,CAAL,CACKkB,EADL,CACQ,EADR,EAEQ;AAAEC,gBAAAA,KAAK,EAAE1C,EAAE,CAACuC,WAAD,EAAcA,WAAd,EAA2B,CAA3B;AAAX,eAFR,EAGQ;AAAEI,gBAAAA,MAAM,EAAE;AAAV,eAHR,EAKKC,KALL;AAMH;AACJ,WAbD,EAD4B,CAgB5B;;;AACA,eAAKC,YAAL,CAAkB,MAAM;AAAA;;AACpB,sCAAKvB,WAAL,+BAAkBY,YAAlB;AACH,WAFD,EAEGC,SAAS,GAAG,CAAH,GAAO,EAFnB;AAGH;;AAGDF,QAAAA,gBAAgB,GAAG;AACf,eAAKnB,SAAL,CAAesB,OAAf,CAAuB,CAACU,CAAD,EAAIrB,CAAJ,KAAU;AAC7B,gBAAIA,CAAC,IAAI,KAAKV,WAAd,EAA2B;AAC3B,gBAAMgC,EAAE,GAAG;AAAA;AAAA,gCAAMC,GAAN,CAAUF,CAAV,CAAX;AACA,gBAAIC,EAAJ,EAAQ;AAAA;AAAA,gCAAME,MAAN,CAAaH,CAAb;AACX,WAJD;;AAKA,eAAKZ,YAAL,CAAkB,IAAlB;AACH;;AAEKL,QAAAA,OAAO,CAACqB,KAAD,EAAoB;AAAA;;AAAA;AAC7BC,YAAAA,OAAO,CAACC,GAAR,CAAY,SAAZ,EAAuBF,KAAK,CAACG,MAAN,CAAaC,IAApC;;AACA,gBAAMC,KAAK,GAAG,KAAI,CAAC1C,WAAL,CAAiB2C,SAAjB,CAA2BV,CAAC,IAAI;AAC1C,qBAAOA,CAAC,CAACvB,IAAF,KAAW2B,KAAK,CAACG,MAAxB;AACH,aAFa,CAAd;;AAGA,gBAAIE,KAAK,IAAI,KAAI,CAACxC,WAAlB,EAA+B;AAC/B,YAAA,KAAI,CAACA,WAAL,GAAmBwC,KAAnB;AACA,kBAAM;AAAA;AAAA,gCAAME,MAAN,CAAa,KAAI,CAAC3C,SAAL,CAAeyC,KAAf,CAAb,CAAN;;AACA,YAAA,KAAI,CAACtB,gBAAL;AAR6B;AAShC;;AAEKyB,QAAAA,MAAM,GAAgC;AAAA;AAC3C;;AACKC,QAAAA,MAAM,GAAgC;AAAA;AAC3C;;AACKC,QAAAA,OAAO,GAAgC;AAAA;AAC5C;;AACSC,QAAAA,MAAM,CAACC,EAAD,EAAmB,CAClC;;AAxEgC,O", "sourcesContent": ["import { _decorator, EventTouch, Layout, math, tween, UITransform, v3 } from 'cc';\n\nimport { ButtonPlus } from '../components/common/button/ButtonPlus';\nimport { BaseUI, UILayer, UIMgr } from '../UIMgr';\nimport { BattleUI } from './BattleUI';\nimport { PlaneUI } from './plane/PlaneUI';\nimport { ShopUI } from './ShopUI';\nimport { SkyIslandUI } from './SkyIslandUI';\nimport { TalentUI } from './TalentUI';\n\nconst { ccclass, property } = _decorator;\n\n@ccclass('BottomUI')\nexport class BottomUI extends BaseUI {\n    public static getUrl(): string { return \"ui/main/BottomUI\"; }\n    public static getLayer(): UILayer { return UILayer.Default }\n    @property(Layout)\n    bottomLayer: Layout;\n\n    private _moduleBtns: ButtonPlus[] = [];\n    private _ModuleUI: any[] = [ShopUI, TalentUI, BattleUI, PlaneUI, SkyIslandUI];\n    private _clickIndex: number = 2;\n    private _originSize: math.Size\n\n    protected onLoad(): void {\n        const moduleBtns = this.bottomLayer.node.getComponentsInChildren(ButtonPlus);\n        for (let i = 0; i < moduleBtns.length; i++) {\n            const element = moduleBtns[i];\n            element.addClick(this.onClick, this)\n            this._moduleBtns.push(element);\n        }\n        this._originSize = this._moduleBtns[0].node.getComponent(UITransform).contentSize\n        this.updateClickState();\n    }\n\n    updateLayout(immediate = false) {\n        this._moduleBtns.forEach((btn, i) => {\n            const isSelected = i === this._clickIndex;\n            const targetScale = isSelected ? 1.2 : 0.8;\n            if (immediate) {\n                btn.node.setScale(v3(targetScale, targetScale, 1));\n            } else {\n                tween(btn.node)\n                    .to(.3,\n                        { scale: v3(targetScale, targetScale, 1) },\n                        { easing: 'sineOut' }\n                    )\n                    .start();\n            }\n        });\n\n        // 延迟更新Layout以确保动画完成后重新计算位置\n        this.scheduleOnce(() => {\n            this.bottomLayer?.updateLayout();\n        }, immediate ? 0 : .3);\n    }\n\n\n    updateClickState() {\n        this._ModuleUI.forEach((v, i) => {\n            if (i == this._clickIndex) return;\n            const ui = UIMgr.get(v);\n            if (ui) UIMgr.hideUI(v);\n        })\n        this.updateLayout(true)\n    }\n\n    async onClick(event: EventTouch) {\n        console.log(\"onClick\", event.target.name);\n        const index = this._moduleBtns.findIndex(v => {\n            return v.node === event.target;\n        })\n        if (index == this._clickIndex) return;\n        this._clickIndex = index;\n        await UIMgr.openUI(this._ModuleUI[index]);\n        this.updateClickState();\n    }\n\n    async onShow(...args: any[]): Promise<void> {\n    }\n    async onHide(...args: any[]): Promise<void> {\n    }\n    async onClose(...args: any[]): Promise<void> {\n    }\n    protected update(dt: number): void {\n    }\n\n}\n\n"]}