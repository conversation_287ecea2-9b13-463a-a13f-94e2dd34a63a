{"version": 3, "sources": ["file:///E:/M2Game/Client/assets/scripts/ui/main/plane/components/back_pack/Tabs.ts"], "names": ["_decorator", "Component", "Sprite", "SpriteFrame", "logDebug", "logError", "ButtonPlus", "TabStatus", "ccclass", "property", "Tabs", "_tabStatus", "Bag", "_notifyFunc", "onLoad", "tabBagBtn", "addClick", "onClick", "tabMergeBtn", "init", "notifyFunc", "setTabStatus", "node", "event", "btnNode", "target", "getComponent", "spriteFrame", "pressSpriteFrame", "releaseSpriteFrame", "<PERSON><PERSON>", "name"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAASA,MAAAA,U,OAAAA,U;AAAYC,MAAAA,S,OAAAA,S;AAA6BC,MAAAA,M,OAAAA,M;AAAQC,MAAAA,W,OAAAA,W;;AACjDC,MAAAA,Q,iBAAAA,Q;AAAUC,MAAAA,Q,iBAAAA,Q;;AACVC,MAAAA,U,iBAAAA,U;;AACAC,MAAAA,S,iBAAAA,S;;;;;;;;;OACH;AAAEC,QAAAA,OAAF;AAAWC,QAAAA;AAAX,O,GAAwBT,U;;sBAGjBU,I,WADZF,OAAO,CAAC,MAAD,C,UAEHC,QAAQ;AAAA;AAAA,mC,UAERA,QAAQ;AAAA;AAAA,mC,UAGRA,QAAQ,CAACN,WAAD,C,UAERM,QAAQ,CAACN,WAAD,C,2BATb,MACaO,IADb,SAC0BT,SAD1B,CACoC;AAAA;AAAA;;AAAA;;AAAA;;AAAA;;AAAA;;AAAA,eAWxBU,UAXwB,GAWA;AAAA;AAAA,sCAAUC,GAXV;AAAA,eAYxBC,WAZwB,GAYsB,IAZtB;AAAA;;AActBC,QAAAA,MAAM,GAAS;AACrB,eAAKC,SAAL,CAAeC,QAAf,CAAwB,KAAKC,OAA7B,EAAsC,IAAtC;AACA,eAAKC,WAAL,CAAiBF,QAAjB,CAA0B,KAAKC,OAA/B,EAAwC,IAAxC;AACH;;AAEDE,QAAAA,IAAI,CAACC,UAAD,EAA6C;AAC7C,eAAKP,WAAL,GAAmBO,UAAnB;AACA,eAAKC,YAAL,CAAkB,KAAKN,SAAL,CAAeO,IAAjC;AACH;;AAEOL,QAAAA,OAAO,CAACM,KAAD,EAA0B;AACrC,cAAMC,OAAO,GAAGD,KAAK,CAACE,MAAtB;AACA,eAAKJ,YAAL,CAAkBG,OAAlB;AACA;AAAA;AAAA,oCAAS,SAAT,oBAAoC,KAAKb,UAAzC;;AACA,eAAKE,WAAL,CAAiB,KAAKF,UAAtB;AACH;;AAEOU,QAAAA,YAAY,CAACG,OAAD,EAAgB;AAChC,kBAAQA,OAAR;AACI,iBAAK,KAAKT,SAAL,CAAeO,IAApB;AACI,mBAAKX,UAAL,GAAkB;AAAA;AAAA,0CAAUC,GAA5B;AACA,mBAAKG,SAAL,CAAeW,YAAf,CAA4BxB,MAA5B,EAAoCyB,WAApC,GAAkD,KAAKC,gBAAvD;AACA,mBAAKV,WAAL,CAAiBQ,YAAjB,CAA8BxB,MAA9B,EAAsCyB,WAAtC,GAAoD,KAAKE,kBAAzD;AACA;;AACJ,iBAAK,KAAKX,WAAL,CAAiBI,IAAtB;AACI,mBAAKX,UAAL,GAAkB;AAAA;AAAA,0CAAUmB,KAA5B;AACA,mBAAKZ,WAAL,CAAiBQ,YAAjB,CAA8BxB,MAA9B,EAAsCyB,WAAtC,GAAoD,KAAKC,gBAAzD;AACA,mBAAKb,SAAL,CAAeW,YAAf,CAA4BxB,MAA5B,EAAoCyB,WAApC,GAAkD,KAAKE,kBAAvD;AACA;;AACJ;AACI;AAAA;AAAA,wCAAS,SAAT,+BAA+CL,OAAO,CAACO,IAAvD;AACA;AAbR;AAeH;;AA/C+B,O;;;;;iBAER,I;;;;;;;iBAEE,I;;;;;;;iBAGM,I;;;;;;;iBAEE,I", "sourcesContent": ["import { _decorator, Component, EventTouch, Node, Sprite, SpriteFrame } from 'cc';\nimport { logDebug, logError } from 'db://assets/scripts/Utils/Logger';\nimport { ButtonPlus } from '../../../../components/common/button/ButtonPlus';\nimport { TabStatus } from '../../PlaneTypes';\nconst { ccclass, property } = _decorator;\n\n@ccclass('Tabs')\nexport class Tabs extends Component {\n    @property(ButtonPlus)\n    tabBagBtn: ButtonPlus = null!;\n    @property(ButtonPlus)\n    tabMergeBtn: ButtonPlus = null!;\n\n    @property(SpriteFrame)\n    pressSpriteFrame: SpriteFrame = null!;\n    @property(SpriteFrame)\n    releaseSpriteFrame: SpriteFrame = null!;\n\n    private _tabStatus: TabStatus = TabStatus.Bag;\n    private _notifyFunc: (tabStatus: TabStatus) => void = null!;\n\n    protected onLoad(): void {\n        this.tabBagBtn.addClick(this.onClick, this);\n        this.tabMergeBtn.addClick(this.onClick, this);\n    }\n\n    init(notifyFunc: (tabStatus: TabStatus) => void) {\n        this._notifyFunc = notifyFunc;\n        this.setTabStatus(this.tabBagBtn.node)\n    }\n\n    private onClick(event: EventTouch): void {\n        const btnNode = event.target as Node;\n        this.setTabStatus(btnNode);\n        logDebug(\"PlaneUI\", `Tabs onclick ${this._tabStatus}`);\n        this._notifyFunc(this._tabStatus);\n    }\n\n    private setTabStatus(btnNode: Node) {\n        switch (btnNode) {\n            case this.tabBagBtn.node:\n                this._tabStatus = TabStatus.Bag;\n                this.tabBagBtn.getComponent(Sprite).spriteFrame = this.pressSpriteFrame;\n                this.tabMergeBtn.getComponent(Sprite).spriteFrame = this.releaseSpriteFrame;\n                break;\n            case this.tabMergeBtn.node:\n                this._tabStatus = TabStatus.Merge;\n                this.tabMergeBtn.getComponent(Sprite).spriteFrame = this.pressSpriteFrame;\n                this.tabBagBtn.getComponent(Sprite).spriteFrame = this.releaseSpriteFrame;\n                break;\n            default:\n                logError(\"PlaneUI\", `Tabs setTabStatus error ${btnNode.name}`)\n                break;\n        }\n    }\n}"]}