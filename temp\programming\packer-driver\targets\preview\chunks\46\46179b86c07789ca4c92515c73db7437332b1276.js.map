{"version": 3, "sources": ["file:///E:/M2Game/Client/assets/scripts/Game/ui/bulletDanmu/GridScreen.ts"], "names": [], "mappings": "AAAA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AAEA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "sourcesContent": ["// const { ccclass, property } = cc._decorator;\r\n\r\n// @ccclass\r\n// export default class GridScreen extends BaseScreen {\r\n//     private props: {\r\n//         bulletNum: number;\r\n//         beginAngle: number;\r\n//         endAngle: number;\r\n//         posOffset: cc.Vec2;\r\n//     };\r\n//     private diffDegrees: number | null = null;\r\n\r\n//     /**\r\n//      * 初始化网格屏幕\r\n//      * @param config 配置数据\r\n//      * @param mainEntity 主实体\r\n//      */\r\n//     constructor(config: any, mainEntity: any) {\r\n//         super();\r\n//         this.setData(config, mainEntity);\r\n\r\n//         const params = this.m_config.para;\r\n//         const bulletNum = params[0];\r\n//         const beginAngle = params[1];\r\n//         const endAngle = params[2];\r\n//         const posOffset = this.m_config.offset;\r\n\r\n//         this.props = {\r\n//             bulletNum,\r\n//             beginAngle,\r\n//             endAngle,\r\n//             posOffset,\r\n//         };\r\n\r\n//         this.diffDegrees = null;\r\n//     }\r\n\r\n//     /**\r\n//      * 每帧更新逻辑\r\n//      * @param deltaTime 时间增量\r\n//      */\r\n//     update(deltaTime: number): void {\r\n//         // 可根据需求实现更新逻辑\r\n//     }\r\n\r\n//     /**\r\n//      * 初始化\r\n//      */\r\n//     onInit(): void {\r\n//         // 可根据需求实现初始化逻辑\r\n//     }\r\n\r\n//     /**\r\n//      * 发射子弹\r\n//      */\r\n//     fire(): void {\r\n//         let bulletNum = this.props.bulletNum;\r\n\r\n//         // 如果计数为奇数，减少一个子弹\r\n//         if (this.m_count % 2 !== 0) {\r\n//             bulletNum--;\r\n//         }\r\n\r\n//         const attackPoint = this.getAttackPoint();\r\n//         const x = attackPoint.x;\r\n//         const y = attackPoint.y;\r\n\r\n//         if (bulletNum === 1) {\r\n//             // 单发子弹\r\n//             const bullet = this.createBullet();\r\n//             let angle = (this.props.beginAngle + this.props.endAngle) / 2;\r\n//             angle -= 90;\r\n\r\n//             if (bullet) {\r\n//                 bullet.init(\r\n//                     this.m_enemy,\r\n//                     { x, y, angle },\r\n//                     this.m_bulletState,\r\n//                     this.m_mainEntity\r\n//                 );\r\n//             }\r\n//         } else if (bulletNum > 0) {\r\n//             // 多发子弹\r\n//             if (this.diffDegrees === null) {\r\n//                 this.diffDegrees = (this.props.endAngle - this.props.beginAngle) / (bulletNum - 1);\r\n//             }\r\n\r\n//             if (bulletNum % 2 === 0) {\r\n//                 // 偶数子弹\r\n//                 for (let i = 0; i < bulletNum; i++) {\r\n//                     const bullet = this.createBullet();\r\n//                     let angle = this.props.beginAngle + this.diffDegrees * i;\r\n//                     angle -= 90;\r\n\r\n//                     if (bullet) {\r\n//                         bullet.init(\r\n//                             this.m_enemy,\r\n//                             { x, y, angle },\r\n//                             this.m_bulletState,\r\n//                             this.m_mainEntity\r\n//                         );\r\n//                     }\r\n//                 }\r\n//             } else {\r\n//                 // 奇数子弹\r\n//                 let offset = 0;\r\n//                 for (let i = 0; i < bulletNum; i++) {\r\n//                     const bullet = this.createBullet();\r\n//                     let angle =\r\n//                         (this.props.beginAngle + this.props.endAngle) / 2 +\r\n//                         Math.pow(-1, i + 1) * this.diffDegrees * offset;\r\n//                     angle -= 90;\r\n\r\n//                     if (bullet) {\r\n//                         bullet.init(\r\n//                             this.m_enemy,\r\n//                             { x, y, angle },\r\n//                             this.m_bulletState,\r\n//                             this.m_mainEntity\r\n//                         );\r\n//                     }\r\n\r\n//                     if (i % 2 === 0) {\r\n//                         offset++;\r\n//                     }\r\n//                 }\r\n//             }\r\n//         }\r\n//     }\r\n// }"]}