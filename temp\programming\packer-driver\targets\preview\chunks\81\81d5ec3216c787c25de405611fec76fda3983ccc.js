System.register(["__unresolved_0", "cc", "__unresolved_1", "__unresolved_2", "__unresolved_3", "__unresolved_4", "__unresolved_5"], function (_export, _context) {
  "use strict";

  var _reporterNs, _cclegacy, __checkObsolete__, __checkObsoleteInNamespace__, v2, GameConst, GameIns, Tools, BaseComp, ColliderComp, BulletFly, _crd;

  function _reportPossibleCrUseOfGameConst(extras) {
    _reporterNs.report("GameConst", "../../const/GameConst", _context.meta, extras);
  }

  function _reportPossibleCrUseOfGameIns(extras) {
    _reporterNs.report("GameIns", "../../GameIns", _context.meta, extras);
  }

  function _reportPossibleCrUseOfTools(extras) {
    _reporterNs.report("Tools", "../../utils/Tools", _context.meta, extras);
  }

  function _reportPossibleCrUseOfBaseComp(extras) {
    _reporterNs.report("BaseComp", "../base/BaseComp", _context.meta, extras);
  }

  function _reportPossibleCrUseOfColliderComp(extras) {
    _reporterNs.report("ColliderComp", "../base/ColliderComp", _context.meta, extras);
  }

  _export("default", void 0);

  return {
    setters: [function (_unresolved_) {
      _reporterNs = _unresolved_;
    }, function (_cc) {
      _cclegacy = _cc.cclegacy;
      __checkObsolete__ = _cc.__checkObsolete__;
      __checkObsoleteInNamespace__ = _cc.__checkObsoleteInNamespace__;
      v2 = _cc.v2;
    }, function (_unresolved_2) {
      GameConst = _unresolved_2.GameConst;
    }, function (_unresolved_3) {
      GameIns = _unresolved_3.GameIns;
    }, function (_unresolved_4) {
      Tools = _unresolved_4.Tools;
    }, function (_unresolved_5) {
      BaseComp = _unresolved_5.default;
    }, function (_unresolved_6) {
      ColliderComp = _unresolved_6.ColliderComp;
    }],
    execute: function () {
      _crd = true;

      _cclegacy._RF.push({}, "76f65HtEd5CdYWe9asFZnaM", "BulletFly", undefined);

      __checkObsolete__(['v2']);

      _export("default", BulletFly = class BulletFly extends (_crd && BaseComp === void 0 ? (_reportPossibleCrUseOfBaseComp({
        error: Error()
      }), BaseComp) : BaseComp) {
        // Y方向速度
        constructor(props) {
          super();
          this.m_aim = false;
          // 是否为瞄准子弹
          this.m_aimWait = 0;
          // 瞄准等待时间
          this.m_isEnemy = false;
          // 是否为敌方子弹
          this.m_target = null;
          // 目标实体
          this.m_targetCollider = null;
          // 目标碰撞组件
          this.props = void 0;
          // 子弹配置参数
          this.m_beginTime = 0;
          // 子弹开始时间
          this.m_changeTime = 0;
          // 子弹加速时间
          this.m_waittime = 0;
          // 子弹等待时间
          this.m_idx = 0;
          // 当前等待时间索引
          this.m_speed = 0;
          // 初始速度
          this.m_mainEntity = void 0;
          // 发射子弹的实体
          this.m_angle = 0;
          // 子弹角度
          this.m_speedX = 0;
          // X方向速度
          this.m_speedY = 0;
          this.props = props; // 子弹配置参数

          this.m_aim = props.bustyle === 58; // 判断是否为瞄准子弹

          this.m_aimWait = this.m_aim ? props.para[2] : 0; // 设置瞄准等待时间
        }
        /**
         * 初始化组件
         */


        onInit() {
          this.m_beginTime = 0; // 子弹开始时间

          this.m_changeTime = 0; // 子弹加速时间

          this.m_waittime = 0; // 子弹等待时间

          this.m_idx = 0; // 当前等待时间索引

          this.m_speed = this.props.initialve + Math.random() * this.props.spdiff; // 初始速度
        }
        /**
         * 设置子弹数据
         * @param {number} angle 子弹角度
         * @param {Entity} mainEntity 发射子弹的实体
         * @param {boolean} isEnemy 是否为敌方子弹
         */


        setData(angle, mainEntity, isEnemy) {
          if (isEnemy === void 0) {
            isEnemy = true;
          }

          this.m_isEnemy = isEnemy;
          this.m_idx = 0;
          this.m_mainEntity = mainEntity;
          this.m_angle = angle;
          this.refreshSpeed(this.m_speed);

          if (this.props.waittime.length > 0) {
            this.m_waittime = this.props.waittime[this.m_idx];
          }
        }
        /**
         * 计算子弹速度
         * @param {number} deltaTime 帧间隔时间
         */


        calculateSpeed(deltaTime) {
          if (this.m_beginTime >= this.props.time && this.m_changeTime < this.props.accnumber) {
            this.m_changeTime += deltaTime;
            var newSpeed = this.m_speed + this.m_changeTime * this.props.acc;
            this.refreshSpeed(newSpeed);
          }
        }
        /**
         * 刷新子弹速度
         * @param {number} speed 子弹速度
         */


        refreshSpeed(speed) {
          var radian = -this.m_angle / 180 * Math.PI;
          this.m_speedX = Math.sin(radian) * speed;
          this.m_speedY = Math.cos(radian) * speed;
        }
        /**
         * 更新子弹逻辑
         * @param {number} deltaTime 帧间隔时间
         */


        update(deltaTime) {
          if (!(_crd && GameConst === void 0 ? (_reportPossibleCrUseOfGameConst({
            error: Error()
          }), GameConst) : GameConst).GameAble) {
            return;
          }

          if (deltaTime > 0.2) {
            deltaTime = 0.016666666666667; // 限制最大帧间隔时间
          }

          if (this.m_waittime > 0) {
            this.m_waittime -= deltaTime;
          } else {
            if (this.m_idx < this.props.waittime.length) {
              this.m_idx++;
              this.m_waittime = this.props.waittime[this.m_idx];
              return;
            }

            if (this.m_aimWait > 0) {
              this.m_aimWait -= deltaTime;

              if (this.m_aim) {
                this.m_angle = -this.getAngle();
                var adjustedAngle = this.m_isEnemy ? this.m_angle + 180 : this.m_angle;
                this.m_entity.node.angle = adjustedAngle;

                if (this.m_aimWait <= 0) {
                  this.refreshSpeed(this.m_speed);
                }
              }
            } else {
              this.m_beginTime += deltaTime;
              this.calculateSpeed(deltaTime);
              var layerSpeed = 0;

              if (this.m_mainEntity) {
                layerSpeed = (_crd && GameIns === void 0 ? (_reportPossibleCrUseOfGameIns({
                  error: Error()
                }), GameIns) : GameIns).sceneManager.getLayerSpeed(this.m_mainEntity);
              }

              var posx = this.m_entity.node.position.x + this.m_speedX * deltaTime;
              var posy = this.m_entity.node.position.y + this.m_speedY * deltaTime - layerSpeed * deltaTime;
              this.m_entity.node.setPosition(posx, posy);
            }
          }
        }
        /**
         * 获取子弹的角度
         * @returns {number} 子弹的角度
         */


        getAngle() {
          if (this.m_isEnemy) {
            this.getTarget();
          }

          if (!this.m_target) {
            return 0;
          }

          var position = this.m_entity.node.position;

          if (this.m_isEnemy) {
            var mainPlaneNode = (_crd && GameIns === void 0 ? (_reportPossibleCrUseOfGameIns({
              error: Error()
            }), GameIns) : GameIns).planeManager.mainPlane.node;
            return mainPlaneNode ? (_crd && Tools === void 0 ? (_reportPossibleCrUseOfTools({
              error: Error()
            }), Tools) : Tools).getAngle(position, v2(mainPlaneNode.position.x, mainPlaneNode.position.y)) : 0;
          } else {
            var targetPosition = this.m_targetCollider.getScreenPos();
            return (_crd && Tools === void 0 ? (_reportPossibleCrUseOfTools({
              error: Error()
            }), Tools) : Tools).getAngle(position, targetPosition);
          }
        }
        /**
         * 获取子弹的目标
         */


        getTarget() {
          if (!this.m_target) {
            if (this.m_isEnemy) {
              this.m_target = (_crd && GameIns === void 0 ? (_reportPossibleCrUseOfGameIns({
                error: Error()
              }), GameIns) : GameIns).planeManager.mainPlane;
              this.m_targetCollider = (_crd && GameIns === void 0 ? (_reportPossibleCrUseOfGameIns({
                error: Error()
              }), GameIns) : GameIns).planeManager.mainPlane.getComp(_crd && ColliderComp === void 0 ? (_reportPossibleCrUseOfColliderComp({
                error: Error()
              }), ColliderComp) : ColliderComp);
            } else {
              this.m_targetCollider = (_crd && GameIns === void 0 ? (_reportPossibleCrUseOfGameIns({
                error: Error()
              }), GameIns) : GameIns).planeManager.getRandomTargetEnemy();
              this.m_target = this.m_targetCollider.entity;
            }
          }
        }

      });

      _cclegacy._RF.pop();

      _crd = false;
    }
  };
});
//# sourceMappingURL=81d5ec3216c787c25de405611fec76fda3983ccc.js.map