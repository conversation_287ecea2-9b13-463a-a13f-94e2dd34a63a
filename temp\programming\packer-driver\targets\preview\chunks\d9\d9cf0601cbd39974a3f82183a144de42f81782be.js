System.register(["__unresolved_0", "cc", "__unresolved_1", "__unresolved_2"], function (_export, _context) {
  "use strict";

  var _reporterNs, _cclegacy, __checkObsolete__, __checkObsoleteInNamespace__, _decorator, BaseComp, GameConst, _dec, _class, _crd, ccclass, ScaleComp;

  function _reportPossibleCrUseOfBaseComp(extras) {
    _reporterNs.report("BaseComp", "./BaseComp", _context.meta, extras);
  }

  function _reportPossibleCrUseOfGameConst(extras) {
    _reporterNs.report("GameConst", "../../const/GameConst", _context.meta, extras);
  }

  return {
    setters: [function (_unresolved_) {
      _reporterNs = _unresolved_;
    }, function (_cc) {
      _cclegacy = _cc.cclegacy;
      __checkObsolete__ = _cc.__checkObsolete__;
      __checkObsoleteInNamespace__ = _cc.__checkObsoleteInNamespace__;
      _decorator = _cc._decorator;
    }, function (_unresolved_2) {
      BaseComp = _unresolved_2.default;
    }, function (_unresolved_3) {
      GameConst = _unresolved_3.GameConst;
    }],
    execute: function () {
      _crd = true;

      _cclegacy._RF.push({}, "482a0uk+IRISr6hP6A6suHK", "ScaleComp", undefined);

      __checkObsolete__(['_decorator', 'Component']);

      ({
        ccclass
      } = _decorator);

      _export("default", ScaleComp = (_dec = ccclass('ScaleComp'), _dec(_class = class ScaleComp extends (_crd && BaseComp === void 0 ? (_reportPossibleCrUseOfBaseComp({
        error: Error()
      }), BaseComp) : BaseComp) {
        constructor() {
          super(...arguments);
          this.m_initScale = 1;
          this.m_endScale = 1;
          this.m_scaleSpeed = 1;
          this.m_curScale = 1;
          this.m_tag = 0;
          this.m_able = false;
          this.m_collide = null;
          this.m_width = 0;
          this.m_height = 0;
          this.m_nodeScale = 1;
        }

        onInit() {// 初始化逻辑
        }

        setData(data, collide) {
          this.m_initScale = data.para[4] || 1;
          this.m_endScale = data.para[5] || 1;
          this.m_scaleSpeed = data.para[6] || 1;
          this.m_curScale = this.m_initScale;
          this.m_tag = this.m_initScale > this.m_endScale ? 0 : 1;
          this.m_scaleSpeed = this.m_tag === 0 ? -Math.abs(this.m_scaleSpeed) : Math.abs(this.m_scaleSpeed);
          this.m_able = true;
          this.m_collide = collide;
          this.m_width = collide.data.width;
          this.m_height = collide.data.height;
          this.m_nodeScale = this.entity.node.scale;
          this.entity.node.setScale(this.m_nodeScale * this.m_initScale, this.m_nodeScale * this.m_initScale);
          this.entity.node.opacity = 0;
          this.entity.scheduleOnce(() => {
            this.entity.node.opacity = 255;
          });
        }

        update(deltaTime) {
          if ((_crd && GameConst === void 0 ? (_reportPossibleCrUseOfGameConst({
            error: Error()
          }), GameConst) : GameConst).GameAble && this.m_able) {
            var newScale = this.m_curScale + this.m_scaleSpeed * (deltaTime > 0.2 ? 0.016666666666667 : deltaTime);

            if (this.m_tag === 0) {
              if (newScale < this.m_endScale) {
                newScale = this.m_endScale;
                this.m_able = false;
              }
            } else {
              if (newScale > this.m_endScale) {
                newScale = this.m_endScale;
                this.m_able = false;
              }
            }

            this.m_curScale = newScale;
            this.entity.node.setScale(newScale * this.m_nodeScale, newScale * this.m_nodeScale);
            var scaleRatio = this.entity.node.scale / this.m_nodeScale;
            this.m_collide.setSize(this.m_width * scaleRatio, this.m_height * scaleRatio);
          }
        }

      }) || _class));

      _cclegacy._RF.pop();

      _crd = false;
    }
  };
});
//# sourceMappingURL=d9cf0601cbd39974a3f82183a144de42f81782be.js.map