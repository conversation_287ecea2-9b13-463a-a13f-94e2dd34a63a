System.register(["__unresolved_0", "cc", "__unresolved_1", "__unresolved_2", "__unresolved_3"], function (_export, _context) {
  "use strict";

  var _reporterNs, _cclegacy, __checkObsolete__, __checkObsoleteInNamespace__, _decorator, EventAction, Message, ChangeBgSpeedMsg, _dec, _dec2, _class, _class2, _descriptor, _crd, ccclass, property, ChangeBackgroundSpeedAction;

  function _initializerDefineProperty(target, property, descriptor, context) { if (!descriptor) return; Object.defineProperty(target, property, { enumerable: descriptor.enumerable, configurable: descriptor.configurable, writable: descriptor.writable, value: descriptor.initializer ? descriptor.initializer.call(context) : void 0 }); }

  function _applyDecoratedDescriptor(target, property, decorators, descriptor, context) { var desc = {}; Object.keys(descriptor).forEach(function (key) { desc[key] = descriptor[key]; }); desc.enumerable = !!desc.enumerable; desc.configurable = !!desc.configurable; if ('value' in desc || desc.initializer) { desc.writable = true; } desc = decorators.slice().reverse().reduce(function (desc, decorator) { return decorator(target, property, desc) || desc; }, desc); if (context && desc.initializer !== void 0) { desc.value = desc.initializer ? desc.initializer.call(context) : void 0; desc.initializer = undefined; } if (desc.initializer === void 0) { Object.defineProperty(target, property, desc); desc = null; } return desc; }

  function _initializerWarningHelper(descriptor, context) { throw new Error('Decorating class property failed. Please ensure that ' + 'transform-class-properties is enabled and runs after the decorators transform.'); }

  function _reportPossibleCrUseOfEventAction(extras) {
    _reporterNs.report("EventAction", "../../base/EventAction", _context.meta, extras);
  }

  function _reportPossibleCrUseOfMessage(extras) {
    _reporterNs.report("Message", "../../base/Messaging", _context.meta, extras);
  }

  function _reportPossibleCrUseOfChangeBgSpeedMsg(extras) {
    _reporterNs.report("ChangeBgSpeedMsg", "../bg_layer/Background", _context.meta, extras);
  }

  return {
    setters: [function (_unresolved_) {
      _reporterNs = _unresolved_;
    }, function (_cc) {
      _cclegacy = _cc.cclegacy;
      __checkObsolete__ = _cc.__checkObsolete__;
      __checkObsoleteInNamespace__ = _cc.__checkObsoleteInNamespace__;
      _decorator = _cc._decorator;
    }, function (_unresolved_2) {
      EventAction = _unresolved_2.EventAction;
    }, function (_unresolved_3) {
      Message = _unresolved_3.Message;
    }, function (_unresolved_4) {
      ChangeBgSpeedMsg = _unresolved_4.ChangeBgSpeedMsg;
    }],
    execute: function () {
      _crd = true;

      _cclegacy._RF.push({}, "e33d4Nc52hEHY6CCuUiErKM", "ChangeBackgroundSpeedAction", undefined);

      __checkObsolete__(['_decorator', 'Sprite', 'Node']);

      ({
        ccclass,
        property
      } = _decorator);

      _export("ChangeBackgroundSpeedAction", ChangeBackgroundSpeedAction = (_dec = ccclass('ChangeBackgroundSpeedAction'), _dec2 = property({
        displayName: "Speed Modifier",
        tooltip: "Speed modifier for background"
      }), _dec(_class = (_class2 = class ChangeBackgroundSpeedAction extends (_crd && EventAction === void 0 ? (_reportPossibleCrUseOfEventAction({
        error: Error()
      }), EventAction) : EventAction) {
        constructor() {
          super(...arguments);

          _initializerDefineProperty(this, "speed", _descriptor, this);
        }

        execute() {
          (_crd && Message === void 0 ? (_reportPossibleCrUseOfMessage({
            error: Error()
          }), Message) : Message).emit(_crd && ChangeBgSpeedMsg === void 0 ? (_reportPossibleCrUseOfChangeBgSpeedMsg({
            error: Error()
          }), ChangeBgSpeedMsg) : ChangeBgSpeedMsg, {
            speedModifier: 2.0
          });
        }

      }, (_descriptor = _applyDecoratedDescriptor(_class2.prototype, "speed", [_dec2], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function initializer() {
          return 1.0;
        }
      })), _class2)) || _class));

      _cclegacy._RF.pop();

      _crd = false;
    }
  };
});
//# sourceMappingURL=b083de1af2ad701f26f524c256c9aa1a7ce7d933.js.map