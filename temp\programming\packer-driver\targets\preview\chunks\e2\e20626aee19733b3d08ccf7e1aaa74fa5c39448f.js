System.register(["__unresolved_0", "cc", "__unresolved_1", "__unresolved_2", "__unresolved_3", "__unresolved_4", "__unresolved_5", "__unresolved_6", "__unresolved_7", "__unresolved_8", "__unresolved_9", "__unresolved_10"], function (_export, _context) {
  "use strict";

  var _reporterNs, _cclegacy, __checkObsolete__, __checkObsoleteInNamespace__, _decorator, Component, Node, Sprite, tween, UITransform, v2, view, GameMapData, MapItemData, GameIns, ResourceList, Tools, ExchangeMap, ImageSequence, GameConst, GameEnum, NodeMove, _dec, _dec2, _dec3, _class, _class2, _descriptor, _descriptor2, _class3, _crd, ccclass, property, GameMapRun;

  function _initializerDefineProperty(target, property, descriptor, context) { if (!descriptor) return; Object.defineProperty(target, property, { enumerable: descriptor.enumerable, configurable: descriptor.configurable, writable: descriptor.writable, value: descriptor.initializer ? descriptor.initializer.call(context) : void 0 }); }

  function _applyDecoratedDescriptor(target, property, decorators, descriptor, context) { var desc = {}; Object.keys(descriptor).forEach(function (key) { desc[key] = descriptor[key]; }); desc.enumerable = !!desc.enumerable; desc.configurable = !!desc.configurable; if ('value' in desc || desc.initializer) { desc.writable = true; } desc = decorators.slice().reverse().reduce(function (desc, decorator) { return decorator(target, property, desc) || desc; }, desc); if (context && desc.initializer !== void 0) { desc.value = desc.initializer ? desc.initializer.call(context) : void 0; desc.initializer = undefined; } if (desc.initializer === void 0) { Object.defineProperty(target, property, desc); desc = null; } return desc; }

  function _initializerWarningHelper(descriptor, context) { throw new Error('Decorating class property failed. Please ensure that ' + 'transform-class-properties is enabled and runs after the decorators transform.'); }

  function _reportPossibleCrUseOfGameMapData(extras) {
    _reporterNs.report("GameMapData", "../../data/GameMapData", _context.meta, extras);
  }

  function _reportPossibleCrUseOfMapItemData(extras) {
    _reporterNs.report("MapItemData", "../../data/MapItemData", _context.meta, extras);
  }

  function _reportPossibleCrUseOfGameIns(extras) {
    _reporterNs.report("GameIns", "../../GameIns", _context.meta, extras);
  }

  function _reportPossibleCrUseOfResourceList(extras) {
    _reporterNs.report("ResourceList", "../../const/ResourceList", _context.meta, extras);
  }

  function _reportPossibleCrUseOfTools(extras) {
    _reporterNs.report("Tools", "../../utils/Tools", _context.meta, extras);
  }

  function _reportPossibleCrUseOfExchangeMap(extras) {
    _reporterNs.report("ExchangeMap", "../base/ExchangeMap", _context.meta, extras);
  }

  function _reportPossibleCrUseOfImageSequence(extras) {
    _reporterNs.report("ImageSequence", "../base/ImageSequence", _context.meta, extras);
  }

  function _reportPossibleCrUseOfGameConst(extras) {
    _reporterNs.report("GameConst", "../../const/GameConst", _context.meta, extras);
  }

  function _reportPossibleCrUseOfGameEnum(extras) {
    _reporterNs.report("GameEnum", "../../const/GameEnum", _context.meta, extras);
  }

  function _reportPossibleCrUseOfNodeMove(extras) {
    _reporterNs.report("NodeMove", "../base/NodeMove", _context.meta, extras);
  }

  return {
    setters: [function (_unresolved_) {
      _reporterNs = _unresolved_;
    }, function (_cc) {
      _cclegacy = _cc.cclegacy;
      __checkObsolete__ = _cc.__checkObsolete__;
      __checkObsoleteInNamespace__ = _cc.__checkObsoleteInNamespace__;
      _decorator = _cc._decorator;
      Component = _cc.Component;
      Node = _cc.Node;
      Sprite = _cc.Sprite;
      tween = _cc.tween;
      UITransform = _cc.UITransform;
      v2 = _cc.v2;
      view = _cc.view;
    }, function (_unresolved_2) {
      GameMapData = _unresolved_2.default;
    }, function (_unresolved_3) {
      MapItemData = _unresolved_3.default;
    }, function (_unresolved_4) {
      GameIns = _unresolved_4.GameIns;
    }, function (_unresolved_5) {
      ResourceList = _unresolved_5.default;
    }, function (_unresolved_6) {
      Tools = _unresolved_6.Tools;
    }, function (_unresolved_7) {
      ExchangeMap = _unresolved_7.default;
    }, function (_unresolved_8) {
      ImageSequence = _unresolved_8.default;
    }, function (_unresolved_9) {
      GameConst = _unresolved_9.GameConst;
    }, function (_unresolved_10) {
      GameEnum = _unresolved_10.default;
    }, function (_unresolved_11) {
      NodeMove = _unresolved_11.default;
    }],
    execute: function () {
      _crd = true;

      _cclegacy._RF.push({}, "879aeDtOuZIk7CiSj2Twdv6", "GameMapRun", undefined);

      __checkObsolete__(['_decorator', 'Canvas', 'Component', 'Node', 'Sprite', 'SpriteFrame', 'tween', 'UITransform', 'v2', 'view']);

      ({
        ccclass,
        property
      } = _decorator);

      _export("default", GameMapRun = (_dec = ccclass('GameMapRun'), _dec2 = property(Node), _dec3 = property(Node), _dec(_class = (_class2 = (_class3 = class GameMapRun extends Component {
        constructor() {
          super(...arguments);

          _initializerDefineProperty(this, "floorLayerParent", _descriptor, this);

          _initializerDefineProperty(this, "skyLayerParent", _descriptor2, this);

          this.floorDataSprite = [];
          this.skyDataSprite = [];
          this.floorSpeed = [];
          this.startY = 0;
          this.floorLayer = [];
          this.skyLayer = [];
          this.skyLinkYDis = [];
          this.skySpeed = [];
          this.inMapItem = [];
          this.skyNodeMove = [];
          this.skyNodeAngle = [];
          this.hideImg = [];
          this.imageSqueDataSprite = [];
          this.imageSqueSpeed = [];
          this.imageSqueLayer = [];
          this.imageSquePos = [];
          this.randomNum = null;
          this.LayerData = new Map();
          this.LayerParents = new Map();
          this.allFloorLayers = [];
          this.allSkyLayers = [];
          this.allSqueLayers = [];
          this.loadedNode = new Map();
          this.itemDatas = [];
          this.changeMapSpeedRatio = 1;
          this.changeSkySpeedRatio = 1;
          this.posOffIndex = 0;
          this.initOver = false;
          this.loadedAtlas = new Map();
          this.maskOver = true;
          this.lightingPoor = [];
          this.thunders = new Map();
          this.nowThunder = 0;
          this.isPlayThunder = false;
          this.mapCanRun = true;
          this.isInitParkour = false;
          this.totalHeight = 0;
          this._lineArr = [];
          this.delayInit = 40;
          this.delay = 40;
          this.m_paokuRun = false;
          this._loadFinish = false;
          this._loadTotal = 0;
          this._loadCount = 0;
          this.canParkoutRun = false;
          this.frist = true;
          this.viewHeight = view.getVisibleSize().height;
          this.canHideImg = true;
        }

        get loadFinish() {
          return this._loadFinish;
        }

        onLoad() {
          GameMapRun.instance = this;
        }

        checkLoadFinish() {
          this._loadCount++;

          if (this._loadCount >= this._loadTotal) {
            this.getAllMapLayer();
            this.getAllSkyLayer();
            this.getAllSqueLayer();
            this.getAllBuildAndTurret();
            this.initMapAndSky();
            this._loadFinish = true;
          }

          (_crd && GameIns === void 0 ? (_reportPossibleCrUseOfGameIns({
            error: Error()
          }), GameIns) : GameIns).battleManager.addLoadingPercent(5 / this._loadTotal);
        }

        update(deltaTime) {
          if (!(_crd && GameConst === void 0 ? (_reportPossibleCrUseOfGameConst({
            error: Error()
          }), GameConst) : GameConst).GameAble) {
            return;
          }

          if (deltaTime > 0.2) {
            deltaTime = 0.016666666666667;
          }

          var gameState = (_crd && GameIns === void 0 ? (_reportPossibleCrUseOfGameIns({
            error: Error()
          }), GameIns) : GameIns).gameRuleManager.gameState;

          if (gameState !== (_crd && GameEnum === void 0 ? (_reportPossibleCrUseOfGameEnum({
            error: Error()
          }), GameEnum) : GameEnum).GameState.Battle && gameState !== (_crd && GameEnum === void 0 ? (_reportPossibleCrUseOfGameEnum({
            error: Error()
          }), GameEnum) : GameEnum).GameState.Sortie && gameState !== (_crd && GameEnum === void 0 ? (_reportPossibleCrUseOfGameEnum({
            error: Error()
          }), GameEnum) : GameEnum).GameState.Ready && gameState !== (_crd && GameEnum === void 0 ? (_reportPossibleCrUseOfGameEnum({
            error: Error()
          }), GameEnum) : GameEnum).GameState.WillOver && gameState !== (_crd && GameEnum === void 0 ? (_reportPossibleCrUseOfGameEnum({
            error: Error()
          }), GameEnum) : GameEnum).GameState.Idle && gameState !== (_crd && GameEnum === void 0 ? (_reportPossibleCrUseOfGameEnum({
            error: Error()
          }), GameEnum) : GameEnum).GameState.Over) {
            return;
          }

          if (!this.mapCanRun) {
            return;
          }

          this.allFloorLayers.forEach(layer => {
            this.mapLayer1Run(deltaTime, layer);
          });
          this.allSkyLayers.forEach(layer => {
            this.skyLayer1Run(deltaTime, layer);
          }); // this.allSqueLayers.forEach((layer) => {
          //     this.imageSqueRun(deltaTime, layer);
          // });
          // if (this.LayerData.get(888) && this.m_paokuRun) {
          //     this.parkourLayerRun(deltaTime);
          //     this.updateSpeedLine(deltaTime);
          // }
        } // clear() {
        //     // 清理加载的资源
        //     this.LayerData.forEach((data, key) => {
        //         data.clear();
        //     });
        //     this.LayerData.clear();
        //     this.LayerParents.clear();
        //     this.itemDatas = [];
        //     this.allFloorLayers = [];
        //     this.allSkyLayers = [];
        //     this.allSqueLayers = [];
        // }


        paokuRun(enable) {
          this.m_paokuRun = enable;
        } // loadNode(key: string, node: Node) {
        //     // this.loadedNode.set(key, node);
        // }
        // changeLevel(level: number, subLevel: number) {
        //     this.initData(level, subLevel);
        // }


        initData(level, subLevel) {
          this._loadFinish = false;
          this._loadTotal = 0;
          this._loadCount = 0;
          this.reset();
          this.posOffIndex = 0;
          this.LayerParents.set(0, this.floorLayerParent);
          this.LayerParents.set(1, this.skyLayerParent);
          var randomIndex = 0;
          this._loadTotal++;
          (_crd && GameIns === void 0 ? (_reportPossibleCrUseOfGameIns({
            error: Error()
          }), GameIns) : GameIns).beanManager.loadTableByName1((_crd && ResourceList === void 0 ? (_reportPossibleCrUseOfResourceList({
            error: Error()
          }), ResourceList) : ResourceList)["GameMap_" + level], table => {
            var ids = table.getAllID();
            var index = 0;

            do {
              var record = table.getRecorder(ids[randomIndex]);

              if (subLevel === record.level) {
                var totalRules = record.total_rules;

                if (this.randomNum === null) {
                  this.randomNum = Math.floor(Math.random() * (totalRules - 1) + 1) + index;
                }

                if (record.id === this.randomNum) {
                  this.floorDataSprite = record.floor_res;
                  this.skyDataSprite = record.sky_res;
                  this.floorSpeed = record.floor_speed;
                  this.floorLayer = record.floor_layer;
                  this.skyLayer = record.sky_layer;
                  this.startY = record.start_y;
                  this.skyLinkYDis = record.link_y_distance;
                  this.skySpeed = record.sky_speed;
                  this.inMapItem = record.in_map_item;
                  this.skyNodeMove = record.skyNode_move;
                  this.skyNodeAngle = record.sky_angle;
                  this.hideImg = record.hide_img;
                  this.imageSqueDataSprite = record.imageSque_res;
                  this.imageSqueSpeed = record.imageSque_speed;
                  this.imageSqueLayer = record.imageSque_layer;
                  this.imageSquePos = record.imageSque_pos;
                  break;
                }
              } else {
                index = randomIndex + 1;
              }
            } while (++randomIndex < ids.length);

            this.atlasManage();
            this.checkLoadFinish();
          }, () => {// GameFunc.wxLoadErr();
          });
        }

        getAllBuildAndTurret() {
          for (var i = 0; i < this.inMapItem.length; i++) {
            var itemData = this.inMapItem[i].split("!");
            var mapItem = new (_crd && MapItemData === void 0 ? (_reportPossibleCrUseOfMapItemData({
              error: Error()
            }), MapItemData) : MapItemData)(Number(itemData[0]), Number(itemData[1]), Number(itemData[2]), Number(itemData[3]), Number(itemData[4]), Number(itemData[5]), Number(itemData[6]), Number(itemData[7]), Number(itemData[8]), Number(itemData[9]), itemData[10]);
            this.itemDatas[i] = mapItem;
          } // Sort items by creation position


          for (var _i = 0; _i < this.itemDatas.length - 1; _i++) {
            for (var j = 0; j < this.itemDatas.length - 1 - _i; j++) {
              if (this.itemDatas[j].createPos > this.itemDatas[j + 1].createPos) {
                var temp = this.itemDatas[j];
                this.itemDatas[j] = this.itemDatas[j + 1];
                this.itemDatas[j + 1] = temp;
              }
            }
          }
        }

        getAllSqueLayer() {
          for (var i = 0; i < this.imageSqueLayer.length; i++) {
            var layer = Number(this.imageSqueLayer[i]);

            if (!(_crd && Tools === void 0 ? (_reportPossibleCrUseOfTools({
              error: Error()
            }), Tools) : Tools).arrContain(this.allSqueLayers, layer)) {
              this.allSqueLayers.push(layer);
            }
          } // Sort layers by position


          for (var _i2 = 0; _i2 < this.imageSquePos.length - 1; _i2++) {
            for (var j = 0; j < this.imageSquePos.length - 1 - _i2; j++) {
              var pos1 = Number(this.imageSquePos[j].split(",")[1]);
              var pos2 = Number(this.imageSquePos[j + 1].split(",")[1]);

              if (pos1 > pos2) {
                // Swap positions
                [this.imageSquePos[j], this.imageSquePos[j + 1]] = [this.imageSquePos[j + 1], this.imageSquePos[j]];
                [this.imageSqueDataSprite[j], this.imageSqueDataSprite[j + 1]] = [this.imageSqueDataSprite[j + 1], this.imageSqueDataSprite[j]];
                [this.imageSqueLayer[j], this.imageSqueLayer[j + 1]] = [this.imageSqueLayer[j + 1], this.imageSqueLayer[j]];
                [this.imageSqueSpeed[j], this.imageSqueSpeed[j + 1]] = [this.imageSqueSpeed[j + 1], this.imageSqueSpeed[j]];
              }
            }
          }
        }

        getAllSkyLayer() {
          for (var i = 0; i < this.skyLayer.length; i++) {
            if (!(_crd && Tools === void 0 ? (_reportPossibleCrUseOfTools({
              error: Error()
            }), Tools) : Tools).arrContain(this.allSkyLayers, this.skyLayer[i])) {
              this.allSkyLayers.push(this.skyLayer[i]);
            }
          } // Sort layers by link distance


          for (var _i3 = 0; _i3 < this.skyLinkYDis.length - 1; _i3++) {
            for (var j = 0; j < this.skyLinkYDis.length - 1 - _i3; j++) {
              var pos1 = Number(this.skyLinkYDis[j].split(",")[1]);
              var pos2 = Number(this.skyLinkYDis[j + 1].split(",")[1]);

              if (pos1 > pos2) {
                // Swap positions
                [this.skyLinkYDis[j], this.skyLinkYDis[j + 1]] = [this.skyLinkYDis[j + 1], this.skyLinkYDis[j]];
                [this.skyDataSprite[j], this.skyDataSprite[j + 1]] = [this.skyDataSprite[j + 1], this.skyDataSprite[j]];
                [this.skyLayer[j], this.skyLayer[j + 1]] = [this.skyLayer[j + 1], this.skyLayer[j]];
                [this.skySpeed[j], this.skySpeed[j + 1]] = [this.skySpeed[j + 1], this.skySpeed[j]];
                [this.skyNodeMove[j], this.skyNodeMove[j + 1]] = [this.skyNodeMove[j + 1], this.skyNodeMove[j]];
                [this.skyNodeAngle[j], this.skyNodeAngle[j + 1]] = [this.skyNodeAngle[j + 1], this.skyNodeAngle[j]];
              }
            }
          }
        }

        getAllMapLayer() {
          for (var i = 0; i < this.floorLayer.length; i++) {
            if (!(_crd && Tools === void 0 ? (_reportPossibleCrUseOfTools({
              error: Error()
            }), Tools) : Tools).arrContain(this.allFloorLayers, this.floorLayer[i])) {
              this.allFloorLayers.push(this.floorLayer[i]);
            }
          }
        }

        atlasManage() {
          var _this = this;

          var floorAtlas = [];
          var skyAtlas = [];
          var imageSqueAtlas = []; // Collect unique atlas names for floor layers

          for (var sprite of this.floorDataSprite) {
            var atlasName = sprite.split(",")[1];

            if (!(_crd && Tools === void 0 ? (_reportPossibleCrUseOfTools({
              error: Error()
            }), Tools) : Tools).arrContain(floorAtlas, atlasName)) {
              floorAtlas.push(atlasName);
            }
          } // Collect unique atlas names for sky layers


          for (var _sprite of this.skyDataSprite) {
            var _atlasName = _sprite.split(",")[1];

            if (!(_crd && Tools === void 0 ? (_reportPossibleCrUseOfTools({
              error: Error()
            }), Tools) : Tools).arrContain(skyAtlas, _atlasName)) {
              skyAtlas.push(_atlasName);
            }
          } // Collect unique atlas names for image sequence layers


          for (var _sprite2 of this.imageSqueDataSprite) {
            var _atlasName2 = _sprite2.split(",")[1];

            if (!(_crd && Tools === void 0 ? (_reportPossibleCrUseOfTools({
              error: Error()
            }), Tools) : Tools).arrContain(imageSqueAtlas, _atlasName2)) {
              imageSqueAtlas.push(_atlasName2);
            }
          } // Release unused atlases


          this.loadedAtlas.forEach((asset, atlasName) => {
            if (!(_crd && Tools === void 0 ? (_reportPossibleCrUseOfTools({
              error: Error()
            }), Tools) : Tools).arrContain(floorAtlas, atlasName) && !(_crd && Tools === void 0 ? (_reportPossibleCrUseOfTools({
              error: Error()
            }), Tools) : Tools).arrContain(skyAtlas, atlasName) && !(_crd && Tools === void 0 ? (_reportPossibleCrUseOfTools({
              error: Error()
            }), Tools) : Tools).arrContain(imageSqueAtlas, atlasName)) {
              (_crd && GameIns === void 0 ? (_reportPossibleCrUseOfGameIns({
                error: Error()
              }), GameIns) : GameIns).loadManager.releaseAsset(asset);
              this.loadedAtlas.delete(atlasName);
            }
          }); // Load missing atlases for floor layers

          var _loop = function _loop(_atlasName3) {
            if (!_this.loadedAtlas.has(_atlasName3)) {
              _this._loadTotal++;
              (_crd && GameIns === void 0 ? (_reportPossibleCrUseOfGameIns({
                error: Error()
              }), GameIns) : GameIns).loadManager.loadAtlas1("map/" + _atlasName3, asset => {
                _this.loadedAtlas.set(_atlasName3, asset);

                _this.checkLoadFinish();
              });
            }
          };

          for (var _atlasName3 of floorAtlas) {
            _loop(_atlasName3);
          } // Load missing atlases for sky layers


          var _loop2 = function _loop2(_atlasName4) {
            if (!_this.loadedAtlas.has(_atlasName4)) {
              _this._loadTotal++;
              (_crd && GameIns === void 0 ? (_reportPossibleCrUseOfGameIns({
                error: Error()
              }), GameIns) : GameIns).loadManager.loadAtlas1("map/" + _atlasName4, asset => {
                _this.loadedAtlas.set(_atlasName4, asset);

                _this.checkLoadFinish();
              });
            }
          };

          for (var _atlasName4 of skyAtlas) {
            _loop2(_atlasName4);
          } // Load missing atlases for image sequence layers


          var _loop3 = function _loop3(_atlasName5) {
            if (!_this.loadedAtlas.has(_atlasName5)) {
              _this._loadTotal++;
              (_crd && GameIns === void 0 ? (_reportPossibleCrUseOfGameIns({
                error: Error()
              }), GameIns) : GameIns).loadManager.loadAtlas1(_atlasName5, asset => {
                _this.loadedAtlas.set(_atlasName5, asset);

                _this.checkLoadFinish();
              });
            }
          };

          for (var _atlasName5 of imageSqueAtlas) {
            _loop3(_atlasName5);
          }
        }

        initMapAndSky() {
          // 初始化地面层、天空层和队列层
          this.allFloorLayers.forEach(layer => {
            this.mapLayerinit(layer);
          });
          this.allSkyLayers.forEach(layer => {
            this.skyLayer1init(layer);
          });
          this.allSqueLayers.forEach(layer => {
            this.imageSqueinit(layer);
          }); // 设置所有层的初始位置

          this.LayerParents.forEach((parent, layer) => {
            if (layer !== 888) {
              parent.y = -1880;
            }
          });
          this.initOver = true;
        }

        mapEndChange() {
          // 处理地图结束切换
          (_crd && ExchangeMap === void 0 ? (_reportPossibleCrUseOfExchangeMap({
            error: Error()
          }), ExchangeMap) : ExchangeMap).me.endChange();
        }

        reset() {
          // 重置地图数据
          this.lightingPoor.splice(0);
          this.initOver = false;
          this.posOffIndex = 0;
          this.changeMapSpeedRatio = 1;
          this.changeMapSpeedRatio = 1;
          this.randomNum = null;
          this.itemDatas.splice(0);

          if (this.allFloorLayers.length > 0) {
            this.LayerReset(this.allFloorLayers);
          }

          if (this.allSkyLayers.length > 0) {
            this.LayerReset(this.allSkyLayers);
          }

          if (this.allSqueLayers.length > 0) {
            this.LayerReset(this.allSqueLayers);
          }

          if (this.LayerParents.get(888)) {
            this.LayerParents.get(888).destroy();
            this.LayerParents.delete(888);
            this.LayerData.get(888).clear();
            this.LayerData.delete(888);
          }

          this.allFloorLayers = [];
          this.allSkyLayers = [];
          this.allSqueLayers = [];
        }

        LayerReset(layers) {
          // 重置指定层
          layers.forEach(layer => {
            var layerData = this.LayerData.get(layer);
            var parent = this.LayerParents.get(layer);

            if (parent) {
              parent.destroyAllChildren();
              parent.y = 0;
            }

            if (layerData) {
              layerData.clear();
            }
          });
        }

        mapLayerinit(layer) {
          // 初始化地面层
          var layerData = this.LayerData.get(layer) || new (_crd && GameMapData === void 0 ? (_reportPossibleCrUseOfGameMapData({
            error: Error()
          }), GameMapData) : GameMapData)();
          layerData.ViewTop = this.viewHeight / 2;
          layerData.ViewBot = -this.viewHeight / 2;
          var spriteNames = [];
          this.floorDataSprite.forEach(sprite => {
            spriteNames.push(sprite.split(",")[0]);
          });
          layerData.loadSprite.forEach((_, key) => {
            if (!(_crd && Tools === void 0 ? (_reportPossibleCrUseOfTools({
              error: Error()
            }), Tools) : Tools).arrContain(spriteNames, key)) {
              layerData.loadSprite.delete(key);
            }
          });

          for (var i = 0; i < this.floorDataSprite.length; i++) {
            var node = new Node();
            node.addComponent(UITransform);
            var [spriteName, atlasName] = this.floorDataSprite[i].split(",");
            var spriteFrame = (_crd && GameIns === void 0 ? (_reportPossibleCrUseOfGameIns({
              error: Error()
            }), GameIns) : GameIns).loadManager.getImage(spriteName, "map/" + atlasName);
            node.addComponent(Sprite).spriteFrame = spriteFrame;
            var designWidth = 720 + 20;
            layerData.scale = designWidth / node.getComponent(UITransform).width;
            node.getComponent(UITransform).width = designWidth;
            node.getComponent(UITransform).height *= layerData.scale;

            if (i === 0) {
              node.y = Math.floor(layerData.ViewBot + node.getComponent(UITransform).height / 2);
            } else {
              node.y = layerData.tempY + Math.floor(node.getComponent(UITransform).height / 2 + layerData.tempH / 2);
            }

            layerData.tempY = node.y;
            layerData.tempH = node.getComponent(UITransform).height;

            if (this.floorLayer[i] === layer) {
              layerData.speed = this.floorSpeed[i];
              layerData.speeds = this.floorSpeed;
            }

            if (node.y - node.getComponent(UITransform).height / 2 > layerData.ViewTop) {
              break;
            }

            if (this.floorLayer[i] === layer) {
              if (this.LayerParents.get(this.floorLayer[i])) {
                node.parent = this.LayerParents.get(this.floorLayer[i]);
              } else {
                var parent = new Node();
                parent.addComponent(UITransform);
                parent.parent = this.node;
                this.LayerParents.set(this.floorLayer[i], parent);
                node.parent = parent;
              }
            }

            layerData.nowUseNode.push(node);
            layerData.loadSprite.set(spriteName, spriteFrame);
            layerData.index = i;
          }

          this.LayerData.set(layer, layerData);
        }

        skyLayer1init(layer) {
          // 初始化天空层
          var layerData = this.LayerData.get(layer) || new (_crd && GameMapData === void 0 ? (_reportPossibleCrUseOfGameMapData({
            error: Error()
          }), GameMapData) : GameMapData)();
          layerData.ViewTop = this.viewHeight / 2;
          layerData.ViewBot = -this.viewHeight / 2; // 筛选属于当前层的天空数据

          var skyLayerData = this.skyLayer.map((skyLayer, index) => ({
            skyLayer,
            index
          })).filter(_ref => {
            var {
              skyLayer
            } = _ref;
            return skyLayer === layer;
          }); // 初始化层数据

          skyLayerData.forEach(_ref2 => {
            var {
              index
            } = _ref2;
            layerData.spriteName.push(this.skyDataSprite[index]);
            layerData.PosInfo.push(this.skyLinkYDis[index]);
            layerData.speeds.push(this.skySpeed[index]);
            layerData.nodeAngle.push(this.skyNodeAngle[index] || 0);
            layerData.nodeMove.push(this.skyNodeMove[index] || "0,0");
          }); // 清理无用的精灵

          var spriteNames = layerData.spriteName.map(sprite => sprite.split(",")[0]);
          layerData.loadSprite.forEach((_, key) => {
            if (!(_crd && Tools === void 0 ? (_reportPossibleCrUseOfTools({
              error: Error()
            }), Tools) : Tools).arrContain(spriteNames, key)) {
              layerData.loadSprite.delete(key);
            }
          }); // 创建节点并初始化

          skyLayerData.forEach(_ref3 => {
            var {
              index
            } = _ref3;
            var node = new Node();
            node.addComponent(UITransform);
            var [spriteName, atlasName] = this.skyDataSprite[index].split(",");
            var spriteFrame = (_crd && GameIns === void 0 ? (_reportPossibleCrUseOfGameIns({
              error: Error()
            }), GameIns) : GameIns).loadManager.getImage(spriteName, "map/" + atlasName);
            node.addComponent(Sprite).spriteFrame = spriteFrame;
            var [x, y, scaleX, scaleY] = this.skyLinkYDis[index].split(",").map(Number);
            node.setPosition(x, y);
            node.getComponent(UITransform).width = spriteFrame.width * scaleX;
            node.getComponent(UITransform).height = spriteFrame.height * scaleY;
            node.angle = this.skyNodeAngle[index]; // 设置速度

            layerData.speed = this.skySpeed[index]; // 确保父节点存在

            if (!this.LayerParents.has(layer)) {
              var parent = new Node();
              parent.addComponent(UITransform);
              parent.parent = this.node;
              this.LayerParents.set(layer, parent);
            } // 检查是否超出视图顶部


            if (node.position.y - node.getComponent(UITransform).height / 2 > layerData.ViewTop) {
              return;
            }

            node.parent = this.LayerParents.get(layer);
            layerData.nowUseNode.push(node);
            layerData.loadSprite.set(spriteName, spriteFrame);
            layerData.index = index;
          });
          this.LayerData.set(layer, layerData);
        }

        imageSqueinit(layer) {
          // 初始化队列层
          var layerData = this.LayerData.get(layer) || new (_crd && GameMapData === void 0 ? (_reportPossibleCrUseOfGameMapData({
            error: Error()
          }), GameMapData) : GameMapData)();
          layerData.ViewTop = this.viewHeight / 2;
          layerData.ViewBot = -this.viewHeight / 2;

          for (var i = 0; i < this.imageSqueLayer.length; i++) {
            if (Number(this.imageSqueLayer[i]) === layer) {
              layerData.spriteName.push(this.imageSqueDataSprite[i]);
              layerData.PosInfo.push(this.imageSquePos[i]);
              layerData.speeds.push(Number(this.imageSqueSpeed[i]));
            }
          }

          var spriteNames = [];
          layerData.spriteName.forEach(sprite => {
            spriteNames.push(sprite.split(",")[0]);
          });
          layerData.loadImageSque.forEach((_, key) => {
            if (!(_crd && Tools === void 0 ? (_reportPossibleCrUseOfTools({
              error: Error()
            }), Tools) : Tools).arrContain(spriteNames, key)) {
              layerData.loadImageSque.delete(key);
            }
          });

          for (var _i4 = 0; _i4 < this.imageSqueDataSprite.length; _i4++) {
            if (Number(this.imageSqueLayer[_i4]) === layer) {
              var node = new Node();
              node.addComponent(UITransform);

              var [spriteName, atlasName, frameCount] = this.imageSqueDataSprite[_i4].split(",");

              var frames = [];

              for (var j = 0; j < Number(frameCount); j++) {
                var frameName = spriteName + "_" + j;
                var frame = (_crd && GameIns === void 0 ? (_reportPossibleCrUseOfGameIns({
                  error: Error()
                }), GameIns) : GameIns).loadManager.getImage(frameName, "map/" + atlasName);
                frames.push(frame);
              }

              var imageSequence = node.addComponent(_crd && ImageSequence === void 0 ? (_reportPossibleCrUseOfImageSequence({
                error: Error()
              }), ImageSequence) : ImageSequence);
              imageSequence.setData(frames, 30 / layerData.frameTime);
              imageSequence.setStart();

              var [x, y] = this.imageSquePos[_i4].split(",").map(Number);

              node.setPosition(x, y);

              if (!this.LayerParents.get(layer)) {
                var parent = new Node();
                parent.addComponent(UITransform);
                parent.parent = this.node;
                this.LayerParents.set(layer, parent);
              }

              node.parent = this.LayerParents.get(layer);
              layerData.nowUseNode.push(node);
              layerData.loadImageSque.set(spriteName, frames);
              layerData.index = _i4;
            }
          }

          this.LayerData.set(layer, layerData);
        }

        initParkourLayer() {//     if (!this.isInitParkour) {
          //         this.canParkoutRun = false;
          //         const isExpedition = _.ExpeditionMgr.isExpedition;
          //         const isLJStage = isExpedition
          //             ? m.StageMgr.isLJStageInExpedition(_.ExpeditionMgr.currentSection + 1)
          //             : m.StageMgr.isLJStage(c.default.me.subStage);
          //         if (isLJStage) {
          //             let layerData = this.LayerData.get(888) || new GameMapData();
          //             layerData.ViewTop = winSize.height / 2;
          //             layerData.ViewBot = -winSize.height / 2;
          //             let parentNode = this.LayerParents.get(888) || new Node("parkour");
          //             parentNode.y = 0;
          //             parentNode.parent = this.node;
          //             parentNode.zIndex = this.node.children.length - 3;
          //             this.LayerParents.set(888, parentNode);
          //             layerData.index = 0;
          //             layerData.itemIndex = 0;
          //             const runItemData = h.EnemyMgr._runItemData[layerData.index];
          //             for (let i = 0; i < h.EnemyMgr.getRunItemDataLength(runItemData); i++) {
          //                 const itemData = h.EnemyMgr.getRunItemData(runItemData, layerData.itemIndex);
          //                 const id = h.EnemyMgr.getidByUid(runItemData);
          //                 let height = 2560;
          //                 if (id !== null) {
          //                     height = h.EnemyMgr._runTable.getRecorder(id).height;
          //                 }
          //                 const yPos = Number(itemData.y) + layerData.index * height;
          //                 const node = h.EnemyMgr.addItem(runItemData, layerData.index, layerData.itemIndex, false);
          //                 node.parent = parentNode;
          //                 layerData.nowUseNode.push(node);
          //                 layerData.itemIndex++;
          //                 if (layerData.itemIndex === h.EnemyMgr.getRunItemDataLength(runItemData)) {
          //                     layerData.index++;
          //                     layerData.itemIndex = 0;
          //                 }
          //                 if (yPos > layerData.ViewTop) {
          //                     break;
          //                 }
          //             }
          //             layerData.speeds = [];
          //             const runTable = h.EnemyMgr._runTable;
          //             runTable.getAllID().forEach((id) => {
          //                 const speed = runTable.getRecorder(id).speed;
          //                 layerData.speeds.push(speed);
          //             });
          //             this.LayerData.set(888, layerData);
          //             this.frist = true;
          //             this._createSpeedLine();
          //             this.isInitParkour = true;
          //             this.canParkoutRun = true;
          //         }
          //     }
        } // _createSpeedLine() {
        //     if (this._speedLine) {
        //         let yOffset = 0;
        //         for (const line of this._lineArr) {
        //             line.y = yOffset;
        //             yOffset += 0.7 * line.height;
        //         }
        //     } else {
        //         this._speedLine = new Node();
        //         let yOffset = 0;
        //         for (let i = 0; i < 2; i++) {
        //             const line = new Node();
        //             this._speedLine.addChild(line);
        //             line.anchorY = 0;
        //             line.opacity = 200;
        //             line.addComponent(Sprite).spriteFrame = GameIns.loadManager.getImage("speedLine", "chapterParkour_Map");
        //             line.width = GameConst.ViewWidth;
        //             line.height *= 6;
        //             line.y = yOffset;
        //             yOffset += line.height;
        //             this._lineArr.push(line);
        //         }
        //     }
        //     let parentNode = this.node.getChildByName("speedLine");
        //     if (!parentNode) {
        //         parentNode = new Node("speedLine");
        //         parentNode.parent = this.node;
        //         parentNode.zIndex = 5;
        //     }
        //     this._speedLine.opacity = 0;
        //     this._speedLine.parent = parentNode;
        //     this._speedLine.y = -GameConst.ViewHeight;
        //     this._speedLineOpacity = true;
        //     this.delay = this.delayInit;
        // }
        // parkourLayerRun(deltaTime: number) {
        //     if (!this.canParkoutRun) {
        //         return;
        //     }
        //     const layerData = this.LayerData.get(888);
        //     const parentNode = this.LayerParents.get(888);
        //     const speed = layerData.speeds[layerData.index % layerData.speeds.length];
        //     parentNode.y -= speed * deltaTime;
        //     layerData.ViewTop += deltaTime * speed;
        //     layerData.ViewBot = layerData.ViewTop - winSize.height;
        //     if (layerData.nowUseNode.length > 0 && layerData.nowUseNode[0].y + 200 < layerData.ViewBot) {
        //         const node = layerData.nowUseNode.shift();
        //         if (!node.getComponent(f.default).isEndLine) {
        //             h.EnemyMgr.putItem(node, true);
        //         }
        //     }
        //     if (layerData.index >= h.EnemyMgr._runItemData.length && layerData.index !== 1) {
        //         if (this.frist) {
        //             const node = h.EnemyMgr.addItem(0, layerData.index, 0, true, v2(0, this.totalHeight));
        //             node.parent = parentNode;
        //             layerData.nowUseNode.push(node);
        //             this.frist = false;
        //         }
        //         const planes = h.EnemyMgr.planes;
        //         if (planes.length === 1 && planes[0].getComponent(f.default).isEndLine) {
        //             this.hideSpeedLineByDt(deltaTime);
        //         }
        //     } else {
        //         const runItemData = h.EnemyMgr._runItemData[layerData.index];
        //         if (runItemData) {
        //             for (let i = 0; i < 5; i++) {
        //                 const itemData = h.EnemyMgr.getRunItemData(runItemData, layerData.itemIndex);
        //                 const id = h.EnemyMgr.getidByUid(runItemData);
        //                 let height = 2560;
        //                 if (id !== null) {
        //                     height = h.EnemyMgr._runTable.getRecorder(id).height;
        //                 }
        //                 const yPos = Number(itemData.y) + this.totalHeight;
        //                 if (yPos < layerData.ViewTop + 200) {
        //                     const node = h.EnemyMgr.addItem(runItemData, layerData.index, layerData.itemIndex, false);
        //                     node.parent = parentNode;
        //                     layerData.nowUseNode.push(node);
        //                     layerData.itemIndex++;
        //                     if (layerData.itemIndex === h.EnemyMgr.getRunItemDataLength(runItemData)) {
        //                         layerData.index++;
        //                         layerData.itemIndex = 0;
        //                         this.totalHeight += height;
        //                     }
        //                 }
        //             }
        //             this.LayerData.set(888, layerData);
        //         }
        //     }
        // }
        // updateSpeedLine(deltaTime: number) {
        //     if (this.delay > 0) {
        //         this.delay--;
        //     }
        //     if (this.delay === 0 && this._speedLine) {
        //         this._speedLine.y -= 3000 * deltaTime;
        //         const firstLine = this._lineArr[0];
        //         if (firstLine.height + firstLine.y + this._speedLine.y < -GameConst.ViewHeight) {
        //             this._lineArr.shift();
        //             const lastLine = this._lineArr[this._lineArr.length - 1];
        //             firstLine.y = lastLine.y + lastLine.height;
        //             this._lineArr.push(firstLine);
        //         }
        //         if (this._speedLine.opacity < 255 && this._speedLineOpacity) {
        //             this._speedLine.opacity += 255 * deltaTime;
        //         }
        //     }
        // }
        // thunderPlayer(sounds: string[], delay: number, loop: number = 1) {
        //     if (loop === 1) {
        //         for (let i = 0; i < sounds.length; i++) {
        //             if (this.thunders.has(sounds[i])) {
        //                 if (this.thunders.get(sounds[i]) === false) {
        //                     this.thunders.set(sounds[i], true);
        //                     tween(this)
        //                         .delay(delay)
        //                         .call(async () => {
        //                             const soundId = await y.default.audioManager.playEffect(sounds[i]);
        //                             audioEngine.setFinishCallback(soundId, () => {
        //                                 this.thunders.set(sounds[i], false);
        //                             });
        //                         })
        //                         .start();
        //                     return;
        //                 }
        //             } else {
        //                 this.thunders.set(sounds[i], false);
        //             }
        //         }
        //     }
        // }
        // controlLighting(layerData: GameMapData) {
        //     const loopTimes = layerData.loopTimes;
        //     const nodes = layerData.nowUseNode;
        //     nodes.forEach((node, index) => {
        //         if (node.getComponent(S.default)) {
        //             const interval = layerData.getTimeInterval();
        //             node.getComponent(S.default).delayPlay(interval, loopTimes);
        //             if (index - 1 >= 0) {
        //                 this.lightningFlash(interval / 30, node, nodes[index - 1]);
        //             } else {
        //                 this.lightningFlash(interval / 30, node, null);
        //             }
        //         }
        //     });
        // }
        // lightningFlash(delay: number, targetNode: Node, previousNode: Node | null) {
        //     let lightningNode: Node;
        //     if (this.lightingPoor.length > 0) {
        //         lightningNode = this.lightingPoor.pop();
        //     } else {
        //         lightningNode = new Node();
        //         lightningNode.anchorY = 1;
        //         lightningNode.addComponent(Sprite).spriteFrame = y.default.loadManager.getImage("wuyushandian", "map2_sky1_stageWuyun");
        //     }
        //     let height = 0;
        //     let angle = 0;
        //     if (targetNode) {
        //         if (previousNode === null) {
        //             angle = targetNode.x > 0 ? v.Tools.random_int(-90, -135) : v.Tools.random_int(90, 135);
        //             height = 500;
        //         } else {
        //             height = v.Tools.getDisForVec2(targetNode.position, previousNode.position);
        //             angle = v.Tools.getAngle(targetNode.position, previousNode.position);
        //         }
        //         lightningNode.parent = targetNode.parent;
        //         lightningNode.position = targetNode.position;
        //         lightningNode.height = height;
        //         lightningNode.angle = angle;
        //         lightningNode.opacity = 0;
        //         lightningNode.zIndex = -1;
        //         tween(lightningNode)
        //             .delay(0.5 / 30)
        //             .call(() => {
        //                 lightningNode.opacity = 255;
        //                 lightningNode.scaleX = 1;
        //             })
        //             .delay(0.025)
        //             .call(() => {
        //                 lightningNode.scaleX = -1;
        //             })
        //             .delay(delay)
        //             .repeat(2, tween(lightningNode).to(4 / 30, { opacity: 0 }))
        //             .call(() => {
        //                 lightningNode.stopAllActions();
        //                 lightningNode.parent = null;
        //                 this.lightingPoor.push(lightningNode);
        //             })
        //             .start();
        //     }
        // }
        // imageSqueRun(deltaTime: number, layer: number) {
        //     const layerData = this.LayerData.get(layer);
        //     const parentNode = this.LayerParents.get(layer);
        //     const speed = layerData.speeds[layerData.index % layerData.speeds.length];
        //     layerData.speed = speed;
        //     const adjustedSpeed = speed + this.changeSkySpeedRatio;
        //     layerData.ViewTop += deltaTime * adjustedSpeed;
        //     layerData.ViewBot = layerData.ViewTop - winSize.height;
        //     parentNode.y -= deltaTime * adjustedSpeed;
        //     const nextSpriteName = layerData.spriteName[(layerData.index + 1) % layerData.spriteName.length];
        //     const [spriteName, atlasName, frameCount] = nextSpriteName.split(",");
        //     const frames: SpriteFrame[] = [];
        //     if (!layerData.loadImageSque.has(spriteName)) {
        //         for (let i = 0; i < Number(frameCount); i++) {
        //             const frame = y.default.loadManager.getImage(`${spriteName}_${i}`, atlasName);
        //             frames.push(frame);
        //         }
        //         layerData.loadImageSque.set(spriteName, frames);
        //     } else {
        //         frames.push(...layerData.loadImageSque.get(spriteName));
        //     }
        //     if (layerData.nowUseNode.length > 0 && layerData.nowUseNode[0].y + layerData.nowUseNode[0].height < layerData.ViewBot) {
        //         const node = layerData.nowUseNode.shift();
        //         if (node.getComponent(S.default)) {
        //             node.getComponent(S.default).stop();
        //         }
        //         layerData.freeNode.push(node);
        //     }
        //     const nextPos = layerData.PosInfo[(layerData.index + 1) % layerData.PosInfo.length].split(",");
        //     const x = Number(nextPos[0]);
        //     const y = Number(nextPos[1]);
        //     if (y + layerData.starPos - frames[0].getRect().height / 2 < layerData.ViewTop + 1880 + 500) {
        //         let node: Node;
        //         if (layerData.freeNode.length > 0) {
        //             node = layerData.freeNode.shift();
        //         } else {
        //             node = new Node();
        //         }
        //         const imageSequence = node.getComponent(S.default) || node.addComponent(S.default);
        //         node.width = frames[0].getRect().width;
        //         node.height = frames[0].getRect().height;
        //         node.x = x;
        //         node.y = y + layerData.starPos;
        //         node.parent = this.LayerParents.get(layer);
        //         imageSequence.setData(frames, 30 / layerData.frameTime);
        //         imageSequence.setStart();
        //         layerData.nowUseNode.push(node);
        //         layerData.index++;
        //     }
        //     if (layerData.nowTimes > layerData.getTiggerTime()) {
        //         this.controlLighting(layerData);
        //         layerData.nowTimes = 0;
        //         layerData.nowTriggerTime = 0;
        //     }
        //     if ((layerData.index + 1) % layerData.spriteName.length === 0 && layerData.index !== 0 && layerData.nowUseNode.length > 0) {
        //         const lastNode = layerData.nowUseNode[layerData.nowUseNode.length - 1];
        //         layerData.starPos = lastNode.y + lastNode.height / 2 + frames[0].getRect().height / 2;
        //     }
        //     this.createItem(layer, deltaTime);
        // }

        /**
         * 更新天空图层的运行逻辑
         * @param deltaTime 时间增量
         * @param layer 天空图层的索引
         */


        skyLayer1Run(deltaTime, layer) {
          var layerData = this.LayerData.get(layer);
          var layerParent = this.LayerParents.get(layer);

          if (!layerData || !layerParent) {
            return;
          }

          var speed = layerData.speeds[layerData.index % layerData.speeds.length];
          var adjustedSpeed = speed + this.changeSkySpeedRatio; // 更新视图范围

          layerData.ViewTop += deltaTime * adjustedSpeed;
          layerData.ViewBot = layerData.ViewTop - view.getVisibleSize().height; // 更新图层位置

          var posY1 = layerParent.position.y - deltaTime * adjustedSpeed;
          layerParent.setPosition(layerParent.position.x, posY1); // 检查隐藏图片逻辑

          this.canHideImg = this.changeSkySpeedRatio > 5;
          var nextPosInfo = layerData.PosInfo[(layerData.index + 1) % layerData.PosInfo.length].split(",");
          var posX = Number(nextPosInfo[0]);
          var posY = Number(nextPosInfo[1]);
          var scaleX = Number(nextPosInfo[2]);
          var scaleY = Math.abs(Number(nextPosInfo[3]));
          var nextSpriteInfo = layerData.spriteName[(layerData.index + 1) % layerData.spriteName.length].split(",");
          var spriteName = nextSpriteInfo[0];
          var atlasName = nextSpriteInfo[1];
          var spriteFrame = layerData.loadSprite.get(spriteName);

          if (!spriteFrame) {
            spriteFrame = (_crd && GameIns === void 0 ? (_reportPossibleCrUseOfGameIns({
              error: Error()
            }), GameIns) : GameIns).loadManager.getImage(spriteName, "map/" + atlasName);
            layerData.loadSprite.set(spriteName, spriteFrame);
          } // 更新星星位置


          if ((layerData.index + 1) % layerData.spriteName.length === 0 && layerData.index !== 0 && layerData.nowUseNode.length > 0) {
            var lastNode = layerData.nowUseNode[layerData.nowUseNode.length - 1];
            layerData.starPos = lastNode.y + lastNode.height / 2 + spriteFrame.getRect().height * scaleY / 2;
          } // 检查是否需要隐藏图片


          if (this.hideImg.indexOf(spriteName) != -1) {
            if (this.canHideImg) {
              layerData.index++;
              return;
            }

            if (posY + layerData.starPos - spriteFrame.getRect().height / 2 - 1880 < layerData.ViewTop) {
              layerData.index++;
              return;
            }
          } // 平滑调整速度


          if (layerData.index - 1 < this.skySpeed.length && layerData.index - 1 >= 0) {
            var speedDiff = this.skySpeed[(layerData.index - 1) % this.skySpeed.length] - layerData.speed;
            layerData.speed += speedDiff * deltaTime;
          } else {
            var _speedDiff = this.skySpeed[this.skySpeed.length - 1] - layerData.speed;

            layerData.speed += _speedDiff * deltaTime;
          } // 移除超出视图范围的节点


          if (layerData.nowUseNode.length > 0 && layerData.nowUseNode[0].y + layerData.nowUseNode[0].height - 1880 < layerData.ViewBot) {
            var removedNode = layerData.nowUseNode.shift();
            removedNode.active = false;
            layerData.freeNode.push(removedNode);
          } // 创建新节点


          if (posY + layerData.starPos - spriteFrame.getRect().height / 2 * scaleY < layerData.ViewTop + 1880 + 500) {
            var newNode;

            if (layerData.freeNode.length > 0) {
              newNode = layerData.freeNode.shift();
            } else {
              newNode = new Node();
              newNode.addComponent(Sprite);
            }

            this.addNodeMoveComp(newNode, layerData.nodeMove[(layerData.index + 1) % layerData.nodeMove.length], layer);
            var sprite = newNode.getComponent(Sprite);
            sprite.sizeMode = Sprite.SizeMode.RAW;
            sprite.spriteFrame = spriteFrame;

            if (sprite.spriteFrame) {
              // newNode.getComponent(UITransform).width = sprite.spriteFrame.getRect().width * scaleX;
              // newNode.getComponent(UITransform).height = sprite.spriteFrame.height * scaleY;
              newNode.setPosition(posX, posY + layerData.starPos);
              newNode.angle = layerData.nodeAngle[(layerData.index + 1) % layerData.nodeAngle.length];
            }

            newNode.parent = this.LayerParents.get(layer);
            newNode.active = true;
            layerData.nowUseNode.push(newNode);
            layerData.index++;
          } // 创建物品


          this.createItem(layer, deltaTime);
        }
        /**
         * 为节点添加或设置 NodeMove 组件
         * @param node 要操作的节点
         * @param moveData 移动数据，格式为 "xSpeed,ySpeed"
         * @param layer 节点所属的图层
         */


        addNodeMoveComp(node, moveData, layer) {
          var [xSpeed, ySpeed] = moveData.split(",").map(Number);
          var nodeMoveComp = node.getComponent(_crd && NodeMove === void 0 ? (_reportPossibleCrUseOfNodeMove({
            error: Error()
          }), NodeMove) : NodeMove) || node.addComponent(_crd && NodeMove === void 0 ? (_reportPossibleCrUseOfNodeMove({
            error: Error()
          }), NodeMove) : NodeMove);
          nodeMoveComp.setData(xSpeed, ySpeed, layer);
        }

        mapLayer1Run(deltaTime, layer) {
          var layerData = this.LayerData.get(layer);
          var parentNode = this.LayerParents.get(layer);
          var speed = layerData.speeds[layerData.index % layerData.speeds.length];
          layerData.speed = speed;
          var adjustedSpeed = speed + this.changeMapSpeedRatio;
          layerData.ViewTop += deltaTime * adjustedSpeed;
          layerData.ViewBot = layerData.ViewTop - view.getVisibleSize().height;
          parentNode.y -= deltaTime * adjustedSpeed;

          if (layerData.nowUseNode.length > 0 && layerData.nowUseNode[0].y + layerData.nowUseNode[0].height / 2 < layerData.ViewBot) {
            var node = layerData.nowUseNode.shift();
            node.active = false;
            layerData.freeNode.push(node);
          }

          var nextSpriteName = this.floorDataSprite[(layerData.index + 1) % this.floorDataSprite.length].split(",");
          var spriteName = nextSpriteName[0];
          var atlasName = nextSpriteName[1];
          var spriteFrame = layerData.loadSprite.get(spriteName);

          if (!spriteFrame) {
            spriteFrame = (_crd && GameIns === void 0 ? (_reportPossibleCrUseOfGameIns({
              error: Error()
            }), GameIns) : GameIns).loadManager.getImage(spriteName, "map/" + atlasName);
            layerData.loadSprite.set(spriteName, spriteFrame);
          }

          var lastNode = layerData.nowUseNode[layerData.nowUseNode.length - 1];
          var newNode = layerData.freeNode.length > 0 ? layerData.freeNode.shift() : new Node();
          var sprite = newNode.getComponent(Sprite) || newNode.addComponent(Sprite);
          sprite.spriteFrame = spriteFrame;
          sprite.sizeMode = Sprite.SizeMode.RAW; // newNode.width = spriteFrame.getRect().width;
          // newNode.height = spriteFrame.getRect().height;

          var posY = lastNode.position.y + lastNode.getComponent(UITransform).height / 2 + newNode.getComponent(UITransform).height / 2;
          newNode.setPosition(newNode.position.x, posY);
          newNode.parent = parentNode;
          newNode.active = true;
          layerData.nowUseNode.push(newNode);
          layerData.index++;
          this.createItem(layer, deltaTime);
        }
        /**
         * 创建地图中的物品（建筑、飞机、炮塔等）。
         * @param layer 当前图层
         * @param deltaTime 时间增量
         */


        createItem(layer, deltaTime) {
          var layerData = this.LayerData.get(layer);
          var layerParent = this.LayerParents.get(layer);

          if (layerData.itemIndex < this.itemDatas.length && this.itemDatas[layerData.itemIndex].backLayer === layer && layerData.ViewBot > this.itemDatas[layerData.itemIndex].createPos) {
            var itemData = this.itemDatas[layerData.itemIndex];
            var itemNode;
            var posX = itemData.backPosX;
            var posY = itemData.backPosY;
            var scaleX = itemData.backScaleX;
            var scaleY = itemData.backScaleY;

            switch (itemData.enemy) {
              case 0:
                // 创建建筑
                (_crd && Tools === void 0 ? (_reportPossibleCrUseOfTools({
                  error: Error()
                }), Tools) : Tools).log("\u521B\u5EFA\u5EFA\u7B51\uFF0C\u56FE\u5C42: " + layer);
                break;

              case 1:
                // 创建飞机
                itemNode = (_crd && GameIns === void 0 ? (_reportPossibleCrUseOfGameIns({
                  error: Error()
                }), GameIns) : GameIns).enemyManager.addMapPlane(itemData.id, itemData.track, v2(posX, posY), itemData.backLayer);
                break;

              case 2:
                // 创建炮塔
                // itemNode = GameIns.enemyManager.addTurret(itemData.id, itemData.backLayer).node;
                break;
            }

            if (itemNode) {
              itemNode.parent = layerParent;
              itemNode.setSiblingIndex(itemData.zIndex);
              itemNode.setPosition(posX, posY - this.posOffIndex * deltaTime * itemData.Vec);
              itemNode.setScale(scaleX * this.LayerData.get(0).scale, scaleY * this.LayerData.get(0).scale);
              itemNode.angle = 0;
              var nodeMoveComp = itemNode.getComponent(_crd && NodeMove === void 0 ? (_reportPossibleCrUseOfNodeMove({
                error: Error()
              }), NodeMove) : NodeMove) || itemNode.addComponent(_crd && NodeMove === void 0 ? (_reportPossibleCrUseOfNodeMove({
                error: Error()
              }), NodeMove) : NodeMove);
              nodeMoveComp.setData(0, itemData.Vec, layer);
            }

            layerData.itemIndex++;
          }
        } // hideLayerBylayer() {
        //     for (const layer of this.allSkyLayers) {
        //         if (layer > 99) {
        //             const parent = this.LayerParents.get(layer);
        //             if (parent) {
        //                 tween(parent).to(0.5, { opacity: 0 }).start();
        //             }
        //         }
        //     }
        // }


        showLayerBylayer() {
          for (var layer of this.allSkyLayers) {
            if (layer > 99) {
              var parent = this.LayerParents.get(layer);

              if (parent) {
                tween(parent).to(0.5, {
                  opacity: 255
                }).start();
              }
            }
          }
        } // hideSpeedLine() {
        //     this._speedLineOpacity = false;
        //     if (this._speedLine) {
        //         this._speedLine.stopAllActions();
        //         tween(this._speedLine)
        //             .to(1 / 30 * 22, { opacity: 0 })
        //             .call(() => this.removeSpeedLine())
        //             .start();
        //     }
        // }
        // hideSpeedLineByDt(deltaTime: number) {
        //     this._speedLineOpacity = false;
        //     if (this._speedLine) {
        //         this._speedLine.opacity -= 355 * deltaTime;
        //         if (this._speedLine.opacity <= 0) {
        //             this.removeSpeedLine();
        //         }
        //     }
        // }
        // removeSpeedLine() {
        //     Tools.log("remove speedLine");
        //     if (this._speedLine) {
        //         this._speedLine.destroyAllChildren();
        //         this._speedLine.destroy();
        //         this._speedLine = null;
        //     }
        //     if (this._lineArr) {
        //         this._lineArr.splice(0);
        //     }
        // }


      }, _class3.instance = null, _class3), (_descriptor = _applyDecoratedDescriptor(_class2.prototype, "floorLayerParent", [_dec2], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function initializer() {
          return null;
        }
      }), _descriptor2 = _applyDecoratedDescriptor(_class2.prototype, "skyLayerParent", [_dec3], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function initializer() {
          return null;
        }
      })), _class2)) || _class));

      _cclegacy._RF.pop();

      _crd = false;
    }
  };
});
//# sourceMappingURL=e20626aee19733b3d08ccf7e1aaa74fa5c39448f.js.map