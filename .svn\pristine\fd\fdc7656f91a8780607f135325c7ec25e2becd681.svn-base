{"description": "项目配置文件。", "miniprogramRoot": "./", "setting": {"urlCheck": true, "postcss": true, "minified": true, "newFeature": false, "enhance": true, "useIsolateContext": true}, "compileType": "game", "libVersion": "latest", "appid": "", "projectname": "", "condition": {"search": {"current": -1, "list": []}, "conversation": {"current": -1, "list": []}, "game": {"currentL": -1, "list": [], "current": -1}, "miniprogram": {"current": -1, "list": []}}}