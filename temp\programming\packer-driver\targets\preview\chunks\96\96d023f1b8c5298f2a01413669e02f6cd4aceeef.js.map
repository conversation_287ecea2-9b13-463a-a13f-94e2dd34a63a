{"version": 3, "sources": ["file:///E:/M2Game/Client/assets/scripts/GameOld/MainGame.ts"], "names": ["_decorator", "Component", "find", "Vec3", "Global", "GamePersistNode", "ccclass", "property", "MainGame", "enemyFactory", "goodsFactory", "persistNode", "enemy1Timer", "enemy2Timer", "bloodGoodsTimer", "lightGoodsTimer", "missileGoodsTimer", "enemy1MinProduceTime", "enemy1MaxProduceTime", "enemy2MinProduceTime", "enemy2MaxProduceTime", "bloodGoodsMinProduceTime", "bloodGoodsMaxProduceTime", "lightGoodsMinProduceTime", "lightGoodsMaxProduceTime", "missileGoodsMinProduceTime", "missileGoodsMaxProduceTime", "enemy1Random", "enemy2Random", "bloodGoodsRandom", "lightGoodsRandom", "missileGoodsRandom", "onLoad", "getComponent", "Math", "random", "update", "deltaTime", "produceEnemy1", "produceEnemy2", "produceBloodGoods", "produceLightGoods", "produceMissileGoods", "posBegin", "goodsTemp", "createProduct", "MISSILE_GOODS", "node", "<PERSON><PERSON><PERSON><PERSON>", "x", "WIDTH", "y", "HEIGHT", "setPosition", "LIGHT_GOODS", "BLOOD_GOODS", "enemyTemp", "ENEMY_1", "ENEMY_2"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;AAASA,MAAAA,U,OAAAA,U;AAAYC,MAAAA,S,OAAAA,S;AAAiBC,MAAAA,I,OAAAA,I;AAAMC,MAAAA,I,OAAAA,I;;AAEnCC,MAAAA,M,iBAAAA,M;;AACAC,MAAAA,e,iBAAAA,e;;;;;;;;;OACH;AAAEC,QAAAA,OAAF;AAAWC,QAAAA;AAAX,O,GAAwBP,U;;0BAGjBQ,Q,WADZF,OAAO,CAAC,UAAD,C,gBAAR,MACaE,QADb,SAC8BP,SAD9B,CACwC;AAAA;AAAA;AAAA,eAEpCQ,YAFoC,GAER,IAFQ;AAED;AAFC,eAIpCC,YAJoC,GAIR,IAJQ;AAID;AAJC,eAMpCC,WANoC,GAMhB,IANgB;AAMR;AANQ,eAQpCC,WARoC,GAQd,CARc;AAQA;AARA,eAUpCC,WAVoC,GAUd,CAVc;AAUA;AAVA,eAYpCC,eAZoC,GAYV,CAZU;AAYI;AAZJ,eAcpCC,eAdoC,GAcV,CAdU;AAcI;AAdJ,eAgBpCC,iBAhBoC,GAgBR,CAhBQ;AAgBM;AAhBN,eAkBpCC,oBAlBoC,GAkBL,CAlBK;AAkBC;AAlBD,eAoBpCC,oBApBoC,GAoBL,CApBK;AAoBA;AApBA,eAsBpCC,oBAtBoC,GAsBL,CAtBK;AAsBC;AAtBD,eAwBpCC,oBAxBoC,GAwBL,CAxBK;AAwBA;AAxBA,eA0BpCC,wBA1BoC,GA0BD,CA1BC;AA0BK;AA1BL,eA4BpCC,wBA5BoC,GA4BD,CA5BC;AA4BI;AA5BJ,eA8BpCC,wBA9BoC,GA8BD,CA9BC;AA8BK;AA9BL,eAgCpCC,wBAhCoC,GAgCD,CAhCC;AAgCI;AAhCJ,eAkCpCC,0BAlCoC,GAkCC,CAlCD;AAkCO;AAlCP,eAoCpCC,0BApCoC,GAoCC,CApCD;AAoCM;AApCN,eAsCpCC,YAtCoC,GAsCb,CAtCa;AAsCR;AAtCQ,eAwCpCC,YAxCoC,GAwCb,CAxCa;AAwCR;AAxCQ,eA0CpCC,gBA1CoC,GA0CT,CA1CS;AA0CA;AA1CA,eA4CpCC,gBA5CoC,GA4CT,CA5CS;AA4CA;AA5CA,eA8CpCC,kBA9CoC,GA8CP,CA9CO;AAAA;;AA8CE;AAEtCC,QAAAA,MAAM,GAAG;AACL,eAAKrB,WAAL,GAAmBT,IAAI,CAAC,iBAAD,CAAvB;AACA,eAAKO,YAAL,GAAoB,KAAKE,WAAL,CAAiBsB,YAAjB;AAAA;AAAA,kDAA+CxB,YAAnE;AACA,eAAKC,YAAL,GAAoB,KAAKC,WAAL,CAAiBsB,YAAjB;AAAA;AAAA,kDAA+CvB,YAAnE,CAHK,CAKL;;AACA,eAAKO,oBAAL,GAA4B,KAAKN,WAAL,CAAiBsB,YAAjB;AAAA;AAAA,kDAA+ChB,oBAA3E;AACA,eAAKC,oBAAL,GAA4B,KAAKP,WAAL,CAAiBsB,YAAjB;AAAA;AAAA,kDAA+Cf,oBAA3E,CAPK,CASL;;AACA,eAAKC,oBAAL,GAA4B,KAAKR,WAAL,CAAiBsB,YAAjB;AAAA;AAAA,kDAA+Cd,oBAA3E;AACA,eAAKC,oBAAL,GAA4B,KAAKT,WAAL,CAAiBsB,YAAjB;AAAA;AAAA,kDAA+Cb,oBAA3E,CAXK,CAaL;;AACA,eAAKC,wBAAL,GAAgC,KAAKV,WAAL,CAAiBsB,YAAjB;AAAA;AAAA,kDAA+CZ,wBAA/E;AACA,eAAKC,wBAAL,GAAgC,KAAKX,WAAL,CAAiBsB,YAAjB;AAAA;AAAA,kDAA+CX,wBAA/E;AACA,eAAKC,wBAAL,GAAgC,KAAKZ,WAAL,CAAiBsB,YAAjB;AAAA;AAAA,kDAA+CV,wBAA/E;AACA,eAAKC,wBAAL,GAAgC,KAAKb,WAAL,CAAiBsB,YAAjB;AAAA;AAAA,kDAA+CT,wBAA/E;AACA,eAAKC,0BAAL,GAAkC,KAAKd,WAAL,CAAiBsB,YAAjB;AAAA;AAAA,kDAA+CR,0BAAjF;AACA,eAAKC,0BAAL,GAAkC,KAAKf,WAAL,CAAiBsB,YAAjB;AAAA;AAAA,kDAA+CP,0BAAjF,CAnBK,CAqBL;;AACA,eAAKC,YAAL,GAAoB,KAAKV,oBAAL,GAA4BiB,IAAI,CAACC,MAAL,MAAiB,KAAKjB,oBAAL,GAA4B,KAAKD,oBAAlD,CAAhD;AACA,eAAKW,YAAL,GAAoB,KAAKT,oBAAL,GAA4Be,IAAI,CAACC,MAAL,MAAiB,KAAKf,oBAAL,GAA4B,KAAKD,oBAAlD,CAAhD;AACA,eAAKU,gBAAL,GAAwB,KAAKR,wBAAL,GAAgCa,IAAI,CAACC,MAAL,MAAiB,KAAKb,wBAAL,GAAgC,KAAKD,wBAAtD,CAAxD;AACA,eAAKS,gBAAL,GAAwB,KAAKP,wBAAL,GAAgCW,IAAI,CAACC,MAAL,MAAiB,KAAKT,0BAAL,GAAkC,KAAKH,wBAAxD,CAAxD;AACA,eAAKQ,kBAAL,GAA0B,KAAKN,0BAAL,GAAkCS,IAAI,CAACC,MAAL,MAAiB,KAAKT,0BAAL,GAAkC,KAAKD,0BAAxD,CAA5D;AACH;;AAEDW,QAAAA,MAAM,CAACC,SAAD,EAAoB;AACtB;AACA,eAAKzB,WAAL,IAAoByB,SAApB;;AACA,cAAI,KAAKzB,WAAL,GAAmB,KAAKe,YAA5B,EAA0C;AACtC,iBAAKW,aAAL;AACA,iBAAK1B,WAAL,GAAmB,CAAnB;AACA,iBAAKe,YAAL,GAAoB,KAAKV,oBAAL,GAA4BiB,IAAI,CAACC,MAAL,MAAiB,KAAKjB,oBAAL,GAA4B,KAAKD,oBAAlD,CAAhD;AACH,WAPqB,CAStB;;;AACA,eAAKJ,WAAL,IAAoBwB,SAApB;;AACA,cAAI,KAAKxB,WAAL,GAAmB,KAAKe,YAA5B,EAA0C;AACtC,iBAAKW,aAAL;AACA,iBAAK1B,WAAL,GAAmB,CAAnB;AACA,iBAAKe,YAAL,GAAoB,KAAKT,oBAAL,GAA4Be,IAAI,CAACC,MAAL,MAAiB,KAAKf,oBAAL,GAA4B,KAAKD,oBAAlD,CAAhD;AACH,WAfqB,CAiBtB;;;AACA,eAAKL,eAAL,IAAwBuB,SAAxB;;AACA,cAAI,KAAKvB,eAAL,GAAuB,KAAKe,gBAAhC,EAAkD;AAC9C,iBAAKW,iBAAL;AACA,iBAAK1B,eAAL,GAAuB,CAAvB;AACA,iBAAKe,gBAAL,GAAwB,KAAKR,wBAAL,GAAgCa,IAAI,CAACC,MAAL,MAAiB,KAAKb,wBAAL,GAAgC,KAAKD,wBAAtD,CAAxD;AACH,WAvBqB,CAyBtB;;;AACA,eAAKN,eAAL,IAAwBsB,SAAxB;;AACA,cAAI,KAAKtB,eAAL,GAAuB,KAAKe,gBAAhC,EAAkD;AAC9C,iBAAKW,iBAAL;AACA,iBAAK1B,eAAL,GAAuB,CAAvB;AACA,iBAAKe,gBAAL,GAAwB,KAAKP,wBAAL,GAAgCW,IAAI,CAACC,MAAL,MAAiB,KAAKT,0BAAL,GAAkC,KAAKH,wBAAxD,CAAxD;AACH,WA/BqB,CAiCtB;;;AACA,eAAKP,iBAAL,IAA0BqB,SAA1B;;AACA,cAAI,KAAKrB,iBAAL,GAAyB,KAAKe,kBAAlC,EAAsD;AAClD,iBAAKW,mBAAL;AACA,iBAAK1B,iBAAL,GAAyB,CAAzB;AACA,iBAAKe,kBAAL,GAA0B,KAAKN,0BAAL,GAAkCS,IAAI,CAACC,MAAL,MAAiB,KAAKT,0BAAL,GAAkC,KAAKD,0BAAxD,CAA5D;AACH;AACJ,SArHmC,CAuHpC;;;AACAiB,QAAAA,mBAAmB,GAAG;AAClB,cAAIC,QAAc,GAAG,IAAIxC,IAAJ,EAArB;AACA,cAAIyC,SAAe,GAAG,IAAtB;AAEAA,UAAAA,SAAS,GAAG,KAAKlC,YAAL,CAAkBmC,aAAlB,CAAgC;AAAA;AAAA,gCAAOC,aAAvC,CAAZ;AACA,eAAKC,IAAL,CAAUC,QAAV,CAAmBJ,SAAnB;AAEAD,UAAAA,QAAQ,CAACM,CAAT,GAAc,CAACf,IAAI,CAACC,MAAL,KAAe,GAAhB,IAAuB,CAAxB,GAA6B;AAAA;AAAA,gCAAOe,KAApC,GAA4C,CAAzD;AAEAP,UAAAA,QAAQ,CAACQ,CAAT,GAAa;AAAA;AAAA,gCAAOC,MAAP,GAAgB,CAA7B;AAEAR,UAAAA,SAAS,CAACS,WAAV,CAAsBV,QAAtB;AACH,SApImC,CAsIpC;;;AACAF,QAAAA,iBAAiB,GAAG;AAChB,cAAIE,QAAc,GAAG,IAAIxC,IAAJ,EAArB;AACA,cAAIyC,SAAe,GAAG,IAAtB;AAEAA,UAAAA,SAAS,GAAG,KAAKlC,YAAL,CAAkBmC,aAAlB,CAAgC;AAAA;AAAA,gCAAOS,WAAvC,CAAZ;AACA,eAAKP,IAAL,CAAUC,QAAV,CAAmBJ,SAAnB;AAEAD,UAAAA,QAAQ,CAACM,CAAT,GAAc,CAACf,IAAI,CAACC,MAAL,KAAe,GAAhB,IAAuB,CAAxB,GAA6B;AAAA;AAAA,gCAAOe,KAApC,GAA4C,CAAzD;AAEAP,UAAAA,QAAQ,CAACQ,CAAT,GAAa;AAAA;AAAA,gCAAOC,MAAP,GAAgB,CAA7B;AAEAR,UAAAA,SAAS,CAACS,WAAV,CAAsBV,QAAtB;AACH,SAnJmC,CAqJpC;;;AACAH,QAAAA,iBAAiB,GAAG;AAChB,cAAIG,QAAc,GAAG,IAAIxC,IAAJ,EAArB;AACA,cAAIyC,SAAe,GAAG,IAAtB;AAEAA,UAAAA,SAAS,GAAG,KAAKlC,YAAL,CAAkBmC,aAAlB,CAAgC;AAAA;AAAA,gCAAOU,WAAvC,CAAZ;AACA,eAAKR,IAAL,CAAUC,QAAV,CAAmBJ,SAAnB;AAEAD,UAAAA,QAAQ,CAACM,CAAT,GAAc,CAACf,IAAI,CAACC,MAAL,KAAe,GAAhB,IAAuB,CAAxB,GAA6B;AAAA;AAAA,gCAAOe,KAApC,GAA4C,CAAzD;AAEAP,UAAAA,QAAQ,CAACQ,CAAT,GAAa;AAAA;AAAA,gCAAOC,MAAP,GAAgB,CAA7B;AAEAR,UAAAA,SAAS,CAACS,WAAV,CAAsBV,QAAtB;AACH;AAED;AACJ;AACA;;;AACIL,QAAAA,aAAa,GAAG;AACZ,cAAIK,QAAc,GAAG,IAAIxC,IAAJ,EAArB;AACA,cAAIqD,SAAe,GAAG,IAAtB;AAEAA,UAAAA,SAAS,GAAG,KAAK/C,YAAL,CAAkBoC,aAAlB,CAAgC;AAAA;AAAA,gCAAOY,OAAvC,CAAZ;AACA,eAAKV,IAAL,CAAUC,QAAV,CAAmBQ,SAAnB;AAEAb,UAAAA,QAAQ,CAACM,CAAT,GAAc,CAACf,IAAI,CAACC,MAAL,KAAe,GAAhB,IAAuB,CAAxB,GAA6B;AAAA;AAAA,gCAAOe,KAApC,GAA4C,CAAzD;AAEAP,UAAAA,QAAQ,CAACQ,CAAT,GAAa;AAAA;AAAA,gCAAOC,MAAP,GAAgB,CAA7B;AAEAI,UAAAA,SAAS,CAACH,WAAV,CAAsBV,QAAtB;AACH;AAED;AACJ;AACA;;;AACKJ,QAAAA,aAAa,GAAG;AACb,cAAII,QAAc,GAAG,IAAIxC,IAAJ,EAArB;AACA,cAAIqD,SAAe,GAAG,IAAtB;AAEAA,UAAAA,SAAS,GAAG,KAAK/C,YAAL,CAAkBoC,aAAlB,CAAgC;AAAA;AAAA,gCAAOa,OAAvC,CAAZ;AACA,eAAKX,IAAL,CAAUC,QAAV,CAAmBQ,SAAnB;AAEAb,UAAAA,QAAQ,CAACM,CAAT,GAAc,CAACf,IAAI,CAACC,MAAL,KAAe,GAAhB,IAAuB,CAAxB,GAA6B;AAAA;AAAA,gCAAOe,KAApC,GAA4C,CAAzD;AAEAP,UAAAA,QAAQ,CAACQ,CAAT,GAAa;AAAA;AAAA,gCAAOC,MAAP,GAAgB,CAA7B;AAEAI,UAAAA,SAAS,CAACH,WAAV,CAAsBV,QAAtB;AACH;;AApMmC,O", "sourcesContent": ["import { _decorator, Component, Node, find, Vec3 } from 'cc';\nimport { GameFactory } from './factroy/GameFactory';\nimport { Global } from './Global';\nimport { GamePersistNode } from './GamePersistNode';\nconst { ccclass, property } = _decorator;\n\n@ccclass('MainGame')\nexport class MainGame extends Component {\n\n    enemyFactory: GameFactory = null;  //敌机工厂\n\n    goodsFactory: GameFactory = null;  //物资工厂\n\n    persistNode: Node = null;   //持久节点\n\n    enemy1Timer: number = 0;            //产生敌机1的定时器\n\n    enemy2Timer: number = 0;            //产生敌机2的定时器\n\n    bloodGoodsTimer: number = 0;            //加血物资的定时器\n\n    lightGoodsTimer: number = 0;            //激光物资的定时器\n\n    missileGoodsTimer: number = 0;            //激光物资的定时器\n\n    enemy1MinProduceTime: number = 0;    //enemy1出现时间的间隔的下限\n\n    enemy1MaxProduceTime: number = 0;   //enemy1出现时间的间隔的上限\n\n    enemy2MinProduceTime: number = 0;    //enemy2出现时间的间隔的下限\n\n    enemy2MaxProduceTime: number = 0;   //enemy2出现时间的间隔的上限\n\n    bloodGoodsMinProduceTime: number = 0;    //加血物资出现时间的间隔的下限\n\n    bloodGoodsMaxProduceTime: number = 0;   //加血物资出现时间的间隔的上限\n\n    lightGoodsMinProduceTime: number = 0;    //激光物资出现时间的间隔的下限\n\n    lightGoodsMaxProduceTime: number = 0;   //激光物资出现时间的间隔的上限\n\n    missileGoodsMinProduceTime: number = 0;    //导弹物资出现时间的间隔的下限\n\n    missileGoodsMaxProduceTime: number = 0;   //导弹物资出现时间的间隔的上限\n\n    enemy1Random: number = 0;   //enemy1出现具体时间（随机的）\n\n    enemy2Random: number = 0;   //enemy2出现具体时间（随机的）\n\n    bloodGoodsRandom: number = 0;       //加血物资出现具体时间（随机的）\n\n    lightGoodsRandom: number = 0;       //加血物资出现具体时间（随机的）\n\n    missileGoodsRandom: number = 0;       //加血物资出现具体时间（随机的）\n\n    onLoad() {\n        this.persistNode = find(\"GamePersistNode\");\n        this.enemyFactory = this.persistNode.getComponent(GamePersistNode).enemyFactory;\n        this.goodsFactory = this.persistNode.getComponent(GamePersistNode).goodsFactory;\n\n        //从persist node面板上获取实际的值\n        this.enemy1MinProduceTime = this.persistNode.getComponent(GamePersistNode).enemy1MinProduceTime;\n        this.enemy1MaxProduceTime = this.persistNode.getComponent(GamePersistNode).enemy1MaxProduceTime;\n\n        //从persist node面板上获取实际的值\n        this.enemy2MinProduceTime = this.persistNode.getComponent(GamePersistNode).enemy2MinProduceTime;\n        this.enemy2MaxProduceTime = this.persistNode.getComponent(GamePersistNode).enemy2MaxProduceTime;\n\n        //从persist node面板上获取实际的值\n        this.bloodGoodsMinProduceTime = this.persistNode.getComponent(GamePersistNode).bloodGoodsMinProduceTime;\n        this.bloodGoodsMaxProduceTime = this.persistNode.getComponent(GamePersistNode).bloodGoodsMaxProduceTime;\n        this.lightGoodsMinProduceTime = this.persistNode.getComponent(GamePersistNode).lightGoodsMinProduceTime;\n        this.lightGoodsMaxProduceTime = this.persistNode.getComponent(GamePersistNode).lightGoodsMaxProduceTime;\n        this.missileGoodsMinProduceTime = this.persistNode.getComponent(GamePersistNode).missileGoodsMinProduceTime;\n        this.missileGoodsMaxProduceTime = this.persistNode.getComponent(GamePersistNode).missileGoodsMaxProduceTime;\n\n        //初始化各种随机数\n        this.enemy1Random = this.enemy1MinProduceTime + Math.random() * (this.enemy1MaxProduceTime - this.enemy1MinProduceTime);  \n        this.enemy2Random = this.enemy2MinProduceTime + Math.random() * (this.enemy2MaxProduceTime - this.enemy2MinProduceTime);  \n        this.bloodGoodsRandom = this.bloodGoodsMinProduceTime + Math.random() * (this.bloodGoodsMaxProduceTime - this.bloodGoodsMinProduceTime);  \n        this.lightGoodsRandom = this.lightGoodsMinProduceTime + Math.random() * (this.missileGoodsMaxProduceTime - this.lightGoodsMinProduceTime);  \n        this.missileGoodsRandom = this.missileGoodsMinProduceTime + Math.random() * (this.missileGoodsMaxProduceTime - this.missileGoodsMinProduceTime);  \n    }\n\n    update(deltaTime: number) {\n        //产生敌机1\n        this.enemy1Timer += deltaTime;\n        if (this.enemy1Timer > this.enemy1Random) {\n            this.produceEnemy1();\n            this.enemy1Timer = 0;\n            this.enemy1Random = this.enemy1MinProduceTime + Math.random() * (this.enemy1MaxProduceTime - this.enemy1MinProduceTime);    \n        }\n\n        //产生敌机2\n        this.enemy2Timer += deltaTime;\n        if (this.enemy2Timer > this.enemy2Random) {\n            this.produceEnemy2();\n            this.enemy2Timer = 0;\n            this.enemy2Random = this.enemy2MinProduceTime + Math.random() * (this.enemy2MaxProduceTime - this.enemy2MinProduceTime);\n        }\n\n        //产生血量物资\n        this.bloodGoodsTimer += deltaTime;\n        if (this.bloodGoodsTimer > this.bloodGoodsRandom) {\n            this.produceBloodGoods();\n            this.bloodGoodsTimer = 0;\n            this.bloodGoodsRandom = this.bloodGoodsMinProduceTime + Math.random() * (this.bloodGoodsMaxProduceTime - this.bloodGoodsMinProduceTime);\n        }\n\n        //产生激光物资\n        this.lightGoodsTimer += deltaTime;\n        if (this.lightGoodsTimer > this.lightGoodsRandom) {\n            this.produceLightGoods();\n            this.lightGoodsTimer = 0;\n            this.lightGoodsRandom = this.lightGoodsMinProduceTime + Math.random() * (this.missileGoodsMaxProduceTime - this.lightGoodsMinProduceTime);\n        }\n\n        //产生导弹物资\n        this.missileGoodsTimer += deltaTime;\n        if (this.missileGoodsTimer > this.missileGoodsRandom) {\n            this.produceMissileGoods();\n            this.missileGoodsTimer = 0;\n            this.missileGoodsRandom = this.missileGoodsMinProduceTime + Math.random() * (this.missileGoodsMaxProduceTime - this.missileGoodsMinProduceTime);  \n        }\n    }\n\n    //产生导弹物资\n    produceMissileGoods() {\n        let posBegin: Vec3 = new Vec3();\n        let goodsTemp: Node = null;\n\n        goodsTemp = this.goodsFactory.createProduct(Global.MISSILE_GOODS);\n        this.node.addChild(goodsTemp);\n\n        posBegin.x = ((Math.random() -0.5) * 2) * Global.WIDTH / 2;\n\n        posBegin.y = Global.HEIGHT / 2;\n\n        goodsTemp.setPosition(posBegin);\n    }\n\n    //产生激光物资\n    produceLightGoods() {\n        let posBegin: Vec3 = new Vec3();\n        let goodsTemp: Node = null;\n\n        goodsTemp = this.goodsFactory.createProduct(Global.LIGHT_GOODS);\n        this.node.addChild(goodsTemp);\n\n        posBegin.x = ((Math.random() -0.5) * 2) * Global.WIDTH / 2;\n\n        posBegin.y = Global.HEIGHT / 2;\n\n        goodsTemp.setPosition(posBegin);\n    }\n\n    //产生血量物资\n    produceBloodGoods() {\n        let posBegin: Vec3 = new Vec3();\n        let goodsTemp: Node = null;\n\n        goodsTemp = this.goodsFactory.createProduct(Global.BLOOD_GOODS);\n        this.node.addChild(goodsTemp);\n\n        posBegin.x = ((Math.random() -0.5) * 2) * Global.WIDTH / 2;\n\n        posBegin.y = Global.HEIGHT / 2;\n\n        goodsTemp.setPosition(posBegin);\n    }\n\n    /**\n     * //产生敌机1\n     */\n    produceEnemy1() {\n        let posBegin: Vec3 = new Vec3();\n        let enemyTemp: Node = null;\n\n        enemyTemp = this.enemyFactory.createProduct(Global.ENEMY_1);\n        this.node.addChild(enemyTemp);\n\n        posBegin.x = ((Math.random() -0.5) * 2) * Global.WIDTH / 2;\n\n        posBegin.y = Global.HEIGHT / 2;\n\n        enemyTemp.setPosition(posBegin);\n    }\n\n    /**\n     * //产生敌机2\n     */\n     produceEnemy2() {\n        let posBegin: Vec3 = new Vec3();\n        let enemyTemp: Node = null;\n\n        enemyTemp = this.enemyFactory.createProduct(Global.ENEMY_2);\n        this.node.addChild(enemyTemp);\n\n        posBegin.x = ((Math.random() -0.5) * 2) * Global.WIDTH / 2;\n\n        posBegin.y = Global.HEIGHT / 2;\n\n        enemyTemp.setPosition(posBegin);\n    }\n\n}\n\n"]}