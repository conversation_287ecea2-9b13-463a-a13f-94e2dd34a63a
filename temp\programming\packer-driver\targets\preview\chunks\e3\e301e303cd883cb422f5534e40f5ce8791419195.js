System.register(["__unresolved_0", "cc", "__unresolved_1", "__unresolved_2", "__unresolved_3", "__unresolved_4", "__unresolved_5"], function (_export, _context) {
  "use strict";

  var _reporterNs, _cclegacy, __checkObsolete__, __checkObsoleteInNamespace__, _decorator, Component, rect, EnemyAttrData, GameEnum, Tools, EnemyAttrDoctorCom, EnemyAttrShieldCom, _class, _crd, ccclass, property, EnemyAttrComponent;

  function _reportPossibleCrUseOfEnemyAttrData(extras) {
    _reporterNs.report("EnemyAttrData", "../../../data/EnemyData", _context.meta, extras);
  }

  function _reportPossibleCrUseOfGameEnum(extras) {
    _reporterNs.report("GameEnum", "../../../const/GameEnum", _context.meta, extras);
  }

  function _reportPossibleCrUseOfTools(extras) {
    _reporterNs.report("Tools", "../../../utils/Tools", _context.meta, extras);
  }

  function _reportPossibleCrUseOfEnemyAttrDoctorCom(extras) {
    _reporterNs.report("EnemyAttrDoctorCom", "./EnemyAttrDoctorCom", _context.meta, extras);
  }

  function _reportPossibleCrUseOfEnemyAttrShieldCom(extras) {
    _reporterNs.report("EnemyAttrShieldCom", "./EnemyAttrShieldCom", _context.meta, extras);
  }

  return {
    setters: [function (_unresolved_) {
      _reporterNs = _unresolved_;
    }, function (_cc) {
      _cclegacy = _cc.cclegacy;
      __checkObsolete__ = _cc.__checkObsolete__;
      __checkObsoleteInNamespace__ = _cc.__checkObsoleteInNamespace__;
      _decorator = _cc._decorator;
      Component = _cc.Component;
      rect = _cc.rect;
    }, function (_unresolved_2) {
      EnemyAttrData = _unresolved_2.EnemyAttrData;
    }, function (_unresolved_3) {
      GameEnum = _unresolved_3.default;
    }, function (_unresolved_4) {
      Tools = _unresolved_4.Tools;
    }, function (_unresolved_5) {
      EnemyAttrDoctorCom = _unresolved_5.default;
    }, function (_unresolved_6) {
      EnemyAttrShieldCom = _unresolved_6.default;
    }],
    execute: function () {
      _crd = true;

      _cclegacy._RF.push({}, "5d189FiOBJC5KMcFPOuM/EA", "EnemyAttrComponent", undefined);

      __checkObsolete__(['_decorator', 'Component', 'rect']);

      ({
        ccclass,
        property
      } = _decorator);

      _export("default", EnemyAttrComponent = ccclass(_class = class EnemyAttrComponent extends Component {
        constructor() {
          super(...arguments);
          this.entity = null;
          // 敌人实体
          this.attr = [];
          // 属性列表
          this.attrData = {};
          // 属性数据映射
          this.awakeRect = rect();
          // 唤醒区域
          this.comArr = [];
          // 属性组件数组
          this.comDict = {};
        }

        // 属性组件字典

        /**
         * 重置组件状态
         */
        reset() {
          this.entity = null;
          this.attr = [];
          this.attrData = {};
          this.awakeRect = rect();
          this.comArr = [];
          this.comDict = {};
        }
        /**
         * 初始化属性组件
         * @param {Node} entity 敌人实体
         * @param {string} attrString 属性字符串
         */


        init(entity, attrString) {
          this.entity = entity;
          this.attr = [];
          this.attrData = {}; // 解析属性字符串

          var attributes = attrString.split('#');

          for (var attr of attributes) {
            if (attr.length > 0) {
              var parts = attr.split(';');

              if (parts.length > 1) {
                var attrData = new (_crd && EnemyAttrData === void 0 ? (_reportPossibleCrUseOfEnemyAttrData({
                  error: Error()
                }), EnemyAttrData) : EnemyAttrData)();
                attrData.type = parseInt(parts[0]);
                attrData.param = parts[1];
                this.attrData[attrData.type] = attrData;
                this.attr.push(attrData.type);
              }
            }
          }

          var components = [];
          var component; // 初始化医生属性组件

          if (this.hasAttribution((_crd && GameEnum === void 0 ? (_reportPossibleCrUseOfGameEnum({
            error: Error()
          }), GameEnum) : GameEnum).EnemyAttr.Doctor)) {
            component = (_crd && Tools === void 0 ? (_reportPossibleCrUseOfTools({
              error: Error()
            }), Tools) : Tools).addScript(this.node, _crd && EnemyAttrDoctorCom === void 0 ? (_reportPossibleCrUseOfEnemyAttrDoctorCom({
              error: Error()
            }), EnemyAttrDoctorCom) : EnemyAttrDoctorCom);
            component.init(this.attrData[(_crd && GameEnum === void 0 ? (_reportPossibleCrUseOfGameEnum({
              error: Error()
            }), GameEnum) : GameEnum).EnemyAttr.Doctor]);
            components.push(component);
          } // 初始化护盾属性组件


          if (this.hasAttribution((_crd && GameEnum === void 0 ? (_reportPossibleCrUseOfGameEnum({
            error: Error()
          }), GameEnum) : GameEnum).EnemyAttr.Shield)) {
            component = (_crd && Tools === void 0 ? (_reportPossibleCrUseOfTools({
              error: Error()
            }), Tools) : Tools).addScript(this.node, _crd && EnemyAttrShieldCom === void 0 ? (_reportPossibleCrUseOfEnemyAttrShieldCom({
              error: Error()
            }), EnemyAttrShieldCom) : EnemyAttrShieldCom);
            component.init(entity, this.attrData[(_crd && GameEnum === void 0 ? (_reportPossibleCrUseOfGameEnum({
              error: Error()
            }), GameEnum) : GameEnum).EnemyAttr.Shield]);
            components.push(component);
          } // 清理无效组件


          for (var i = 0; i < this.comArr.length; i++) {
            var existingComponent = this.comArr[i];

            if (!(_crd && Tools === void 0 ? (_reportPossibleCrUseOfTools({
              error: Error()
            }), Tools) : Tools).arrContain(components, existingComponent)) {
              existingComponent.destroy();
              this.comArr.splice(i, 1);
              this.comDict[existingComponent.attrData.type] = null;
              i--;
            }
          } // 添加新组件


          for (var newComponent of components) {
            if (!(_crd && Tools === void 0 ? (_reportPossibleCrUseOfTools({
              error: Error()
            }), Tools) : Tools).arrContain(this.comArr, newComponent)) {
              this.comArr.push(newComponent);
              this.comDict[newComponent.attrData.type] = newComponent;
            }
          }
        }
        /**
         * 更新游戏逻辑
         * @param {number} deltaTime 帧间隔时间
         */


        updateGameLogic(deltaTime) {
          for (var component of this.comArr) {
            component.updateGameLogic(deltaTime);
          }
        }
        /**
         * 检查是否具有指定属性
         * @param {number} attrType 属性类型
         * @returns {boolean} 是否具有该属性
         */


        hasAttribution(attrType) {
          return (_crd && Tools === void 0 ? (_reportPossibleCrUseOfTools({
            error: Error()
          }), Tools) : Tools).arrContain(this.attr, attrType);
        }
        /**
         * 处理敌人死亡逻辑
         */


        die() {
          for (var component of this.comArr) {
            component.die();
          }
        }
        /**
         * 显示护盾属性
         */


        showAttrShield() {
          for (var component of this.comArr) {
            if (component.showAttrShield) {
              component.showAttrShield();
            }
          }
        }

      }) || _class);

      _cclegacy._RF.pop();

      _crd = false;
    }
  };
});
//# sourceMappingURL=e301e303cd883cb422f5534e40f5ce8791419195.js.map