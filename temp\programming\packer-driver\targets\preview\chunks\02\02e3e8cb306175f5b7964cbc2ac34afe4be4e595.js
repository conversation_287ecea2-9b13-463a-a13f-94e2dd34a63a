System.register(["__unresolved_0", "cc", "__unresolved_1"], function (_export, _context) {
  "use strict";

  var _reporterNs, _cclegacy, __checkObsolete__, __checkObsoleteInNamespace__, _decorator, Color, Component, instantiate, Label, Layout, Node, Prefab, Sprite, UITransform, uiSelectItem, _dec, _dec2, _dec3, _dec4, _dec5, _dec6, _dec7, _dec8, _class, _class2, _descriptor, _descriptor2, _descriptor3, _descriptor4, _descriptor5, _descriptor6, _descriptor7, _crd, ccclass, property, uiSelect;

  function _initializerDefineProperty(target, property, descriptor, context) { if (!descriptor) return; Object.defineProperty(target, property, { enumerable: descriptor.enumerable, configurable: descriptor.configurable, writable: descriptor.writable, value: descriptor.initializer ? descriptor.initializer.call(context) : void 0 }); }

  function _applyDecoratedDescriptor(target, property, decorators, descriptor, context) { var desc = {}; Object.keys(descriptor).forEach(function (key) { desc[key] = descriptor[key]; }); desc.enumerable = !!desc.enumerable; desc.configurable = !!desc.configurable; if ('value' in desc || desc.initializer) { desc.writable = true; } desc = decorators.slice().reverse().reduce(function (desc, decorator) { return decorator(target, property, desc) || desc; }, desc); if (context && desc.initializer !== void 0) { desc.value = desc.initializer ? desc.initializer.call(context) : void 0; desc.initializer = undefined; } if (desc.initializer === void 0) { Object.defineProperty(target, property, desc); desc = null; } return desc; }

  function _initializerWarningHelper(descriptor, context) { throw new Error('Decorating class property failed. Please ensure that ' + 'transform-class-properties is enabled and runs after the decorators transform.'); }

  function _reportPossibleCrUseOfuiSelectItem(extras) {
    _reporterNs.report("uiSelectItem", "./uiSelectItem", _context.meta, extras);
  }

  return {
    setters: [function (_unresolved_) {
      _reporterNs = _unresolved_;
    }, function (_cc) {
      _cclegacy = _cc.cclegacy;
      __checkObsolete__ = _cc.__checkObsolete__;
      __checkObsoleteInNamespace__ = _cc.__checkObsoleteInNamespace__;
      _decorator = _cc._decorator;
      Color = _cc.Color;
      Component = _cc.Component;
      instantiate = _cc.instantiate;
      Label = _cc.Label;
      Layout = _cc.Layout;
      Node = _cc.Node;
      Prefab = _cc.Prefab;
      Sprite = _cc.Sprite;
      UITransform = _cc.UITransform;
    }, function (_unresolved_2) {
      uiSelectItem = _unresolved_2.uiSelectItem;
    }],
    execute: function () {
      _crd = true;

      _cclegacy._RF.push({}, "1c390cWrxBMHoIIU5Ebdcrh", "uiSelect", undefined);

      __checkObsolete__(['_decorator', 'Color', 'Component', 'instantiate', 'Label', 'Layout', 'Node', 'Prefab', 'ScrollView', 'Sprite', 'UITransform']);

      ({
        ccclass,
        property
      } = _decorator);

      _export("uiSelect", uiSelect = (_dec = ccclass('uiSelect'), _dec2 = property({
        type: Node,
        tooltip: "控制选择列表显示处理的按钮"
      }), _dec3 = property({
        type: Node,
        tooltip: "选择列表"
      }), _dec4 = property({
        type: Node,
        tooltip: "项目节点容器"
      }), _dec5 = property({
        type: Prefab,
        tooltip: "选择列表中的项目预制体"
      }), _dec6 = property({
        type: Number,
        tooltip: "选择列表中可展示的最大数据数量 超过可以滚动显示"
      }), _dec7 = property({
        type: String,
        tooltip: "默认项目"
      }), _dec8 = property({
        type: [String],
        tooltip: "项目数据列表"
      }), _dec(_class = (_class2 = class uiSelect extends Component {
        constructor() {
          super(...arguments);

          // 控制选择列表显示处理的按钮
          _initializerDefineProperty(this, "btnSelect", _descriptor, this);

          // 选择列表节点
          _initializerDefineProperty(this, "scrollViewSelect", _descriptor2, this);

          // 项目节点容器
          _initializerDefineProperty(this, "contentSelect", _descriptor3, this);

          // 选择列表中的项目预制体
          _initializerDefineProperty(this, "prefabItem", _descriptor4, this);

          // 选择列表中可展示的最大数据数量超过可以滚动显示
          _initializerDefineProperty(this, "itemMaxNum", _descriptor5, this);

          // 默认项目
          _initializerDefineProperty(this, "defaultItem", _descriptor6, this);

          // 项目数据列表
          _initializerDefineProperty(this, "itemDatas", _descriptor7, this);

          // 选择的项目
          this.chooseItemData = "";
          this._onChooseItem = void 0;
        }

        SetOnChooseItem(callBack) {
          this._onChooseItem = callBack;
        }

        onLoad() {
          this.setSelectShow(false); // 注册按钮事件

          this.btnSelect.on(Node.EventType.TOUCH_END, this.onShowSelect, this);
          this.setItemData(this.itemDatas); // 默认数据在数据列表当中

          if (this.itemDatas.indexOf(this.defaultItem) != -1) {
            this.chooseItemData = this.defaultItem;
          } // 选择项目数据


          if (this.chooseItemData.length > 0) {
            this.setChooseItemData(this.chooseItemData);
          } else {
            this.setChooseItemData("空");
          }
        }

        onDestroy() {
          // 注销按钮事件
          if (this.btnSelect && this.btnSelect.isValid) {
            this.btnSelect.off(Node.EventType.TOUCH_END, this.onShowSelect, this);
          }
        } // 处理选择列表显示


        onShowSelect() {
          if (this.scrollViewSelect.active) {
            this.setSelectShow(false);
          } else {
            this.setSelectShow(true);
          }
        }
        /**
         * 设置选择列表显示状态
         * @param show 显示/隐藏
         */


        setSelectShow(show) {
          this.scrollViewSelect.active = show;
        }
        /**
         * 设置选择列表的数据
         * @param itemDatas 项目数据
         */


        setItemData(itemDatas) {
          var _this$contentSelect$g;

          // 移除上一次的全部子节点
          this.contentSelect.destroyAllChildren(); // 更新数据

          this.itemDatas = itemDatas; // 生成新的选择节点

          for (var _itemData of this.itemDatas) {
            var itemNode = instantiate(this.prefabItem);
            itemNode.setParent(this.contentSelect); // 更新数据-可自定义

            itemNode.getComponent(_crd && uiSelectItem === void 0 ? (_reportPossibleCrUseOfuiSelectItem({
              error: Error()
            }), uiSelectItem) : uiSelectItem).updateValue(this, _itemData);
          } // 获取项目节点真实高度 node原先高度+spacingY(纵向间隔)
          // 更新选择列表高度


          var itemHeight = this.prefabItem.data.getComponent(UITransform).height + ((_this$contentSelect$g = this.contentSelect.getComponent(Layout)) == null ? void 0 : _this$contentSelect$g.spacingY);
          this.scrollViewSelect.getComponent(UITransform).height = this.itemMaxNum * itemHeight;
          this.contentSelect.getComponent(UITransform).height = itemDatas.length * itemHeight;
        }
        /**
         * 设置选择项目
         * @param itemData 项目数据 
         */


        setChooseItemData(itemData) {
          this.chooseItemData = itemData; // 选中项目节点处理

          for (var itemNode of this.contentSelect.children) {
            // 选中
            if (itemNode.getComponent(_crd && uiSelectItem === void 0 ? (_reportPossibleCrUseOfuiSelectItem({
              error: Error()
            }), uiSelectItem) : uiSelectItem).itemData === this.chooseItemData) {
              itemNode.getComponent(Sprite).color = new Color(125, 205, 205, 255);
            } else {
              // 未选中
              itemNode.getComponent(Sprite).color = new Color(105, 105, 105, 255);
            }
          }

          this.updateChooseItem();
        }
        /**
         * 更新选择的项目数据
         */


        updateChooseItem() {
          var btnLabel = this.btnSelect.getChildByName("Label");
          btnLabel.getComponent(Label).string = this.chooseItemData;
          this.setSelectShow(false);
        }

        onChooseItem(itemData) {
          this.setChooseItemData(itemData);

          this._onChooseItem(itemData);
        }

      }, (_descriptor = _applyDecoratedDescriptor(_class2.prototype, "btnSelect", [_dec2], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: null
      }), _descriptor2 = _applyDecoratedDescriptor(_class2.prototype, "scrollViewSelect", [_dec3], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: null
      }), _descriptor3 = _applyDecoratedDescriptor(_class2.prototype, "contentSelect", [_dec4], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: null
      }), _descriptor4 = _applyDecoratedDescriptor(_class2.prototype, "prefabItem", [_dec5], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: null
      }), _descriptor5 = _applyDecoratedDescriptor(_class2.prototype, "itemMaxNum", [_dec6], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function initializer() {
          return 3;
        }
      }), _descriptor6 = _applyDecoratedDescriptor(_class2.prototype, "defaultItem", [_dec7], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function initializer() {
          return "";
        }
      }), _descriptor7 = _applyDecoratedDescriptor(_class2.prototype, "itemDatas", [_dec8], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function initializer() {
          return [];
        }
      })), _class2)) || _class));

      _cclegacy._RF.pop();

      _crd = false;
    }
  };
});
//# sourceMappingURL=02e3e8cb306175f5b7964cbc2ac34afe4be4e595.js.map