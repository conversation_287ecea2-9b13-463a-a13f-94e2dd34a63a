{"version": 3, "sources": ["file:///E:/M2Game/Client/assets/scripts/Game/ui/plane/enemy/EnemyAttrShieldCom.ts"], "names": ["_decorator", "Vec2", "EnemyAttrBaseCom", "Tools", "GameIns", "BattleLayer", "ccclass", "property", "EnemyAttrShieldCom", "_imageId", "imageRatio", "_offsetY", "_collide", "_cdTime", "_hpRatio", "_shieldEntity", "_offset", "ZERO", "_initHp", "_timeCount", "init", "enemyEntity", "attrData", "params", "param", "split", "Number", "stringToNumber", "getMaxHp", "reset", "updateGameLogic", "deltaTime", "isDead", "node", "active", "playShowAnim", "position", "add", "die", "stopAllAnim", "enemyManager", "removeShield", "addShield", "parent", "me", "enemyEffectLayer", "showAttrShield"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;AAASA,MAAAA,U,OAAAA,U;AAAYC,MAAAA,I,OAAAA,I;;AACdC,MAAAA,gB;;AACEC,MAAAA,K,iBAAAA,K;;AACAC,MAAAA,O,iBAAAA,O;;AACFC,MAAAA,W;;;;;;;;;OAED;AAAEC,QAAAA,OAAF;AAAWC,QAAAA;AAAX,O,GAAwBP,U;;yBAGTQ,kB,WADpBF,OAAO,CAAC,oBAAD,C,gBAAR,MACqBE,kBADrB;AAAA;AAAA,gDACiE;AAAA;AAAA;AAAA,eAE7DC,QAF6D,GAElD,CAFkD;AAE/C;AAF+C,eAG7DC,UAH6D,GAGhD,CAHgD;AAG7C;AAH6C,eAI7DC,QAJ6D,GAIlD,CAJkD;AAI/C;AAJ+C,eAK7DC,QAL6D,GAKlD,EALkD;AAK9C;AAL8C,eAM7DC,OAN6D,GAMnD,CANmD;AAMhD;AANgD,eAO7DC,QAP6D,GAOlD,CAPkD;AAO/C;AAP+C,eAQ7DC,aAR6D,GAQ7C,IAR6C;AAQvC;AARuC,eAS7DC,OAT6D,GASnDf,IAAI,CAACgB,IAT8C;AASxC;AATwC,eAU7DC,OAV6D,GAUnD,CAVmD;AAUhD;AAVgD,eAW7DC,UAX6D,GAWhD,CAXgD;AAAA;;AAW7C;;AAEhB;AACJ;AACA;AACA;AACA;AACIC,QAAAA,IAAI,CAACC,WAAD,EAAcC,QAAd,EAAwB;AACxB,eAAKD,WAAL,GAAmBA,WAAnB;AACA,eAAKC,QAAL,GAAgBA,QAAhB;AAEA,cAAMC,MAAM,GAAGD,QAAQ,CAACE,KAAT,CAAeC,KAAf,CAAqB,GAArB,CAAf;AACA,eAAKhB,QAAL,GAAgBiB,MAAM,CAACH,MAAM,CAAC,CAAD,CAAP,CAAtB;AACA,eAAKb,UAAL,GAAkBgB,MAAM,CAACH,MAAM,CAAC,CAAD,CAAP,CAAxB;AACA,eAAKZ,QAAL,GAAgBe,MAAM,CAACH,MAAM,CAAC,CAAD,CAAP,CAAtB;AACA,eAAKX,QAAL,GAAgB;AAAA;AAAA,8BAAMe,cAAN,CAAqBJ,MAAM,CAAC,CAAD,CAA3B,EAAgC,GAAhC,CAAhB;AACA,eAAKV,OAAL,GAAea,MAAM,CAACH,MAAM,CAAC,CAAD,CAAP,CAArB;AACA,eAAKT,QAAL,GAAgBY,MAAM,CAACH,MAAM,CAAC,CAAD,CAAP,CAAtB;AAEA,eAAKP,OAAL,GAAe,IAAIf,IAAJ,CAAS,CAAT,EAAY,KAAKU,QAAjB,CAAf;AACA,eAAKO,OAAL,GAAe,KAAKG,WAAL,CAAiBO,QAAjB,KAA8B,KAAKd,QAAlD;AAEA,eAAKe,KAAL;AACH;AAED;AACJ;AACA;;;AACIA,QAAAA,KAAK,GAAG;AACJ,eAAKV,UAAL,GAAkB,CAAlB;AACH;AAED;AACJ;AACA;AACA;;;AACIW,QAAAA,eAAe,CAACC,SAAD,EAAY;AACvB,cAAI,KAAKhB,aAAT,EAAwB;AACpB,iBAAKA,aAAL,CAAmBe,eAAnB,CAAmCC,SAAnC;;AAEA,gBAAI,KAAKhB,aAAL,CAAmBiB,MAAvB,EAA+B;AAC3B,mBAAKb,UAAL,IAAmBY,SAAnB;;AACA,kBAAI,KAAKZ,UAAL,IAAmB,KAAKN,OAA5B,EAAqC;AACjC,qBAAKM,UAAL,GAAkB,CAAlB;;AACA,qBAAKJ,aAAL,CAAmBK,IAAnB,CACI,KAAKX,QADT,EAEI,KAAKC,UAFT,EAGI,KAAKQ,OAHT,EAII,CAAC,CAAD,EAAI,CAAJ,EAAO,KAAKN,QAAL,CAAc,CAAd,CAAP,EAAyB,KAAKA,QAAL,CAAc,CAAd,CAAzB,EAA2C,KAAKA,QAAL,CAAc,CAAd,CAA3C,CAJJ;;AAMA,qBAAKG,aAAL,CAAmBkB,IAAnB,CAAwBC,MAAxB,GAAiC,IAAjC;;AACA,qBAAKnB,aAAL,CAAmBoB,YAAnB;AACH;AACJ,aAbD,MAaO;AACH,mBAAKpB,aAAL,CAAmBkB,IAAnB,CAAwBG,QAAxB,GAAmC,KAAKf,WAAL,CAAiBY,IAAjB,CAAsBG,QAAtB,CAA+BC,GAA/B,CAAmC,KAAKrB,OAAxC,CAAnC;AACH;AACJ;AACJ;AAED;AACJ;AACA;;;AACIsB,QAAAA,GAAG,GAAG;AACF,cAAI,KAAKvB,aAAT,EAAwB;AACpB,iBAAKA,aAAL,CAAmBwB,WAAnB;;AACA;AAAA;AAAA,oCAAQC,YAAR,CAAqBC,YAArB,CAAkC,KAAK1B,aAAvC;AACA,iBAAKA,aAAL,GAAqB,IAArB;AACH;AACJ;AAED;AACJ;AACA;;;AACI2B,QAAAA,SAAS,GAAG;AACR,cAAI,CAAC,KAAK3B,aAAV,EAAyB;AACrB,iBAAKA,aAAL,GAAqB;AAAA;AAAA,oCAAQyB,YAAR,CAAqBE,SAArB,EAArB;;AACA,iBAAK3B,aAAL,CAAmBK,IAAnB,CACI,KAAKX,QADT,EAEI,KAAKC,UAFT,EAGI,KAAKQ,OAHT,EAII,CAAC,CAAD,EAAI,CAAJ,EAAO,KAAKN,QAAL,CAAc,CAAd,CAAP,EAAyB,KAAKA,QAAL,CAAc,CAAd,CAAzB,EAA2C,KAAKA,QAAL,CAAc,CAAd,CAA3C,CAJJ;;AAMA,iBAAKG,aAAL,CAAmBkB,IAAnB,CAAwBU,MAAxB,GAAiC;AAAA;AAAA,4CAAYC,EAAZ,CAAeC,gBAAhD;AACA,iBAAK9B,aAAL,CAAmBkB,IAAnB,CAAwBG,QAAxB,GAAmC,KAAKf,WAAL,CAAiBY,IAAjB,CAAsBG,QAAtB,CAA+BC,GAA/B,CAAmC,KAAKrB,OAAxC,CAAnC;AACH;;AACD,eAAKD,aAAL,CAAmBoB,YAAnB;AACH;AAED;AACJ;AACA;;;AACIW,QAAAA,cAAc,GAAG;AACb,eAAKJ,SAAL;AACH;;AAxG4D,O", "sourcesContent": ["import { _decorator, Vec2 } from 'cc';\r\nimport EnemyAttrBaseCom from './EnemyAttrBaseCom';\r\nimport { Tools } from '../../../utils/Tools';\r\nimport { GameIns } from '../../../GameIns';\r\nimport BattleLayer from '../../layer/BattleLayer';\r\n\r\nconst { ccclass, property } = _decorator;\r\n\r\n@ccclass('EnemyAttrShieldCom')\r\nexport default class EnemyAttrShieldCom extends EnemyAttrBaseCom {\r\n\r\n    _imageId = 1; // 护盾图片 ID\r\n    imageRatio = 0; // 护盾缩放比例\r\n    _offsetY = 0; // 护盾的 Y 偏移量\r\n    _collide = []; // 碰撞数据\r\n    _cdTime = 0; // 护盾冷却时间\r\n    _hpRatio = 0; // 护盾血量比例\r\n    _shieldEntity = null; // 护盾实体\r\n    _offset = Vec2.ZERO; // 护盾偏移量\r\n    _initHp = 0; // 护盾初始血量\r\n    _timeCount = 0; // 冷却计时器\r\n\r\n    /**\r\n     * 初始化护盾属性组件\r\n     * @param {Object} enemyEntity 敌人实体\r\n     * @param {Object} attrData 属性数据\r\n     */\r\n    init(enemyEntity, attrData) {\r\n        this.enemyEntity = enemyEntity;\r\n        this.attrData = attrData;\r\n\r\n        const params = attrData.param.split('*');\r\n        this._imageId = Number(params[0]);\r\n        this.imageRatio = Number(params[1]);\r\n        this._offsetY = Number(params[2]);\r\n        this._collide = Tools.stringToNumber(params[3], ',');\r\n        this._cdTime = Number(params[4]);\r\n        this._hpRatio = Number(params[5]);\r\n\r\n        this._offset = new Vec2(0, this._offsetY);\r\n        this._initHp = this.enemyEntity.getMaxHp() * this._hpRatio;\r\n\r\n        this.reset();\r\n    }\r\n\r\n    /**\r\n     * 重置护盾状态\r\n     */\r\n    reset() {\r\n        this._timeCount = 0;\r\n    }\r\n\r\n    /**\r\n     * 更新游戏逻辑\r\n     * @param {number} deltaTime 帧间隔时间\r\n     */\r\n    updateGameLogic(deltaTime) {\r\n        if (this._shieldEntity) {\r\n            this._shieldEntity.updateGameLogic(deltaTime);\r\n\r\n            if (this._shieldEntity.isDead) {\r\n                this._timeCount += deltaTime;\r\n                if (this._timeCount >= this._cdTime) {\r\n                    this._timeCount = 0;\r\n                    this._shieldEntity.init(\r\n                        this._imageId,\r\n                        this.imageRatio,\r\n                        this._initHp,\r\n                        [1, 0, this._collide[0], this._collide[1], this._collide[2]]\r\n                    );\r\n                    this._shieldEntity.node.active = true;\r\n                    this._shieldEntity.playShowAnim();\r\n                }\r\n            } else {\r\n                this._shieldEntity.node.position = this.enemyEntity.node.position.add(this._offset);\r\n            }\r\n        }\r\n    }\r\n\r\n    /**\r\n     * 处理护盾死亡逻辑\r\n     */\r\n    die() {\r\n        if (this._shieldEntity) {\r\n            this._shieldEntity.stopAllAnim();\r\n            GameIns.enemyManager.removeShield(this._shieldEntity);\r\n            this._shieldEntity = null;\r\n        }\r\n    }\r\n\r\n    /**\r\n     * 添加护盾\r\n     */\r\n    addShield() {\r\n        if (!this._shieldEntity) {\r\n            this._shieldEntity = GameIns.enemyManager.addShield();\r\n            this._shieldEntity.init(\r\n                this._imageId,\r\n                this.imageRatio,\r\n                this._initHp,\r\n                [1, 0, this._collide[0], this._collide[1], this._collide[2]]\r\n            );\r\n            this._shieldEntity.node.parent = BattleLayer.me.enemyEffectLayer;\r\n            this._shieldEntity.node.position = this.enemyEntity.node.position.add(this._offset);\r\n        }\r\n        this._shieldEntity.playShowAnim();\r\n    }\r\n\r\n    /**\r\n     * 显示护盾属性\r\n     */\r\n    showAttrShield() {\r\n        this.addShield();\r\n    }\r\n}"]}