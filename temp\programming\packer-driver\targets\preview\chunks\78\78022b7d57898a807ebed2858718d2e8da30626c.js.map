{"version": 3, "sources": ["file:///E:/M2Game/Client/assets/scripts/Game/ui/base/NodeMove.ts"], "names": ["_decorator", "Component", "UITransform", "view", "GameConst", "GameIns", "GameEnum", "GameMapRun", "ccclass", "property", "NodeMove", "moveSpeed", "sideswaySpeed", "layer", "setData", "xSpeed", "ySpeed", "update", "deltaTime", "GameAble", "gameState", "gameRuleManager", "GameState", "Battle", "<PERSON><PERSON><PERSON>", "Ready", "WillOver", "Over", "posX", "node", "position", "x", "posY", "y", "setPosition", "layerData", "instance", "LayerData", "get", "getComponent", "height", "ViewBot", "width", "getVisibleSize"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;AAASA,MAAAA,U,OAAAA,U;AAAYC,MAAAA,S,OAAAA,S;AAAmCC,MAAAA,W,OAAAA,W;AAAaC,MAAAA,I,OAAAA,I;;AAC5DC,MAAAA,S,iBAAAA,S;;AACAC,MAAAA,O,iBAAAA,O;;AACFC,MAAAA,Q;;AACAC,MAAAA,U;;;;;;;;;OAED;AAAEC,QAAAA,OAAF;AAAWC,QAAAA;AAAX,O,GAAwBT,U;;yBAGTU,Q,WADpBF,OAAO,CAAC,UAAD,C,gBAAR,MACqBE,QADrB,SACsCT,SADtC,CACgD;AAAA;AAAA;AAAA,eACpCU,SADoC,GAChB,CADgB;AACP;AADO,eAEpCC,aAFoC,GAEZ,CAFY;AAER;AAFQ,eAGpCC,KAHoC,GAGpB,CAHoB;AAAA;;AAGR;;AAEpC;AACJ;AACA;AACA;AACA;AACA;AACIC,QAAAA,OAAO,CAACC,MAAD,EAAiBC,MAAjB,EAAiCH,KAAjC,EAAsD;AACzD,eAAKD,aAAL,GAAqBG,MAArB;AACA,eAAKJ,SAAL,GAAiBK,MAAjB;AACA,eAAKH,KAAL,GAAaA,KAAb;AACH;AAED;AACJ;AACA;AACA;;;AACII,QAAAA,MAAM,CAACC,SAAD,EAA0B;AAC5B,cAAI,CAAC;AAAA;AAAA,sCAAUC,QAAf,EAAyB;AACrB;AACH,WAH2B,CAK5B;;;AACA,cAAID,SAAS,GAAG,GAAhB,EAAqB;AACjBA,YAAAA,SAAS,GAAG,iBAAZ,CADiB,CACc;AAClC;;AAED,cAAME,SAAS,GAAG;AAAA;AAAA,kCAAQC,eAAR,CAAwBD,SAA1C;;AACA,cACIA,SAAS,KAAK;AAAA;AAAA,oCAASE,SAAT,CAAmBC,MAAjC,IACAH,SAAS,KAAK;AAAA;AAAA,oCAASE,SAAT,CAAmBE,MADjC,IAEAJ,SAAS,KAAK;AAAA;AAAA,oCAASE,SAAT,CAAmBG,KAFjC,IAGAL,SAAS,KAAK;AAAA;AAAA,oCAASE,SAAT,CAAmBI,QAHjC,IAIAN,SAAS,KAAK;AAAA;AAAA,oCAASE,SAAT,CAAmBK,IALrC,EAME;AACE;AACA,gBAAIC,IAAI,GAAG,KAAKC,IAAL,CAAUC,QAAV,CAAmBC,CAAnB,GAAuBb,SAAS,GAAG,KAAKP,SAAnD;AACA,gBAAIqB,IAAI,GAAG,KAAKH,IAAL,CAAUC,QAAV,CAAmBG,CAAnB,GAAuBf,SAAS,GAAG,KAAKN,aAAnD;AACA,iBAAKiB,IAAL,CAAUK,WAAV,CAAsBN,IAAtB,EAA4BI,IAA5B,EAJF,CAME;;AACA,gBAAMG,SAAS,GAAG;AAAA;AAAA,0CAAWC,QAAX,CAAoBC,SAApB,CAA8BC,GAA9B,CAAkC,KAAKzB,KAAvC,CAAlB;;AACA,gBAAI,KAAKgB,IAAL,CAAUI,CAAV,GAAc,KAAKJ,IAAL,CAAUU,YAAV,CAAuBrC,WAAvB,EAAoCsC,MAAlD,GAA2DL,SAAS,CAACM,OAAzE,EAAkF,CAC9E;AACA;AACA;AACA;AACH,aALD,MAKO,IACH,KAAKZ,IAAL,CAAUC,QAAV,CAAmBC,CAAnB,GAAuB,KAAKF,IAAL,CAAUU,YAAV,CAAuBrC,WAAvB,EAAoCwC,KAA3D,GAAmE,CAACvC,IAAI,CAACwC,cAAL,GAAsBD,KAAvB,GAA+B,CAA/B,GAAmC,GAAtG,IACA,KAAKb,IAAL,CAAUC,QAAV,CAAmBC,CAAnB,GAAuB5B,IAAI,CAACwC,cAAL,GAAsBD,KAAtB,GAA8B,CAA9B,GAAkC,GAFtD,EAGL,CACE;AACH;AAGJ;AACJ;;AA5D2C,O", "sourcesContent": ["import { _decorator, Component, Sprite, tween, isValid, UITransform, view } from 'cc';\r\nimport { GameConst } from '../../const/GameConst';\r\nimport { GameIns } from '../../GameIns';\r\nimport GameEnum from '../../const/GameEnum';\r\nimport GameMapRun from '../map/GameMapRun';\r\n\r\nconst { ccclass, property } = _decorator;\r\n\r\n@ccclass('NodeMove')\r\nexport default class NodeMove extends Component {\r\n    private moveSpeed: number = 0;       // 垂直移动速度\r\n    private sideswaySpeed: number = 0;  // 水平移动速度\r\n    private layer: number = 0;          // 节点所属图层\r\n\r\n    /**\r\n     * 设置节点的移动数据\r\n     * @param xSpeed 水平移动速度\r\n     * @param ySpeed 垂直移动速度\r\n     * @param layer 节点所属图层\r\n     */\r\n    setData(xSpeed: number, ySpeed: number, layer: number): void {\r\n        this.sideswaySpeed = xSpeed;\r\n        this.moveSpeed = ySpeed;\r\n        this.layer = layer;\r\n    }\r\n\r\n    /**\r\n     * 每帧更新节点的位置\r\n     * @param deltaTime 时间增量\r\n     */\r\n    update(deltaTime: number): void {\r\n        if (!GameConst.GameAble) {\r\n            return;\r\n        }\r\n\r\n        // 限制 deltaTime 的最大值\r\n        if (deltaTime > 0.2) {\r\n            deltaTime = 0.016666666666667; // 约等于 1/60 秒\r\n        }\r\n\r\n        const gameState = GameIns.gameRuleManager.gameState;\r\n        if (\r\n            gameState === GameEnum.GameState.Battle ||\r\n            gameState === GameEnum.GameState.Sortie ||\r\n            gameState === GameEnum.GameState.Ready ||\r\n            gameState === GameEnum.GameState.WillOver ||\r\n            gameState === GameEnum.GameState.Over\r\n        ) {\r\n            // 更新节点位置\r\n            let posX = this.node.position.x - deltaTime * this.moveSpeed;\r\n            let posY = this.node.position.y + deltaTime * this.sideswaySpeed;\r\n            this.node.setPosition(posX, posY);\r\n\r\n            // 检查节点是否超出视图范围\r\n            const layerData = GameMapRun.instance.LayerData.get(this.layer);\r\n            if (this.node.y + this.node.getComponent(UITransform).height < layerData.ViewBot) {\r\n                // const enemyComponent = this.getComponent(EnemyBuild);\r\n                // if (enemyComponent) {\r\n                //     EnemyManager.EnemyMgr.removePlane(enemyComponent);\r\n                // }\r\n            } else if (\r\n                this.node.position.x + this.node.getComponent(UITransform).width < -view.getVisibleSize().width / 2 - 500 ||\r\n                this.node.position.x > view.getVisibleSize().width / 2 + 500\r\n            ) {\r\n                // 节点超出屏幕宽度范围\r\n            }\r\n\r\n\r\n        }\r\n    }\r\n}"]}