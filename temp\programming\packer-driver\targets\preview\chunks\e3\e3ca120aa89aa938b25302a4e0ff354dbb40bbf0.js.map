{"version": 3, "sources": ["file:///E:/M2Game/Client/assets/scripts/Game/ui/plane/mainPlane/FireShells.ts"], "names": ["_decorator", "GameConst", "MainPlane", "Entity", "ColliderComp", "GameIns", "CircleScreen", "GameEnum", "ccclass", "property", "FireShells", "m_enemy", "m_screen", "m_inGroup", "m_groupTime", "m_coolTime", "m_groupNum", "m_wait<PERSON>rame", "sceneEntity", "coolTime", "m_isMainPlane", "m_updateStrict", "m_batSkill", "test", "count", "countTime", "state", "props", "m_danmuConfig", "setData", "data", "enemy", "length", "defaultProps", "x", "y", "cooltime", "screenId", "screenNum", "screenTime", "attack", "wait", "m_config", "id", "node", "setPosition", "bulletManager", "getConfig", "getScreenComp", "init", "setState", "clearData", "bustyle", "<PERSON><PERSON><PERSON><PERSON>", "target", "collider", "getComp", "isStrict", "isMainPlane", "key", "attackValue", "multiplier", "bonus", "hpAttackLv", "hpBonus", "skillManager", "getValueOfNumber", "mainPlaneManager", "hp", "maxhp", "doubleSkillMap", "get", "attackLv", "attackMultiplier", "intensifyAtk", "m_data", "trueAtk", "laserSpeedUp", "setBulletState", "isFollowBullet", "bulletType", "fire", "toFire", "setCoolTime", "update", "deltaTime", "GameAble", "gameRuleManager", "isInBattle", "active", "gameState", "GameState", "Battle", "isGameWillOver", "swordBullet", "fireEnable", "addCirt", "level", "changeScreenData", "value", "onChangeData", "getType", "removeBullet", "getScreen"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAASA,MAAAA,U,OAAAA,U;;AACAC,MAAAA,S,iBAAAA,S;;AACAC,MAAAA,S,iBAAAA,S;;AACFC,MAAAA,M;;AACEC,MAAAA,Y,iBAAAA,Y;;AACAC,MAAAA,O,iBAAAA,O;;AACFC,MAAAA,Y;;AACAC,MAAAA,Q;;;;;;;;;OAED;AAAEC,QAAAA,OAAF;AAAWC,QAAAA;AAAX,O,GAAwBT,U;;yBAGTU,U,WADpBF,OAAO,CAAC,YAAD,C,gBAAR,MACqBE,UADrB;AAAA;AAAA,4BAC+C;AAAA;AAAA;AAAA,eACnCC,OADmC,GAChB,IADgB;AAAA,eAEnCC,QAFmC,GAEnB,IAFmB;AAAA,eAGnCC,SAHmC,GAGd,IAHc;AAAA,eAInCC,WAJmC,GAIb,CAJa;AAAA,eAKnCC,UALmC,GAKd,CALc;AAAA,eAMnCC,UANmC,GAMd,CANc;AAAA,eAOnCC,WAPmC,GAOb,CAPa;AAAA,eAQnCC,WARmC,GAQhB,IARgB;AAAA,eASnCC,QATmC,GAShB,CATgB;AAAA,eAUnCC,aAVmC,GAUV,KAVU;AAAA,eAWnCC,cAXmC,GAWT,KAXS;AAAA,eAYnCC,UAZmC,GAYb,KAZa;AAAA,eAanCC,IAbmC,GAanB,KAbmB;AAAA,eAcnCC,KAdmC,GAcnB,CAdmB;AAAA,eAenCC,SAfmC,GAef,CAfe;AAAA,eAgBnCC,KAhBmC,GAgBtB,EAhBsB;AAAA,eAiBnCC,KAjBmC,GAiBtB,EAjBsB;AAAA,eAkBnCC,aAlBmC,GAkBd,IAlBc;AAAA;;AAoB3C;AACJ;AACA;AACA;AACA;AACA;AACA;AACIC,QAAAA,OAAO,CAACC,IAAD,EAAcJ,KAAd,EAA0BK,KAA1B,EAAsCb,WAAtC,EAA8D;AACjE,cAAI,CAACY,IAAD,IAASA,IAAI,CAACE,MAAL,KAAgB,CAA7B,EAAgC;AAC5B,gBAAMC,YAAY,GAAG,CAAC,CAAD,EAAI,CAAJ,EAAO,CAAP,EAAU,CAAV,EAAa,CAAb,EAAgB,CAAhB,EAAmB,CAAnB,EAAsB,CAAtB,CAArB;AACA,iBAAKN,KAAL,GAAa;AACTO,cAAAA,CAAC,EAAED,YAAY,CAAC,CAAD,CADN;AAETE,cAAAA,CAAC,EAAEF,YAAY,CAAC,CAAD,CAFN;AAGTG,cAAAA,QAAQ,EAAEH,YAAY,CAAC,CAAD,CAHb;AAITI,cAAAA,QAAQ,EAAEJ,YAAY,CAAC,CAAD,CAJb;AAKTK,cAAAA,SAAS,EAAEL,YAAY,CAAC,CAAD,CALd;AAMTM,cAAAA,UAAU,EAAEN,YAAY,CAAC,CAAD,CANf;AAOTO,cAAAA,MAAM,EAAEP,YAAY,CAAC,CAAD,CAPX;AAQTQ,cAAAA,IAAI,EAAER,YAAY,CAAC,CAAD;AART,aAAb,CAF4B,CAY5B;AACA;AACA;;AACA;AACH;;AAED,eAAKP,KAAL,GAAa;AAAEc,YAAAA,MAAM,EAAE;AAAV,WAAb;AACA,eAAKtB,WAAL,GAAmBA,WAAnB;AACA,eAAKE,aAAL,GAAqB,KAArB;AACA,eAAKC,cAAL,GAAsB,KAAtB;;AAEA,cAAI,KAAKH,WAAL,IAAoB,KAAKA,WAAL;AAAA;AAAA,qCAAxB,EAA+D;AAC3D,iBAAKE,aAAL,GAAqB,IAArB;;AACA,gBAAI,KAAKF,WAAL,CAAiBwB,QAAjB,KAA8B,KAAKxB,WAAL,CAAiBwB,QAAjB,CAA0BC,EAA1B,KAAiC,GAAjC,IAAwC,KAAKzB,WAAL,CAAiBwB,QAAjB,CAA0BC,EAA1B,KAAiC,GAAvG,CAAJ,EAAiH;AAC7G,mBAAKtB,cAAL,GAAsB,IAAtB;AACH;AACJ;;AAED,cAAM,CAACa,CAAD,EAAIC,CAAJ,EAAOC,QAAP,EAAiBC,QAAjB,EAA2BC,SAA3B,EAAsCC,UAAtC,EAAkDC,MAAlD,EAA0DC,IAA1D,IAAkEX,IAAxE;AACA,eAAKH,KAAL,GAAa;AACTO,YAAAA,CADS;AAETC,YAAAA,CAFS;AAGTC,YAAAA,QAHS;AAITC,YAAAA,QAJS;AAKTC,YAAAA,SALS;AAMTC,YAAAA,UANS;AAOTC,YAAAA,MAPS;AAQTC,YAAAA,IAAI,EAAEA,IAAI,IAAI;AARL,WAAb;AAWA,eAAKG,IAAL,CAAUC,WAAV,CAAsBX,CAAtB,EAAyBC,CAAzB;AACA,eAAKxB,OAAL,GAAeoB,KAAf;;AAEA,cAAI,KAAKJ,KAAL,CAAWU,QAAX,KAAwB,CAA5B,EAA+B;AAC3B,iBAAKlB,QAAL,GAAgB,KAAKQ,KAAL,CAAWS,QAA3B;AACA,iBAAKR,aAAL,GAAqB;AAAA;AAAA,oCAAQkB,aAAR,CAAsBC,SAAtB,CAAgC,KAAKpB,KAAL,CAAWU,QAA3C,CAArB;AACA,iBAAKzB,QAAL,GAAgB,KAAKoC,aAAL,EAAhB;AACA,iBAAKpC,QAAL,CAAcqC,IAAd,CAAmB,IAAnB;AACA,iBAAKC,QAAL,CAAcxB,KAAd,EAAqBR,WAArB;AACA,iBAAKiC,SAAL;AACA,iBAAK7B,UAAL,GAAkB,KAAKM,aAAL,CAAmBwB,OAAnB,KAA+B,EAAjD;AACH;AACJ;AAED;AACJ;AACA;AACA;AACA;;;AACIC,QAAAA,SAAS,CAACC,MAAD,EAAcC,QAAd,EAA0C;AAAA,cAA5BA,QAA4B;AAA5BA,YAAAA,QAA4B,GAAZ,IAAY;AAAA;;AAC/C,cAAID,MAAM,KAAK,IAAX,IAAmBC,QAAQ,KAAK,IAApC,EAA0C;AACtCA,YAAAA,QAAQ,GAAGD,MAAM,CAACE,OAAP;AAAA;AAAA,6CAAX;AACH,WAH8C,CAI/C;AACA;AACA;;AACH;AAED;AACJ;AACA;;;AACIL,QAAAA,SAAS,GAAS;AACd,eAAKtC,SAAL,GAAiB,IAAjB;AACA,eAAKC,WAAL,GAAmB,CAAnB;AACA,eAAKC,UAAL,GAAkB,CAAlB;AACA,eAAKC,UAAL,GAAkB,CAAlB;AACA,eAAKC,WAAL,GAAmB,CAAnB;AACH;AAED;AACJ;AACA;AACA;AACA;AACA;AACA;;;AACIiC,QAAAA,QAAQ,CAACxB,KAAD,EAAaR,WAAb,EAA+BuC,QAA/B,EAA0DC,WAA1D,EAA8F;AAAA,cAA/DD,QAA+D;AAA/DA,YAAAA,QAA+D,GAA3C,KAA2C;AAAA;;AAAA,cAApCC,WAAoC;AAApCA,YAAAA,WAAoC,GAAb,KAAa;AAAA;;AAClG,eAAK,IAAMC,GAAX,IAAkBjC,KAAlB,EAAyB;AACrB,iBAAKA,KAAL,CAAWiC,GAAX,IAAkBjC,KAAK,CAACiC,GAAD,CAAvB;;AACA,oBAAQA,GAAR;AACI,mBAAK,QAAL;AACA,mBAAK,UAAL;AACA,mBAAK,YAAL;AACI,oBAAIC,WAAW,GAAG,CAAlB,CADJ,CAEI;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AACI,oBAAIC,UAAU,GAAG,CAAjB;AACA,oBAAIC,KAAK,GAAG,CAAZ;;AACA,oBAAI5C,WAAW;AAAA;AAAA,2CAAf,EAAsC;AAClC,sBAAM6C,UAAU,GAAG,KAAKrC,KAAL,CAAWqC,UAAX,IAAyB,CAA5C;AACA,sBAAMC,OAAO,GAAG;AAAA;AAAA,0CAAQC,YAAR,CAAqBC,gBAArB,CAAsCH,UAAtC,EAAkD,CAAlD,CAAhB;AACAF,kBAAAA,UAAU,IAAI,CAAC,IAAI;AAAA;AAAA,0CAAQM,gBAAR,CAAyBrC,IAAzB,CAA8BsC,EAA9B,GAAmC;AAAA;AAAA,0CAAQD,gBAAR,CAAyBrC,IAAzB,CAA8BuC,KAAtE,IAA+EL,OAA7F;AACAF,kBAAAA,KAAK,GAAG;AAAA;AAAA,0CAAQG,YAAR,CAAqBK,cAArB,CAAoCC,GAApC,CAAwC,CAAxC,KAA8C,CAAtD;AACH;;AACD,oBAAMC,QAAQ,GAAG,KAAK9C,KAAL,CAAW8C,QAAX,IAAuB,CAAxC;AACA,oBAAMC,gBAAgB,GAAG;AAAA;AAAA,wCAAQR,YAAR,CAAqBC,gBAArB,CAAsCM,QAAtC,EAAgD,CAAhD,EAAmDf,QAAnD,CAAzB;AACAG,gBAAAA,WAAW,GAAG,KAAKlC,KAAL,CAAWc,MAAX,GAAoB,KAAKb,KAAL,CAAWa,MAA/B,GAAwC,GAAxC,IAA+CiC,gBAAgB,GAAGX,KAAlE,IAA2ED,UAAzF;AACAD,gBAAAA,WAAW,IAAIA,WAAW,IAAI;AAAA;AAAA,wCAAQO,gBAAR,CAAyBrC,IAAzB,CAA8B4C,YAA9B,CAA2C,CAA3C,KAAiD,CAArD,CAA1B;;AACA,oBAAIxD,WAAW;AAAA;AAAA,2CAAf,EAAsC;AAClCA,kBAAAA,WAAW,CAACyD,MAAZ,CAAmBC,OAAnB,GAA6BhB,WAA7B;AACH;;AACD,oBAAMiB,YAAY,GAAG;AAAA;AAAA,wCAAQV,gBAAR,CAAyBrC,IAAzB,CAA8B+C,YAA9B,GAA6C,GAA7C,IAAoD,CAAzE;AACAjB,gBAAAA,WAAW,IAAI,KAAKlC,KAAL,CAAWc,MAAX,GAAoBqC,YAAnC,CA5BR,CA6BI;;AACA,qBAAKjE,QAAL,CAAckE,cAAd,CAA6B;AAAEtC,kBAAAA,MAAM,EAAEoB;AAAV,iBAA7B,EAAsD,KAAK1C,WAA3D;AACA;AAEZ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AAEA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AAEA;AACA;AACA;AAxGI;AA0GH;AACJ;AACD;AACJ;AACA;AACA;AACA;;;AACyB,eAAd6D,cAAc,CAACC,UAAD,EAA8B;AAC/C,iBAAOA,UAAU,KAAK,EAAf,IAAqBA,UAAU,KAAK,EAA3C;AACH;AAED;AACJ;AACA;AACA;;;AACIhC,QAAAA,aAAa,GAAQ;AACjB,kBAAQ,KAAKpB,aAAL,CAAmBwB,OAA3B;AACI;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,iBAAK,CAAL;AACA,iBAAK,EAAL;AACA,iBAAK,EAAL;AACI,qBAAO;AAAA;AAAA,gDAAiB,KAAKzB,KAAL,CAAWU,QAA5B,EAAsC,KAAK1B,OAA3C,CAAP;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAzEJ;AA2EH;AAED;AACJ;AACA;;;AACIsE,QAAAA,IAAI,GAAS;AACT,eAAKrE,QAAL,CAAcsE,MAAd,CAAqB,KAAKlE,UAA1B,EADS,CAGT;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACH;AAED;AACJ;AACA;AACA;;;AACImE,QAAAA,WAAW,CAAChE,QAAD,EAAyB;AAChC,cAAIA,QAAQ,GAAG,CAAf,EAAkB;AACd,iBAAKA,QAAL,GAAgB,IAAhB;AACH,WAFD,MAEO;AACH,iBAAKJ,UAAL,GAAkB,CAAlB;AACA,iBAAKI,QAAL,GAAgB,KAAKQ,KAAL,CAAWS,QAA3B;AACH;AACJ;AAED;AACJ;AACA;AACA;;;AACIgD,QAAAA,MAAM,CAACC,SAAD,EAA0B;AAC5B;AACA,cAAI,CAAC;AAAA;AAAA,sCAAUC,QAAf,EAAyB;AACrB;AACH,WAJ2B,CAM5B;;;AACA,cAAI,CAAC;AAAA;AAAA,kCAAQC,eAAR,CAAwBC,UAAxB,EAAD,KAA0C,CAAC,KAAKtE,WAAN,IAAqB,CAAC,KAAKA,WAAL,CAAiB0B,IAAvC,IAA+C,CAAC,KAAK1B,WAAL,CAAiB0B,IAAjB,CAAsB6C,MAAhH,KAA2H,CAAC,KAAKnE,UAArI,EAAiJ;AAC7I;AACH,WAT2B,CAW5B;;;AACA,cAAI+D,SAAS,GAAG,GAAhB,EAAqB;AACjBA,YAAAA,SAAS,GAAG,iBAAZ,CADiB,CACc;AAClC,WAd2B,CAgB5B;;;AACA,cAAI,KAAKhE,cAAL,IAAuBgE,SAAS,GAAG,iBAAvC,EAA0D;AACtDA,YAAAA,SAAS,GAAG,iBAAZ;AACH,WAnB2B,CAqB5B;AACA;AACA;AACA;AAEA;;;AACA,cAAI,CAAC,KAAKnE,WAAV,EAAuB;AACnB,gBAAI,KAAKD,WAAL,GAAmB,KAAKU,KAAL,CAAWc,IAAlC,EAAwC;AACpC,mBAAKxB,WAAL;AACA;AACH;AACJ,WAhC2B,CAkC5B;;;AACA,cAAI;AAAA;AAAA,kCAAQsE,eAAR,CAAwBG,SAAxB,KAAsC;AAAA;AAAA,oCAASC,SAAT,CAAmBC,MAA7D,EAAqE;AACjE,gBAAI;AAAA;AAAA,oCAAQL,eAAR,CAAwBM,cAAxB,MAA4C,KAAKjF,QAArD,EAA+D;AAC3D,mBAAKA,QAAL,CAAcwE,MAAd,CAAqBC,SAArB;AACH,aAHgE,CAKjE;;;AACA,gBAAI,CAAC,KAAKjE,aAAN,IAAuB,CAAC;AAAA;AAAA,oCAAQ+C,gBAAR,CAAyB2B,WAArD,EAAkE;AAC9D;AACH,aARgE,CAUjE;;;AACA;AAAA;AAAA,oCAAQ3B,gBAAR,CAAyB2B,WAAzB,GAAuC,KAAvC;AACA,iBAAKb,IAAL;AACA;AACH,WAjD2B,CAmD5B;;;AACA,cAAI,CAAC;AAAA;AAAA,kCAAQd,gBAAR,CAAyB4B,UAA9B,EAA0C;AACtC;AACH,WAtD2B,CAwD5B;;;AACA,cAAI,KAAKpE,KAAL,CAAWU,QAAX,KAAwB,CAA5B,EAA+B;AAC3B;AACH,WA3D2B,CA6D5B;;;AACA,cAAI,KAAKpB,WAAL,GAAmB,KAAKU,KAAL,CAAWc,IAAlC,EAAwC;AACpC,iBAAKxB,WAAL;AACA;AACH,WAjE2B,CAmE5B;;;AACA,cAAI,KAAKL,QAAT,EAAmB;AACf,iBAAKA,QAAL,CAAcwE,MAAd,CAAqBC,SAArB;AACH,WAtE2B,CAwE5B;;;AACA,cAAI,CAAC,KAAKxE,SAAN,IAAmB,KAAKc,KAAL,CAAWS,QAAX,IAAuB,CAA9C,EAAiD;AAC7C,iBAAKrB,UAAL,IAAmBsE,SAAnB;;AACA,gBAAI,KAAKtE,UAAL,IAAmB,KAAKI,QAA5B,EAAsC;AAClC,mBAAKN,SAAL,GAAiB,IAAjB;AACA,mBAAKE,UAAL,IAAmB,KAAKI,QAAxB;AACH;AACJ,WA/E2B,CAiF5B;;;AACA,cAAI,KAAKN,SAAT,EAAoB;AAChB,iBAAKC,WAAL,IAAoBuE,SAApB;;AACA,gBAAI,KAAKvE,WAAL,IAAoB,KAAKa,KAAL,CAAWY,UAAnC,EAA+C;AAC3C,mBAAK0C,IAAL;AACA,mBAAKjE,UAAL;AACA,mBAAKF,WAAL,GAAmB,CAAnB;AACH;;AAED,gBAAI,KAAKE,UAAL,IAAmB,KAAKW,KAAL,CAAWW,SAAlC,EAA6C;AACzC,mBAAKzB,SAAL,GAAiB,KAAjB;AACA,mBAAKG,UAAL,GAAkB,CAAlB;AACH;AACJ;AACJ;AAED;AACJ;AACA;AACA;AACA;;;AACIgF,QAAAA,OAAO,CAACC,KAAD,EAAqB,CACxB;AACH;AAED;AACJ;AACA;AACA;AACA;;;AACIC,QAAAA,gBAAgB,CAACvC,GAAD,EAAcwC,KAAd,EAAgC;AAC5C,cAAI,KAAKvF,QAAL,IAAiB,KAAKA,QAAL,CAAcwF,YAAnC,EAAiD;AAC7C,iBAAKxF,QAAL,CAAcwF,YAAd,CAA2BzC,GAA3B,EAAgCwC,KAAhC;AACH;AACJ;AAED;AACJ;AACA;AACA;;;AACIE,QAAAA,OAAO,GAAW;AACd,iBAAO,KAAKzE,aAAL,GAAqB,KAAKA,aAAL,CAAmBwB,OAAxC,GAAkD,CAAzD;AACH;AAED;AACJ;AACA;AACA;;;AACIkD,QAAAA,YAAY,CAACtB,UAAD,EAA2B;AACnC,cAAI,KAAKpD,aAAL,CAAmBwB,OAAnB,KAA+B4B,UAA/B,IAA6C,KAAKpE,QAAL,CAAc0F,YAA/D,EAA6E;AACzE,iBAAK1F,QAAL,CAAc0F,YAAd;AACH;AACJ;AAED;AACJ;AACA;AACA;;;AACIC,QAAAA,SAAS,GAAQ;AACb,iBAAO,KAAK3F,QAAZ;AACH;;AAlf0C,O", "sourcesContent": ["import { _decorator, Game } from 'cc';\r\nimport { GameConst } from \"../../../const/GameConst\";\r\nimport { MainPlane } from \"./MainPlane\";\r\nimport Entity from '../../base/Entity';\r\nimport { ColliderComp } from '../../base/ColliderComp';\r\nimport { GameIns } from '../../../GameIns';\r\nimport CircleScreen from '../../bulletDanmu/CircleScreen';\r\nimport GameEnum from '../../../const/GameEnum';\r\n\r\nconst { ccclass, property } = _decorator;\r\n\r\n@ccclass(\"FireShells\")\r\nexport default class FireShells extends Entity {\r\n    private m_enemy: boolean = true;\r\n    private m_screen: any = null;\r\n    private m_inGroup: boolean = true;\r\n    private m_groupTime: number = 0;\r\n    private m_coolTime: number = 0;\r\n    private m_groupNum: number = 0;\r\n    private m_waitFrame: number = 0;\r\n    private sceneEntity: any = null;\r\n    private coolTime: number = 0;\r\n    private m_isMainPlane: boolean = false;\r\n    private m_updateStrict: boolean = false;\r\n    private m_batSkill: boolean = false;\r\n    private test: boolean = false;\r\n    private count: number = 0;\r\n    private countTime: number = 0;\r\n    private state: any = {};\r\n    private props: any = {};\r\n    private m_danmuConfig: any = null;\r\n\r\n    /**\r\n     * 设置数据\r\n     * @param data 配置数据\r\n     * @param state 状态数据\r\n     * @param enemy 敌人实体\r\n     * @param sceneEntity 场景实体\r\n     */\r\n    setData(data: any[], state: any, enemy: any, sceneEntity: any): void {\r\n        if (!data || data.length === 0) {\r\n            const defaultProps = [0, 0, 0, 0, 0, 0, 0, 0];\r\n            this.props = {\r\n                x: defaultProps[0],\r\n                y: defaultProps[1],\r\n                cooltime: defaultProps[2],\r\n                screenId: defaultProps[3],\r\n                screenNum: defaultProps[4],\r\n                screenTime: defaultProps[5],\r\n                attack: defaultProps[6],\r\n                wait: defaultProps[7],\r\n            };\r\n            // if (this.m_screen && (this.m_screen instanceof LaserScreen || this.m_screen instanceof SwordScreen)) {\r\n            //     this.m_screen.removeBullet();\r\n            // }\r\n            return;\r\n        }\r\n\r\n        this.state = { attack: 0 };\r\n        this.sceneEntity = sceneEntity;\r\n        this.m_isMainPlane = false;\r\n        this.m_updateStrict = false;\r\n\r\n        if (this.sceneEntity && this.sceneEntity instanceof MainPlane) {\r\n            this.m_isMainPlane = true;\r\n            if (this.sceneEntity.m_config && (this.sceneEntity.m_config.id === 701 || this.sceneEntity.m_config.id === 703)) {\r\n                this.m_updateStrict = true;\r\n            }\r\n        }\r\n\r\n        const [x, y, cooltime, screenId, screenNum, screenTime, attack, wait] = data;\r\n        this.props = {\r\n            x,\r\n            y,\r\n            cooltime,\r\n            screenId,\r\n            screenNum,\r\n            screenTime,\r\n            attack,\r\n            wait: wait || 0,\r\n        };\r\n\r\n        this.node.setPosition(x, y);\r\n        this.m_enemy = enemy;\r\n\r\n        if (this.props.screenId !== 0) {\r\n            this.coolTime = this.props.cooltime;\r\n            this.m_danmuConfig = GameIns.bulletManager.getConfig(this.props.screenId);\r\n            this.m_screen = this.getScreenComp();\r\n            this.m_screen.init(this);\r\n            this.setState(state, sceneEntity);\r\n            this.clearData();\r\n            this.m_batSkill = this.m_danmuConfig.bustyle === 43;\r\n        }\r\n    }\r\n\r\n    /**\r\n * 设置目标\r\n * @param target 目标实体\r\n * @param collider 目标的碰撞组件\r\n */\r\n    setTarget(target: any, collider: any = null): void {\r\n        if (target !== null && collider === null) {\r\n            collider = target.getComp(ColliderComp);\r\n        }\r\n        // if (this.m_screen instanceof AimBaseScreen) {\r\n        //     this.m_screen.setTarget(target, collider);\r\n        // }\r\n    }\r\n\r\n    /**\r\n     * 清理数据\r\n     */\r\n    clearData(): void {\r\n        this.m_inGroup = true;\r\n        this.m_groupTime = 0;\r\n        this.m_coolTime = 0;\r\n        this.m_groupNum = 0;\r\n        this.m_waitFrame = 0;\r\n    }\r\n\r\n    /**\r\n * 设置状态\r\n * @param state 状态数据\r\n * @param sceneEntity 场景实体\r\n * @param isStrict 是否严格模式\r\n * @param isMainPlane 是否为主飞机\r\n */\r\n    setState(state: any, sceneEntity: any, isStrict: boolean = false, isMainPlane: boolean = false): void {\r\n        for (const key in state) {\r\n            this.state[key] = state[key];\r\n            switch (key) {\r\n                case \"attack\":\r\n                case \"attackLv\":\r\n                case \"hpAttackLv\":\r\n                    let attackValue = 0;\r\n                    // if (sceneEntity instanceof WinePlane) {\r\n                    //     const attackPromote = WinePlaneManager.me.getAttackPromote();\r\n                    //     const techPromote = WinePlaneManager.me.techPromote.get(WinePlaneManager.TurretPromote.atk);\r\n                    //     attackValue = (this.state.attack + attackPromote) * this.props.attack / 100 * (techPromote ? 1 + techPromote / 100 : 1);\r\n                    //     if (GameIns.skillManager.getLv(SkillManager.SkillType.wineAttack) > 0) {\r\n                    //         const skillLevel = GameIns.skillManager.getLv(SkillManager.SkillType.wineAttack);\r\n                    //         const skillData = GameIns.skillManager.getSkillRecord(skillLevel).skillcs;\r\n                    //         attackValue *= skillData[0];\r\n                    //     }\r\n                    // } else {\r\n                        let multiplier = 1;\r\n                        let bonus = 0;\r\n                        if (sceneEntity instanceof MainPlane) {\r\n                            const hpAttackLv = this.state.hpAttackLv || 0;\r\n                            const hpBonus = GameIns.skillManager.getValueOfNumber(hpAttackLv, 0);\r\n                            multiplier += (1 - GameIns.mainPlaneManager.data.hp / GameIns.mainPlaneManager.data.maxhp) * hpBonus;\r\n                            bonus = GameIns.skillManager.doubleSkillMap.get(2) || 0;\r\n                        }\r\n                        const attackLv = this.state.attackLv || 0;\r\n                        const attackMultiplier = GameIns.skillManager.getValueOfNumber(attackLv, 1, isStrict);\r\n                        attackValue = this.state.attack * this.props.attack / 100 * (attackMultiplier + bonus) * multiplier;\r\n                        attackValue += attackValue * (GameIns.mainPlaneManager.data.intensifyAtk[0] || 0);\r\n                        if (sceneEntity instanceof MainPlane) {\r\n                            sceneEntity.m_data.trueAtk = attackValue;\r\n                        }\r\n                        const laserSpeedUp = GameIns.mainPlaneManager.data.laserSpeedUp / 100 || 0;\r\n                        attackValue += this.state.attack * laserSpeedUp;\r\n                    // }\r\n                    this.m_screen.setBulletState({ attack: attackValue }, this.sceneEntity);\r\n                    break;\r\n\r\n        //         case \"speedLv\":\r\n        //             if (sceneEntity instanceof CopyPlane) {\r\n        //                 if (GameIns.skillManager.getLv(SkillManager.SkillType.copySpeed) > 0) {\r\n        //                     const skillLevel = GameIns.skillManager.getLv(SkillManager.SkillType.copySpeed);\r\n        //                     const skillData = GameIns.skillManager.getSkillRecord(skillLevel);\r\n        //                     this.coolTime = skillData ? this.props.cooltime * skillData.skillcs[0] : this.props.cooltime;\r\n        //                 } else {\r\n        //                     this.coolTime = this.props.cooltime;\r\n        //                 }\r\n        //             } else {\r\n        //                 let speedMultiplier = GameIns.skillManager.getValueOfNumber(this.state.speedLv, 1, isStrict);\r\n        //                 if (sceneEntity instanceof MainPlane) {\r\n        //                     const bonus = GameIns.skillManager.doubleSkillMap.get(1) || 0;\r\n        //                     speedMultiplier += bonus;\r\n        //                 }\r\n        //                 this.coolTime = speedMultiplier * this.props.cooltime;\r\n        //             }\r\n        //             break;\r\n\r\n        //         case \"through\":\r\n        //             this.m_screen.setBulletState({ through: this.state[key] }, this.sceneEntity);\r\n        //             break;\r\n\r\n        //         case \"extra\":\r\n        //             this.m_screen.setBulletState({ extra: this.state[key] }, this.sceneEntity);\r\n        //             break;\r\n\r\n        //         case \"cirtLv\":\r\n        //             let critChance, critDamage, critBonus, critMultiplier;\r\n        //             if (sceneEntity instanceof MainPlane) {\r\n        //                 critChance = GameIns.mainPlaneManager.getMainPlaneCirt();\r\n        //                 critDamage = [0, 0];\r\n        //                 critBonus = ConfigDataManager.CDMgr.getGlobalNumber(GameEnum.GBKey.InitCirtHurt);\r\n        //                 critMultiplier = GameIns.skillManager.doubleSkillMap.get(16) || 1;\r\n        //                 if (GameIns.skillManager.getLv(SkillManager.SkillType.addCirt) > 0) {\r\n        //                     critDamage = this.addCirt(GameIns.skillManager.getLv(SkillManager.SkillType.addCirt));\r\n        //                 }\r\n        //                 const critPromote = GameIns.mainPlaneManager.attributePromote.get(MainPlaneManager.attribute.cirt) || 0;\r\n        //                 const critAtkPromote = GameIns.mainPlaneManager.attributePromote.get(MainPlaneManager.attribute.cirtAtk) || 0;\r\n        //                 const critState = [\r\n        //                     critChance + critDamage[1] + critPromote / 100,\r\n        //                     critDamage[0] * critMultiplier + critAtkPromote / 100 + critBonus,\r\n        //                 ];\r\n        //                 this.m_screen.setBulletState({ cirt: critState }, this.sceneEntity);\r\n        //             } else if (sceneEntity instanceof CopyPlane) {\r\n        //                 critDamage = GameIns.skillManager.getLv(SkillManager.SkillType.addCirt) > 0\r\n        //                     ? this.addCirt(GameIns.skillManager.getLv(SkillManager.SkillType.addCirt))\r\n        //                     : [0, 0];\r\n        //                 const critState = [critDamage[1], critDamage[0]];\r\n        //                 this.m_screen.setBulletState({ cirt: critState }, this.sceneEntity);\r\n        //             }\r\n        //             break;\r\n\r\n        //         case \"catapultLv\":\r\n        //             if (sceneEntity instanceof MainPlane) {\r\n        //                 const catapultLevel = GameIns.skillManager.getLv(SkillManager.SkillType.catapult);\r\n        //                 const catapultData = catapultLevel > 0 ? GameIns.skillManager.getSkillRecord(catapultLevel).skillcs : [];\r\n        //                 this.m_screen.setBulletState({ catapult: catapultData }, this.sceneEntity);\r\n        //             }\r\n        //             break;\r\n\r\n        //         case \"atkChallenge\":\r\n        //             const atkChallenge = state.atkChallenge * this.state.attack * GameData.me.getAtkAddCount();\r\n        //             this.m_screen.setBulletState({ atkChallenge }, this.sceneEntity);\r\n        //             break;\r\n\r\n        //         case \"fireIntensify\":\r\n        //             this.m_screen.setBulletState({ fireIntensify: this.state.fireIntensify }, this.sceneEntity);\r\n        //             break;\r\n            }\r\n        }\r\n    }\r\n    /**\r\n     * 判断是否为跟随子弹\r\n     * @param bulletType 子弹类型\r\n     * @returns 是否为跟随子弹\r\n     */\r\n    static isFollowBullet(bulletType: number): boolean {\r\n        return bulletType === 22 || bulletType === 30;\r\n    }\r\n\r\n    /**\r\n     * 获取屏幕组件\r\n     * @returns 屏幕组件实例\r\n     */\r\n    getScreenComp(): any {\r\n        switch (this.m_danmuConfig.bustyle) {\r\n            // case 0:\r\n            //     return new SingleLineScreen(this.props.screenId, this.m_enemy);\r\n            // case 1:\r\n            //     return new AimSingleLineScreen(this.props.screenId, this.m_enemy);\r\n            // case 22:\r\n            // case 32:\r\n            // case 33:\r\n            // case 27:\r\n            //     return new CircleScreen(this.props.screenId, this.m_enemy);\r\n            // case 28:\r\n            //     return new LeftRightProScreen(this.props.screenId, this.m_enemy);\r\n            case 2:\r\n            case 50:\r\n            case 59:\r\n                return new CircleScreen(this.props.screenId, this.m_enemy);\r\n            // case 3:\r\n            //     return new LoftScreen(this.props.screenId, this.m_enemy);\r\n            // case 9:\r\n            //     return new LineScreen(this.props.screenId, this.m_enemy);\r\n            // case 10:\r\n            //     return new AimLineScreen(this.props.screenId, this.m_enemy);\r\n            // case 26:\r\n            //     return new AroundBallScreen(this.props.screenId, this.m_enemy);\r\n            // case 11:\r\n            //     return new AimCircleScreen(this.props.screenId, this.m_enemy);\r\n            // case 13:\r\n            //     return new GridScreen(this.props.screenId, this.m_enemy);\r\n            // case 24:\r\n            //     return new AimLaserScreen(this.props.screenId, this.m_enemy);\r\n            // case 19:\r\n            //     return new WhipScreen(this.props.screenId, this.m_enemy);\r\n            // case 29:\r\n            //     return new AroundBallScreen(this.props.screenId, this.m_enemy);\r\n            // case 23:\r\n            //     return new LaserScreen(this.props.screenId, this.m_enemy);\r\n            // case 30:\r\n            //     return new CircleScreen(this.props.screenId, this.m_enemy);\r\n            // case 31:\r\n            //     return new LightingBallScreen(this.props.screenId, this.m_enemy);\r\n            // case 7:\r\n            //     return new FormulaScreen(this.props.screenId, this.m_enemy);\r\n            // case 25:\r\n            //     return new CircleZoomScreen(this.props.screenId, this.m_enemy);\r\n            // case 34:\r\n            //     return new SidePointScreen(this.props.screenId, this.m_enemy);\r\n            // case 35:\r\n            //     return new LeftRightScreen(this.props.screenId, this.m_enemy);\r\n            // case 36:\r\n            //     return new WavesScreen(this.props.screenId, this.m_enemy);\r\n            // case 37:\r\n            //     return new CircleScreen(this.props.screenId, this.m_enemy);\r\n            // case 40:\r\n            //     return new LJAimProScreen(this.props.screenId, this.m_enemy);\r\n            // case 41:\r\n            //     return new SwordScreen(this.props.screenId, this.m_enemy);\r\n            // case 42:\r\n            //     return new FooScreen(this.props.screenId, this.m_enemy);\r\n            // case 43:\r\n            //     return new SingleLineScreen(this.props.screenId, this.m_enemy);\r\n            // case 46:\r\n            //     return new PeachScreen(this.props.screenId, this.m_enemy);\r\n            // case 47:\r\n            //     return new PeachRotateScreen(this.props.screenId, this.m_enemy);\r\n            // case 48:\r\n            //     return new KnifeScreen(this.props.screenId, this.m_enemy);\r\n            // case 49:\r\n            //     return new LoftScreen1(this.props.screenId, this.m_enemy);\r\n            // case 53:\r\n            //     return new AngleDeltaScreen(this.props.screenId, this.m_enemy);\r\n            // case 100:\r\n            //     return new LashScreen(this.props.screenId);\r\n            // default:\r\n            //     return null;\r\n        }\r\n    }\r\n\r\n    /**\r\n     * 发射子弹\r\n     */\r\n    fire(): void {\r\n        this.m_screen.toFire(this.m_groupNum);\r\n\r\n        // switch (this.m_danmuConfig.bustyle) {\r\n        //     case 27:\r\n        //         frameWork.audioManager.playEffect(\"b_ice\");\r\n        //         break;\r\n        //     case 28:\r\n        //         frameWork.audioManager.playEffectForCD(\"b_fire\", this.coolTime);\r\n        //         break;\r\n        //     case 31:\r\n        //         frameWork.audioManager.playEffect(\"flashstart\");\r\n        //         break;\r\n        //     case 22:\r\n        //         frameWork.audioManager.playEffectForCD(\"targetFly\", this.coolTime);\r\n        //         break;\r\n        // }\r\n    }\r\n\r\n    /**\r\n     * 设置冷却时间\r\n     * @param coolTime 冷却时间\r\n     */\r\n    setCoolTime(coolTime: number): void {\r\n        if (coolTime < 0) {\r\n            this.coolTime = 1000;\r\n        } else {\r\n            this.m_coolTime = 0;\r\n            this.coolTime = this.props.cooltime;\r\n        }\r\n    }\r\n\r\n    /**\r\n     * 更新逻辑\r\n     * @param deltaTime 时间增量\r\n     */\r\n    update(deltaTime: number): void {\r\n        // 检查游戏是否处于可运行状态\r\n        if (!GameConst.GameAble) {\r\n            return;\r\n        }\r\n\r\n        // 如果不在战斗中，或者场景实体无效，且不是特殊技能状态，则直接返回\r\n        if (!GameIns.gameRuleManager.isInBattle() && (!this.sceneEntity || !this.sceneEntity.node || !this.sceneEntity.node.active) && !this.m_batSkill) {\r\n            return;\r\n        }\r\n\r\n        // 限制 deltaTime 的最大值，避免因帧率过低导致的时间跳跃\r\n        if (deltaTime > 0.2) {\r\n            deltaTime = 0.016666666666667; // 约等于 1/60 秒\r\n        }\r\n\r\n        // 如果启用了严格更新模式，进一步限制 deltaTime\r\n        if (this.m_updateStrict && deltaTime > 0.016666666666667) {\r\n            deltaTime = 0.016666666666667;\r\n        }\r\n\r\n        // // 如果主飞机未显示，且子弹发射器的 UI 节点无效，直接返回\r\n        // if (!GameIns.mainPlaneManager.isShow && !GameIns.bulletManager.fireShellUINode) {\r\n        //     return;\r\n        // }\r\n\r\n        // 如果场景实体无效，处理等待帧逻辑\r\n        if (!this.sceneEntity) {\r\n            if (this.m_waitFrame < this.props.wait) {\r\n                this.m_waitFrame++;\r\n                return;\r\n            }\r\n        }\r\n\r\n        // 如果游戏状态不是战斗状态，处理特殊逻辑\r\n        if (GameIns.gameRuleManager.gameState !== GameEnum.GameState.Battle) {\r\n            if (GameIns.gameRuleManager.isGameWillOver() && this.m_screen) {\r\n                this.m_screen.update(deltaTime);\r\n            }\r\n\r\n            // 如果不是主飞机，或者主飞机的剑气子弹未激活，直接返回\r\n            if (!this.m_isMainPlane || !GameIns.mainPlaneManager.swordBullet) {\r\n                return;\r\n            }\r\n\r\n            // 激活剑气子弹并触发发射逻辑\r\n            GameIns.mainPlaneManager.swordBullet = false;\r\n            this.fire();\r\n            return;\r\n        }\r\n\r\n        // 如果主飞机未启用发射功能，直接返回\r\n        if (!GameIns.mainPlaneManager.fireEnable) {\r\n            return;\r\n        }\r\n\r\n        // 如果屏幕 ID 为 0，直接返回\r\n        if (this.props.screenId === 0) {\r\n            return;\r\n        }\r\n\r\n        // 处理等待帧逻辑\r\n        if (this.m_waitFrame < this.props.wait) {\r\n            this.m_waitFrame++;\r\n            return;\r\n        }\r\n\r\n        // 更新屏幕组件\r\n        if (this.m_screen) {\r\n            this.m_screen.update(deltaTime);\r\n        }\r\n\r\n        // 处理冷却时间逻辑\r\n        if (!this.m_inGroup && this.props.cooltime >= 0) {\r\n            this.m_coolTime += deltaTime;\r\n            if (this.m_coolTime >= this.coolTime) {\r\n                this.m_inGroup = true;\r\n                this.m_coolTime -= this.coolTime;\r\n            }\r\n        }\r\n\r\n        // 处理分组发射逻辑\r\n        if (this.m_inGroup) {\r\n            this.m_groupTime += deltaTime;\r\n            if (this.m_groupTime >= this.props.screenTime) {\r\n                this.fire();\r\n                this.m_groupNum++;\r\n                this.m_groupTime = 0;\r\n            }\r\n\r\n            if (this.m_groupNum >= this.props.screenNum) {\r\n                this.m_inGroup = false;\r\n                this.m_groupNum = 0;\r\n            }\r\n        }\r\n    }\r\n\r\n    /**\r\n     * 添加暴击\r\n     * @param level 技能等级\r\n     * @returns 暴击数据\r\n     */\r\n    addCirt(level: number): any {\r\n        // return GameIns.skillManager.getSkillRecord(level).skillcs;\r\n    }\r\n\r\n    /**\r\n     * 更改屏幕数据\r\n     * @param key 数据键\r\n     * @param value 数据值\r\n     */\r\n    changeScreenData(key: string, value: any): void {\r\n        if (this.m_screen && this.m_screen.onChangeData) {\r\n            this.m_screen.onChangeData(key, value);\r\n        }\r\n    }\r\n\r\n    /**\r\n     * 获取子弹类型\r\n     * @returns 子弹类型\r\n     */\r\n    getType(): number {\r\n        return this.m_danmuConfig ? this.m_danmuConfig.bustyle : 0;\r\n    }\r\n\r\n    /**\r\n     * 移除子弹\r\n     * @param bulletType 子弹类型\r\n     */\r\n    removeBullet(bulletType: number): void {\r\n        if (this.m_danmuConfig.bustyle === bulletType && this.m_screen.removeBullet) {\r\n            this.m_screen.removeBullet();\r\n        }\r\n    }\r\n\r\n    /**\r\n     * 获取屏幕组件\r\n     * @returns 屏幕组件实例\r\n     */\r\n    getScreen(): any {\r\n        return this.m_screen;\r\n    }\r\n}"]}