{"version": 3, "sources": ["file:///E:/M2Game/Client/assets/scripts/GameOld/world/level/event_group/ChangeBackgroundAction.ts"], "names": ["_decorator", "EventAction", "ccclass", "property", "ChangeBackgroundAction", "execute"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;AAASA,MAAAA,U,OAAAA,U;;AACAC,MAAAA,W,iBAAAA,W;;;;;;;;;OAEH;AAAEC,QAAAA,OAAF;AAAWC,QAAAA;AAAX,O,GAAwBH,U;;wCAGjBI,sB,WADZF,OAAO,CAAC,wBAAD,C,2BAAR,MACaE,sBADb;AAAA;AAAA,sCACwD;AAAA;AAAA;;AAAA;AAAA;;AAI1CC,QAAAA,OAAO,GAAS,CAEzB;;AANmD,O,6EACnDF,Q;;;;;iBACwB,I", "sourcesContent": ["import { _decorator, Sprite, Node } from 'cc'; \r\nimport { EventAction } from '../../base/EventAction';\r\nimport { Background } from '../bg_layer/Background';\r\nconst { ccclass, property } = _decorator;\r\n\r\n@ccclass('ChangeBackgroundAction')\r\nexport class ChangeBackgroundAction extends EventAction {\r\n    @property\r\n    background: Background = null;\r\n\r\n    protected execute(): void {\r\n        \r\n    }\r\n}"]}