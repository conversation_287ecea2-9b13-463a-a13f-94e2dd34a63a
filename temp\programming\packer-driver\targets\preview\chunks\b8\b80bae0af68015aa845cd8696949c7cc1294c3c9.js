System.register(["__unresolved_0", "cc", "__unresolved_1"], function (_export, _context) {
  "use strict";

  var _reporterNs, _cclegacy, __checkObsolete__, __checkObsoleteInNamespace__, _decorator, Component, World, _dec, _class, _crd, ccclass, Object;

  function _reportPossibleCrUseOfWorld(extras) {
    _reporterNs.report("World", "./World", _context.meta, extras);
  }

  return {
    setters: [function (_unresolved_) {
      _reporterNs = _unresolved_;
    }, function (_cc) {
      _cclegacy = _cc.cclegacy;
      __checkObsolete__ = _cc.__checkObsolete__;
      __checkObsoleteInNamespace__ = _cc.__checkObsoleteInNamespace__;
      _decorator = _cc._decorator;
      Component = _cc.Component;
    }, function (_unresolved_2) {
      World = _unresolved_2.World;
    }],
    execute: function () {
      _crd = true;

      _cclegacy._RF.push({}, "53e33KoZStHCrH2mvKoOyXd", "Object", undefined);

      __checkObsolete__(['_decorator', 'Component']);

      ({
        ccclass
      } = _decorator);
      /**
       * Abstract base class for all world Object
       * Inherits from Cocos Creator Component and provides common functionality
       */

      _export("Object", Object = (_dec = ccclass('Object'), _dec(_class = class Object extends Component {
        constructor() {
          super(...arguments);
          this.world = null;
        }

        /**
         * Called when the object is initialized
         * Override this method to implement object-specific initialization logic
         */
        onWorldCreate(world) {
          this.world = world;
        }
        /**
         * Called when the object is destroyed
         * Override this method to implement object-specific cleanup logic
         */


        onWorldDestroy(world) {
          this.world = null;
        }

        onLoad() {
          // TODO: 待定是否采用这种方式
          this.onWorldCreate((_crd && World === void 0 ? (_reportPossibleCrUseOfWorld({
            error: Error()
          }), World) : World).getInstance());
        }

        onDestroy() {
          this.onWorldDestroy((_crd && World === void 0 ? (_reportPossibleCrUseOfWorld({
            error: Error()
          }), World) : World).getInstance());
        }

      }) || _class));

      _cclegacy._RF.pop();

      _crd = false;
    }
  };
});
//# sourceMappingURL=b80bae0af68015aa845cd8696949c7cc1294c3c9.js.map