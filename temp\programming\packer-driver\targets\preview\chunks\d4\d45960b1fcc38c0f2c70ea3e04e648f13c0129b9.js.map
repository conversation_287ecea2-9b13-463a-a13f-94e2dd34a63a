{"version": 3, "sources": ["file:///E:/M2Game/Client/assets/scripts/PlatformSDK/DevLoginData.ts"], "names": ["_decorator", "sys", "logDebug", "ccclass", "DevLoginData", "data", "instance", "_instance", "loginData", "strData", "localStorage", "getItem", "JSON", "parse", "saveLoginData", "serverList", "has", "servername", "keys", "next", "value", "setItem", "stringify", "getServerAddr", "get", "user", "username", "password", "Map"], "mappings": ";;;;;;;;;;;;;;;;AAASA,MAAAA,U,OAAAA,U;AAAYC,MAAAA,G,OAAAA,G;;AACZC,MAAAA,Q,iBAAAA,Q;;;;;;;;;OACH;AAAEC,QAAAA;AAAF,O,GAAcH,U;;8BAGPI,Y,WADZD,OAAO,CAAC,cAAD,C,2BAAR,MACaC,YADb,CAC0B;AAAA;AAAA,eACdC,IADc;AAAA;;AAMI,mBAARC,QAAQ,GAAG;AACzB,cAAI,CAAC,KAAKC,SAAV,EAAqB;AACjB,gBAAIC,SAAS,GAAG,IAAIJ,YAAJ,EAAhB;AACA,gBAAIK,OAAO,GAAGR,GAAG,CAACS,YAAJ,CAAiBC,OAAjB,CAAyB,cAAzB,CAAd;AACA;AAAA;AAAA,sCAAS,cAAT,iCAAsDF,OAAtD;AACAD,YAAAA,SAAS,CAACH,IAAV,GAAiBO,IAAI,CAACC,KAAL,CAAWJ,OAAX,CAAjB;;AACA,gBAAI,CAACD,SAAS,CAACH,IAAf,EAAqB;AACjBG,cAAAA,SAAS,CAACH,IAAV,GAAiB,EAAjB;AACAG,cAAAA,SAAS,CAACM,aAAV;AACH;;AACD,gBAAI,CAAC,KAAKC,UAAL,CAAgBC,GAAhB,CAAoBR,SAAS,CAACH,IAAV,CAAeY,UAAnC,CAAL,EAAqD;AACjDT,cAAAA,SAAS,CAACH,IAAV,CAAeY,UAAf,GAA4B,KAAKF,UAAL,CAAgBG,IAAhB,GAAuBC,IAAvB,GAA8BC,KAA1D;AACAZ,cAAAA,SAAS,CAACM,aAAV;AACH;;AACD,iBAAKP,SAAL,GAAiBC,SAAjB;AACH;;AACD,iBAAO,KAAKD,SAAZ;AACH;;AAEOO,QAAAA,aAAa,GAAG;AACpBb,UAAAA,GAAG,CAACS,YAAJ,CAAiBW,OAAjB,CAAyB,cAAzB,EAAyCT,IAAI,CAACU,SAAL,CAAe,KAAKjB,IAApB,CAAzC;AACH;;AACMkB,QAAAA,aAAa,GAAU;AAC1B,iBAAOnB,YAAY,CAACW,UAAb,CAAwBS,GAAxB,CAA4B,KAAKnB,IAAL,CAAUY,UAAtC,CAAP;AACH;;AACa,YAAVA,UAAU,GAAU;AACpB,iBAAO,KAAKZ,IAAL,CAAUY,UAAjB;AACH;;AACa,YAAVA,UAAU,CAACG,KAAD,EAAgB;AAC1B,cAAI,KAAKf,IAAL,CAAUY,UAAV,IAAwBG,KAA5B,EAAmC;AAC/B,iBAAKf,IAAL,CAAUY,UAAV,GAAuBG,KAAvB;AACA,iBAAKN,aAAL;AACH;AACJ;;AACO,YAAJW,IAAI,GAAW;AACf,iBAAO,KAAKpB,IAAL,CAAUqB,QAAV,IAAsB,EAA7B;AACH;;AACO,YAAJD,IAAI,CAACL,KAAD,EAAgB;AACpB,cAAI,KAAKf,IAAL,CAAUqB,QAAV,IAAsBN,KAA1B,EAAiC;AAC7B,iBAAKf,IAAL,CAAUqB,QAAV,GAAqBN,KAArB;AACA,iBAAKN,aAAL;AACH;AACJ;;AACW,YAARa,QAAQ,GAAW;AACnB,iBAAO,KAAKtB,IAAL,CAAUsB,QAAV,IAAsB,EAA7B;AACH;;AACW,YAARA,QAAQ,CAACP,KAAD,EAAgB;AACxB,cAAI,KAAKf,IAAL,CAAUsB,QAAV,IAAsBP,KAA1B,EAAiC;AAC7B,iBAAKf,IAAL,CAAUsB,QAAV,GAAqBP,KAArB;AACA,iBAAKN,aAAL;AACH;AACJ;;AAzDqB,O,UAERC,U,GAAkC,IAAIa,GAAJ,CAAQ,CACpD,CAAC,OAAD,EAAU,0BAAV,CADoD,CAAR,C,UAGjCrB,S", "sourcesContent": ["import { _decorator, sys } from \"cc\";\nimport { logDebug } from \"../Utils/Logger\";\nconst { ccclass } = _decorator;\n\n@ccclass(\"DevLoginData\")\nexport class DevLoginData {\n    private data: any;\n    public static serverList: Map<string, string> = new Map([\n        [\"jerry\", \"ws://175.178.238.98:9011\"],\n    ]);\n    private static _instance: DevLoginData;\n    public static get instance() {\n        if (!this._instance) {\n            var loginData = new DevLoginData();\n            var strData = sys.localStorage.getItem(\"devLoginData\");\n            logDebug(\"DevLoginData\", `localStorage devLoginData:${strData}`)\n            loginData.data = JSON.parse(strData);\n            if (!loginData.data) {\n                loginData.data = {};\n                loginData.saveLoginData();\n            }\n            if (!this.serverList.has(loginData.data.servername)) {\n                loginData.data.servername = this.serverList.keys().next().value;\n                loginData.saveLoginData();\n            }\n            this._instance = loginData;\n        }\n        return this._instance;\n    }\n\n    private saveLoginData() {\n        sys.localStorage.setItem(\"devLoginData\", JSON.stringify(this.data));\n    }\n    public getServerAddr():string {\n        return DevLoginData.serverList.get(this.data.servername);\n    }\n    get servername():string {\n        return this.data.servername\n    }\n    set servername(value: string) {\n        if (this.data.servername != value) {\n            this.data.servername = value;\n            this.saveLoginData();\n        }\n    }\n    get user(): string {\n        return this.data.username || \"\";\n    }\n    set user(value: string) {\n        if (this.data.username != value) {\n            this.data.username = value;\n            this.saveLoginData();\n        }\n    }\n    get password(): string {\n        return this.data.password || \"\";\n    }\n    set password(value: string) {\n        if (this.data.password != value) {\n            this.data.password = value;\n            this.saveLoginData();\n        }\n    }\n}"]}