{"version": 3, "sources": ["file:///E:/M2Game/Client/assets/scripts/ui/main/BattleUI.ts"], "names": ["_decorator", "director", "ButtonPlus", "LoadingUI", "BaseUI", "<PERSON><PERSON><PERSON><PERSON>", "UIMgr", "BottomUI", "PlaneUI", "ShopUI", "TalentUI", "TopUI", "ccclass", "property", "BattleUI", "getUrl", "<PERSON><PERSON><PERSON><PERSON>", "Background", "onLoad", "btnBattle", "addClick", "onClick", "onShow", "onHide", "onClose", "update", "dt", "openUI", "closeUI", "preloadScene", "loadScene"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAASA,MAAAA,U,OAAAA,U;AAAYC,MAAAA,Q,OAAAA,Q;;AACZC,MAAAA,U,iBAAAA,U;;AACAC,MAAAA,S,iBAAAA,S;;AACAC,MAAAA,M,iBAAAA,M;AAAQC,MAAAA,O,iBAAAA,O;AAASC,MAAAA,K,iBAAAA,K;;AACjBC,MAAAA,Q,iBAAAA,Q;;AACAC,MAAAA,O,iBAAAA,O;;AACAC,MAAAA,M,iBAAAA,M;;AACAC,MAAAA,Q,iBAAAA,Q;;AACAC,MAAAA,K,iBAAAA,K;;;;;;;;;OACH;AAAEC,QAAAA,OAAF;AAAWC,QAAAA;AAAX,O,GAAwBb,U;;0BAGjBc,Q,WADZF,OAAO,CAAC,UAAD,C,UAIHC,QAAQ;AAAA;AAAA,mC,2BAJb,MACaC,QADb;AAAA;AAAA,4BACqC;AAAA;AAAA;;AAAA;AAAA;;AACb,eAANC,MAAM,GAAW;AAAE,iBAAO,kBAAP;AAA4B;;AACvC,eAARC,QAAQ,GAAY;AAAE,iBAAO;AAAA;AAAA,kCAAQC,UAAf;AAA2B;;AAIrDC,QAAAA,MAAM,GAAS;AACrB,eAAKC,SAAL,CAAeC,QAAf,CAAwB,KAAKC,OAA7B,EAAsC,IAAtC;AACH;;AAEKC,QAAAA,MAAM,GAAgC;AAAA;AAC3C;;AACKC,QAAAA,MAAM,GAAgC;AAAA;AAC3C;;AACKC,QAAAA,OAAO,GAAgC;AAAA;AAC5C;;AACSC,QAAAA,MAAM,CAACC,EAAD,EAAmB,CAClC;;AAEKL,QAAAA,OAAO,GAAG;AAAA;AACZ,kBAAM;AAAA;AAAA,gCAAMM,MAAN;AAAA;AAAA,wCAAwB,CAAxB,EAA2B,IAA3B,CAAN;AACA;AAAA;AAAA,gCAAMC,OAAN,CAAcd,QAAd;AACA;AAAA;AAAA,gCAAMc,OAAN;AAAA;AAAA;AACA;AAAA;AAAA,gCAAMA,OAAN;AAAA;AAAA;AACA;AAAA;AAAA,gCAAMA,OAAN;AAAA;AAAA;AACA;AAAA;AAAA,gCAAMA,OAAN;AAAA;AAAA;AACA;AAAA;AAAA,gCAAMA,OAAN;AAAA;AAAA;AACA3B,YAAAA,QAAQ,CAAC4B,YAAT,CAAsB,MAAtB,iCAA8B,aAAY;AACtC,oBAAM;AAAA;AAAA,kCAAMD,OAAN;AAAA;AAAA,0CAAyB,EAAzB,CAAN;AACA3B,cAAAA,QAAQ,CAAC6B,SAAT,CAAmB,MAAnB;AACH,aAHD;AARY;AAYf;;AA/BgC,O", "sourcesContent": ["import { _decorator, director } from 'cc';\nimport { ButtonPlus } from '../components/common/button/ButtonPlus';\nimport { LoadingUI } from '../LoadingUI';\nimport { BaseUI, UILayer, UIMgr } from '../UIMgr';\nimport { BottomUI } from './BottomUI';\nimport { PlaneUI } from './plane/PlaneUI';\nimport { ShopUI } from './ShopUI';\nimport { TalentUI } from './TalentUI';\nimport { TopUI } from './TopUI';\nconst { ccclass, property } = _decorator;\n\n@ccclass('BattleUI')\nexport class BattleUI extends BaseUI {\n    public static getUrl(): string { return \"ui/main/BattleUI\"; }\n    public static getLayer(): UILayer { return UILayer.Background }\n    @property(ButtonPlus)\n    btnBattle: ButtonPlus;\n\n    protected onLoad(): void {\n        this.btnBattle.addClick(this.onClick, this);\n    }\n\n    async onShow(...args: any[]): Promise<void> {\n    }\n    async onHide(...args: any[]): Promise<void> {\n    }\n    async onClose(...args: any[]): Promise<void> {\n    }\n    protected update(dt: number): void {\n    }\n\n    async onClick() {\n        await UIMgr.openUI(LoadingUI, 2, 0.99)\n        UIMgr.closeUI(BattleUI)\n        UIMgr.closeUI(BottomUI)\n        UIMgr.closeUI(PlaneUI)\n        UIMgr.closeUI(TalentUI)\n        UIMgr.closeUI(ShopUI)\n        UIMgr.closeUI(TopUI)\n        director.preloadScene(\"Game\", async () => {\n            await UIMgr.closeUI(LoadingUI, .5)\n            director.loadScene(\"Game\")\n        })\n    }\n}\n\n"]}