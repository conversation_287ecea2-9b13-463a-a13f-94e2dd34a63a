System.register(["__unresolved_0", "cc", "__unresolved_1", "__unresolved_2", "__unresolved_3", "__unresolved_4", "__unresolved_5", "__unresolved_6", "__unresolved_7", "__unresolved_8"], function (_export, _context) {
  "use strict";

  var _reporterNs, _cclegacy, __checkObsolete__, __checkObsoleteInNamespace__, _decorator, director, ButtonPlus, LoadingUI, BaseUI, UILayer, UIMgr, BottomUI, PlaneUI, ShopUI, TalentUI, TopUI, _dec, _dec2, _class, _class2, _descriptor, _crd, ccclass, property, BattleUI;

  function asyncGeneratorStep(gen, resolve, reject, _next, _throw, key, arg) { try { var info = gen[key](arg); var value = info.value; } catch (error) { reject(error); return; } if (info.done) { resolve(value); } else { Promise.resolve(value).then(_next, _throw); } }

  function _asyncToGenerator(fn) { return function () { var self = this, args = arguments; return new Promise(function (resolve, reject) { var gen = fn.apply(self, args); function _next(value) { asyncGeneratorStep(gen, resolve, reject, _next, _throw, "next", value); } function _throw(err) { asyncGeneratorStep(gen, resolve, reject, _next, _throw, "throw", err); } _next(undefined); }); }; }

  function _initializerDefineProperty(target, property, descriptor, context) { if (!descriptor) return; Object.defineProperty(target, property, { enumerable: descriptor.enumerable, configurable: descriptor.configurable, writable: descriptor.writable, value: descriptor.initializer ? descriptor.initializer.call(context) : void 0 }); }

  function _applyDecoratedDescriptor(target, property, decorators, descriptor, context) { var desc = {}; Object.keys(descriptor).forEach(function (key) { desc[key] = descriptor[key]; }); desc.enumerable = !!desc.enumerable; desc.configurable = !!desc.configurable; if ('value' in desc || desc.initializer) { desc.writable = true; } desc = decorators.slice().reverse().reduce(function (desc, decorator) { return decorator(target, property, desc) || desc; }, desc); if (context && desc.initializer !== void 0) { desc.value = desc.initializer ? desc.initializer.call(context) : void 0; desc.initializer = undefined; } if (desc.initializer === void 0) { Object.defineProperty(target, property, desc); desc = null; } return desc; }

  function _initializerWarningHelper(descriptor, context) { throw new Error('Decorating class property failed. Please ensure that ' + 'transform-class-properties is enabled and runs after the decorators transform.'); }

  function _reportPossibleCrUseOfButtonPlus(extras) {
    _reporterNs.report("ButtonPlus", "../components/common/button/ButtonPlus", _context.meta, extras);
  }

  function _reportPossibleCrUseOfLoadingUI(extras) {
    _reporterNs.report("LoadingUI", "../LoadingUI", _context.meta, extras);
  }

  function _reportPossibleCrUseOfBaseUI(extras) {
    _reporterNs.report("BaseUI", "../UIMgr", _context.meta, extras);
  }

  function _reportPossibleCrUseOfUILayer(extras) {
    _reporterNs.report("UILayer", "../UIMgr", _context.meta, extras);
  }

  function _reportPossibleCrUseOfUIMgr(extras) {
    _reporterNs.report("UIMgr", "../UIMgr", _context.meta, extras);
  }

  function _reportPossibleCrUseOfBottomUI(extras) {
    _reporterNs.report("BottomUI", "./BottomUI", _context.meta, extras);
  }

  function _reportPossibleCrUseOfPlaneUI(extras) {
    _reporterNs.report("PlaneUI", "./plane/PlaneUI", _context.meta, extras);
  }

  function _reportPossibleCrUseOfShopUI(extras) {
    _reporterNs.report("ShopUI", "./ShopUI", _context.meta, extras);
  }

  function _reportPossibleCrUseOfTalentUI(extras) {
    _reporterNs.report("TalentUI", "./TalentUI", _context.meta, extras);
  }

  function _reportPossibleCrUseOfTopUI(extras) {
    _reporterNs.report("TopUI", "./TopUI", _context.meta, extras);
  }

  return {
    setters: [function (_unresolved_) {
      _reporterNs = _unresolved_;
    }, function (_cc) {
      _cclegacy = _cc.cclegacy;
      __checkObsolete__ = _cc.__checkObsolete__;
      __checkObsoleteInNamespace__ = _cc.__checkObsoleteInNamespace__;
      _decorator = _cc._decorator;
      director = _cc.director;
    }, function (_unresolved_2) {
      ButtonPlus = _unresolved_2.ButtonPlus;
    }, function (_unresolved_3) {
      LoadingUI = _unresolved_3.LoadingUI;
    }, function (_unresolved_4) {
      BaseUI = _unresolved_4.BaseUI;
      UILayer = _unresolved_4.UILayer;
      UIMgr = _unresolved_4.UIMgr;
    }, function (_unresolved_5) {
      BottomUI = _unresolved_5.BottomUI;
    }, function (_unresolved_6) {
      PlaneUI = _unresolved_6.PlaneUI;
    }, function (_unresolved_7) {
      ShopUI = _unresolved_7.ShopUI;
    }, function (_unresolved_8) {
      TalentUI = _unresolved_8.TalentUI;
    }, function (_unresolved_9) {
      TopUI = _unresolved_9.TopUI;
    }],
    execute: function () {
      _crd = true;

      _cclegacy._RF.push({}, "6a490vtGG5JG5pYsczeTP4G", "BattleUI", undefined);

      __checkObsolete__(['_decorator', 'director']);

      ({
        ccclass,
        property
      } = _decorator);

      _export("BattleUI", BattleUI = (_dec = ccclass('BattleUI'), _dec2 = property(_crd && ButtonPlus === void 0 ? (_reportPossibleCrUseOfButtonPlus({
        error: Error()
      }), ButtonPlus) : ButtonPlus), _dec(_class = (_class2 = class BattleUI extends (_crd && BaseUI === void 0 ? (_reportPossibleCrUseOfBaseUI({
        error: Error()
      }), BaseUI) : BaseUI) {
        constructor() {
          super(...arguments);

          _initializerDefineProperty(this, "btnBattle", _descriptor, this);
        }

        static getUrl() {
          return "ui/main/BattleUI";
        }

        static getLayer() {
          return (_crd && UILayer === void 0 ? (_reportPossibleCrUseOfUILayer({
            error: Error()
          }), UILayer) : UILayer).Background;
        }

        onLoad() {
          this.btnBattle.addClick(this.onClick, this);
        }

        onShow() {
          return _asyncToGenerator(function* () {})();
        }

        onHide() {
          return _asyncToGenerator(function* () {})();
        }

        onClose() {
          return _asyncToGenerator(function* () {})();
        }

        update(dt) {}

        onClick() {
          return _asyncToGenerator(function* () {
            yield (_crd && UIMgr === void 0 ? (_reportPossibleCrUseOfUIMgr({
              error: Error()
            }), UIMgr) : UIMgr).openUI(_crd && LoadingUI === void 0 ? (_reportPossibleCrUseOfLoadingUI({
              error: Error()
            }), LoadingUI) : LoadingUI, 2, 0.99);
            (_crd && UIMgr === void 0 ? (_reportPossibleCrUseOfUIMgr({
              error: Error()
            }), UIMgr) : UIMgr).closeUI(BattleUI);
            (_crd && UIMgr === void 0 ? (_reportPossibleCrUseOfUIMgr({
              error: Error()
            }), UIMgr) : UIMgr).closeUI(_crd && BottomUI === void 0 ? (_reportPossibleCrUseOfBottomUI({
              error: Error()
            }), BottomUI) : BottomUI);
            (_crd && UIMgr === void 0 ? (_reportPossibleCrUseOfUIMgr({
              error: Error()
            }), UIMgr) : UIMgr).closeUI(_crd && PlaneUI === void 0 ? (_reportPossibleCrUseOfPlaneUI({
              error: Error()
            }), PlaneUI) : PlaneUI);
            (_crd && UIMgr === void 0 ? (_reportPossibleCrUseOfUIMgr({
              error: Error()
            }), UIMgr) : UIMgr).closeUI(_crd && TalentUI === void 0 ? (_reportPossibleCrUseOfTalentUI({
              error: Error()
            }), TalentUI) : TalentUI);
            (_crd && UIMgr === void 0 ? (_reportPossibleCrUseOfUIMgr({
              error: Error()
            }), UIMgr) : UIMgr).closeUI(_crd && ShopUI === void 0 ? (_reportPossibleCrUseOfShopUI({
              error: Error()
            }), ShopUI) : ShopUI);
            (_crd && UIMgr === void 0 ? (_reportPossibleCrUseOfUIMgr({
              error: Error()
            }), UIMgr) : UIMgr).closeUI(_crd && TopUI === void 0 ? (_reportPossibleCrUseOfTopUI({
              error: Error()
            }), TopUI) : TopUI);
            director.preloadScene("Game", /*#__PURE__*/_asyncToGenerator(function* () {
              yield (_crd && UIMgr === void 0 ? (_reportPossibleCrUseOfUIMgr({
                error: Error()
              }), UIMgr) : UIMgr).closeUI(_crd && LoadingUI === void 0 ? (_reportPossibleCrUseOfLoadingUI({
                error: Error()
              }), LoadingUI) : LoadingUI, .5);
              director.loadScene("Game");
            }));
          })();
        }

      }, (_descriptor = _applyDecoratedDescriptor(_class2.prototype, "btnBattle", [_dec2], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: null
      })), _class2)) || _class));

      _cclegacy._RF.pop();

      _crd = false;
    }
  };
});
//# sourceMappingURL=d76c4c3e5817bb91737df46b7182d1489f90970e.js.map