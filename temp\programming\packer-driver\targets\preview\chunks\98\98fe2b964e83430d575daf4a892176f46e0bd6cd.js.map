{"version": 3, "sources": ["file:///E:/M2Game/Client/assets/scripts/GameOld/world/move/IMovable.ts"], "names": ["_decorator", "ccclass", "property", "eSolverTarget", "eEasing"], "mappings": ";;;;;;;;;;AAASA,MAAAA,U,OAAAA,U;;;;;;;;;OACH;AAAEC,QAAAA,OAAF;AAAWC,QAAAA;AAAX,O,GAAwBF,U;AAE9B;AACA;AACA;;+BAUYG,a,0BAAAA,a;AAAAA,QAAAA,a,CAAAA,a;AAAAA,QAAAA,a,CAAAA,a;AAAAA,QAAAA,a,CAAAA,a;eAAAA,a;;;yBAMAC,O,0BAAAA,O;AAAAA,QAAAA,O,CAAAA,O;AAAAA,QAAAA,O,CAAAA,O;AAAAA,QAAAA,O,CAAAA,O;AAAAA,QAAAA,O,CAAAA,O;AAAAA,QAAAA,O,CAAAA,O;AAAAA,QAAAA,O,CAAAA,O;AAAAA,QAAAA,O,CAAAA,O;eAAAA,O", "sourcesContent": ["import { _decorator, Component, Node } from 'cc';\r\nconst { ccclass, property } = _decorator;\r\n\r\n/**\r\n * Base interface for all move-able objects\r\n */\r\nexport interface IMovable {\r\n    angle: number;    // 角度\r\n    //wa: number;   // 角速度加速度\r\n    vx: number;   // 水平方向速度\r\n    //vax: number;  // 水平方向加速度\r\n    vy: number;   // 垂直方向速度\r\n    //vay: number;  // 垂直方向加速度\r\n}\r\n\r\nexport enum eSolverTarget {\r\n    Angle,   // 影响角速度与加速度\r\n    Vx,  // 影响水平速度与加速度\r\n    Vy   // 影响垂直速度与加速度\r\n}\r\n\r\nexport enum eEasing {\r\n    Linear,\r\n    InSine, OutSine, InOutSine,\r\n    InQuad, OutQuad, InOutQuad\r\n}\r\n\r\nexport interface IMoveModifier {\r\n    targetType: eSolverTarget;\r\n    targetValue: number;\r\n    duration: number;\r\n    easing: eEasing;\r\n\r\n    // 如果需要节点始终朝向运动方向\r\n    // this.angle = Math.atan2(this.vy, this.vx) * 180 / Math.PI;\r\n    tick(movable: IMovable, deltaTime: number) : void;\r\n    isFinished() : boolean;\r\n}\r\n"]}