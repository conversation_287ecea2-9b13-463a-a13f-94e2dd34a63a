System.register(["__unresolved_0", "cc", "__unresolved_1", "__unresolved_2", "__unresolved_3", "__unresolved_4", "__unresolved_5"], function (_export, _context) {
  "use strict";

  var _reporterNs, _cclegacy, __checkObsolete__, __checkObsoleteInNamespace__, _decorator, v2, Entity, ColliderComp, GameIns, GameConst, HDSpine, _dec, _dec2, _class, _class2, _descriptor, _crd, ccclass, property, MainRelifeComp;

  function _initializerDefineProperty(target, property, descriptor, context) { if (!descriptor) return; Object.defineProperty(target, property, { enumerable: descriptor.enumerable, configurable: descriptor.configurable, writable: descriptor.writable, value: descriptor.initializer ? descriptor.initializer.call(context) : void 0 }); }

  function _applyDecoratedDescriptor(target, property, decorators, descriptor, context) { var desc = {}; Object.keys(descriptor).forEach(function (key) { desc[key] = descriptor[key]; }); desc.enumerable = !!desc.enumerable; desc.configurable = !!desc.configurable; if ('value' in desc || desc.initializer) { desc.writable = true; } desc = decorators.slice().reverse().reduce(function (desc, decorator) { return decorator(target, property, desc) || desc; }, desc); if (context && desc.initializer !== void 0) { desc.value = desc.initializer ? desc.initializer.call(context) : void 0; desc.initializer = undefined; } if (desc.initializer === void 0) { Object.defineProperty(target, property, desc); desc = null; } return desc; }

  function _initializerWarningHelper(descriptor, context) { throw new Error('Decorating class property failed. Please ensure that ' + 'transform-class-properties is enabled and runs after the decorators transform.'); }

  function _reportPossibleCrUseOfEntity(extras) {
    _reporterNs.report("Entity", "../../base/Entity", _context.meta, extras);
  }

  function _reportPossibleCrUseOfColliderComp(extras) {
    _reporterNs.report("ColliderComp", "../../base/ColliderComp", _context.meta, extras);
  }

  function _reportPossibleCrUseOfGameIns(extras) {
    _reporterNs.report("GameIns", "../../../GameIns", _context.meta, extras);
  }

  function _reportPossibleCrUseOfGameConst(extras) {
    _reporterNs.report("GameConst", "../../../const/GameConst", _context.meta, extras);
  }

  function _reportPossibleCrUseOfHDSpine(extras) {
    _reporterNs.report("HDSpine", "../../base/HDSpine", _context.meta, extras);
  }

  return {
    setters: [function (_unresolved_) {
      _reporterNs = _unresolved_;
    }, function (_cc) {
      _cclegacy = _cc.cclegacy;
      __checkObsolete__ = _cc.__checkObsolete__;
      __checkObsoleteInNamespace__ = _cc.__checkObsoleteInNamespace__;
      _decorator = _cc._decorator;
      v2 = _cc.v2;
    }, function (_unresolved_2) {
      Entity = _unresolved_2.default;
    }, function (_unresolved_3) {
      ColliderComp = _unresolved_3.ColliderComp;
    }, function (_unresolved_4) {
      GameIns = _unresolved_4.GameIns;
    }, function (_unresolved_5) {
      GameConst = _unresolved_5.GameConst;
    }, function (_unresolved_6) {
      HDSpine = _unresolved_6.default;
    }],
    execute: function () {
      _crd = true;

      _cclegacy._RF.push({}, "9a2c0gbJXRE/5/DXSPC1lbs", "MainRelifeComp", undefined);

      __checkObsolete__(['_decorator', 'Component', 'Node', 'v2']);

      ({
        ccclass,
        property
      } = _decorator);

      _export("default", MainRelifeComp = (_dec = ccclass('MainRelifeComp'), _dec2 = property(_crd && HDSpine === void 0 ? (_reportPossibleCrUseOfHDSpine({
        error: Error()
      }), HDSpine) : HDSpine), _dec(_class = (_class2 = class MainRelifeComp extends (_crd && Entity === void 0 ? (_reportPossibleCrUseOfEntity({
        error: Error()
      }), Entity) : Entity) {
        constructor() {
          super(...arguments);

          _initializerDefineProperty(this, "relifeAnim", _descriptor, this);

          this.m_callBack = null;
          this.m_time = 19 / 30 * 1000;
          // 动画时间
          this.m_radius = 290;
          // 半径
          this.m_changeTime = 0;
          // 当前变化时间
          this.collider = null;
        }

        onLoad() {
          this.collider = this.addComp(_crd && ColliderComp === void 0 ? (_reportPossibleCrUseOfColliderComp({
            error: Error()
          }), ColliderComp) : ColliderComp, new (_crd && ColliderComp === void 0 ? (_reportPossibleCrUseOfColliderComp({
            error: Error()
          }), ColliderComp) : ColliderComp)());
          this.collider.init(this);
        }

        onEnable() {
          var _this$collider, _this$relifeAnim, _this$relifeAnim2;

          (_this$collider = this.collider) == null || _this$collider.setData([0, 0, 0, 0, 0], (_crd && GameIns === void 0 ? (_reportPossibleCrUseOfGameIns({
            error: Error()
          }), GameIns) : GameIns).mainPlaneManager.plane, v2(this.node.position.x, this.node.position.y));
          (_this$relifeAnim = this.relifeAnim) == null || _this$relifeAnim.setAnimation(0, 'play', false);
          (_this$relifeAnim2 = this.relifeAnim) == null || _this$relifeAnim2.setCompleteListener(() => {
            if (this.m_callBack) {
              this.m_callBack();
            }
          });
        }

        setCompleteListener(callback) {
          this.m_callBack = callback;
        }

        updateSize(deltaTime) {
          var _this$collider2;

          this.m_changeTime += 1000 * deltaTime;

          if (this.m_changeTime >= this.m_time) {
            this.m_changeTime = this.m_time;
          }

          var size = this.m_radius * (this.m_changeTime / this.m_time);
          (_this$collider2 = this.collider) == null || _this$collider2.setSize(size);
        }

        onCollide(other) {// 碰撞逻辑可以在这里实现
        }

        update(deltaTime) {
          if ((_crd && GameConst === void 0 ? (_reportPossibleCrUseOfGameConst({
            error: Error()
          }), GameConst) : GameConst).GameAble) {
            var _this$collider3;

            var clampedDeltaTime = deltaTime > 0.2 ? 0.016666666666667 : deltaTime;
            this.updateSize(clampedDeltaTime);
            (_this$collider3 = this.collider) == null || _this$collider3.update();
          }
        }

      }, (_descriptor = _applyDecoratedDescriptor(_class2.prototype, "relifeAnim", [_dec2], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function initializer() {
          return null;
        }
      })), _class2)) || _class));

      _cclegacy._RF.pop();

      _crd = false;
    }
  };
});
//# sourceMappingURL=c3da70250868dfec550aff5f1720cf8877359660.js.map