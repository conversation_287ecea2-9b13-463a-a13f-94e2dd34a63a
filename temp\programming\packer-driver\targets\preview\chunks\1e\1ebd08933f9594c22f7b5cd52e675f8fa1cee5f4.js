System.register(["__unresolved_0", "cc", "__unresolved_1", "__unresolved_2", "__unresolved_3"], function (_export, _context) {
  "use strict";

  var _reporterNs, _cclegacy, __checkObsolete__, __checkObsoleteInNamespace__, _decorator, Component, Sprite, find, Collider2D, Contact2DType, ProgressBar, Global, GamePersistNode, Player, _dec, _class, _crd, ccclass, property, Goods;

  function _reportPossibleCrUseOfGameFactory(extras) {
    _reporterNs.report("GameFactory", "./factroy/GameFactory", _context.meta, extras);
  }

  function _reportPossibleCrUseOfGlobal(extras) {
    _reporterNs.report("Global", "./Global", _context.meta, extras);
  }

  function _reportPossibleCrUseOfGamePersistNode(extras) {
    _reporterNs.report("GamePersistNode", "./GamePersistNode", _context.meta, extras);
  }

  function _reportPossibleCrUseOfPlayer(extras) {
    _reporterNs.report("Player", "./Player", _context.meta, extras);
  }

  return {
    setters: [function (_unresolved_) {
      _reporterNs = _unresolved_;
    }, function (_cc) {
      _cclegacy = _cc.cclegacy;
      __checkObsolete__ = _cc.__checkObsolete__;
      __checkObsoleteInNamespace__ = _cc.__checkObsoleteInNamespace__;
      _decorator = _cc._decorator;
      Component = _cc.Component;
      Sprite = _cc.Sprite;
      find = _cc.find;
      Collider2D = _cc.Collider2D;
      Contact2DType = _cc.Contact2DType;
      ProgressBar = _cc.ProgressBar;
    }, function (_unresolved_2) {
      Global = _unresolved_2.Global;
    }, function (_unresolved_3) {
      GamePersistNode = _unresolved_3.GamePersistNode;
    }, function (_unresolved_4) {
      Player = _unresolved_4.Player;
    }],
    execute: function () {
      _crd = true;

      _cclegacy._RF.push({}, "279dflHBJ9IMJzwZdNO4Wp+", "Goods", undefined);

      __checkObsolete__(['_decorator', 'Component', 'Node', 'SpriteFrame', 'Sprite', 'find', 'Vec3', 'Collider2D', 'Contact2DType', 'IPhysics2DContact', 'ProgressBar']);

      ({
        ccclass,
        property
      } = _decorator);

      _export("Goods", Goods = (_dec = ccclass('Goods'), _dec(_class = class Goods extends Component {
        constructor() {
          super(...arguments);
          this.goodType = null;
          this.bloodGoodsMoveSpeed = 0;
          //加血物资移动速度
          this.lightGoodsMoveSpeed = 0;
          //激光物资移动速度
          this.missileGoodsMoveSpeed = 0;
          //导弹物资移动速度
          this.persistNode = null;
          this.goodsFactory = null;
          this.curPos = null;
        }

        onLoad() {
          this.persistNode = find("GamePersistNode");
          this.goodsFactory = this.persistNode.getComponent(_crd && GamePersistNode === void 0 ? (_reportPossibleCrUseOfGamePersistNode({
            error: Error()
          }), GamePersistNode) : GamePersistNode).goodsFactory; //拿到面板值

          this.bloodGoodsMoveSpeed = this.persistNode.getComponent(_crd && GamePersistNode === void 0 ? (_reportPossibleCrUseOfGamePersistNode({
            error: Error()
          }), GamePersistNode) : GamePersistNode).bloodGoodsMoveSpeed;
          this.lightGoodsMoveSpeed = this.persistNode.getComponent(_crd && GamePersistNode === void 0 ? (_reportPossibleCrUseOfGamePersistNode({
            error: Error()
          }), GamePersistNode) : GamePersistNode).lightGoodsMoveSpeed;
          this.missileGoodsMoveSpeed = this.persistNode.getComponent(_crd && GamePersistNode === void 0 ? (_reportPossibleCrUseOfGamePersistNode({
            error: Error()
          }), GamePersistNode) : GamePersistNode).missileGoodsMoveSpeed;
          var collider = this.node.getComponent(Collider2D);

          if (collider) {
            collider.on(Contact2DType.BEGIN_CONTACT, this.onBeginContact, this);
          }
        }

        onBeginContact(selfCollider, otherCollider, contact) {
          //判断吃到了那种道具
          if (this.goodType == (_crd && Global === void 0 ? (_reportPossibleCrUseOfGlobal({
            error: Error()
          }), Global) : Global).BLOOD_GOODS) {
            otherCollider.node.getChildByName("Blood").getComponent(ProgressBar).progress = 1;
            otherCollider.node.getComponent(_crd && Player === void 0 ? (_reportPossibleCrUseOfPlayer({
              error: Error()
            }), Player) : Player).planeBlood = otherCollider.node.getComponent(_crd && Player === void 0 ? (_reportPossibleCrUseOfPlayer({
              error: Error()
            }), Player) : Player).planeTotalBlood;
          } else if (this.goodType == (_crd && Global === void 0 ? (_reportPossibleCrUseOfGlobal({
            error: Error()
          }), Global) : Global).LIGHT_GOODS) {
            otherCollider.node.getComponent(_crd && Player === void 0 ? (_reportPossibleCrUseOfPlayer({
              error: Error()
            }), Player) : Player).isShootLight = true;
          } else if (this.goodType == (_crd && Global === void 0 ? (_reportPossibleCrUseOfGlobal({
            error: Error()
          }), Global) : Global).MISSILE_GOODS) {
            otherCollider.node.getComponent(_crd && Player === void 0 ? (_reportPossibleCrUseOfPlayer({
              error: Error()
            }), Player) : Player).isShootMissile = true;
          }

          this.goodsFactory.recycleProduct(this.node);
        }
        /**
         * 物资初始化函数
         */


        init(goodType, spriteFrame) {
          this.goodType = goodType;
          this.node.getComponent(Sprite).spriteFrame = spriteFrame;
        }

        update(deltaTime) {
          if (this.goodType == (_crd && Global === void 0 ? (_reportPossibleCrUseOfGlobal({
            error: Error()
          }), Global) : Global).BLOOD_GOODS) {
            this.bloodGoodsMove(deltaTime);
          } else if (this.goodType == (_crd && Global === void 0 ? (_reportPossibleCrUseOfGlobal({
            error: Error()
          }), Global) : Global).LIGHT_GOODS) {
            this.lightGoodsMove(deltaTime);
          } else if (this.goodType == (_crd && Global === void 0 ? (_reportPossibleCrUseOfGlobal({
            error: Error()
          }), Global) : Global).MISSILE_GOODS) {
            this.missileGoodsMove(deltaTime);
          }
        }
        /**
         * 导弹物资移动
         * @param deltaTime 
         */


        missileGoodsMove(deltaTime) {
          this.curPos = this.node.getPosition();
          this.curPos.y -= this.missileGoodsMoveSpeed * deltaTime;
          this.node.setPosition(this.curPos);

          if (this.curPos.y < -(_crd && Global === void 0 ? (_reportPossibleCrUseOfGlobal({
            error: Error()
          }), Global) : Global).HEIGHT / 2) {
            this.goodsFactory.recycleProduct(this.node);
          }
        }
        /**
         * 激光物资移动
         * @param deltaTime 
         */


        lightGoodsMove(deltaTime) {
          this.curPos = this.node.getPosition();
          this.curPos.y -= this.lightGoodsMoveSpeed * deltaTime;
          this.node.setPosition(this.curPos);

          if (this.curPos.y < -(_crd && Global === void 0 ? (_reportPossibleCrUseOfGlobal({
            error: Error()
          }), Global) : Global).HEIGHT / 2) {
            this.goodsFactory.recycleProduct(this.node);
          }
        }
        /**
         * 加血物资移动
         * @param deltaTime 
         */


        bloodGoodsMove(deltaTime) {
          this.curPos = this.node.getPosition();
          this.curPos.y -= this.bloodGoodsMoveSpeed * deltaTime;
          this.node.setPosition(this.curPos);

          if (this.curPos.y < -(_crd && Global === void 0 ? (_reportPossibleCrUseOfGlobal({
            error: Error()
          }), Global) : Global).HEIGHT / 2) {
            this.goodsFactory.recycleProduct(this.node);
          }
        }

      }) || _class));

      _cclegacy._RF.pop();

      _crd = false;
    }
  };
});
//# sourceMappingURL=1ebd08933f9594c22f7b5cd52e675f8fa1cee5f4.js.map