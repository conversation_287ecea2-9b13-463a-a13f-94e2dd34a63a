{"version": 3, "sources": ["file:///E:/M2Game/Client/assets/scripts/ui/common/ClickControlUI.ts"], "names": ["_decorator", "Input", "Node", "UITransform", "view", "BaseUI", "<PERSON><PERSON><PERSON><PERSON>", "UIMgr", "ccclass", "property", "ClickControlUI", "_touchNodes", "_onCancel", "_onClick", "_target", "_clickContinue", "_onlyHideOnTargetClick", "getUrl", "<PERSON><PERSON><PERSON><PERSON>", "Top", "isTouchControl", "length", "onLoad", "trans", "getComponent", "width", "getVisibleSize", "height", "node", "on", "EventType", "TOUCH_CANCEL", "onTouchCancel", "TOUCH_END", "onTouchEnd", "_isTouchOnTarget", "touchPos", "target", "targetUITrans", "rect", "getBoundingBoxToWorld", "contains", "onShow", "config", "onClickMiss", "onClickHit", "touchNodes", "clickContinue", "onlyHideOnTargetClick", "hideUI", "event", "getUILocation", "touchNode", "find", "nd", "call", "splice", "indexOf", "onHide", "onClose"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;AAASA,MAAAA,U,OAAAA,U;AAAwBC,MAAAA,K,OAAAA,K;AAAaC,MAAAA,I,OAAAA,I;AAAMC,MAAAA,W,OAAAA,W;AAAaC,MAAAA,I,OAAAA,I;;AACxDC,MAAAA,M,iBAAAA,M;AAAQC,MAAAA,O,iBAAAA,O;AAASC,MAAAA,K,iBAAAA,K;;;;;;;;;OAEpB;AAAEC,QAAAA,OAAF;AAAWC,QAAAA;AAAX,O,GAAwBT,U;AAE9B;AACA;AACA;;AAUA;AACA;AACA;AACA;AACA;gCAEaU,c,WADZF,OAAO,CAAC,gBAAD,C,gBAAR,MACaE,cADb;AAAA;AAAA,4BAC2C;AAAA;AAAA;AAAA,eAI/BC,WAJ+B,GAIT,EAJS;AAAA,eAK/BC,SAL+B,GAKE,IALF;AAAA,eAM/BC,QAN+B,GAMW,IANX;AAAA,eAO/BC,OAP+B,GAOhB,IAPgB;AAAA,eAQ/BC,cAR+B,GAQL,KARK;AAAA,eAS/BC,sBAT+B,GASG,KATH;AAAA;;AACnB,eAANC,MAAM,GAAW;AAAE,iBAAO,0BAAP;AAAoC;;AAC/C,eAARC,QAAQ,GAAY;AAAE,iBAAO;AAAA;AAAA,kCAAQC,GAAf;AAAqB;;AASzDC,QAAAA,cAAc,GAAY;AACtB,iBAAO,KAAKT,WAAL,CAAiBU,MAAjB,GAA0B,CAAjC;AACH;;AAESC,QAAAA,MAAM,GAAS;AACrB,cAAMC,KAAK,GAAG,KAAKC,YAAL,CAAkBrB,WAAlB,CAAd;AACAoB,UAAAA,KAAK,CAACE,KAAN,GAAcrB,IAAI,CAACsB,cAAL,GAAsBD,KAApC;AACAF,UAAAA,KAAK,CAACI,MAAN,GAAevB,IAAI,CAACsB,cAAL,GAAsBC,MAArC;AAEA,eAAKC,IAAL,CAAUC,EAAV,CAAa5B,KAAK,CAAC6B,SAAN,CAAgBC,YAA7B,EAA2C,KAAKC,aAAhD,EAA+D,IAA/D;AACA,eAAKJ,IAAL,CAAUC,EAAV,CAAa5B,KAAK,CAAC6B,SAAN,CAAgBG,SAA7B,EAAwC,KAAKC,UAA7C,EAAyD,IAAzD;AACH;;AAEOC,QAAAA,gBAAgB,CAACC,QAAD,EAAsBC,MAAtB,EAA6C;AACjE,cAAMC,aAAa,GAAGD,MAAM,CAACb,YAAP,CAAoBrB,WAApB,CAAtB;AACA,cAAI,CAACmC,aAAL,EAAoB,OAAO,KAAP;AACpB,cAAMC,IAAe,GAAGD,aAAa,CAACE,qBAAd,EAAxB;AACA,iBAAOD,IAAI,CAACE,QAAL,CAAcL,QAAd,CAAP;AACH;;AAEKM,QAAAA,MAAM,CAACC,MAAD,EAA4C;AAAA;;AAAA;AACpD,gBAAI,KAAI,CAACvB,cAAL,EAAJ,EAA2B;AAE3B,YAAA,KAAI,CAACR,SAAL,GAAiB+B,MAAM,CAACC,WAAP,IAAsB,IAAvC;AACA,YAAA,KAAI,CAAC/B,QAAL,GAAgB8B,MAAM,CAACE,UAAP,IAAqB,IAArC;AACA,YAAA,KAAI,CAAC/B,OAAL,GAAe6B,MAAM,CAACN,MAAP,IAAiB,IAAhC;AACA,YAAA,KAAI,CAAC1B,WAAL,GAAmBgC,MAAM,CAACG,UAAP,YAA6B5C,IAA7B,GAAoC,CAACyC,MAAM,CAACG,UAAR,CAApC,GAA0DH,MAAM,CAACG,UAApF;AACA,YAAA,KAAI,CAAC/B,cAAL,GAAsB4B,MAAM,CAACI,aAAP,IAAwB,KAA9C;AACA,YAAA,KAAI,CAAC/B,sBAAL,GAA8B2B,MAAM,CAACK,qBAAP,IAAgC,KAA9D;AARoD;AASvD;;AAEDhB,QAAAA,aAAa,GAAS;AAClB,cAAI,CAAC,KAAKhB,sBAAV,EAAkC;AAC9B;AAAA;AAAA,gCAAMiC,MAAN,CAAavC,cAAb;AACH;AACJ;;AAEDwB,QAAAA,UAAU,CAACgB,KAAD,EAA0B;AAChC,cAAMd,QAAQ,GAAGc,KAAK,CAACC,aAAN,EAAjB;;AACA,cAAMC,SAAS,GAAG,KAAKzC,WAAL,CAAiB0C,IAAjB,CAAsBC,EAAE,IAAI,KAAKnB,gBAAL,CAAsBC,QAAtB,EAAgCkB,EAAhC,CAA5B,CAAlB;;AAEA,cAAIF,SAAJ,EAAe;AAAA;;AACX;AACA,mCAAKvC,QAAL,4BAAe0C,IAAf,CAAoB,KAAKzC,OAAzB,EAAkCsC,SAAlC;;AACA,gBAAI,KAAKrC,cAAT,EAAyB;AACrB,mBAAKJ,WAAL,CAAiB6C,MAAjB,CAAwB,KAAK7C,WAAL,CAAiB8C,OAAjB,CAAyBL,SAAzB,CAAxB,EAA6D,CAA7D;;AACA;AACH;;AACD;AAAA;AAAA,gCAAMH,MAAN,CAAavC,cAAb;AACH,WARD,MAQO;AACH;AACA,gBAAI,CAAC,KAAKM,sBAAV,EAAkC;AAAA;;AAC9B,sCAAKJ,SAAL,6BAAgB2C,IAAhB,CAAqB,KAAKzC,OAA1B;AACA;AAAA;AAAA,kCAAMmC,MAAN,CAAavC,cAAb;AACH;AACJ;AACJ;;AAEKgD,QAAAA,MAAM,GAAkB;AAAA;;AAAA;AAC1B,YAAA,MAAI,CAAC/C,WAAL,GAAmB,EAAnB;AACA,YAAA,MAAI,CAACC,SAAL,GAAiB,IAAjB;AACA,YAAA,MAAI,CAACC,QAAL,GAAgB,IAAhB;AACA,YAAA,MAAI,CAACC,OAAL,GAAe,IAAf;AACA,YAAA,MAAI,CAACC,cAAL,GAAsB,KAAtB;AACA,YAAA,MAAI,CAACC,sBAAL,GAA8B,KAA9B;AAN0B;AAO7B;;AAEK2C,QAAAA,OAAO,GAAkB;AAAA;AAAG;;AA9EK,O", "sourcesContent": ["import { _decorator, EventTouch, Input, math, Node, UITransform, view } from 'cc';\nimport { BaseUI, UILayer, UIMgr } from '../UIMgr';\n\nconst { ccclass, property } = _decorator;\n\n/**\n * 点击控制UI的配置参数\n */\nexport interface ClickControlConfig {\n    onClickMiss?: () => void;        // 点击非目标区域回调\n    onClickHit?: (node: Node) => void;  // 点击目标区域回调\n    target?: any;                    // 回调函数的this对象\n    touchNodes: Node[] | Node;       // 需要监听的节点或节点数组\n    clickContinue?: boolean;         // 是否允许连续点击\n    onlyHideOnTargetClick?: boolean; // 是否只在点击目标节点时才隐藏\n}\n\n/**\n * 点击控制UI组件\n * 功能：创建一个全屏透明遮罩，用于控制特定区域的点击事件\n * 当点击在指定节点外时触发取消回调，点击在节点内时触发点击回调\n */\n@ccclass('ClickControlUI')\nexport class ClickControlUI extends BaseUI {\n    public static getUrl(): string { return \"ui/common/ClickControlUI\"; }\n    public static getLayer(): UILayer { return UILayer.Top; }\n\n    private _touchNodes: Node[] = [];\n    private _onCancel: (() => void) | null = null;\n    private _onClick: ((node: Node) => void) | null = null;\n    private _target: any = null;\n    private _clickContinue: boolean = false;\n    private _onlyHideOnTargetClick: boolean = false;\n\n    isTouchControl(): boolean {\n        return this._touchNodes.length > 0;\n    }\n\n    protected onLoad(): void {\n        const trans = this.getComponent(UITransform);\n        trans.width = view.getVisibleSize().width;\n        trans.height = view.getVisibleSize().height;\n\n        this.node.on(Input.EventType.TOUCH_CANCEL, this.onTouchCancel, this);\n        this.node.on(Input.EventType.TOUCH_END, this.onTouchEnd, this);\n    }\n\n    private _isTouchOnTarget(touchPos: math.Vec2, target: Node): boolean {\n        const targetUITrans = target.getComponent(UITransform);\n        if (!targetUITrans) return false;\n        const rect: math.Rect = targetUITrans.getBoundingBoxToWorld();\n        return rect.contains(touchPos);\n    }\n\n    async onShow(config: ClickControlConfig): Promise<void> {\n        if (this.isTouchControl()) return;\n\n        this._onCancel = config.onClickMiss || null;\n        this._onClick = config.onClickHit || null;\n        this._target = config.target || null;\n        this._touchNodes = config.touchNodes instanceof Node ? [config.touchNodes] : config.touchNodes;\n        this._clickContinue = config.clickContinue || false;\n        this._onlyHideOnTargetClick = config.onlyHideOnTargetClick || false;\n    }\n\n    onTouchCancel(): void {\n        if (!this._onlyHideOnTargetClick) {\n            UIMgr.hideUI(ClickControlUI);\n        }\n    }\n\n    onTouchEnd(event: EventTouch): void {\n        const touchPos = event.getUILocation();\n        const touchNode = this._touchNodes.find(nd => this._isTouchOnTarget(touchPos, nd));\n\n        if (touchNode) {\n            // 点击目标节点时触发点击回调\n            this._onClick?.call(this._target, touchNode);\n            if (this._clickContinue) {\n                this._touchNodes.splice(this._touchNodes.indexOf(touchNode), 1);\n                return;\n            }\n            UIMgr.hideUI(ClickControlUI);\n        } else {\n            // 点击非目标区域时触发取消回调\n            if (!this._onlyHideOnTargetClick) {\n                this._onCancel?.call(this._target);\n                UIMgr.hideUI(ClickControlUI);\n            }\n        }\n    }\n\n    async onHide(): Promise<void> {\n        this._touchNodes = [];\n        this._onCancel = null;\n        this._onClick = null;\n        this._target = null;\n        this._clickContinue = false;\n        this._onlyHideOnTargetClick = false;\n    }\n\n    async onClose(): Promise<void> { }\n}"]}