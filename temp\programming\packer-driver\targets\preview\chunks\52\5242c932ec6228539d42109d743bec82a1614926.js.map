{"version": 3, "sources": ["file:///E:/M2Game/Client/assets/scripts/ui/main/plane/components/display/MergeDisplay.ts"], "names": ["_decorator", "Component", "ccclass", "property", "MergeDisplay"], "mappings": ";;;;;;;;;;AAASA,MAAAA,U,OAAAA,U;AAAYC,MAAAA,S,OAAAA,S;;;;;;;;;OACf;AAAEC,QAAAA,OAAF;AAAWC,QAAAA;AAAX,O,GAAwBH,U;;8BAGjBI,Y,WADZF,OAAO,CAAC,cAAD,C,gBAAR,MACaE,YADb,SACkCH,SADlC,CAC4C,E", "sourcesContent": ["import { _decorator, Component } from 'cc';\nconst { ccclass, property } = _decorator;\n\n@ccclass('MergeDisplay')\nexport class MergeDisplay extends Component {\n\n}\n\n"]}