{"version": 3, "sources": ["file:///E:/M2Game/Client/assets/scripts/GameOld/Enemy.ts"], "names": ["_decorator", "Component", "Sprite", "Vec3", "find", "ProgressBar", "Collider2D", "Contact2DType", "Label", "director", "Animation", "Global", "GamePersistNode", "Player", "audioManager", "ccclass", "property", "Enemy", "enemyType", "curPos", "enemyFactory", "enemyBulletFactory", "persistNode", "enemy1MoveSpeed", "enemy2MoveSpeed", "enemy1ShootTimer", "enemy2ShootTimer", "enemy1ShootSpeed", "enemy2ShootSpeed", "enemyTotalBlood", "enemyBlood", "enemyContactPlayerReduce", "onLoad", "getComponent", "collider", "node", "on", "BEGIN_CONTACT", "onBeginContact", "selfCollider", "otherCollider", "contact", "tag", "planeBlood", "getChildByName", "progress", "planeTotalBlood", "ENEMY_1", "SCORE", "string", "toString", "anim", "animFactory", "createAnim", "setPosition", "getPosition", "<PERSON><PERSON><PERSON><PERSON>", "play", "recycleProduct", "instance", "playSound", "boomAudioClip", "loadScene", "gameOverAudioClip", "init", "spriteFrame", "update", "deltaTime", "enem1Move", "enemy1Shoot", "ENEMY_2", "enem2Move", "enemy2Shoot", "posBegin", "enemyBullet", "createProduct", "ENEMY_BULLET_2", "parent", "x", "y", "ENEMY_BULLET_1", "HEIGHT"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAASA,MAAAA,U,OAAAA,U;AAAYC,MAAAA,S,OAAAA,S;AAA8BC,MAAAA,M,OAAAA,M;AAAQC,MAAAA,I,OAAAA,I;AAAMC,MAAAA,I,OAAAA,I;AAAMC,MAAAA,W,OAAAA,W;AAAaC,MAAAA,U,OAAAA,U;AAAYC,MAAAA,a,OAAAA,a;AAAkCC,MAAAA,K,OAAAA,K;AAAYC,MAAAA,Q,OAAAA,Q;AAAUC,MAAAA,S,OAAAA,S;;AAG/IC,MAAAA,M,iBAAAA,M;;AACAC,MAAAA,e,iBAAAA,e;;AACAC,MAAAA,M,iBAAAA,M;;AACAC,MAAAA,Y,iBAAAA,Y;;;;;;;;;OACH;AAAEC,QAAAA,OAAF;AAAWC,QAAAA;AAAX,O,GAAwBhB,U;;uBAGjBiB,K,WADZF,OAAO,CAAC,OAAD,C,gBAAR,MACaE,KADb,SAC2BhB,SAD3B,CACqC;AAAA;AAAA;AAAA,eAEjCiB,SAFiC,GAEb,IAFa;AAAA,eAIjCC,MAJiC,GAIlB,IAJkB;AAAA,eAMjCC,YANiC,GAML,IANK;AAAA,eAOjCC,kBAPiC,GAOC,IAPD;AAAA,eASjCC,WATiC,GASb,IATa;AAAA,eAWjCC,eAXiC,GAWP,CAXO;AAWD;AAXC,eAajCC,eAbiC,GAaP,CAbO;AAaD;AAbC,eAejCC,gBAfiC,GAeN,CAfM;AAeD;AAfC,eAiBjCC,gBAjBiC,GAiBN,CAjBM;AAiBD;AAjBC,eAmBjCC,gBAnBiC,GAmBN,CAnBM;AAmBD;AAnBC,eAqBjCC,gBArBiC,GAqBN,CArBM;AAqBD;AArBC,eAuBjCC,eAvBiC,GAuBP,CAvBO;AAuBD;AAvBC,eAyBjCC,UAzBiC,GAyBZ,CAzBY;AAyBD;AAzBC,eA2BjCC,wBA3BiC,GA2BE,CA3BF;AAAA;;AA2BM;AAEvCC,QAAAA,MAAM,GAAG;AACL,eAAKV,WAAL,GAAmBlB,IAAI,CAAC,iBAAD,CAAvB;AACA,eAAKgB,YAAL,GAAoB,KAAKE,WAAL,CAAiBW,YAAjB;AAAA;AAAA,kDAA+Cb,YAAnE;AACA,eAAKC,kBAAL,GAA0B,KAAKC,WAAL,CAAiBW,YAAjB;AAAA;AAAA,kDAA+CZ,kBAAzE;AAEA,eAAKE,eAAL,GAAuB,KAAKD,WAAL,CAAiBW,YAAjB;AAAA;AAAA,kDAA+CV,eAAtE;AACA,eAAKC,eAAL,GAAuB,KAAKF,WAAL,CAAiBW,YAAjB;AAAA;AAAA,kDAA+CT,eAAtE;AAEA,eAAKG,gBAAL,GAAwB,KAAKL,WAAL,CAAiBW,YAAjB;AAAA;AAAA,kDAA+CN,gBAAvE;AACA,eAAKC,gBAAL,GAAwB,KAAKN,WAAL,CAAiBW,YAAjB;AAAA;AAAA,kDAA+CL,gBAAvE;AAEA,eAAKC,eAAL,GAAuB,KAAKP,WAAL,CAAiBW,YAAjB;AAAA;AAAA,kDAA+CJ,eAAtE;AACA,eAAKC,UAAL,GAAkB,KAAKD,eAAvB;AAEA,eAAKE,wBAAL,GAAgC,KAAKT,WAAL,CAAiBW,YAAjB;AAAA;AAAA,kDAA+CF,wBAA/E;AAEA,cAAIG,QAAQ,GAAG,KAAKC,IAAL,CAAUF,YAAV,CAAuB3B,UAAvB,CAAf;;AAEA,cAAI4B,QAAJ,EAAc;AACVA,YAAAA,QAAQ,CAACE,EAAT,CAAY7B,aAAa,CAAC8B,aAA1B,EAAyC,KAAKC,cAA9C,EAA8D,IAA9D;AACH;AAEJ;;AAEAA,QAAAA,cAAc,CAACC,YAAD,EAA2BC,aAA3B,EAAsDC,OAAtD,EAAyF;AACpG,cAAID,aAAa,CAACE,GAAd,IAAqB,CAAzB,EAA4B;AACxBF,YAAAA,aAAa,CAACP,YAAd;AAAA;AAAA,kCAAmCU,UAAnC,IAAiD,KAAKZ,wBAAtD;AACAS,YAAAA,aAAa,CAACL,IAAd,CAAmBS,cAAnB,CAAkC,OAAlC,EAA2CX,YAA3C,CAAwD5B,WAAxD,EAAqEwC,QAArE,GAAgFL,aAAa,CAACP,YAAd;AAAA;AAAA,kCAAmCU,UAAnC,GAAgDH,aAAa,CAACP,YAAd;AAAA;AAAA,kCAAmCa,eAAnK;;AAEA,gBAAI,KAAK5B,SAAL,IAAkB;AAAA;AAAA,kCAAO6B,OAA7B,EAAsC;AAClC;AAAA;AAAA,oCAAOC,KAAP,IAAgB,EAAhB;AACH,aAFD,MAEO;AACH;AAAA;AAAA,oCAAOA,KAAP,IAAgB,EAAhB;AACH;;AACD5C,YAAAA,IAAI,CAAC,cAAD,CAAJ,CAAqB6B,YAArB,CAAkCzB,KAAlC,EAAyCyC,MAAzC,GAAkD,WAAW;AAAA;AAAA,kCAAOD,KAAP,CAAaE,QAAb,EAA7D,CATwB,CAWxB;;AACA,gBAAIC,IAAI,GAAG,KAAK7B,WAAL,CAAiBW,YAAjB;AAAA;AAAA,oDAA+CmB,WAA/C,CAA2DC,UAA3D,EAAX;AACAF,YAAAA,IAAI,CAACG,WAAL,CAAiB,KAAKnB,IAAL,CAAUoB,WAAV,EAAjB;AACAnD,YAAAA,IAAI,CAAC,QAAD,CAAJ,CAAeoD,QAAf,CAAwBL,IAAxB;AACAA,YAAAA,IAAI,CAAClB,YAAL,CAAkBvB,SAAlB,EAA6B+C,IAA7B,GAfwB,CAegB;;AAExC,iBAAKrC,YAAL,CAAkBsC,cAAlB,CAAiC,KAAKvB,IAAtC,EAjBwB,CAiBwB;;AAChD;AAAA;AAAA,8CAAawB,QAAb,CAAsBC,SAAtB,CAAgC,KAAKtC,WAAL,CAAiBW,YAAjB;AAAA;AAAA,oDAA+C4B,aAA/E;;AAEA,gBAAIrB,aAAa,CAACP,YAAd;AAAA;AAAA,kCAAmCU,UAAnC,IAAiD,CAArD,EAAwD;AACpDlC,cAAAA,QAAQ,CAACqD,SAAT,CAAmB,MAAnB;AACA;AAAA;AAAA,gDAAaH,QAAb,CAAsBC,SAAtB,CAAgC,KAAKtC,WAAL,CAAiBW,YAAjB;AAAA;AAAA,sDAA+C8B,iBAA/E;AACH;AACJ;AACJ;AAED;AACJ;AACA;;;AACIC,QAAAA,IAAI,CAAC9C,SAAD,EAAoB+C,WAApB,EAA8C;AAC9C,eAAK/C,SAAL,GAAiBA,SAAjB;AACA,eAAKiB,IAAL,CAAUF,YAAV,CAAuB/B,MAAvB,EAA+B+D,WAA/B,GAA6CA,WAA7C;AACA,eAAKnC,UAAL,GAAkB,KAAKD,eAAvB,CAH8C,CAGF;;AAC5C,eAAKM,IAAL,CAAUS,cAAV,CAAyB,YAAzB,EAAuCX,YAAvC,CAAoD5B,WAApD,EAAiEwC,QAAjE,GAA4E,CAA5E;AACH;;AAEDqB,QAAAA,MAAM,CAACC,SAAD,EAAoB;AACtB,cAAI,KAAKjD,SAAL,IAAkB;AAAA;AAAA,gCAAO6B,OAA7B,EAAsC;AAAM;AACxC,iBAAKqB,SAAL,CAAeD,SAAf,EADkC,CACE;AAEpC;;AACA,iBAAK1C,gBAAL,IAAyB0C,SAAzB;;AACA,gBAAI,KAAK1C,gBAAL,GAAwB,KAAKE,gBAAjC,EAAmD;AAC/C,mBAAK0C,WAAL;AACA,mBAAK5C,gBAAL,GAAwB,CAAxB;AACH;AAEJ,WAVD,MAUO,IAAI,KAAKP,SAAL,IAAkB;AAAA;AAAA,gCAAOoD,OAA7B,EAAsC;AAAO;AAChD,iBAAKC,SAAL,CAAeJ,SAAf,EADyC,CAGzC;;AACA,iBAAKzC,gBAAL,IAAyByC,SAAzB;;AACA,gBAAI,KAAKzC,gBAAL,GAAwB,KAAKE,gBAAjC,EAAmD;AAC/C,mBAAK4C,WAAL;AACA,mBAAK9C,gBAAL,GAAwB,CAAxB;AACH;AACJ;AACJ;AAED;AACJ;AACA;;;AACK8C,QAAAA,WAAW,GAAG;AACX,cAAIC,QAAc,GAAG,IAAItE,IAAJ,EAArB,CADW,CACyB;;AACpC,cAAIuE,WAAiB,GAAG,IAAxB;AAEAA,UAAAA,WAAW,GAAG,KAAKrD,kBAAL,CAAwBsD,aAAxB,CAAsC;AAAA;AAAA,gCAAOC,cAA7C,CAAd,CAJW,CAIkE;;AAC7E,eAAKzC,IAAL,CAAU0C,MAAV,CAAiBrB,QAAjB,CAA0BkB,WAA1B,EALW,CAKoC;;AAE/C,eAAKvD,MAAL,GAAc,KAAKgB,IAAL,CAAUoB,WAAV,EAAd,CAPW,CAOiC;;AAC5CkB,UAAAA,QAAQ,CAACK,CAAT,GAAa,KAAK3D,MAAL,CAAY2D,CAAzB;AACAL,UAAAA,QAAQ,CAACM,CAAT,GAAa,KAAK5D,MAAL,CAAY4D,CAAZ,GAAgB,EAA7B,CATW,CAS6B;;AACxCL,UAAAA,WAAW,CAACpB,WAAZ,CAAwBmB,QAAxB;AACH;AAED;AACJ;AACA;;;AACIJ,QAAAA,WAAW,GAAG;AACV,cAAII,QAAc,GAAG,IAAItE,IAAJ,EAArB,CADU,CAC0B;;AACpC,cAAIuE,WAAiB,GAAG,IAAxB;AAEAA,UAAAA,WAAW,GAAG,KAAKrD,kBAAL,CAAwBsD,aAAxB,CAAsC;AAAA;AAAA,gCAAOK,cAA7C,CAAd,CAJU,CAImE;;AAC7E,eAAK7C,IAAL,CAAU0C,MAAV,CAAiBrB,QAAjB,CAA0BkB,WAA1B,EALU,CAKqC;;AAE/C,eAAKvD,MAAL,GAAc,KAAKgB,IAAL,CAAUoB,WAAV,EAAd,CAPU,CAOkC;;AAC5CkB,UAAAA,QAAQ,CAACK,CAAT,GAAa,KAAK3D,MAAL,CAAY2D,CAAzB;AACAL,UAAAA,QAAQ,CAACM,CAAT,GAAa,KAAK5D,MAAL,CAAY4D,CAAZ,GAAgB,EAA7B,CATU,CAS8B;;AACxCL,UAAAA,WAAW,CAACpB,WAAZ,CAAwBmB,QAAxB;AACH;AAED;AACJ;AACA;;;AACIL,QAAAA,SAAS,CAACD,SAAD,EAAoB;AACzB,eAAKhD,MAAL,GAAc,KAAKgB,IAAL,CAAUoB,WAAV,EAAd;AACA,eAAKpC,MAAL,CAAY4D,CAAZ,IAAiB,KAAKxD,eAAL,GAAuB4C,SAAxC;AACA,eAAKhC,IAAL,CAAUmB,WAAV,CAAsB,KAAKnC,MAA3B;;AAEA,cAAI,KAAKA,MAAL,CAAY4D,CAAZ,GAAgB,CAAC;AAAA;AAAA,gCAAOE,MAAR,GAAiB,CAArC,EAAwC;AACpC,iBAAK7D,YAAL,CAAkBsC,cAAlB,CAAiC,KAAKvB,IAAtC;AACH;AACJ;AAED;AACJ;AACA;;;AACKoC,QAAAA,SAAS,CAACJ,SAAD,EAAoB;AAC1B,eAAKhD,MAAL,GAAc,KAAKgB,IAAL,CAAUoB,WAAV,EAAd;AACA,eAAKpC,MAAL,CAAY4D,CAAZ,IAAiB,KAAKvD,eAAL,GAAuB2C,SAAxC;AACA,eAAKhC,IAAL,CAAUmB,WAAV,CAAsB,KAAKnC,MAA3B;;AAEA,cAAI,KAAKA,MAAL,CAAY4D,CAAZ,GAAgB,CAAC;AAAA;AAAA,gCAAOE,MAAR,GAAiB,CAArC,EAAwC;AACpC,iBAAK7D,YAAL,CAAkBsC,cAAlB,CAAiC,KAAKvB,IAAtC;AACH;AACJ;;AA1KgC,O", "sourcesContent": ["import { _decorator, Component, Node, Sprite<PERSON>rame, Sprite, Vec3, find, ProgressBar, Collider2D, Contact2DType, IPhysics2DContact, Label, log, director, Animation } from 'cc';\nimport { EnemyBullet } from './EnemyBullet';\nimport { GameFactory } from './factroy/GameFactory';\nimport { Global } from './Global';\nimport { GamePersistNode } from './GamePersistNode';\nimport { Player } from './Player';\nimport { audioManager } from '../ResUpdate/audioManager';\nconst { ccclass, property } = _decorator;\n\n@ccclass('Enemy')\nexport class Enemy extends Component {\n    \n    enemyType: string = null;\n\n    curPos: Vec3 = null;\n\n    enemyFactory: GameFactory = null;\n    enemyBulletFactory: GameFactory = null;\n\n    persistNode: Node = null;\n\n    enemy1MoveSpeed: number = 0;    //敌机1的移动速度\n\n    enemy2MoveSpeed: number = 0;    //敌机1的移动速度\n\n    enemy1ShootTimer: number = 0;   //敌机1发射子弹计时器\n\n    enemy2ShootTimer: number = 0;   //敌机2发射子弹计时器\n\n    enemy1ShootSpeed: number = 0;   //敌机1发射子弹时间间隔\n\n    enemy2ShootSpeed: number = 0;   //敌机2发射子弹时间间隔\n\n    enemyTotalBlood: number = 0;    //敌机总血量\n\n    enemyBlood: number = 0;         //敌机当前血量\n\n    enemyContactPlayerReduce: number = 0;  //敌机碰到玩家，玩家掉多少血\n\n    onLoad() {\n        this.persistNode = find(\"GamePersistNode\");\n        this.enemyFactory = this.persistNode.getComponent(GamePersistNode).enemyFactory;\n        this.enemyBulletFactory = this.persistNode.getComponent(GamePersistNode).enemyBulletFactory;\n\n        this.enemy1MoveSpeed = this.persistNode.getComponent(GamePersistNode).enemy1MoveSpeed;\n        this.enemy2MoveSpeed = this.persistNode.getComponent(GamePersistNode).enemy2MoveSpeed;\n\n        this.enemy1ShootSpeed = this.persistNode.getComponent(GamePersistNode).enemy1ShootSpeed;\n        this.enemy2ShootSpeed = this.persistNode.getComponent(GamePersistNode).enemy2ShootSpeed;\n\n        this.enemyTotalBlood = this.persistNode.getComponent(GamePersistNode).enemyTotalBlood;\n        this.enemyBlood = this.enemyTotalBlood; \n\n        this.enemyContactPlayerReduce = this.persistNode.getComponent(GamePersistNode).enemyContactPlayerReduce;\n\n        let collider = this.node.getComponent(Collider2D);\n\n        if (collider) {\n            collider.on(Contact2DType.BEGIN_CONTACT, this.onBeginContact, this);\n        }\n\n    }\n\n     onBeginContact(selfCollider: Collider2D, otherCollider: Collider2D, contact: IPhysics2DContact | null) {\n        if (otherCollider.tag == 1) {\n            otherCollider.getComponent(Player).planeBlood -= this.enemyContactPlayerReduce;\n            otherCollider.node.getChildByName(\"Blood\").getComponent(ProgressBar).progress = otherCollider.getComponent(Player).planeBlood / otherCollider.getComponent(Player).planeTotalBlood;\n            \n            if (this.enemyType == Global.ENEMY_1) {\n                Global.SCORE += 20;\n            } else {\n                Global.SCORE += 40;\n            }\n            find(\"Canvas/Score\").getComponent(Label).string = \"Score:\" + Global.SCORE.toString();\n\n            //添加动画节点\n            let anim = this.persistNode.getComponent(GamePersistNode).animFactory.createAnim();\n            anim.setPosition(this.node.getPosition());\n            find(\"Canvas\").addChild(anim);\n            anim.getComponent(Animation).play();    //播放动画\n\n            this.enemyFactory.recycleProduct(this.node);    //敌机消失\n            audioManager.instance.playSound(this.persistNode.getComponent(GamePersistNode).boomAudioClip);\n\n            if (otherCollider.getComponent(Player).planeBlood <= 0) {\n                director.loadScene(\"Main\");\n                audioManager.instance.playSound(this.persistNode.getComponent(GamePersistNode).gameOverAudioClip);\n            }\n        }\n    }\n\n    /**\n     * 敌机初始化函数\n     */\n    init(enemyType: string, spriteFrame: SpriteFrame) {\n        this.enemyType = enemyType;\n        this.node.getComponent(Sprite).spriteFrame = spriteFrame;\n        this.enemyBlood = this.enemyTotalBlood;     //敌机满血复活\n        this.node.getChildByName(\"EnemyBlood\").getComponent(ProgressBar).progress = 1;\n    }\n\n    update(deltaTime: number) {\n        if (this.enemyType == Global.ENEMY_1) {     //敌机1相关操作\n            this.enem1Move(deltaTime);          //敌机1移动\n\n            //敌机1发射子弹\n            this.enemy1ShootTimer += deltaTime;\n            if (this.enemy1ShootTimer > this.enemy1ShootSpeed) {\n                this.enemy1Shoot();\n                this.enemy1ShootTimer = 0;\n            }\n            \n        } else if (this.enemyType == Global.ENEMY_2) {      //敌机2相关操作\n            this.enem2Move(deltaTime);\n\n            //敌机2发射子弹\n            this.enemy2ShootTimer += deltaTime;\n            if (this.enemy2ShootTimer > this.enemy2ShootSpeed) {\n                this.enemy2Shoot();\n                this.enemy2ShootTimer = 0;\n            }\n        }\n    }\n\n    /**\n     * 敌机2发射子弹\n     */\n     enemy2Shoot() {\n        let posBegin: Vec3 = new Vec3();    //定义子弹开始的位置\n        let enemyBullet: Node = null;\n\n        enemyBullet = this.enemyBulletFactory.createProduct(Global.ENEMY_BULLET_2);  //制作子弹\n        this.node.parent.addChild(enemyBullet);        //添加节点到画布\n\n        this.curPos = this.node.getPosition();      //得到敌机机当前位置\n        posBegin.x = this.curPos.x; \n        posBegin.y = this.curPos.y - 50;        //设置到机头位置\n        enemyBullet.setPosition(posBegin);\n    }\n\n    /**\n     * 敌机1发射子弹\n     */\n    enemy1Shoot() {\n        let posBegin: Vec3 = new Vec3();    //定义子弹开始的位置\n        let enemyBullet: Node = null;\n\n        enemyBullet = this.enemyBulletFactory.createProduct(Global.ENEMY_BULLET_1);  //制作子弹\n        this.node.parent.addChild(enemyBullet);        //添加节点到画布\n\n        this.curPos = this.node.getPosition();      //得到敌机机当前位置\n        posBegin.x = this.curPos.x; \n        posBegin.y = this.curPos.y - 50;        //设置到机头位置\n        enemyBullet.setPosition(posBegin);\n    }\n\n    /**\n     * 敌机1移动\n     */\n    enem1Move(deltaTime: number) {\n        this.curPos = this.node.getPosition();\n        this.curPos.y -= this.enemy1MoveSpeed * deltaTime;\n        this.node.setPosition(this.curPos);\n\n        if (this.curPos.y < -Global.HEIGHT / 2) {\n            this.enemyFactory.recycleProduct(this.node);\n        }\n    }\n\n    /**\n     * 敌机2移动\n     */\n     enem2Move(deltaTime: number) {\n        this.curPos = this.node.getPosition();\n        this.curPos.y -= this.enemy2MoveSpeed * deltaTime;\n        this.node.setPosition(this.curPos);\n\n        if (this.curPos.y < -Global.HEIGHT / 2) {\n            this.enemyFactory.recycleProduct(this.node);\n        }\n    }\n}\n\n"]}