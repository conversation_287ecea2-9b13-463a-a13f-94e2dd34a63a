{"version": 3, "sources": ["file:///E:/M2Game/Client/assets/scripts/ui/main/TopUI.ts"], "names": ["_decorator", "BaseUI", "<PERSON><PERSON><PERSON><PERSON>", "ccclass", "property", "TopUI", "getUrl", "<PERSON><PERSON><PERSON><PERSON>", "Top", "onLoad", "onShow", "onHide", "onClose", "update", "dt"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;AAASA,MAAAA,U,OAAAA,U;;AACAC,MAAAA,M,iBAAAA,M;AAAQC,MAAAA,O,iBAAAA,O;;;;;;;;;OACX;AAAEC,QAAAA,OAAF;AAAWC,QAAAA;AAAX,O,GAAwBJ,U;;uBAGjBK,K,WADZF,OAAO,CAAC,OAAD,C,gBAAR,MACaE,KADb;AAAA;AAAA,4BACkC;AACV,eAANC,MAAM,GAAW;AAAE,iBAAO,eAAP;AAAyB;;AACpC,eAARC,QAAQ,GAAY;AAAE,iBAAO;AAAA;AAAA,kCAAQC,GAAf;AAAoB;;AAE9CC,QAAAA,MAAM,GAAS,CAExB;;AAEKC,QAAAA,MAAM,GAAgC;AAAA;AAC3C;;AACKC,QAAAA,MAAM,GAAgC;AAAA;AAC3C;;AACKC,QAAAA,OAAO,GAAgC;AAAA;AAC5C;;AACSC,QAAAA,MAAM,CAACC,EAAD,EAAmB,CAClC;;AAf6B,O", "sourcesContent": ["import { _decorator } from 'cc';\nimport { BaseUI, UILayer } from '../UIMgr';\nconst { ccclass, property } = _decorator;\n\n@ccclass('TopUI')\nexport class TopUI extends BaseUI {\n    public static getUrl(): string { return \"ui/main/TopUI\"; }\n    public static getLayer(): UILayer { return UILayer.Top }\n\n    protected onLoad(): void {\n\n    }\n\n    async onShow(...args: any[]): Promise<void> {\n    }\n    async onHide(...args: any[]): Promise<void> {\n    }\n    async onClose(...args: any[]): Promise<void> {\n    }\n    protected update(dt: number): void {\n    }\n\n}\n\n"]}