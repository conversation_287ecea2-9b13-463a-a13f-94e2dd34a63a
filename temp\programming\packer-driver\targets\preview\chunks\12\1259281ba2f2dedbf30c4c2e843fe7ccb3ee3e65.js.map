{"version": 3, "sources": ["file:///E:/M2Game/Client/assets/scripts/GameOld/world/base/Entity.ts"], "names": ["_decorator", "Object", "ccclass", "Entity", "_eid", "eid", "onWorldCreate", "world", "generateId", "registerEntity"], "mappings": ";;;;;;;;;;;;;;;;;;;;AAASA,MAAAA,U,OAAAA,U;;AACAC,MAAAA,M,iBAAAA,M;;;;;;;;;OAEH;AAAEC,QAAAA;AAAF,O,GAAcF,U;AAEpB;AACA;AACA;AACA;;wBAEsBG,M,WADrBD,OAAO,CAAC,QAAD,C,gBAAR,MACsBC,MADtB;AAAA;AAAA,4BAC4C;AAAA;AAAA;AAAA,eAE9BC,IAF8B,GAEf,CAFe;AAAA;;AAG1B,YAAHC,GAAG,GAAW;AACrB,iBAAO,KAAKD,IAAZ;AACH;;AAESE,QAAAA,aAAa,CAACC,KAAD,EAAqB;AACxC,gBAAMD,aAAN,CAAoBC,KAApB;AACA,eAAKH,IAAL,GAAYG,KAAK,CAACC,UAAN,EAAZ;AAEAD,UAAAA,KAAK,CAACE,cAAN,CAAqB,IAArB;AACH;;AAZuC,O", "sourcesContent": ["import { _decorator, Component } from 'cc';\r\nimport { Object } from './Object';\r\nimport { World } from './World';\r\nconst { ccclass } = _decorator;\r\n\r\n/**\r\n * Abstract base class for all world Entity\r\n * Inherits from Cocos Creator Component and provides common functionality\r\n */\r\n@ccclass('Entity')\r\nexport abstract class Entity extends Object {\r\n    \r\n    protected _eid: number = 0;\r\n    public get eid(): number {\r\n        return this._eid;\r\n    }\r\n\r\n    protected onWorldCreate(world: World): void {\r\n        super.onWorldCreate(world);\r\n        this._eid = world.generateId();\r\n\r\n        world.registerEntity(this);\r\n    }\r\n}"]}