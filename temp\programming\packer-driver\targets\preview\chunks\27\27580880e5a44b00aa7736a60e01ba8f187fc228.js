// const { ccclass, property } = cc._decorator;
// enum LaserState {
//     idle = 0,
//     launch = 1,
//     fire = 2,
//     remove = 3,
// }
// @ccclass
// export default class LJLaserBullet extends LaserBullet {
//     private m_target: any = null;
//     private m_targetUuid: number = 0;
//     private attacked: boolean = false;
//     private sceneEntity: any = null;
//     private lifePos: cc.Vec2 = cc.Vec2.ZERO;
//     private time: number = 0;
//     private m_state: LaserState = LaserState.idle;
//     private m_hurtEffect: cc.Node = null;
//     /**
//      * 初始化激光子弹
//      * @param enemy 敌人
//      * @param position 子弹初始位置
//      * @param bulletState 子弹状态
//      * @param mainEntity 主实体
//      */
//     init(enemy: any, position: cc.Vec2, bulletState: any, mainEntity: any): void {
//         this.node.x = position.x;
//         this.node.y = position.y;
//         this.enemy = enemy;
//         this.bulletState = bulletState;
//         this.attacked = false;
//         this.laserImg.node.parent.x = 0;
//         this.m_state = LaserState.idle;
//         this.m_comps.forEach((comp) => {
//             comp.init(this);
//         });
//         this.playHurt = false;
//         this.node.angle = 180;
//         this.launchImg.node.angle = 180;
//         this.laserImg.node.parent.scaleX = this.props.scale;
//         const sceneEntity = SceneManager.me.getSceneEntity(mainEntity);
//         this.m_flyComp.setTarget(sceneEntity, mainEntity, { x: 0, y: 0 }, true);
//         this.sceneEntity = sceneEntity;
//         this.m_hurtEffect = cc.instantiate(GameConst.LJLaserPoint);
//         this.launchImg.setSpeed(15);
//         this.launch();
//         this.scheduleOnce(() => {
//             this.fireLaser();
//             this.time = 0;
//         }, this.props.launchTime);
//     }
//     /**
//      * 激光发射阶段
//      */
//     launch(): void {
//         if (this.sceneEntity instanceof WinePlane) {
//             this.sceneEntity.updateAngle();
//         }
//         this.m_state = LaserState.launch;
//         this.m_collideComp.enabled = false;
//         this.launchImg.play(0);
//     }
//     /**
//      * 设置目标
//      * @param target 目标实体
//      * @param targetCollider 目标碰撞器
//      */
//     setTarget(target: any, targetCollider: any): void {
//         this.m_target = target;
//         this.m_targetCollider = targetCollider;
//         if (target) {
//             this.m_targetUuid = target.new_uuid;
//             if (!this.m_hurtEffect) {
//                 this.m_hurtEffect = cc.instantiate(GameConst.LJLaserPoint);
//             }
//             this.node.opacity = 255;
//         }
//     }
//     /**
//      * 播放激光发射音效
//      */
//     async playFireAudio(): Promise<void> {
//         await frameWork.audioManager.playEffect("pt_jg");
//     }
//     /**
//      * 激光发射逻辑
//      */
//     fireLaser(): void {
//         if (this.m_state === LaserState.remove) {
//             return;
//         }
//         if (this.sceneEntity instanceof WinePlane) {
//             this.sceneEntity.updateAngle();
//             const angle = this.sceneEntity.getFireBulletAngle();
//             const topY = this.sceneEntity.top.y;
//             const topX = this.sceneEntity.top.x;
//             cc.tween(this.sceneEntity.top)
//                 .to(UIAnimMethods.fromTo(1, 4), {
//                     y: topY - 24 * Math.cos((angle / 180) * Math.PI),
//                     x: topX + 24 * Math.sin((angle / 180) * Math.PI),
//                 })
//                 .to(UIAnimMethods.fromTo(4, 9), {
//                     y: topY + 2 * Math.cos((angle / 180) * Math.PI),
//                     x: topX - 2 * Math.sin((angle / 180) * Math.PI),
//                 })
//                 .to(UIAnimMethods.fromTo(9, 12), {
//                     y: topY,
//                     x: topX,
//                 })
//                 .call(this.sceneEntity.top.stopAllActions.bind(this.sceneEntity.top))
//                 .start();
//         }
//         this.playFireAudio();
//         this.m_state = LaserState.fire;
//         this.laserImg.node.parent.height = 0;
//         this.laserImg.node.parent.scaleX = this.props.scale;
//         cc.tween(this.laserImg.node.parent)
//             .delay(this.props.laserTime)
//             .to(0.2, { scaleX: 0 })
//             .start();
//         this.laserImg.play(0);
//         this.playHurtEffect();
//         this.scheduleOnce(() => {
//             this.m_collideComp.enabled = true;
//             if (this.m_state === LaserState.fire) {
//                 this.remove();
//             }
//         }, this.props.laserTime + 0.2);
//     }
//     /**
//      * 播放受击效果
//      */
//     playHurtEffect(): void {
//         if (this.m_hurtEffect) {
//             this.m_hurtEffect.parent = EnemyEffectLayer.me.hurtEffectLayer;
//             this.m_hurtEffect.opacity = 0;
//         }
//     }
//     /**
//      * 每帧更新逻辑
//      * @param deltaTime 时间增量
//      */
//     update(deltaTime: number): void {
//         if (!GameConst.GameAble || this.m_state === LaserState.remove) {
//             return;
//         }
//         // 限制 deltaTime 的最大值
//         if (deltaTime > 0.2) {
//             deltaTime = 0.016666666666667; // 约等于 1/60 秒
//         }
//         // 如果目标碰撞器为空且处于发射阶段
//         if (this.m_targetCollider == null && this.m_state === LaserState.launch) {
//             if (this.sceneEntity instanceof WinePlane) {
//                 this.sceneEntity.stopActions();
//                 this.launchImg.node.angle = 180 - this.sceneEntity.angleChange(this.lifePos);
//             }
//             this.remove();
//             return;
//         }
//         let targetPos: cc.Vec2;
//         let targetHeight = 0;
//         // 如果目标碰撞器存在
//         if (this.m_targetCollider != null) {
//             targetPos = this.m_targetCollider.getScreenPos();
//             this.lifePos.x = targetPos.x;
//             this.lifePos.y = targetPos.y;
//             // 检查目标是否有效
//             if (
//                 this.m_target == null ||
//                 this.m_target.new_uuid !== this.m_targetUuid ||
//                 this.m_target.node == null ||
//                 this.m_target.node.parent == null ||
//                 !this.m_target.node.active ||
//                 !this.m_targetCollider.enabled ||
//                 targetPos.x < -360 ||
//                 targetPos.x > 360 ||
//                 targetPos.y > 0 ||
//                 targetPos.y < 300 - GameConst.ViewHeight
//             ) {
//                 if (this.sceneEntity instanceof WinePlane) {
//                     this.launchImg.node.angle = 180 - this.sceneEntity.angleChange(this.lifePos);
//                 }
//             } else {
//                 targetHeight = this.m_targetCollider.data.height / 2;
//                 if (this.sceneEntity instanceof WinePlane) {
//                     this.launchImg.node.angle = 180 - this.sceneEntity.angleChange();
//                 }
//             }
//         } else {
//             targetPos = this.lifePos || cc.Vec2.ZERO;
//             if (this.sceneEntity instanceof WinePlane) {
//                 this.launchImg.node.angle = 180 - this.sceneEntity.angleChange(this.lifePos);
//             }
//         }
//         // 更新组件
//         this.m_comps.forEach((comp) => {
//             comp.update(deltaTime);
//         });
//         this.node.opacity = 255;
//         // 更新激光位置和角度
//         const currentPos = this.node.position;
//         this.m_hurtEffect.x = targetPos.x;
//         this.m_hurtEffect.y = targetPos.y - targetHeight;
//         this.m_hurtEffect.opacity = 255;
//         if (
//             this.m_targetCollider != null &&
//             "onCollide" in this.m_targetCollider.entity &&
//             this.m_state === LaserState.fire &&
//             !this.attacked
//         ) {
//             this.m_targetCollider.entity.onCollide(this.m_collideComp);
//         }
//         const distance = this.m_target == null ||
//             this.m_target.new_uuid !== this.m_targetUuid ||
//             this.m_target.node == null ||
//             this.m_target.node.parent == null ||
//             !this.m_target.node.active ||
//             !this.m_targetCollider.enabled
//             ? 2000
//             : Tools.DYTools.getPosDis(currentPos, targetPos);
//         const angle = Tools.DYTools.getAngle(currentPos, targetPos);
//         const progress = Math.min(1, 5 * this.time);
//         this.laserImg.node.parent.height = cc.misc.lerp(0, distance, progress);
//         this.time += deltaTime;
//         this.laserImg.node.parent.angle = -angle;
//         this.fireImg.node.angle = -angle;
//         if (this.sceneEntity instanceof WinePlane) {
//             this.launchImg.node.angle = 180 - this.sceneEntity.angleChange(targetPos);
//         }
//     }
//     /**
//      * 移除激光子弹
//      */
//     remove(): void {
//         this.m_state = LaserState.remove;
//         this.launchImg.clear();
//         this.fireImg.clear();
//         this.m_collideComp.enabled = false;
//         this.toremove();
//     }
//     /**
//      * 清理逻辑
//      */
//     toremove(): void {
//         if (this.sceneEntity instanceof WinePlane) {
//             this.sceneEntity.setAngle0();
//         }
//         this.m_targets = [];
//         this.launchImg.clear();
//         this.fireImg.clear();
//         this.laserImg.clear();
//         this.m_collideComp && (this.m_collideComp.enabled = false);
//         BulletManager.me.removeBullet(this);
//         if (this.m_hurtEffect) {
//             this.m_hurtEffect.opacity = 0;
//         }
//     }
//     /**
//      * 销毁逻辑
//      */
//     willDestroy(): void {
//         this.m_targets = [];
//         this.launchImg.clear();
//         this.fireImg.clear();
//         this.laserImg.clear();
//         this.m_collideComp && (this.m_collideComp.enabled = false);
//         if (this.m_hurtEffect) {
//             this.m_hurtEffect.opacity = 0;
//         }
//     }
// }
System.register(["cc"], function (_export, _context) {
  "use strict";

  var _cclegacy, _crd;

  return {
    setters: [function (_cc) {
      _cclegacy = _cc.cclegacy;
    }],
    execute: function () {
      _crd = true;

      _cclegacy._RF.push({}, "7762ea5bFJOPb0bym8z7Duk", "LJLaserBullet", undefined);

      _cclegacy._RF.pop();

      _crd = false;
    }
  };
});
//# sourceMappingURL=27580880e5a44b00aa7736a60e01ba8f187fc228.js.map