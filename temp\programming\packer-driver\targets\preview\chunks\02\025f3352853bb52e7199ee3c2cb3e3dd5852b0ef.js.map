{"version": 3, "sources": ["file:///E:/M2Game/Client/assets/scripts/GameOld/Anim.ts"], "names": ["_decorator", "Component", "find", "GamePersistNode", "ccclass", "property", "<PERSON><PERSON>", "animFactory", "onLoad", "getComponent", "recycle", "recycleProduct", "node"], "mappings": ";;;;;;;;;;;;;;;;;;;;AAASA,MAAAA,U,OAAAA,U;AAAYC,MAAAA,S,OAAAA,S;AAAiBC,MAAAA,I,OAAAA,I;;AAE7BC,MAAAA,e,iBAAAA,e;;;;;;;;;OACH;AAAEC,QAAAA,OAAF;AAAWC,QAAAA;AAAX,O,GAAwBL,U;;sBAGjBM,I,WADZF,OAAO,CAAC,MAAD,C,gBAAR,MACaE,IADb,SAC0BL,SAD1B,CACoC;AAAA;AAAA;AAAA,eAChCM,WADgC,GACL,IADK;AAAA;;AAGhCC,QAAAA,MAAM,GAAE;AACJ,eAAKD,WAAL,GAAmBL,IAAI,CAAC,iBAAD,CAAJ,CAAwBO,YAAxB;AAAA;AAAA,kDAAsDF,WAAzE;AACH;;AAEDG,QAAAA,OAAO,GAAG;AACN,eAAKH,WAAL,CAAiBI,cAAjB,CAAgC,KAAKC,IAArC;AACH;;AAT+B,O", "sourcesContent": ["import { _decorator, Component, Node, find } from 'cc';\nimport { AnimFactory } from './factroy/AnimFactory';\nimport { GamePersistNode } from './GamePersistNode';\nconst { ccclass, property } = _decorator;\n\n@ccclass('Anim')\nexport class Anim extends Component {\n    animFactory: AnimFactory = null;\n\n    onLoad(){\n        this.animFactory = find(\"GamePersistNode\").getComponent(GamePersistNode).animFactory;\n    }\n    \n    recycle() {\n        this.animFactory.recycleProduct(this.node);\n    }\n}\n\n"]}