{"version": 3, "sources": ["file:///E:/M2Game/Client/assets/scripts/ui/main/plane/components/back_pack/BagGrid.ts"], "names": ["_decorator", "Component", "Label", "Node", "randomRangeInt", "UITransform", "logDebug", "List", "BagSortType", "TabStatus", "ccclass", "property", "mockItems", "mockPlaneParts", "BagGrid", "_sortedItems", "_sortedPlaneParts", "_lineGridNum", "onLoad", "separator", "removeFromParent", "setGridStatus", "tabStatus", "sortType", "length", "i", "push", "name", "partyID", "part", "quality", "Bag", "sort", "a", "b", "Part", "planePartRowNum", "Math", "ceil", "itemRowNum", "bagList", "numItems", "<PERSON><PERSON>", "sortWithMergePriority", "scrollTo", "onBagListRender", "item", "row", "labels", "getComponentsInChildren", "for<PERSON>ach", "label", "node", "parent", "active", "onRenderPartPlane", "onRenderSeparator", "onRenderItem", "beginIndex", "endIndex", "<PERSON><PERSON><PERSON><PERSON>", "index", "planePart", "string", "normalSize", "tmpNode", "getComponent", "contentSize", "itemUITrans", "setContentSize", "width", "height", "<PERSON><PERSON><PERSON><PERSON>", "item_row", "itemData", "data", "mergeNum", "countMap", "Map", "key", "set", "get", "a<PERSON><PERSON>", "b<PERSON><PERSON>", "aCanMerge", "bCanMerge"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAASA,MAAAA,U,OAAAA,U;AAAYC,MAAAA,S,OAAAA,S;AAAWC,MAAAA,K,OAAAA,K;AAAOC,MAAAA,I,OAAAA,I;AAAMC,MAAAA,c,OAAAA,c;AAAgBC,MAAAA,W,OAAAA,W;;AACpDC,MAAAA,Q,iBAAAA,Q;;AACFC,MAAAA,I;;AACEC,MAAAA,W,iBAAAA,W;AAAaC,MAAAA,S,iBAAAA,S;;;;;;;;;OAChB;AAAEC,QAAAA,OAAF;AAAWC,QAAAA;AAAX,O,GAAwBX,U;AAExBY,MAAAA,S,GAAY,E;AACZC,MAAAA,c,GAAiB,E;;yBAEVC,O,WADZJ,OAAO,CAAC,SAAD,C,UAEHC,QAAQ;AAAA;AAAA,uB,UAERA,QAAQ,CAACR,IAAD,C,2BAJb,MACaW,OADb,SAC6Bb,SAD7B,CACuC;AAAA;AAAA;;AAAA;;AAAA;;AAAA,eAM3Bc,YAN2B,GAMQ,EANR;AAAA,eAO3BC,iBAP2B,GAO4C,EAP5C;AAAA,eAQ3BC,YAR2B,GAQJ,CARI;AAAA;;AAUnCC,QAAAA,MAAM,GAAG;AACL,eAAKC,SAAL,CAAeC,gBAAf;AACH;;AAEDC,QAAAA,aAAa,CAACC,SAAD,EAAuBC,QAAvB,EAA8C;AACvD,cAAIX,SAAS,CAACY,MAAV,GAAmB,GAAvB,EAA4B;AACxB,iBAAK,IAAIC,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAG,GAApB,EAAyBA,CAAC,EAA1B,EAA8B;AAC1Bb,cAAAA,SAAS,CAACc,IAAV,CAAe;AAAEC,gBAAAA,IAAI,mBAAOF;AAAb,eAAf;AACH;AACJ;;AACD,cAAIZ,cAAc,CAACW,MAAf,GAAwB,GAA5B,EAAiC;AAC7B,iBAAK,IAAIC,EAAC,GAAG,CAAb,EAAgBA,EAAC,GAAG,GAApB,EAAyBA,EAAC,EAA1B,EAA8B;AAC1B,kBAAMG,OAAO,GAAGxB,cAAc,CAAC,CAAD,EAAI,CAAJ,CAA9B;AACAS,cAAAA,cAAc,CAACa,IAAf,CAAoB;AAAEG,gBAAAA,IAAI,EAAED,OAAR;AAAiBD,gBAAAA,IAAI,mBAAOC,OAA5B;AAAuCE,gBAAAA,OAAO,EAAE1B,cAAc,CAAC,CAAD,EAAI,CAAJ;AAA9D,eAApB;AACH;AACJ;;AAED,kBAAQkB,SAAR;AACI,iBAAK;AAAA;AAAA,wCAAUS,GAAf;AACI,mBAAKhB,YAAL,GAAoBH,SAApB;AACA,mBAAKI,iBAAL,GAAyB,CAAC,GAAGH,cAAJ,EAAoBmB,IAApB,CAAyB,CAACC,CAAD,EAAIC,CAAJ,KAAU;AACxD,oBAAIX,QAAQ,KAAK;AAAA;AAAA,gDAAYY,IAA7B,EAAmC;AAC/B,yBAAOD,CAAC,CAACL,IAAF,GAASI,CAAC,CAACJ,IAAX,IAAmBK,CAAC,CAACJ,OAAF,GAAYG,CAAC,CAACH,OAAxC;AACH,iBAFD,MAEO;AACH,yBAAOI,CAAC,CAACJ,OAAF,GAAYG,CAAC,CAACH,OAAd,IAAyBI,CAAC,CAACL,IAAF,GAASI,CAAC,CAACJ,IAA3C;AACH;AACJ,eANwB,CAAzB;AAOA,kBAAMO,eAAe,GAAGC,IAAI,CAACC,IAAL,CAAU,KAAKtB,iBAAL,CAAuBQ,MAAvB,GAAgC,KAAKP,YAA/C,CAAxB;AACA,kBAAMsB,UAAU,GAAGF,IAAI,CAACC,IAAL,CAAU,KAAKvB,YAAL,CAAkBS,MAAlB,GAA2B,KAAKP,YAA1C,CAAnB;AACA,mBAAKuB,OAAL,CAAaC,QAAb,GAAwBL,eAAe,GAAGG,UAAlB,GAA+B,CAAvD;AACA;AAAA;AAAA,wCAAS,SAAT,qCAAqD,KAAKvB,iBAAL,CAAuBQ,MAA5E,kBAA+F,KAAKT,YAAL,CAAkBS,MAAjH,yBAA2IY,eAA3I,oBAAyKG,UAAzK;AACA;;AACJ,iBAAK;AAAA;AAAA,wCAAUG,KAAf;AACI,mBAAK3B,YAAL,GAAoBF,cAApB;AACA,mBAAKG,iBAAL,GAAyB,KAAK2B,qBAAL,CAA2B9B,cAA3B,EAA2C,CAA3C,CAAzB;AACA;AAlBR;;AAoBA,eAAK2B,OAAL,CAAaI,QAAb,CAAsB,CAAtB,EAAyB,CAAzB;AACH;;AAEDC,QAAAA,eAAe,CAACC,IAAD,EAAaC,GAAb,EAA0B;AACrC,cAAMC,MAAM,GAAGF,IAAI,CAACG,uBAAL,CAA6B/C,KAA7B,CAAf;AACA8C,UAAAA,MAAM,CAACE,OAAP,CAAeC,KAAK,IAAI;AACpBA,YAAAA,KAAK,CAACC,IAAN,CAAWC,MAAX,CAAkBC,MAAlB,GAA2B,IAA3B;AACH,WAFD;;AAGA,cAAIN,MAAM,CAACxB,MAAP,GAAgB,KAAKP,YAAzB,EAAuC;AACnC,iBAAKE,SAAL,CAAeC,gBAAf;AACA,iBAAKD,SAAL,CAAemC,MAAf,GAAwB,KAAxB;AACH;;AACD,cAAMrB,CAAC,GAAGI,IAAI,CAACC,IAAL,CAAU,KAAKtB,iBAAL,CAAuBQ,MAAvB,GAAgC,KAAKP,YAA/C,CAAV;;AACA,cAAI8B,GAAG,GAAGd,CAAV,EAAa;AACT,iBAAKsB,iBAAL,CAAuBT,IAAvB,EAA6BC,GAA7B;AACH,WAFD,MAEO,IAAIA,GAAG,IAAId,CAAX,EAAc;AACjB,iBAAKuB,iBAAL,CAAuBV,IAAvB,EAA6BC,GAA7B;AACH,WAFM,MAEA;AACH,iBAAKU,YAAL,CAAkBX,IAAlB,EAAwBC,GAAG,GAAGd,CAAN,GAAU,CAAlC;AACH;AACJ;;AAEDsB,QAAAA,iBAAiB,CAACT,IAAD,EAAaC,GAAb,EAA0B;AACvC,cAAMW,UAAU,GAAGX,GAAG,GAAG,KAAK9B,YAA9B;AACA,cAAM0C,QAAQ,GAAG,CAACZ,GAAG,GAAG,CAAP,IAAY,KAAK9B,YAAlC;AACA,cAAM2C,WAAW,GAAGd,IAAI,CAACG,uBAAL,CAA6B/C,KAA7B,CAApB;;AACA,eAAK,IAAI2D,KAAK,GAAGH,UAAjB,EAA6BG,KAAK,GAAGF,QAArC,EAA+CE,KAAK,EAApD,EAAwD;AACpD,gBAAMV,KAAK,GAAGS,WAAW,CAACC,KAAK,GAAGH,UAAT,CAAzB;;AACA,gBAAIG,KAAK,GAAG,KAAK7C,iBAAL,CAAuBQ,MAAnC,EAA2C;AACvC,kBAAMsC,SAAS,GAAG,KAAK9C,iBAAL,CAAuB6C,KAAvB,CAAlB;AACAV,cAAAA,KAAK,CAACY,MAAN,GAAkBD,SAAS,CAACnC,IAA5B,sBAAuCmC,SAAS,CAAChC,OAAjD;AACH,aAHD,MAGO;AACHqB,cAAAA,KAAK,CAACC,IAAN,CAAWC,MAAX,CAAkBC,MAAlB,GAA2B,KAA3B;AACH;AACJ;AACJ;;AAEDE,QAAAA,iBAAiB,CAACV,IAAD,EAAaC,GAAb,EAA0B;AACvC,cAAMiB,UAAU,GAAG,KAAKxB,OAAL,CAAayB,OAAb,CAAqBC,YAArB,CAAkC7D,WAAlC,EAA+C8D,WAAlE;AACA,cAAMC,WAAW,GAAGtB,IAAI,CAACoB,YAAL,CAAkB7D,WAAlB,CAApB;AACA+D,UAAAA,WAAW,CAACC,cAAZ,CAA2BL,UAAU,CAACM,KAAtC,EAA6CN,UAAU,CAACO,MAAX,GAAoB,CAAjE;AACAzB,UAAAA,IAAI,CAACG,uBAAL,CAA6B/C,KAA7B,EAAoCgD,OAApC,CAA4CC,KAAK,IAAI;AACjDA,YAAAA,KAAK,CAACC,IAAN,CAAWC,MAAX,CAAkBC,MAAlB,GAA2B,KAA3B;AACH,WAFD;AAGA,eAAKnC,SAAL,CAAemC,MAAf,GAAwB,IAAxB;AACAR,UAAAA,IAAI,CAAC0B,QAAL,CAAc,KAAKrD,SAAnB;AACH;;AAEDsC,QAAAA,YAAY,CAACX,IAAD,EAAa2B,QAAb,EAA+B;AACvC,cAAMf,UAAU,GAAGe,QAAQ,GAAG,KAAKxD,YAAnC;AACA,cAAM0C,QAAQ,GAAG,CAACc,QAAQ,GAAG,CAAZ,IAAiB,KAAKxD,YAAvC;AACA,cAAM2C,WAAW,GAAGd,IAAI,CAACG,uBAAL,CAA6B/C,KAA7B,CAApB;;AACA,eAAK,IAAI2D,KAAK,GAAGH,UAAjB,EAA6BG,KAAK,GAAGF,QAArC,EAA+CE,KAAK,EAApD,EAAwD;AACpD,gBAAMV,KAAK,GAAGS,WAAW,CAACC,KAAK,GAAGH,UAAT,CAAzB;;AACA,gBAAIG,KAAK,GAAG,KAAK9C,YAAL,CAAkBS,MAA9B,EAAsC;AAClC,kBAAMkD,QAAQ,GAAG,KAAK3D,YAAL,CAAkB8C,KAAlB,CAAjB;AACAV,cAAAA,KAAK,CAACY,MAAN,GAAeW,QAAQ,CAAC/C,IAAxB;AACH,aAHD,MAGO;AACHwB,cAAAA,KAAK,CAACC,IAAN,CAAWC,MAAX,CAAkBC,MAAlB,GAA2B,KAA3B;AACH;AACJ;AACJ;;AAEDX,QAAAA,qBAAqB,CAACgC,IAAD,EAA0DC,QAA1D,EAA4E;AAC7F;AACA,cAAMC,QAAQ,GAAG,IAAIC,GAAJ,EAAjB,CAF6F,CAG7F;;AACAH,UAAAA,IAAI,CAACzB,OAAL,CAAaJ,IAAI,IAAI;AACjB,gBAAMiC,GAAG,GAAMjC,IAAI,CAACjB,IAAX,SAAmBiB,IAAI,CAAChB,OAAjC;AACA+C,YAAAA,QAAQ,CAACG,GAAT,CAAaD,GAAb,EAAkB,CAACF,QAAQ,CAACI,GAAT,CAAaF,GAAb,KAAqB,CAAtB,IAA2B,CAA7C;AACH,WAHD,EAJ6F,CAQ7F;;AACA,iBAAO,CAAC,GAAGJ,IAAJ,EAAU3C,IAAV,CAAe,CAACC,CAAD,EAAIC,CAAJ,KAAU;AAC5B,gBAAMgD,IAAI,GAAMjD,CAAC,CAACJ,IAAR,SAAgBI,CAAC,CAACH,OAA5B;AACA,gBAAMqD,IAAI,GAAMjD,CAAC,CAACL,IAAR,SAAgBK,CAAC,CAACJ,OAA5B;AACA,gBAAMsD,SAAS,GAAG,CAACP,QAAQ,CAACI,GAAT,CAAaC,IAAb,KAAsB,CAAvB,KAA6BN,QAA/C;AACA,gBAAMS,SAAS,GAAG,CAACR,QAAQ,CAACI,GAAT,CAAaE,IAAb,KAAsB,CAAvB,KAA6BP,QAA/C,CAJ4B,CAM5B;;AACA,gBAAIQ,SAAS,KAAKC,SAAlB,EAA6B,OAAOD,SAAS,GAAG,CAAC,CAAJ,GAAQ,CAAxB,CAPD,CAS5B;;AACA,mBAAOlD,CAAC,CAACJ,OAAF,GAAYG,CAAC,CAACH,OAArB;AACH,WAXM,CAAP;AAYH;;AAnIkC,O;;;;;iBAEnB,I;;;;;;;iBAEE,I", "sourcesContent": ["import { _decorator, Component, Label, Node, randomRangeInt, UITransform } from 'cc';\nimport { logDebug } from 'db://assets/scripts/Utils/Logger';\nimport List from '../../../../components/common/list/List';\nimport { BagSortType, TabStatus } from '../../PlaneTypes';\nconst { ccclass, property } = _decorator;\n\nconst mockItems = [];\nconst mockPlaneParts = []\n@ccclass('BagGrid')\nexport class BagGrid extends Component {\n    @property(List)\n    bagList: List = null;\n    @property(Node)\n    separator: Node = null;\n\n    private _sortedItems: { name: string }[] = [];\n    private _sortedPlaneParts: { part: number, name: string, quality: number }[] = [];\n    private _lineGridNum: number = 5;\n\n    onLoad() {\n        this.separator.removeFromParent();\n    }\n\n    setGridStatus(tabStatus: TabStatus, sortType: BagSortType) {\n        if (mockItems.length < 100) {\n            for (let i = 0; i < 501; i++) {\n                mockItems.push({ name: `道具${i}` })\n            }\n        }\n        if (mockPlaneParts.length < 100) {\n            for (let i = 0; i < 501; i++) {\n                const partyID = randomRangeInt(1, 4)\n                mockPlaneParts.push({ part: partyID, name: `零件${partyID}`, quality: randomRangeInt(1, 4) })\n            }\n        }\n\n        switch (tabStatus) {\n            case TabStatus.Bag:\n                this._sortedItems = mockItems;\n                this._sortedPlaneParts = [...mockPlaneParts].sort((a, b) => {\n                    if (sortType === BagSortType.Part) {\n                        return b.part - a.part || b.quality - a.quality;\n                    } else {\n                        return b.quality - a.quality || b.part - a.part;\n                    }\n                });\n                const planePartRowNum = Math.ceil(this._sortedPlaneParts.length / this._lineGridNum)\n                const itemRowNum = Math.ceil(this._sortedItems.length / this._lineGridNum)\n                this.bagList.numItems = planePartRowNum + itemRowNum + 1\n                logDebug(\"PlaneUI\", `setGridStatus plane_parts_len:${this._sortedPlaneParts.length} item_num:${this._sortedItems.length} planePartRowNum:${planePartRowNum} itemRowNum:${itemRowNum}`)\n                break;\n            case TabStatus.Merge:\n                this._sortedItems = mockPlaneParts;\n                this._sortedPlaneParts = this.sortWithMergePriority(mockPlaneParts, 3);\n                break;\n        }\n        this.bagList.scrollTo(0, 1)\n    }\n\n    onBagListRender(item: Node, row: number) {\n        const labels = item.getComponentsInChildren(Label)\n        labels.forEach(label => {\n            label.node.parent.active = true\n        })\n        if (labels.length > this._lineGridNum) {\n            this.separator.removeFromParent();\n            this.separator.active = false;\n        }\n        const a = Math.ceil(this._sortedPlaneParts.length / this._lineGridNum);\n        if (row < a) {\n            this.onRenderPartPlane(item, row)\n        } else if (row == a) {\n            this.onRenderSeparator(item, row)\n        } else {\n            this.onRenderItem(item, row - a - 1)\n        }\n    }\n\n    onRenderPartPlane(item: Node, row: number) {\n        const beginIndex = row * this._lineGridNum;\n        const endIndex = (row + 1) * this._lineGridNum;\n        const childLabels = item.getComponentsInChildren(Label)\n        for (let index = beginIndex; index < endIndex; index++) {\n            const label = childLabels[index - beginIndex]\n            if (index < this._sortedPlaneParts.length) {\n                const planePart = this._sortedPlaneParts[index];\n                label.string = `${planePart.name}(品质:${planePart.quality})`;\n            } else {\n                label.node.parent.active = false;\n            }\n        }\n    }\n\n    onRenderSeparator(item: Node, row: number) {\n        const normalSize = this.bagList.tmpNode.getComponent(UITransform).contentSize\n        const itemUITrans = item.getComponent(UITransform)\n        itemUITrans.setContentSize(normalSize.width, normalSize.height / 2)\n        item.getComponentsInChildren(Label).forEach(label => {\n            label.node.parent.active = false\n        })\n        this.separator.active = true;\n        item.addChild(this.separator)\n    }\n\n    onRenderItem(item: Node, item_row: number) {\n        const beginIndex = item_row * this._lineGridNum;\n        const endIndex = (item_row + 1) * this._lineGridNum;\n        const childLabels = item.getComponentsInChildren(Label)\n        for (let index = beginIndex; index < endIndex; index++) {\n            const label = childLabels[index - beginIndex]\n            if (index < this._sortedItems.length) {\n                const itemData = this._sortedItems[index];\n                label.string = itemData.name;\n            } else {\n                label.node.parent.active = false;\n            }\n        }\n    }\n\n    sortWithMergePriority(data: { part: number, name: string, quality: number }[], mergeNum: number) {\n        // 统计每个part-quality组合的数量\n        const countMap = new Map<string, number>();\n        // 第一次遍历：统计数量\n        data.forEach(item => {\n            const key = `${item.part}-${item.quality}`;\n            countMap.set(key, (countMap.get(key) || 0) + 1);\n        });\n        // 第二次遍历：排序\n        return [...data].sort((a, b) => {\n            const aKey = `${a.part}-${a.quality}`;\n            const bKey = `${b.part}-${b.quality}`;\n            const aCanMerge = (countMap.get(aKey) || 0) >= mergeNum;\n            const bCanMerge = (countMap.get(bKey) || 0) >= mergeNum;\n\n            // 可合成的优先\n            if (aCanMerge !== bCanMerge) return aCanMerge ? -1 : 1;\n\n            // 最后按quality排序\n            return b.quality - a.quality;\n        });\n    }\n}"]}