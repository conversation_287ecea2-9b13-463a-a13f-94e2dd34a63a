System.register(["__unresolved_0", "cc"], function (_export, _context) {
  "use strict";

  var _reporterNs, _cclegacy, __checkObsolete__, __checkObsoleteInNamespace__, _decorator, Component, Vec3, _dec, _class, _crd, ccclass, property, executeInEditMode, DefaultMovable;

  function _reportPossibleCrUseOfIMovable(extras) {
    _reporterNs.report("IMovable", "./IMovable", _context.meta, extras);
  }

  function _reportPossibleCrUseOfIMoveModifier(extras) {
    _reporterNs.report("IMoveModifier", "./IMovable", _context.meta, extras);
  }

  function _reportPossibleCrUseOfeSolverTarget(extras) {
    _reporterNs.report("eSolverTarget", "./IMovable", _context.meta, extras);
  }

  return {
    setters: [function (_unresolved_) {
      _reporterNs = _unresolved_;
    }, function (_cc) {
      _cclegacy = _cc.cclegacy;
      __checkObsolete__ = _cc.__checkObsolete__;
      __checkObsoleteInNamespace__ = _cc.__checkObsoleteInNamespace__;
      _decorator = _cc._decorator;
      Component = _cc.Component;
      Vec3 = _cc.Vec3;
    }],
    execute: function () {
      _crd = true;

      _cclegacy._RF.push({}, "fb3f8/ogAFC4KcYQSS2DYX7", "DefaultMovable", undefined);

      __checkObsolete__(['_decorator', 'Component', 'Node', 'Vec3']);

      ({
        ccclass,
        property,
        executeInEditMode
      } = _decorator);

      _export("DefaultMovable", DefaultMovable = (_dec = ccclass('DefaultMovable'), _dec(_class = executeInEditMode(_class = class DefaultMovable extends Component {
        constructor() {
          super(...arguments);
          this.angle = void 0;
          //wa: number;
          this.vx = void 0;
          //vax: number;
          this.vy = void 0;
          //vay: number;
          // make a map by eSolverTarget
          this.activeModifiers = new Map();
        }

        addSolver(solver) {
          this.activeModifiers.set(solver.targetType, solver);
        }

        update(dt) {
          this.activeModifiers.forEach((solver, key) => solver.tick(this, dt)); // 根据angle, vx&vy更新位置和朝向

          this.node.angle = this.angle;
          var lastPos = this.node.getPosition();
          var newPos = new Vec3(lastPos.x + this.vx * dt, lastPos.y + this.vy * dt, lastPos.z);
          this.node.setPosition(newPos); // remove finished solvers

          this.activeModifiers.forEach((solver, key) => {
            if (solver.isFinished()) {
              this.activeModifiers.delete(key);
            }
          });
        }

      }) || _class) || _class));

      _cclegacy._RF.pop();

      _crd = false;
    }
  };
});
//# sourceMappingURL=644f1a6dd659c486414d78616cde9b46ee1c90e1.js.map