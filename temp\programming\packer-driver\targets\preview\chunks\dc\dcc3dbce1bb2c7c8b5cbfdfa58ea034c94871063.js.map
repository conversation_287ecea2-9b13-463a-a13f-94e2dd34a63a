{"version": 3, "sources": ["file:///E:/M2Game/Client/assets/scripts/Game/ui/plane/enemy/EnemyAttrBaseCom.ts"], "names": ["_decorator", "Component", "ccclass", "property", "EnemyAttrBaseCom", "enemyEntity", "attrData", "attrMgr", "updateGameLogic", "deltaTime", "die"], "mappings": ";;;;;;;;;;AAASA,MAAAA,U,OAAAA,U;AAAYC,MAAAA,S,OAAAA,S;;;;;;;;;OACf;AAAEC,QAAAA,OAAF;AAAWC,QAAAA;AAAX,O,GAAwBH,U;;yBAGTI,gB,WADpBF,OAAO,CAAC,kBAAD,C,gBAAR,MACqBE,gBADrB,SAC8CH,SAD9C,CACwD;AAAA;AAAA;AAAA,eAEpDI,WAFoD,GAEtC,IAFsC;AAEhC;AAFgC,eAGpDC,QAHoD,GAGzC,IAHyC;AAGnC;AAHmC,eAIpDC,OAJoD,GAI1C,IAJ0C;AAAA;;AAIpC;;AAGhB;AACJ;AACA;AACA;AACIC,QAAAA,eAAe,CAACC,SAAD,EAAY,CACvB;AACH;AAED;AACJ;AACA;;;AACIC,QAAAA,GAAG,GAAG,CACF;AACH;;AApBmD,O", "sourcesContent": ["import { _decorator, Component } from 'cc';\r\nconst { ccclass, property } = _decorator;\r\n\r\n@ccclass('EnemyAttrBaseCom')\r\nexport default class EnemyAttrBaseCom extends Component {\r\n\r\n    enemyEntity = null; // 敌人实体\r\n    attrData = null; // 属性数据\r\n    attrMgr = null; // 属性管理器\r\n\r\n\r\n    /**\r\n     * 更新游戏逻辑\r\n     * @param {number} deltaTime 帧间隔时间\r\n     */\r\n    updateGameLogic(deltaTime) {\r\n        // 子类实现具体逻辑\r\n    }\r\n\r\n    /**\r\n     * 处理敌人死亡逻辑\r\n     */\r\n    die() {\r\n        // 子类实现具体逻辑\r\n    }\r\n}"]}