{"version": 3, "sources": ["file:///E:/M2Game/Client/assets/scripts/GameOld/world/base/EntityContainer.ts"], "names": ["EntityContainer", "TypeID", "_allEntities", "_allEntitiesById", "Map", "_singletonEntities", "registerEntity", "entity", "set", "eid", "push", "unregisterEntity", "index", "indexOf", "splice", "getEntities", "registerSingletonEntity", "typeId", "getFromInstance", "unregisterSingletonEntity", "delete", "getSingletonEntity", "entityConstructor", "get"], "mappings": ";;;sCAMaA,e;;;;;;;;;;;;;;;;;;AALJC,MAAAA,M,iBAAAA,M;;;;;;;AAET;AACA;AACA;iCACaD,e,GAAN,MAAMA,eAAN,CAAsB;AAAA;AAAA,eAEjBE,YAFiB,GAES,EAFT;AAAA,eAGjBC,gBAHiB,GAGqC,IAAIC,GAAJ,EAHrC;AAKzB;AALyB,eAMjBC,kBANiB,GAMqC,IAAID,GAAJ,EANrC;AAAA;;AAQlBE,QAAAA,cAAc,CAACC,MAAD,EAAuB;AACxC,eAAKJ,gBAAL,CAAsBK,GAAtB,CAA0BD,MAAM,CAACE,GAAjC,EAAsCF,MAAtC;;AACA,eAAKL,YAAL,CAAkBQ,IAAlB,CAAuBH,MAAvB;AACH;;AAEMI,QAAAA,gBAAgB,CAACJ,MAAD,EAAuB;AAC1C,cAAMK,KAAK,GAAG,KAAKV,YAAL,CAAkBW,OAAlB,CAA0BN,MAA1B,CAAd;;AACA,cAAIK,KAAK,IAAI,CAAb,EAAgB;AACZ,iBAAKV,YAAL,CAAkBY,MAAlB,CAAyBF,KAAzB,EAAgC,CAAhC;AACH;AACJ;;AAEMG,QAAAA,WAAW,GAAa;AAC3B,iBAAO,KAAKb,YAAZ;AACH;;AAEMc,QAAAA,uBAAuB,CAACT,MAAD,EAAuB;AACjD,cAAMU,MAAM,GAAG;AAAA;AAAA,gCAAOC,eAAP,CAAuBX,MAAvB,CAAf;;AACA,eAAKF,kBAAL,CAAwBG,GAAxB,CAA4BS,MAA5B,EAAoCV,MAApC;AACH;;AAEMY,QAAAA,yBAAyB,CAACZ,MAAD,EAAuB;AACnD,cAAMU,MAAM,GAAG;AAAA;AAAA,gCAAOC,eAAP,CAAuBX,MAAvB,CAAf;;AACA,eAAKF,kBAAL,CAAwBe,MAAxB,CAA+BH,MAA/B;AACH;;AAEMI,QAAAA,kBAAkB,CAAmBC,iBAAnB,EAA2E;AAChG,cAAML,MAAM,GAAG;AAAA;AAAA,gCAAOM,GAAP,CAAWD,iBAAX,CAAf;AACA,iBAAO,KAAKjB,kBAAL,CAAwBkB,GAAxB,CAA4BN,MAA5B,CAAP;AACH;;AArCwB,O", "sourcesContent": ["import { Entity } from \"./Entity\";\r\nimport { TypeID, TypeIDUtils } from \"./TypeID\";\r\n\r\n/**\r\n * EntityContainer manages a collection of game entities\r\n */\r\nexport class EntityContainer {\r\n\r\n    private _allEntities : Entity[] = [];\r\n    private _allEntitiesById : Map<number/*entity id*/, Entity> = new Map();\r\n    \r\n    // _singletonEntities are differed by type\r\n    private _singletonEntities : Map<number/*type id*/, Entity> = new Map();\r\n\r\n    public registerEntity(entity: Entity): void {\r\n        this._allEntitiesById.set(entity.eid, entity);\r\n        this._allEntities.push(entity);\r\n    }\r\n\r\n    public unregisterEntity(entity: Entity): void {\r\n        const index = this._allEntities.indexOf(entity);\r\n        if (index >= 0) {\r\n            this._allEntities.splice(index, 1);\r\n        }\r\n    }\r\n\r\n    public getEntities(): Entity[] {\r\n        return this._allEntities;\r\n    }\r\n\r\n    public registerSingletonEntity(entity: Entity): void {\r\n        const typeId = TypeID.getFromInstance(entity);\r\n        this._singletonEntities.set(typeId, entity);\r\n    }\r\n\r\n    public unregisterSingletonEntity(entity: Entity): void {\r\n        const typeId = TypeID.getFromInstance(entity);\r\n        this._singletonEntities.delete(typeId);\r\n    }\r\n\r\n    public getSingletonEntity<T extends Entity>(entityConstructor: new (...args: any[]) => T): T | null {\r\n        const typeId = TypeID.get(entityConstructor);\r\n        return this._singletonEntities.get(typeId) as T | null;\r\n    }\r\n}"]}