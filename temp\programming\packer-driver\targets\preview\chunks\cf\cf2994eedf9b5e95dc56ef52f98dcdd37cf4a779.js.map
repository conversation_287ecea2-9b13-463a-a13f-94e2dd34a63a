{"version": 3, "sources": ["file:///E:/M2Game/Client/assets/scripts/core/base/SingletonBase.ts"], "names": ["SingletonBase", "_instance", "getInstance", "o", "prototype"], "mappings": ";;;iBAGaA,a;;;;;;;;;;;;;AAHb;AACA;AACA;+BACaA,a,GAAN,MAAMA,aAAN,CAAsB;AAAA;AAAA,eAC3BC,SAD2B,GACZ,IADY;AAAA;;AAGF,eAAXC,WAAW,CAAIC,CAAJ,EAAwB;AAC7C,cAAI,KAAKC,SAAL,CAAeH,SAAf,IAA4B,IAAhC,EAAsC;AAClC,iBAAKG,SAAL,CAAeH,SAAf,GAA2B,IAAIE,CAAJ,EAA3B;AACH;;AACD,iBAAO,KAAKC,SAAL,CAAeH,SAAtB;AACH;;AAR0B,O", "sourcesContent": ["/**\r\n * 单件基类，所有单件对象（一般管理器类使用）\r\n */\r\nexport class SingletonBase<T>{\r\n  _instance: T = null;\r\n\r\n  public static getInstance<T>(o: { new(): T }): T {\r\n      if (this.prototype._instance == null) {\r\n          this.prototype._instance = new o();\r\n      }\r\n      return this.prototype._instance as T;\r\n  }\r\n}"]}