{"version": 3, "sources": ["file:///E:/M2Game/Client/assets/scripts/Game/ui/prefabs/PropEntity.ts"], "names": ["V3", "x", "y", "Error", "_decorator", "Sprite", "Label", "Vec2", "Color", "Tween", "instantiate", "tween", "v2", "UIOpacity", "GameEnum", "GameConst", "Tools", "GameConfig", "GameIns", "PfFrameAnim", "BattleLayer", "Entity", "ccclass", "property", "PropEntity", "_data", "_param", "_count", "_frameAnim", "_bAttract", "_moveSpeed", "_moveDir", "ZERO", "_freeAcd", "_attractAcd", "_actEndCall", "_autoAttract", "init", "data", "count", "param", "actEndCall", "node", "setScale", "lab_num", "active", "_initSelf", "setAppearParam", "speed", "angle", "freeAcd", "rotate", "Math", "PI", "updateGameLogic", "dt", "pos", "position", "pos2", "add", "multiplyScalar", "setPosition", "stopSpread", "icon", "spriteFrame", "light", "stopAllByTarget", "zIndex", "mainType", "ItemType", "Coin", "parent", "me", "enemyPlane<PERSON><PERSON>er", "getComponent", "opacity", "frameAnimNode", "frameAnim", "<PERSON><PERSON><PERSON><PERSON>", "CommonAtlas", "ActionFrameTime", "reset", "round", "string", "toString", "stop", "Box", "getSpriteFrame", "itemManager", "getItemRecord", "id", "image", "getIcon", "isEquip", "loadManager", "getImage", "repeatF<PERSON><PERSON>", "by", "start", "getSiblingIndex", "setSiblingIndex", "_playShowAnim", "callback", "actionFrameTime", "randomVec1", "getRandomVec2", "randomVec2", "randomVec3", "random_int", "to", "call", "remove", "_getLightColor", "color", "WHITE", "itemtype", "type", "Fragment", "Upgrade", "itemId", "subType", "autoAttract", "value", "bAttract", "moveDir", "moveSpeed", "scale"], "mappings": ";;;;;;;;;;;AA4OA,WAASA,EAAT,CAAYC,CAAZ,EAAuBC,CAAvB,EAAwC;AACpC,UAAM,IAAIC,KAAJ,CAAU,2BAAV,CAAN;AACH;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA9OQC,MAAAA,U,OAAAA,U;AAAYC,MAAAA,M,OAAAA,M;AAAQC,MAAAA,K,OAAAA,K;AAAOC,MAAAA,I,OAAAA,I;AAAMC,MAAAA,K,OAAAA,K;AAAOC,MAAAA,K,OAAAA,K;AAAOC,MAAAA,W,OAAAA,W;AAAaC,MAAAA,K,OAAAA,K;AAAaC,MAAAA,E,OAAAA,E;AAAIC,MAAAA,S,OAAAA,S;;AAC/EC,MAAAA,Q;;AACEC,MAAAA,S,iBAAAA,S;;AACAC,MAAAA,K,iBAAAA,K;;AACFC,MAAAA,U;;AACEC,MAAAA,O,iBAAAA,O;;AACFC,MAAAA,W;;AACAC,MAAAA,W;;AACAC,MAAAA,M;;;;;;;;;OAGD;AAAEC,QAAAA,OAAF;AAAWC,QAAAA;AAAX,O,GAAwBnB,U;;yBAGToB,U,WADpBF,OAAO,CAAC,YAAD,C,UAEHC,QAAQ,CAAClB,MAAD,C,UAGRkB,QAAQ,CAAClB,MAAD,C,UAGRkB,QAAQ,CAACjB,KAAD,C,2BARb,MACqBkB,UADrB;AAAA;AAAA,4BAC+C;AAAA;AAAA;;AAAA;;AAAA;;AAAA;;AAAA,eAU3CC,KAV2C,GAUnC,IAVmC;AAAA,eAW3CC,MAX2C,GAWlC,EAXkC;AAAA,eAY3CC,MAZ2C,GAYlC,CAZkC;AAAA,eAa3CC,UAb2C,GAa9B,IAb8B;AAAA,eAc3CC,SAd2C,GAc/B,KAd+B;AAAA,eAe3CC,UAf2C,GAe9B,CAf8B;AAAA,eAgB3CC,QAhB2C,GAgBhCxB,IAAI,CAACyB,IAhB2B;AAAA,eAiB3CC,QAjB2C,GAiBhC,CAAC,GAjB+B;AAAA,eAkB3CC,WAlB2C,GAkB7B,IAlB6B;AAAA,eAmB3CC,WAnB2C,GAmB7B,IAnB6B;AAAA,eAoB3CC,YApB2C,GAoB5B,KApB4B;AAAA;;AAuB3CC,QAAAA,IAAI,CAACC,IAAD,EAAOC,KAAP,EAAcC,KAAd,EAA0BC,UAA1B,EAA6C;AAAA,cAA/BD,KAA+B;AAA/BA,YAAAA,KAA+B,GAAvB,EAAuB;AAAA;;AAAA,cAAnBC,UAAmB;AAAnBA,YAAAA,UAAmB,GAAN,IAAM;AAAA;;AAC7C,eAAKhB,KAAL,GAAaa,IAAb;AACA,eAAKX,MAAL,GAAcY,KAAd;AACA,eAAKb,MAAL,GAAcc,KAAd;AACA,eAAKJ,YAAL,GAAoB,KAApB;AACA,eAAKD,WAAL,GAAmBM,UAAnB;AACA,eAAKZ,SAAL,GAAiB,KAAjB;AACA,eAAKC,UAAL,GAAkB,CAAlB;AACA,eAAKC,QAAL,GAAgBxB,IAAI,CAACyB,IAArB;AACA,eAAKU,IAAL,CAAUC,QAAV,CAAmB,CAAnB,EAAqB,CAArB;AACA,eAAKC,OAAL,CAAaF,IAAb,CAAkBG,MAAlB,GAA2B,KAA3B;;AACA,eAAKC,SAAL;AACH;;AAEDC,QAAAA,cAAc,CAACC,KAAD,EAAQC,KAAR,EAAeC,OAAf,EAAwB;AAClC,eAAKpB,UAAL,GAAkBkB,KAAlB;AACA,eAAKjB,QAAL,GAAgB,IAAIxB,IAAJ,CAAS,CAAT,EAAY,CAAC,CAAb,EAAgB4C,MAAhB,CAAuBF,KAAK,IAAIG,IAAI,CAACC,EAAL,GAAU,GAAd,CAA5B,CAAhB;AACA,eAAKpB,QAAL,GAAgBiB,OAAhB;AACH;;AAEDI,QAAAA,eAAe,CAACC,EAAD,EAAK;AAChB,cAAI,KAAKzB,UAAL,GAAkB,CAAtB,EAAyB;AACrB,gBAAI,KAAKD,SAAT,EAAoB;AAChB,mBAAKC,UAAL,IAAmB,KAAKI,WAAL,GAAmBqB,EAAtC;AACH,aAFD,MAEO;AACH,mBAAKzB,UAAL,IAAmB,KAAKG,QAAL,GAAgBsB,EAAnC;;AACA,kBAAI,KAAKzB,UAAL,GAAkB,CAAtB,EAAyB;AACrB,qBAAKA,UAAL,GAAkB,CAAlB;;AACA,oBAAI,KAAKK,WAAT,EAAsB;AAClB,uBAAKA,WAAL,CAAiB,IAAjB;;AACA,uBAAKA,WAAL,GAAmB,IAAnB;AACH;AACJ;AACJ;;AACD,gBAAIqB,GAAG,GAAG,KAAKd,IAAL,CAAUe,QAApB;AACA,gBAAIC,IAAI,GAAG9C,EAAE,CAAC4C,GAAG,CAACvD,CAAL,EAAOuD,GAAG,CAACtD,CAAX,CAAF,CAAgByD,GAAhB,CAAoB,KAAK5B,QAAL,CAAc6B,cAAd,CAA6B,KAAK9B,UAAL,GAAkByB,EAA/C,CAApB,CAAX;AACA,iBAAKb,IAAL,CAAUmB,WAAV,CAAsBH,IAAI,CAACzD,CAA3B,EAA8ByD,IAAI,CAACxD,CAAnC;AACH;AACJ;;AAED4D,QAAAA,UAAU,GAAG;AACT,eAAKhC,UAAL,GAAkB,CAAlB;;AACA,cAAI,KAAKK,WAAT,EAAsB;AAClB,iBAAKA,WAAL,CAAiB,IAAjB;;AACA,iBAAKA,WAAL,GAAmB,IAAnB;AACH;AACJ;;AAEDW,QAAAA,SAAS,GAAG;AACR,eAAKiB,IAAL,CAAUC,WAAV,GAAwB,IAAxB;AACA,eAAKC,KAAL,CAAWD,WAAX,GAAyB,IAAzB;AACAvD,UAAAA,KAAK,CAACyD,eAAN,CAAsB,KAAKD,KAAL,CAAWvB,IAAjC;AAEA,cAAIyB,MAAM,GAAG,CAAb;;AACA,cAAI,KAAKC,QAAL,KAAkB;AAAA;AAAA,oCAASC,QAAT,CAAkBC,IAAxC,EAA8C;AAC1CH,YAAAA,MAAM,GAAG,KAAKzB,IAAL,CAAU6B,MAAV,KAAqB;AAAA;AAAA,4CAAYC,EAAZ,CAAeC,eAApC,GAAsD,EAAtD,GAA2D,CAApE;AACA,iBAAK7B,OAAL,CAAaF,IAAb,CAAkBgC,YAAlB,CAA+B7D,SAA/B,EAA0C8D,OAA1C,GAAoD,CAApD;;AAEA,gBAAI,CAAC,KAAK/C,UAAV,EAAsB;AAClB,kBAAMgD,aAAa,GAAGlE,WAAW,CAAC;AAAA;AAAA,0CAAUmE,SAAX,CAAjC;AACA,mBAAKnC,IAAL,CAAUoC,QAAV,CAAmBF,aAAnB;AACA,mBAAKhD,UAAL,GAAkBgD,aAAa,CAACF,YAAd;AAAA;AAAA,6CAAlB;;AACA,mBAAK9C,UAAL,CAAgBS,IAAhB,CAAqB;AAAA;AAAA,0CAAU0C,WAA/B,EAA4C,KAA5C,EAAmD,CAAnD,EAAsD,IAAI;AAAA;AAAA,4CAAWC,eAArE;AACH;;AACD,iBAAKpD,UAAL,CAAgBqD,KAAhB;AACH,WAXD,MAWO;AACHd,YAAAA,MAAM,GAAG,CAAT;AACA,gBAAM5B,KAAK,GAAGa,IAAI,CAAC8B,KAAL,CAAW,KAAKvD,MAAhB,CAAd;;AACA,gBAAI,KAAKyC,QAAL,KAAkB;AAAA;AAAA,sCAASC,QAAT,CAAkBC,IAApC,IAA4C/B,KAAK,GAAG,CAAxD,EAA2D;AACvD,mBAAKK,OAAL,CAAauC,MAAb,GAAsB,MAAM5C,KAAK,CAAC6C,QAAN,EAA5B;AACA,mBAAKxC,OAAL,CAAaF,IAAb,CAAkBgC,YAAlB,CAA+B7D,SAA/B,EAA0C8D,OAA1C,GAAoD,GAApD;AACH;;AACD,gBAAI,KAAK/C,UAAT,EAAqB;AACjB,mBAAKA,UAAL,CAAgByD,IAAhB;AACH;;AACD,gBAAI,KAAKjB,QAAL,KAAkB;AAAA;AAAA,sCAASC,QAAT,CAAkBiB,GAAxC,EAA6C;AACzC,mBAAKvB,IAAL,CAAUC,WAAV,GAAwB;AAAA;AAAA,0CAAUe,WAAV,CAAsBQ,cAAtB,CAAqC;AAAA;AAAA,sCAAQC,WAAR,CAAoBC,aAApB,CAAkC,KAAKhE,KAAL,CAAWiE,EAA7C,EAAiDC,KAAtF,CAAxB;AACH,aAFD,MAEO;AACH,mBAAK5B,IAAL,CAAUC,WAAV,GAAwB;AAAA;AAAA,sCAAQwB,WAAR,CAAoBI,OAApB,CAA4B,KAAKnE,KAAL,CAAWiE,EAAvC,CAAxB;AACA,mBAAK3B,IAAL,CAAUrB,IAAV,CAAeC,QAAf,CAAwB,IAAxB,EAA6B,IAA7B;;AACA,kBAAI;AAAA;AAAA,sCAAQ6C,WAAR,CAAoBK,OAApB,CAA4B,KAAKpE,KAAL,CAAWiE,EAAvC,CAAJ,EAAgD;AAC5C,qBAAK3B,IAAL,CAAUrB,IAAV,CAAeC,QAAf,CAAwB,GAAxB,EAA4B,GAA5B;AACAwB,gBAAAA,MAAM,GAAG,CAAT;AACH;;AACD,kBAAI,CAAC,KAAKF,KAAL,CAAWD,WAAhB,EAA6B;AACzB,qBAAKC,KAAL,CAAWD,WAAX,GAAyB;AAAA;AAAA,wCAAQ8B,WAAR,CAAoBC,QAApB,CAA6B,QAA7B,EAAuC,WAAvC,CAAzB;AACH;;AACD,mBAAKhC,IAAL,CAAUrB,IAAV,CAAeC,QAAf,CAAwB,IAAxB,EAA6B,IAA7B;;AAEA,kBAAI,KAAKsB,KAAL,CAAWD,WAAf,EAA4B;AACxBrD,gBAAAA,KAAK,CAAC,KAAKsD,KAAL,CAAWvB,IAAZ,CAAL,CACKsD,aADL,CACmB,IAAIvF,KAAJ,GAAYwF,EAAZ,CAAe,CAAf,EAAkB;AAAEhD,kBAAAA,KAAK,EAAE,CAAC;AAAV,iBAAlB,CADnB,EAEKiD,KAFL;AAGH;AACJ;AACJ;;AACD,cAAI,KAAKxD,IAAL,CAAUyD,eAAV,OAAgChC,MAApC,EAA4C;AACxC,iBAAKzB,IAAL,CAAU0D,eAAV,CAA0BjC,MAA1B;AACH;AACJ;;AAEDkC,QAAAA,aAAa,CAACC,QAAD,EAAkB;AAAA,cAAjBA,QAAiB;AAAjBA,YAAAA,QAAiB,GAAN,IAAM;AAAA;;AAC3B,cAAMC,eAAe,GAAG;AAAA;AAAA,wCAAWvB,eAAnC;AACA,cAAMvB,QAAQ,GAAG,KAAKf,IAAL,CAAUe,QAA3B;AACA,cAAM+C,UAAU,GAAG/C,QAAQ,CAACxD,CAAT,GAAa,CAAb,GACb;AAAA;AAAA,8BAAMwG,aAAN,CAAoB,IAAIlG,IAAJ,CAAS,CAAC,EAAV,EAAc,CAAd,CAApB,EAAsC,IAAIA,IAAJ,CAAS,CAAT,EAAY,EAAZ,CAAtC,CADa,GAEb;AAAA;AAAA,8BAAMkG,aAAN,CAAoB,IAAIlG,IAAJ,CAAS,CAAT,EAAY,EAAZ,CAApB,EAAqC,IAAIA,IAAJ,CAAS,CAAT,EAAY,EAAZ,CAArC,CAFN;AAGA,cAAMmG,UAAU,GAAGjD,QAAQ,CAACxD,CAAT,GAAa,CAAb,GACb;AAAA;AAAA,8BAAMwG,aAAN,CAAoB,IAAIlG,IAAJ,CAAS,CAAC,GAAV,EAAe,CAAC,EAAhB,CAApB,EAAyC,IAAIA,IAAJ,CAAS,EAAT,EAAa,EAAb,CAAzC,CADa,GAEb;AAAA;AAAA,8BAAMkG,aAAN,CAAoB,IAAIlG,IAAJ,CAAS,EAAT,EAAa,GAAb,CAApB,EAAuC,IAAIA,IAAJ,CAAS,EAAT,EAAa,EAAb,CAAvC,CAFN;AAGA,cAAMoG,UAAU,GAAG,IAAIpG,IAAJ,CAASmG,UAAU,CAACzG,CAApB,EAAuB;AAAA;AAAA,8BAAM2G,UAAN,CAAiB,CAAC,EAAlB,EAAsB,EAAtB,CAAvB,CAAnB;AAEAjG,UAAAA,KAAK,CAAC,KAAK+B,IAAN,CAAL,CACKmE,EADL,CACQ,IAAIN,eADZ,EAC6B;AAAE9C,YAAAA,QAAQ,EAAEA,QAAQ,CAACE,GAAT,CAAa3D,EAAE,CAACwG,UAAU,CAACvG,CAAZ,EAAcuG,UAAU,CAACtG,CAAzB,CAAf;AAAZ,WAD7B,EAEK2G,EAFL,CAEQ,IAAIN,eAFZ,EAE6B;AAAE9C,YAAAA,QAAQ,EAAEA,QAAQ,CAACE,GAAT,CAAa3D,EAAE,CAAC0G,UAAU,CAACzG,CAAZ,EAAcyG,UAAU,CAACxG,CAAzB,CAAf;AAAZ,WAF7B,EAGK2G,EAHL,CAGQ,IAAIN,eAHZ,EAG6B;AAAE9C,YAAAA,QAAQ,EAAEA,QAAQ,CAACE,GAAT,CAAa3D,EAAE,CAAC2G,UAAU,CAAC1G,CAAZ,EAAc0G,UAAU,CAACzG,CAAzB,CAAf;AAAZ,WAH7B,EAIK4G,IAJL,CAIU,MAAM;AACR,gBAAIR,QAAJ,EAAcA,QAAQ,CAAC,IAAD,CAAR;AACjB,WANL,EAOKJ,KAPL;AAQH;;AAEDa,QAAAA,MAAM,GAAG;AACLtG,UAAAA,KAAK,CAACyD,eAAN,CAAsB,KAAKxB,IAA3B;AACH;;AAEDsE,QAAAA,cAAc,GAAG;AACb,cAAIC,KAAK,GAAGzG,KAAK,CAAC0G,KAAlB;;AACA,kBAAQ,KAAKzF,KAAL,CAAW0F,QAAnB;AACI,iBAAK;AAAA;AAAA,sCAAS9C,QAAT,CAAkBiB,GAAvB;AACI,sBAAQ,KAAK7D,KAAL,CAAW2F,IAAnB;AACI,qBAAK,CAAL;AACIH,kBAAAA,KAAK,GAAG,IAAIzG,KAAJ,CAAU,GAAV,EAAe,GAAf,EAAoB,EAApB,CAAR;AACA;;AACJ,qBAAK,CAAL;AACIyG,kBAAAA,KAAK,GAAG,IAAIzG,KAAJ,CAAU,EAAV,EAAc,GAAd,EAAmB,GAAnB,CAAR;AACA;;AACJ,qBAAK,CAAL;AACIyG,kBAAAA,KAAK,GAAG,IAAIzG,KAAJ,CAAU,GAAV,EAAe,GAAf,EAAoB,EAApB,CAAR;AACA;AATR;;AAWA;;AACJ,iBAAK;AAAA;AAAA,sCAAS6D,QAAT,CAAkBgD,QAAvB;AACIJ,cAAAA,KAAK,GAAG,IAAIzG,KAAJ,CAAU,GAAV,EAAe,GAAf,EAAoB,CAApB,CAAR;AACA;;AACJ,iBAAK;AAAA;AAAA,sCAAS6D,QAAT,CAAkBiD,OAAvB;AACIL,cAAAA,KAAK,GAAG,IAAIzG,KAAJ,CAAU,CAAV,EAAa,GAAb,EAAkB,GAAlB,CAAR;AACA;AAnBR;;AAqBA,iBAAOyG,KAAP;AACH;;AAES,YAANM,MAAM,GAAG;AACT,iBAAO,KAAKnD,QAAL,KAAkB;AAAA;AAAA,oCAASC,QAAT,CAAkBiB,GAApC,GAA0C,KAAK7D,KAAL,CAAWiE,EAArD,GAA0D,KAAKhE,MAAL,CAAY,CAAZ,CAAjE;AACH;;AAEW,YAAR0C,QAAQ,GAAG;AACX,iBAAO,KAAK3C,KAAL,CAAW0F,QAAlB;AACH;;AAEU,YAAPK,OAAO,GAAG;AACV,iBAAO,KAAK/F,KAAL,CAAW2F,IAAlB;AACH;;AAEQ,YAAL7E,KAAK,GAAG;AACR,iBAAO,KAAKZ,MAAZ;AACH;;AAEc,YAAX8F,WAAW,GAAG;AACd,iBAAO,KAAKrF,YAAZ;AACH;;AAEc,YAAXqF,WAAW,CAACC,KAAD,EAAQ;AACnB,eAAKtF,YAAL,GAAoBsF,KAApB;AACH;;AAEW,YAARC,QAAQ,GAAG;AACX,iBAAO,KAAK9F,SAAZ;AACH;;AAEW,YAAR8F,QAAQ,CAACD,KAAD,EAAQ;AAChB,eAAK7F,SAAL,GAAiB6F,KAAjB;AACH;;AAEU,YAAPE,OAAO,CAACF,KAAD,EAAQ;AACf,eAAK3F,QAAL,GAAgB2F,KAAhB;AACH;;AAEY,YAATG,SAAS,CAACH,KAAD,EAAQ;AACjB,eAAK5F,UAAL,GAAkB4F,KAAlB;AACH;;AAEQ,YAALI,KAAK,CAACJ,KAAD,EAAQ;AACb,cAAI,KAAKhF,IAAL,CAAUoF,KAAV,GAAkBJ,KAAtB,EAA6B;AACzB,iBAAKhF,IAAL,CAAUC,QAAV,CAAmB+E,KAAnB,EAAyBA,KAAzB;AACH;AACJ;;AA3N0C,O", "sourcesContent": ["import { _decorator, Sprite, Label, Vec2, Color, Tween, instantiate, tween, Vec3, v2, UIOpacity, SpriteFrame } from 'cc';\r\nimport GameEnum from '../../const/GameEnum';\r\nimport { GameConst } from '../../const/GameConst';\r\nimport { Tools } from '../../utils/Tools';\r\nimport GameConfig from '../../const/GameConfig';\r\nimport { GameIns } from '../../GameIns';\r\nimport PfFrameAnim from '../base/PfFrameAnim';\r\nimport BattleLayer from '../layer/BattleLayer';\r\nimport Entity from '../base/Entity';\r\n\r\n\r\nconst { ccclass, property } = _decorator;\r\n\r\n@ccclass('PropEntity')\r\nexport default class PropEntity extends Entity {\r\n    @property(Sprite)\r\n    icon:Sprite;\r\n\r\n    @property(Sprite)\r\n    light:Sprite;\r\n\r\n    @property(Label)\r\n    lab_num:Label;\r\n\r\n    _data = null;\r\n    _param = [];\r\n    _count = 0;\r\n    _frameAnim = null;\r\n    _bAttract = false;\r\n    _moveSpeed = 0;\r\n    _moveDir = Vec2.ZERO;\r\n    _freeAcd = -800;\r\n    _attractAcd = 2000;\r\n    _actEndCall = null;\r\n    _autoAttract = false;\r\n\r\n\r\n    init(data, count, param = [], actEndCall = null) {\r\n        this._data = data;\r\n        this._count = count;\r\n        this._param = param;\r\n        this._autoAttract = false;\r\n        this._actEndCall = actEndCall;\r\n        this._bAttract = false;\r\n        this._moveSpeed = 0;\r\n        this._moveDir = Vec2.ZERO;\r\n        this.node.setScale(1,1);\r\n        this.lab_num.node.active = false;\r\n        this._initSelf();\r\n    }\r\n\r\n    setAppearParam(speed, angle, freeAcd) {\r\n        this._moveSpeed = speed;\r\n        this._moveDir = new Vec2(0, -1).rotate(angle * (Math.PI / 180));\r\n        this._freeAcd = freeAcd;\r\n    }\r\n\r\n    updateGameLogic(dt) {\r\n        if (this._moveSpeed > 0) {\r\n            if (this._bAttract) {\r\n                this._moveSpeed += this._attractAcd * dt;\r\n            } else {\r\n                this._moveSpeed += this._freeAcd * dt;\r\n                if (this._moveSpeed < 0) {\r\n                    this._moveSpeed = 0;\r\n                    if (this._actEndCall) {\r\n                        this._actEndCall(this);\r\n                        this._actEndCall = null;\r\n                    }\r\n                }\r\n            }\r\n            let pos = this.node.position\r\n            let pos2 = v2(pos.x,pos.y).add(this._moveDir.multiplyScalar(this._moveSpeed * dt));\r\n            this.node.setPosition(pos2.x, pos2.y);\r\n        }\r\n    }\r\n\r\n    stopSpread() {\r\n        this._moveSpeed = 0;\r\n        if (this._actEndCall) {\r\n            this._actEndCall(this);\r\n            this._actEndCall = null;\r\n        }\r\n    }\r\n\r\n    _initSelf() {\r\n        this.icon.spriteFrame = null;\r\n        this.light.spriteFrame = null;\r\n        Tween.stopAllByTarget(this.light.node);\r\n\r\n        let zIndex = 0;\r\n        if (this.mainType === GameEnum.ItemType.Coin) {\r\n            zIndex = this.node.parent === BattleLayer.me.enemyPlaneLayer ? 10 : 0;\r\n            this.lab_num.node.getComponent(UIOpacity).opacity = 0;\r\n\r\n            if (!this._frameAnim) {\r\n                const frameAnimNode = instantiate(GameConst.frameAnim);\r\n                this.node.addChild(frameAnimNode);\r\n                this._frameAnim = frameAnimNode.getComponent(PfFrameAnim);\r\n                this._frameAnim.init(GameConst.CommonAtlas, 'ag_', 6, 2 * GameConfig.ActionFrameTime);\r\n            }\r\n            this._frameAnim.reset();\r\n        } else {\r\n            zIndex = 1;\r\n            const count = Math.round(this._count);\r\n            if (this.mainType !== GameEnum.ItemType.Coin && count > 1) {\r\n                this.lab_num.string = 'x' + count.toString();\r\n                this.lab_num.node.getComponent(UIOpacity).opacity = 255;\r\n            }\r\n            if (this._frameAnim) {\r\n                this._frameAnim.stop();\r\n            }\r\n            if (this.mainType === GameEnum.ItemType.Box) {\r\n                this.icon.spriteFrame = GameConst.CommonAtlas.getSpriteFrame(GameIns.itemManager.getItemRecord(this._data.id).image);\r\n            } else {\r\n                this.icon.spriteFrame = GameIns.itemManager.getIcon(this._data.id);\r\n                this.icon.node.setScale(0.65,0.65);\r\n                if (GameIns.itemManager.isEquip(this._data.id)) {\r\n                    this.icon.node.setScale(0.5,0.5);\r\n                    zIndex = 2;\r\n                }\r\n                if (!this.light.spriteFrame) {\r\n                    this.light.spriteFrame = GameIns.loadManager.getImage('light6', 'itemImage') as SpriteFrame;\r\n                }\r\n                this.icon.node.setScale(0.75,0.75);\r\n\r\n                if (this.light.spriteFrame) {\r\n                    tween(this.light.node)\r\n                        .repeatForever(new Tween().by(1, { angle: -60 }))\r\n                        .start();\r\n                }\r\n            }\r\n        }\r\n        if (this.node.getSiblingIndex() !== zIndex) {\r\n            this.node.setSiblingIndex(zIndex);\r\n        }\r\n    }\r\n\r\n    _playShowAnim(callback = null) {\r\n        const actionFrameTime = GameConfig.ActionFrameTime;\r\n        const position = this.node.position;\r\n        const randomVec1 = position.x < 0\r\n            ? Tools.getRandomVec2(new Vec2(-40, 0), new Vec2(1, 20))\r\n            : Tools.getRandomVec2(new Vec2(0, 40), new Vec2(1, 20));\r\n        const randomVec2 = position.x < 0\r\n            ? Tools.getRandomVec2(new Vec2(-100, -50), new Vec2(25, 60))\r\n            : Tools.getRandomVec2(new Vec2(50, 100), new Vec2(25, 60));\r\n        const randomVec3 = new Vec2(randomVec2.x, Tools.random_int(-10, 10));\r\n\r\n        tween(this.node)\r\n            .to(2 * actionFrameTime, { position: position.add(V3(randomVec1.x,randomVec1.y)) })\r\n            .to(3 * actionFrameTime, { position: position.add(V3(randomVec2.x,randomVec2.y)) })\r\n            .to(4 * actionFrameTime, { position: position.add(V3(randomVec3.x,randomVec3.y)) })\r\n            .call(() => {\r\n                if (callback) callback(this);\r\n            })\r\n            .start();\r\n    }\r\n\r\n    remove() {\r\n        Tween.stopAllByTarget(this.node);\r\n    }\r\n\r\n    _getLightColor() {\r\n        let color = Color.WHITE;\r\n        switch (this._data.itemtype) {\r\n            case GameEnum.ItemType.Box:\r\n                switch (this._data.type) {\r\n                    case 1:\r\n                        color = new Color(100, 243, 92);\r\n                        break;\r\n                    case 2:\r\n                        color = new Color(19, 192, 236);\r\n                        break;\r\n                    case 3:\r\n                        color = new Color(238, 227, 32);\r\n                        break;\r\n                }\r\n                break;\r\n            case GameEnum.ItemType.Fragment:\r\n                color = new Color(255, 255, 0);\r\n                break;\r\n            case GameEnum.ItemType.Upgrade:\r\n                color = new Color(0, 255, 255);\r\n                break;\r\n        }\r\n        return color;\r\n    }\r\n\r\n    get itemId() {\r\n        return this.mainType !== GameEnum.ItemType.Box ? this._data.id : this._param[0];\r\n    }\r\n\r\n    get mainType() {\r\n        return this._data.itemtype;\r\n    }\r\n\r\n    get subType() {\r\n        return this._data.type;\r\n    }\r\n\r\n    get count() {\r\n        return this._count;\r\n    }\r\n\r\n    get autoAttract() {\r\n        return this._autoAttract;\r\n    }\r\n\r\n    set autoAttract(value) {\r\n        this._autoAttract = value;\r\n    }\r\n\r\n    get bAttract() {\r\n        return this._bAttract;\r\n    }\r\n\r\n    set bAttract(value) {\r\n        this._bAttract = value;\r\n    }\r\n\r\n    set moveDir(value) {\r\n        this._moveDir = value;\r\n    }\r\n\r\n    set moveSpeed(value) {\r\n        this._moveSpeed = value;\r\n    }\r\n\r\n    set scale(value) {\r\n        if (this.node.scale > value) {\r\n            this.node.setScale(value,value);\r\n        }\r\n    }\r\n}\r\n\r\nfunction V3(x: number, y: number): Vec3 {\r\n    throw new Error('Function not implemented.');\r\n}\r\n"]}