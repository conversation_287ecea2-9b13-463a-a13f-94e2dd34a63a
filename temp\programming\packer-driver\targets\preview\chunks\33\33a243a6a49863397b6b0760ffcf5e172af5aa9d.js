// import { _decorator, Sprite } from 'cc';
// import Bullet from './Bullet';
// import frameWork from './frameWork';
// import EnemyManager from './EnemyManager';
// import GameEnum from './GameEnum';
// import ColliderComp from './ColliderComp';
// import Tools from './Tools';
// import ParticleComponent from './ParticleCompenent';
// import HurtEffectManager from './HurtEffectManager';
// import GameConst from './GameConst';
// const { ccclass, property } = _decorator;
// @ccclass('FireBullet')
// export default class FireBullet extends Bullet {
//     @property(Sprite)
//     animIcon = null;
//     constructor() {
//         super();
//         this._animFrames = [];
//         this._animFrameNum = 0;
//         this._animTime = 0;
//         this._animFrameIndex = 0;
//         this._isBlast = false;
//         this._curRadius = 0;
//         this._maxRadius = 0;
//         this._diffuseSpeed = 100;
//         this._enemyArr = [];
//         this._particleCom = null;
//     }
//     create(config) {
//         this._animFrames.splice(0);
//         this._animFrameNum = 0;
//         this.reset();
//         super.create(config);
//         if (this.getType() === 27) {
//             this._particleCom = this.addComp(
//                 ParticleComponent,
//                 new ParticleComponent('p1', 0.2, this.getType(), this.m_config.id)
//             );
//         } else if (this.getType() === 28) {
//             this._particleCom = this.addComp(
//                 ParticleComponent,
//                 new ParticleComponent('p2', 0.2, this.getType(), this.m_config.id)
//             );
//         }
//     }
//     reset() {
//         this._animTime = 0;
//         this._animFrameIndex = 0;
//         this._isBlast = false;
//         this._curRadius = 0;
//         this._maxRadius = 0;
//         this._diffuseSpeed = 100;
//         this._enemyArr.splice(0);
//     }
//     async setSkin() {
//         if (this.m_config.am !== '') {
//             const animData = this.m_config.am.split('*');
//             if (animData.length > 1) {
//                 const baseName = animData[0];
//                 this._animFrameNum = parseInt(animData[1]);
//                 for (let i = 0; i < this._animFrameNum; i++) {
//                     this._animFrames.push(
//                         GameIns.loadManager.getImage(`${baseName}${i}`, 'mainBullet')
//                     );
//                 }
//                 this.animIcon.spriteFrame = this._animFrames[0];
//             }
//         } else {
//             await super.setSkin();
//         }
//     }
//     onCollide(target) {
//         if (this.bulletState.through) return;
//         this.playHurtAudio();
//         if (this.m_config.exstyle1 && this.m_config.exstyle1 !== '') {
//             HurtEffectManager.me.createHurtEffect(
//                 target,
//                 this,
//                 this.m_config.exstyle1,
//                 this.m_config.exstyle2
//             );
//         }
//         if (this.getType() === 28) {
//             this._maxRadius = this.bulletState.extra[1];
//             this._isBlast = true;
//             const screenPos = this.m_collideComp.getScreenPos();
//             EnemyManager.EnemyMgr.planes.forEach((enemy) => {
//                 if (enemy.active && !Tools.arrContain(this._enemyArr, enemy)) {
//                     let enemyPos;
//                     let distance = Infinity;
//                     switch (enemy.type) {
//                         case GameEnum.EnemyType.Normal:
//                             if (enemy.collideLevel > GameEnum.EnemyCollideLevel.None) {
//                                 enemyPos = enemy.getComp(ColliderComp).getScreenPos();
//                                 distance = Tools.getPosDis(screenPos, enemyPos);
//                             }
//                             break;
//                         case GameEnum.EnemyType.Turret:
//                             enemyPos = enemy.getComp(ColliderComp).getScreenPos();
//                             distance = Tools.getPosDis(screenPos, enemyPos);
//                             break;
//                         case GameEnum.EnemyType.Ligature:
//                             enemy.getPlanes().forEach((plane) => {
//                                 const planePos = plane.getComp(ColliderComp).getScreenPos();
//                                 const planeDistance = Tools.getPosDis(screenPos, planePos);
//                                 if (planeDistance < this._curRadius) {
//                                     plane.hurt(this.getAttack(plane));
//                                     this._enemyArr.push(plane);
//                                 }
//                             });
//                             distance = Infinity;
//                             break;
//                         case GameEnum.EnemyType.Build:
//                             if (enemy.collideLevel > GameEnum.EnemyCollideLevel.None) {
//                                 enemyPos = enemy.getComp(ColliderComp).getScreenPos();
//                                 distance = Tools.getPosDis(screenPos, enemyPos);
//                             }
//                             break;
//                         case GameEnum.EnemyType.Ship:
//                             // Handle Ship type if needed
//                             break;
//                     }
//                     if (distance < this._curRadius) {
//                         enemy.hurt(this.getAttack(enemy) * this.bulletState.extra[0]);
//                     }
//                 }
//             });
//         }
//         this.remove();
//     }
//     update(deltaTime) {
//         if (!GameConst.GConst.GameAble) return;
//         deltaTime = deltaTime > 0.2 ? 0.016666666666667 : deltaTime;
//         super.update(deltaTime);
//         if (this._animFrameNum > 0) {
//             this._animTime += deltaTime;
//             if (this._animTime >= 0.06) {
//                 this._animFrameIndex++;
//                 if (this._animFrameIndex >= this._animFrameNum) {
//                     this._animFrameIndex = 0;
//                 }
//                 this._animTime = 0;
//                 this.animIcon.spriteFrame = this._animFrames[this._animFrameIndex];
//             }
//         }
//     }
//     remove() {
//         this.reset();
//         super.remove();
//     }
//     refresh() {
//         super.refresh();
//         if (this.getType() === 27 && this._particleCom) {
//             this._particleCom.setDelay(0.2);
//         }
//     }
// }
System.register(["cc"], function (_export, _context) {
  "use strict";

  var _cclegacy, _crd;

  return {
    setters: [function (_cc) {
      _cclegacy = _cc.cclegacy;
    }],
    execute: function () {
      _crd = true;

      _cclegacy._RF.push({}, "d3f34IZ6c1G+LO8zlJR8CEH", "FireBullet", undefined);

      _cclegacy._RF.pop();

      _crd = false;
    }
  };
});
//# sourceMappingURL=33a243a6a49863397b6b0760ffcf5e172af5aa9d.js.map