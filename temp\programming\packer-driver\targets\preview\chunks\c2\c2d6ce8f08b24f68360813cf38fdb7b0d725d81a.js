System.register(["cc"], function (_export, _context) {
  "use strict";

  var _cclegacy, __checkObsolete__, __checkObsoleteInNamespace__, _decorator, Component, view, _dec, _class, _class2, _crd, ccclass, property, Global;

  return {
    setters: [function (_cc) {
      _cclegacy = _cc.cclegacy;
      __checkObsolete__ = _cc.__checkObsolete__;
      __checkObsoleteInNamespace__ = _cc.__checkObsoleteInNamespace__;
      _decorator = _cc._decorator;
      Component = _cc.Component;
      view = _cc.view;
    }],
    execute: function () {
      _crd = true;

      _cclegacy._RF.push({}, "ffc9ayt/ERKC44NZnC2xOF1", "Global", undefined);

      __checkObsolete__(['_decorator', 'Component', 'Node', 'view']);

      ({
        ccclass,
        property
      } = _decorator);

      _export("Global", Global = (_dec = ccclass('Global'), _dec(_class = (_class2 = class Global extends Component {}, _class2.WIDTH = view.getDesignResolutionSize().width, _class2.HEIGHT = view.getDesignResolutionSize().height, _class2.NORMAL_BULLET = "normal_bullet", _class2.LIGHT_BULLET = "light_bullet", _class2.MISSILE_BULLET = "missile_bullet", _class2.ENEMY_1 = "enemy1", _class2.ENEMY_2 = "enemy2", _class2.ENEMY_BULLET_1 = "enemybullet1", _class2.ENEMY_BULLET_2 = "enemybullet2", _class2.BLOOD_GOODS = "bloodGoods", _class2.LIGHT_GOODS = "lightGoods", _class2.MISSILE_GOODS = "missileGoods", _class2.SCORE = 0, _class2)) || _class));

      _cclegacy._RF.pop();

      _crd = false;
    }
  };
});
//# sourceMappingURL=c2d6ce8f08b24f68360813cf38fdb7b0d725d81a.js.map