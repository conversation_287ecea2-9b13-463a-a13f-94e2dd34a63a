{"version": 3, "sources": ["file:///E:/M2Game/Client/assets/scripts/Game/ui/map/GameMapRun.ts"], "names": ["_decorator", "Component", "Node", "Sprite", "tween", "UITransform", "v2", "view", "GameMapData", "MapItemData", "GameIns", "ResourceList", "Tools", "ExchangeMap", "ImageSequence", "GameConst", "GameEnum", "NodeMove", "ccclass", "property", "GameMapRun", "floorDataSprite", "skyDataSprite", "floorSpeed", "startY", "floorLayer", "skyLayer", "skyLinkYDis", "skySpeed", "inMapItem", "skyNodeMove", "skyNodeAngle", "hideImg", "imageSqueDataSprite", "imageSqueSpeed", "imageSqueLayer", "imageSquePos", "randomNum", "LayerData", "Map", "LayerParents", "allFloorLayers", "allSkyLayers", "allSqueLayers", "loadedNode", "itemDatas", "changeMapSpeedRatio", "changeSkySpeedRatio", "posOffIndex", "initOver", "loadedAtlas", "maskOver", "lightingPoor", "thunders", "nowThunder", "isPlayThunder", "mapCanRun", "isInitParkour", "totalHeight", "_lineArr", "delayInit", "delay", "m_paokuRun", "_loadFinish", "_loadTotal", "_loadCount", "canParkoutRun", "frist", "viewHeight", "getVisibleSize", "height", "canHideImg", "loadFinish", "onLoad", "instance", "checkLoadFinish", "getAllMapLayer", "getAllSkyLayer", "getAllSqueLayer", "getAllBuildAndTurret", "initMapAndSky", "battleManager", "addLoadingPercent", "update", "deltaTime", "GameAble", "gameState", "gameRuleManager", "GameState", "Battle", "<PERSON><PERSON><PERSON>", "Ready", "WillOver", "Idle", "Over", "for<PERSON>ach", "layer", "mapLayer1Run", "skyLayer1Run", "p<PERSON><PERSON><PERSON><PERSON>", "enable", "initData", "level", "subLevel", "reset", "set", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "skyL<PERSON>er<PERSON><PERSON>nt", "randomIndex", "beanManager", "loadTableByName1", "table", "ids", "getAllID", "index", "record", "getRecorder", "totalRules", "total_rules", "Math", "floor", "random", "id", "floor_res", "sky_res", "floor_speed", "floor_layer", "sky_layer", "start_y", "link_y_distance", "sky_speed", "in_map_item", "skyNode_move", "sky_angle", "hide_img", "imageSque_res", "imageSque_speed", "imageSque_layer", "imageSque_pos", "length", "atlasManage", "i", "itemData", "split", "mapItem", "Number", "j", "createPos", "temp", "arrC<PERSON>ain", "push", "pos1", "pos2", "floorAtlas", "skyAtlas", "imageSqueAtlas", "sprite", "atlasName", "asset", "loadManager", "releaseAsset", "delete", "has", "loadAtlas1", "mapLayerinit", "skyLayer1init", "imageSqueinit", "parent", "y", "mapEndChange", "me", "endChange", "splice", "LayerReset", "get", "destroy", "clear", "layers", "layerData", "destroyAllChildren", "ViewTop", "ViewBot", "spriteNames", "loadSprite", "_", "key", "node", "addComponent", "spriteName", "spriteFrame", "getImage", "designWidth", "scale", "getComponent", "width", "tempY", "tempH", "speed", "speeds", "nowUseNode", "skyLayerData", "map", "filter", "PosInfo", "nodeAngle", "nodeMove", "x", "scaleX", "scaleY", "setPosition", "angle", "position", "loadImageSque", "frameCount", "frames", "frameName", "frame", "imageSequence", "setData", "frameTime", "setStart", "initParkourLayer", "layerParent", "adjustedSpeed", "posY1", "nextPosInfo", "posX", "posY", "abs", "nextSpriteInfo", "lastNode", "starPos", "getRect", "indexOf", "speedDiff", "removedNode", "shift", "active", "freeNode", "newNode", "addNodeMoveComp", "sizeMode", "SizeMode", "RAW", "createItem", "moveData", "xSpeed", "ySpeed", "nodeMoveComp", "parentNode", "nextSpriteName", "itemIndex", "back<PERSON>ayer", "itemNode", "backPosX", "backPosY", "backScaleX", "backScaleY", "enemy", "log", "enemyManager", "addMapPlane", "track", "setSiblingIndex", "zIndex", "Vec", "setScale", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "to", "opacity", "start"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAASA,MAAAA,U,OAAAA,U;AAAoBC,MAAAA,S,OAAAA,S;AAAWC,MAAAA,I,OAAAA,I;AAAMC,MAAAA,M,OAAAA,M;AAAqBC,MAAAA,K,OAAAA,K;AAAOC,MAAAA,W,OAAAA,W;AAAaC,MAAAA,E,OAAAA,E;AAAIC,MAAAA,I,OAAAA,I;;AACpFC,MAAAA,W;;AACAC,MAAAA,W;;AACEC,MAAAA,O,iBAAAA,O;;AACFC,MAAAA,Y;;AACEC,MAAAA,K,iBAAAA,K;;AAEFC,MAAAA,W;;AACAC,MAAAA,a;;AACEC,MAAAA,S,iBAAAA,S;;AACFC,MAAAA,Q;;AACAC,MAAAA,Q;;;;;;;;;OAED;AAAEC,QAAAA,OAAF;AAAWC,QAAAA;AAAX,O,GAAwBnB,U;;yBAGToB,U,WADpBF,OAAO,CAAC,YAAD,C,UAKHC,QAAQ,CAACjB,IAAD,C,UAGRiB,QAAQ,CAACjB,IAAD,C,sCARb,MACqBkB,UADrB,SACwCnB,SADxC,CACkD;AAAA;AAAA;;AAAA;;AAAA;;AAAA,eAU9CoB,eAV8C,GAU5B,EAV4B;AAAA,eAW9CC,aAX8C,GAW9B,EAX8B;AAAA,eAY9CC,UAZ8C,GAYjC,EAZiC;AAAA,eAa9CC,MAb8C,GAarC,CAbqC;AAAA,eAc9CC,UAd8C,GAcjC,EAdiC;AAAA,eAe9CC,QAf8C,GAenC,EAfmC;AAAA,eAgB9CC,WAhB8C,GAgBhC,EAhBgC;AAAA,eAiB9CC,QAjB8C,GAiBnC,EAjBmC;AAAA,eAkB9CC,SAlB8C,GAkBlC,EAlBkC;AAAA,eAmB9CC,WAnB8C,GAmBhC,EAnBgC;AAAA,eAoB9CC,YApB8C,GAoB/B,EApB+B;AAAA,eAqB9CC,OArB8C,GAqBpC,EArBoC;AAAA,eAsB9CC,mBAtB8C,GAsBxB,EAtBwB;AAAA,eAuB9CC,cAvB8C,GAuB7B,EAvB6B;AAAA,eAwB9CC,cAxB8C,GAwB7B,EAxB6B;AAAA,eAyB9CC,YAzB8C,GAyB/B,EAzB+B;AAAA,eA0B9CC,SA1B8C,GA0BlC,IA1BkC;AAAA,eA2B9CC,SA3B8C,GA2BlC,IAAIC,GAAJ,EA3BkC;AAAA,eA4B9CC,YA5B8C,GA4B/B,IAAID,GAAJ,EA5B+B;AAAA,eA6B9CE,cA7B8C,GA6B7B,EA7B6B;AAAA,eA8B9CC,YA9B8C,GA8B/B,EA9B+B;AAAA,eA+B9CC,aA/B8C,GA+B9B,EA/B8B;AAAA,eAgC9CC,UAhC8C,GAgCjC,IAAIL,GAAJ,EAhCiC;AAAA,eAiC9CM,SAjC8C,GAiClC,EAjCkC;AAAA,eAkC9CC,mBAlC8C,GAkCxB,CAlCwB;AAAA,eAmC9CC,mBAnC8C,GAmCxB,CAnCwB;AAAA,eAoC9CC,WApC8C,GAoChC,CApCgC;AAAA,eAqC9CC,QArC8C,GAqCnC,KArCmC;AAAA,eAsC9CC,WAtC8C,GAsChC,IAAIX,GAAJ,EAtCgC;AAAA,eAuC9CY,QAvC8C,GAuCnC,IAvCmC;AAAA,eAwC9CC,YAxC8C,GAwC/B,EAxC+B;AAAA,eAyC9CC,QAzC8C,GAyCnC,IAAId,GAAJ,EAzCmC;AAAA,eA0C9Ce,UA1C8C,GA0CjC,CA1CiC;AAAA,eA2C9CC,aA3C8C,GA2C9B,KA3C8B;AAAA,eA4C9CC,SA5C8C,GA4ClC,IA5CkC;AAAA,eA6C9CC,aA7C8C,GA6C9B,KA7C8B;AAAA,eA8C9CC,WA9C8C,GA8ChC,CA9CgC;AAAA,eA+C9CC,QA/C8C,GA+CnC,EA/CmC;AAAA,eAgD9CC,SAhD8C,GAgDlC,EAhDkC;AAAA,eAiD9CC,KAjD8C,GAiDtC,EAjDsC;AAAA,eAkD9CC,UAlD8C,GAkDjC,KAlDiC;AAAA,eAmD9CC,WAnD8C,GAmDhC,KAnDgC;AAAA,eAoD9CC,UApD8C,GAoDjC,CApDiC;AAAA,eAqD9CC,UArD8C,GAqDjC,CArDiC;AAAA,eAsD9CC,aAtD8C,GAsD9B,KAtD8B;AAAA,eAuD9CC,KAvD8C,GAuDtC,IAvDsC;AAAA,eAyD9CC,UAzD8C,GAyDjC7D,IAAI,CAAC8D,cAAL,GAAsBC,MAzDW;AAAA,eA0D9CC,UA1D8C,GA0DjC,IA1DiC;AAAA;;AA4DhC,YAAVC,UAAU,GAAY;AACtB,iBAAO,KAAKT,WAAZ;AACH;;AAEDU,QAAAA,MAAM,GAAG;AACLrD,UAAAA,UAAU,CAACsD,QAAX,GAAsB,IAAtB;AACH;;AAEDC,QAAAA,eAAe,GAAG;AACd,eAAKV,UAAL;;AACA,cAAI,KAAKA,UAAL,IAAmB,KAAKD,UAA5B,EAAwC;AACpC,iBAAKY,cAAL;AACA,iBAAKC,cAAL;AACA,iBAAKC,eAAL;AACA,iBAAKC,oBAAL;AACA,iBAAKC,aAAL;AACA,iBAAKjB,WAAL,GAAmB,IAAnB;AACH;;AACD;AAAA;AAAA,kCAAQkB,aAAR,CAAsBC,iBAAtB,CAAwC,IAAI,KAAKlB,UAAjD;AACH;;AAEDmB,QAAAA,MAAM,CAACC,SAAD,EAAoB;AACtB,cAAI,CAAC;AAAA;AAAA,sCAAUC,QAAf,EAAyB;AACrB;AACH;;AAED,cAAID,SAAS,GAAG,GAAhB,EAAqB;AACjBA,YAAAA,SAAS,GAAG,iBAAZ;AACH;;AAED,cAAME,SAAS,GAAG;AAAA;AAAA,kCAAQC,eAAR,CAAwBD,SAA1C;;AACA,cACIA,SAAS,KAAK;AAAA;AAAA,oCAASE,SAAT,CAAmBC,MAAjC,IACAH,SAAS,KAAK;AAAA;AAAA,oCAASE,SAAT,CAAmBE,MADjC,IAEAJ,SAAS,KAAK;AAAA;AAAA,oCAASE,SAAT,CAAmBG,KAFjC,IAGAL,SAAS,KAAK;AAAA;AAAA,oCAASE,SAAT,CAAmBI,QAHjC,IAIAN,SAAS,KAAK;AAAA;AAAA,oCAASE,SAAT,CAAmBK,IAJjC,IAKAP,SAAS,KAAK;AAAA;AAAA,oCAASE,SAAT,CAAmBM,IANrC,EAOE;AACE;AACH;;AAED,cAAI,CAAC,KAAKtC,SAAV,EAAqB;AACjB;AACH;;AAED,eAAKf,cAAL,CAAoBsD,OAApB,CAA6BC,KAAD,IAAW;AACnC,iBAAKC,YAAL,CAAkBb,SAAlB,EAA6BY,KAA7B;AACH,WAFD;AAIA,eAAKtD,YAAL,CAAkBqD,OAAlB,CAA2BC,KAAD,IAAW;AACjC,iBAAKE,YAAL,CAAkBd,SAAlB,EAA6BY,KAA7B;AACH,WAFD,EA7BsB,CAiCtB;AACA;AACA;AAEA;AACA;AACA;AACA;AACH,SA1H6C,CA4H9C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;AAEAG,QAAAA,QAAQ,CAACC,MAAD,EAAkB;AACtB,eAAKtC,UAAL,GAAkBsC,MAAlB;AACH,SA3I6C,CA6I9C;AACA;AACA;AAEA;AACA;AACA;;;AAEAC,QAAAA,QAAQ,CAACC,KAAD,EAAgBC,QAAhB,EAAkC;AACtC,eAAKxC,WAAL,GAAmB,KAAnB;AACA,eAAKC,UAAL,GAAkB,CAAlB;AACA,eAAKC,UAAL,GAAkB,CAAlB;AACA,eAAKuC,KAAL;AACA,eAAKxD,WAAL,GAAmB,CAAnB;AAEA,eAAKR,YAAL,CAAkBiE,GAAlB,CAAsB,CAAtB,EAAyB,KAAKC,gBAA9B;AACA,eAAKlE,YAAL,CAAkBiE,GAAlB,CAAsB,CAAtB,EAAyB,KAAKE,cAA9B;AAEA,cAAIC,WAAW,GAAG,CAAlB;AACA,eAAK5C,UAAL;AACA;AAAA;AAAA,kCAAQ6C,WAAR,CAAoBC,gBAApB,CAAqC;AAAA;AAAA,yDAAwBR,KAAxB,CAArC,EAAwES,KAAD,IAAW;AAC9E,gBAAMC,GAAG,GAAGD,KAAK,CAACE,QAAN,EAAZ;AACA,gBAAIC,KAAK,GAAG,CAAZ;;AAEA,eAAG;AACC,kBAAMC,MAAM,GAAGJ,KAAK,CAACK,WAAN,CAAkBJ,GAAG,CAACJ,WAAD,CAArB,CAAf;;AACA,kBAAIL,QAAQ,KAAKY,MAAM,CAACb,KAAxB,EAA+B;AAC3B,oBAAMe,UAAU,GAAGF,MAAM,CAACG,WAA1B;;AACA,oBAAI,KAAKjF,SAAL,KAAmB,IAAvB,EAA6B;AACzB,uBAAKA,SAAL,GAAiBkF,IAAI,CAACC,KAAL,CAAWD,IAAI,CAACE,MAAL,MAAiBJ,UAAU,GAAG,CAA9B,IAAmC,CAA9C,IAAmDH,KAApE;AACH;;AACD,oBAAIC,MAAM,CAACO,EAAP,KAAc,KAAKrF,SAAvB,EAAkC;AAC9B,uBAAKhB,eAAL,GAAuB8F,MAAM,CAACQ,SAA9B;AACA,uBAAKrG,aAAL,GAAqB6F,MAAM,CAACS,OAA5B;AACA,uBAAKrG,UAAL,GAAkB4F,MAAM,CAACU,WAAzB;AACA,uBAAKpG,UAAL,GAAkB0F,MAAM,CAACW,WAAzB;AACA,uBAAKpG,QAAL,GAAgByF,MAAM,CAACY,SAAvB;AACA,uBAAKvG,MAAL,GAAc2F,MAAM,CAACa,OAArB;AACA,uBAAKrG,WAAL,GAAmBwF,MAAM,CAACc,eAA1B;AACA,uBAAKrG,QAAL,GAAgBuF,MAAM,CAACe,SAAvB;AACA,uBAAKrG,SAAL,GAAiBsF,MAAM,CAACgB,WAAxB;AACA,uBAAKrG,WAAL,GAAmBqF,MAAM,CAACiB,YAA1B;AACA,uBAAKrG,YAAL,GAAoBoF,MAAM,CAACkB,SAA3B;AACA,uBAAKrG,OAAL,GAAemF,MAAM,CAACmB,QAAtB;AACA,uBAAKrG,mBAAL,GAA2BkF,MAAM,CAACoB,aAAlC;AACA,uBAAKrG,cAAL,GAAsBiF,MAAM,CAACqB,eAA7B;AACA,uBAAKrG,cAAL,GAAsBgF,MAAM,CAACsB,eAA7B;AACA,uBAAKrG,YAAL,GAAoB+E,MAAM,CAACuB,aAA3B;AACA;AACH;AACJ,eAxBD,MAwBO;AACHxB,gBAAAA,KAAK,GAAGN,WAAW,GAAG,CAAtB;AACH;AACJ,aA7BD,QA6BS,EAAEA,WAAF,GAAgBI,GAAG,CAAC2B,MA7B7B;;AA+BA,iBAAKC,WAAL;AACA,iBAAKjE,eAAL;AACH,WArCD,EAqCG,MAAM,CACL;AACH,WAvCD;AAwCH;;AAEDI,QAAAA,oBAAoB,GAAG;AACnB,eAAK,IAAI8D,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAG,KAAKhH,SAAL,CAAe8G,MAAnC,EAA2CE,CAAC,EAA5C,EAAgD;AAC5C,gBAAMC,QAAQ,GAAG,KAAKjH,SAAL,CAAegH,CAAf,EAAkBE,KAAlB,CAAwB,GAAxB,CAAjB;AACA,gBAAMC,OAAO,GAAG;AAAA;AAAA,4CACZC,MAAM,CAACH,QAAQ,CAAC,CAAD,CAAT,CADM,EAEZG,MAAM,CAACH,QAAQ,CAAC,CAAD,CAAT,CAFM,EAGZG,MAAM,CAACH,QAAQ,CAAC,CAAD,CAAT,CAHM,EAIZG,MAAM,CAACH,QAAQ,CAAC,CAAD,CAAT,CAJM,EAKZG,MAAM,CAACH,QAAQ,CAAC,CAAD,CAAT,CALM,EAMZG,MAAM,CAACH,QAAQ,CAAC,CAAD,CAAT,CANM,EAOZG,MAAM,CAACH,QAAQ,CAAC,CAAD,CAAT,CAPM,EAQZG,MAAM,CAACH,QAAQ,CAAC,CAAD,CAAT,CARM,EASZG,MAAM,CAACH,QAAQ,CAAC,CAAD,CAAT,CATM,EAUZG,MAAM,CAACH,QAAQ,CAAC,CAAD,CAAT,CAVM,EAWZA,QAAQ,CAAC,EAAD,CAXI,CAAhB;AAaA,iBAAKjG,SAAL,CAAegG,CAAf,IAAoBG,OAApB;AACH,WAjBkB,CAmBnB;;;AACA,eAAK,IAAIH,EAAC,GAAG,CAAb,EAAgBA,EAAC,GAAG,KAAKhG,SAAL,CAAe8F,MAAf,GAAwB,CAA5C,EAA+CE,EAAC,EAAhD,EAAoD;AAChD,iBAAK,IAAIK,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAG,KAAKrG,SAAL,CAAe8F,MAAf,GAAwB,CAAxB,GAA4BE,EAAhD,EAAmDK,CAAC,EAApD,EAAwD;AACpD,kBAAI,KAAKrG,SAAL,CAAeqG,CAAf,EAAkBC,SAAlB,GAA8B,KAAKtG,SAAL,CAAeqG,CAAC,GAAG,CAAnB,EAAsBC,SAAxD,EAAmE;AAC/D,oBAAMC,IAAI,GAAG,KAAKvG,SAAL,CAAeqG,CAAf,CAAb;AACA,qBAAKrG,SAAL,CAAeqG,CAAf,IAAoB,KAAKrG,SAAL,CAAeqG,CAAC,GAAG,CAAnB,CAApB;AACA,qBAAKrG,SAAL,CAAeqG,CAAC,GAAG,CAAnB,IAAwBE,IAAxB;AACH;AACJ;AACJ;AACJ;;AAEDtE,QAAAA,eAAe,GAAG;AACd,eAAK,IAAI+D,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAG,KAAK1G,cAAL,CAAoBwG,MAAxC,EAAgDE,CAAC,EAAjD,EAAqD;AACjD,gBAAM7C,KAAK,GAAGiD,MAAM,CAAC,KAAK9G,cAAL,CAAoB0G,CAApB,CAAD,CAApB;;AACA,gBAAI,CAAC;AAAA;AAAA,gCAAMQ,UAAN,CAAiB,KAAK1G,aAAtB,EAAqCqD,KAArC,CAAL,EAAkD;AAC9C,mBAAKrD,aAAL,CAAmB2G,IAAnB,CAAwBtD,KAAxB;AACH;AACJ,WANa,CAQd;;;AACA,eAAK,IAAI6C,GAAC,GAAG,CAAb,EAAgBA,GAAC,GAAG,KAAKzG,YAAL,CAAkBuG,MAAlB,GAA2B,CAA/C,EAAkDE,GAAC,EAAnD,EAAuD;AACnD,iBAAK,IAAIK,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAG,KAAK9G,YAAL,CAAkBuG,MAAlB,GAA2B,CAA3B,GAA+BE,GAAnD,EAAsDK,CAAC,EAAvD,EAA2D;AACvD,kBAAMK,IAAI,GAAGN,MAAM,CAAC,KAAK7G,YAAL,CAAkB8G,CAAlB,EAAqBH,KAArB,CAA2B,GAA3B,EAAgC,CAAhC,CAAD,CAAnB;AACA,kBAAMS,IAAI,GAAGP,MAAM,CAAC,KAAK7G,YAAL,CAAkB8G,CAAC,GAAG,CAAtB,EAAyBH,KAAzB,CAA+B,GAA/B,EAAoC,CAApC,CAAD,CAAnB;;AACA,kBAAIQ,IAAI,GAAGC,IAAX,EAAiB;AACb;AACA,iBAAC,KAAKpH,YAAL,CAAkB8G,CAAlB,CAAD,EAAuB,KAAK9G,YAAL,CAAkB8G,CAAC,GAAG,CAAtB,CAAvB,IAAmD,CAAC,KAAK9G,YAAL,CAAkB8G,CAAC,GAAG,CAAtB,CAAD,EAA2B,KAAK9G,YAAL,CAAkB8G,CAAlB,CAA3B,CAAnD;AACA,iBAAC,KAAKjH,mBAAL,CAAyBiH,CAAzB,CAAD,EAA8B,KAAKjH,mBAAL,CAAyBiH,CAAC,GAAG,CAA7B,CAA9B,IAAiE,CAAC,KAAKjH,mBAAL,CAAyBiH,CAAC,GAAG,CAA7B,CAAD,EAAkC,KAAKjH,mBAAL,CAAyBiH,CAAzB,CAAlC,CAAjE;AACA,iBAAC,KAAK/G,cAAL,CAAoB+G,CAApB,CAAD,EAAyB,KAAK/G,cAAL,CAAoB+G,CAAC,GAAG,CAAxB,CAAzB,IAAuD,CAAC,KAAK/G,cAAL,CAAoB+G,CAAC,GAAG,CAAxB,CAAD,EAA6B,KAAK/G,cAAL,CAAoB+G,CAApB,CAA7B,CAAvD;AACA,iBAAC,KAAKhH,cAAL,CAAoBgH,CAApB,CAAD,EAAyB,KAAKhH,cAAL,CAAoBgH,CAAC,GAAG,CAAxB,CAAzB,IAAuD,CAAC,KAAKhH,cAAL,CAAoBgH,CAAC,GAAG,CAAxB,CAAD,EAA6B,KAAKhH,cAAL,CAAoBgH,CAApB,CAA7B,CAAvD;AACH;AACJ;AACJ;AACJ;;AAEDrE,QAAAA,cAAc,GAAG;AACb,eAAK,IAAIgE,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAG,KAAKnH,QAAL,CAAciH,MAAlC,EAA0CE,CAAC,EAA3C,EAA+C;AAC3C,gBAAI,CAAC;AAAA;AAAA,gCAAMQ,UAAN,CAAiB,KAAK3G,YAAtB,EAAoC,KAAKhB,QAAL,CAAcmH,CAAd,CAApC,CAAL,EAA4D;AACxD,mBAAKnG,YAAL,CAAkB4G,IAAlB,CAAuB,KAAK5H,QAAL,CAAcmH,CAAd,CAAvB;AACH;AACJ,WALY,CAOb;;;AACA,eAAK,IAAIA,GAAC,GAAG,CAAb,EAAgBA,GAAC,GAAG,KAAKlH,WAAL,CAAiBgH,MAAjB,GAA0B,CAA9C,EAAiDE,GAAC,EAAlD,EAAsD;AAClD,iBAAK,IAAIK,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAG,KAAKvH,WAAL,CAAiBgH,MAAjB,GAA0B,CAA1B,GAA8BE,GAAlD,EAAqDK,CAAC,EAAtD,EAA0D;AACtD,kBAAMK,IAAI,GAAGN,MAAM,CAAC,KAAKtH,WAAL,CAAiBuH,CAAjB,EAAoBH,KAApB,CAA0B,GAA1B,EAA+B,CAA/B,CAAD,CAAnB;AACA,kBAAMS,IAAI,GAAGP,MAAM,CAAC,KAAKtH,WAAL,CAAiBuH,CAAC,GAAG,CAArB,EAAwBH,KAAxB,CAA8B,GAA9B,EAAmC,CAAnC,CAAD,CAAnB;;AACA,kBAAIQ,IAAI,GAAGC,IAAX,EAAiB;AACb;AACA,iBAAC,KAAK7H,WAAL,CAAiBuH,CAAjB,CAAD,EAAsB,KAAKvH,WAAL,CAAiBuH,CAAC,GAAG,CAArB,CAAtB,IAAiD,CAAC,KAAKvH,WAAL,CAAiBuH,CAAC,GAAG,CAArB,CAAD,EAA0B,KAAKvH,WAAL,CAAiBuH,CAAjB,CAA1B,CAAjD;AACA,iBAAC,KAAK5H,aAAL,CAAmB4H,CAAnB,CAAD,EAAwB,KAAK5H,aAAL,CAAmB4H,CAAC,GAAG,CAAvB,CAAxB,IAAqD,CAAC,KAAK5H,aAAL,CAAmB4H,CAAC,GAAG,CAAvB,CAAD,EAA4B,KAAK5H,aAAL,CAAmB4H,CAAnB,CAA5B,CAArD;AACA,iBAAC,KAAKxH,QAAL,CAAcwH,CAAd,CAAD,EAAmB,KAAKxH,QAAL,CAAcwH,CAAC,GAAG,CAAlB,CAAnB,IAA2C,CAAC,KAAKxH,QAAL,CAAcwH,CAAC,GAAG,CAAlB,CAAD,EAAuB,KAAKxH,QAAL,CAAcwH,CAAd,CAAvB,CAA3C;AACA,iBAAC,KAAKtH,QAAL,CAAcsH,CAAd,CAAD,EAAmB,KAAKtH,QAAL,CAAcsH,CAAC,GAAG,CAAlB,CAAnB,IAA2C,CAAC,KAAKtH,QAAL,CAAcsH,CAAC,GAAG,CAAlB,CAAD,EAAuB,KAAKtH,QAAL,CAAcsH,CAAd,CAAvB,CAA3C;AACA,iBAAC,KAAKpH,WAAL,CAAiBoH,CAAjB,CAAD,EAAsB,KAAKpH,WAAL,CAAiBoH,CAAC,GAAG,CAArB,CAAtB,IAAiD,CAAC,KAAKpH,WAAL,CAAiBoH,CAAC,GAAG,CAArB,CAAD,EAA0B,KAAKpH,WAAL,CAAiBoH,CAAjB,CAA1B,CAAjD;AACA,iBAAC,KAAKnH,YAAL,CAAkBmH,CAAlB,CAAD,EAAuB,KAAKnH,YAAL,CAAkBmH,CAAC,GAAG,CAAtB,CAAvB,IAAmD,CAAC,KAAKnH,YAAL,CAAkBmH,CAAC,GAAG,CAAtB,CAAD,EAA2B,KAAKnH,YAAL,CAAkBmH,CAAlB,CAA3B,CAAnD;AACH;AACJ;AACJ;AACJ;;AAEDtE,QAAAA,cAAc,GAAG;AACb,eAAK,IAAIiE,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAG,KAAKpH,UAAL,CAAgBkH,MAApC,EAA4CE,CAAC,EAA7C,EAAiD;AAC7C,gBAAI,CAAC;AAAA;AAAA,gCAAMQ,UAAN,CAAiB,KAAK5G,cAAtB,EAAsC,KAAKhB,UAAL,CAAgBoH,CAAhB,CAAtC,CAAL,EAAgE;AAC5D,mBAAKpG,cAAL,CAAoB6G,IAApB,CAAyB,KAAK7H,UAAL,CAAgBoH,CAAhB,CAAzB;AACH;AACJ;AACJ;;AAEDD,QAAAA,WAAW,GAAG;AAAA;;AACV,cAAMa,UAAoB,GAAG,EAA7B;AACA,cAAMC,QAAkB,GAAG,EAA3B;AACA,cAAMC,cAAwB,GAAG,EAAjC,CAHU,CAKV;;AACA,eAAK,IAAMC,MAAX,IAAqB,KAAKvI,eAA1B,EAA2C;AACvC,gBAAMwI,SAAS,GAAGD,MAAM,CAACb,KAAP,CAAa,GAAb,EAAkB,CAAlB,CAAlB;;AACA,gBAAI,CAAC;AAAA;AAAA,gCAAMM,UAAN,CAAiBI,UAAjB,EAA6BI,SAA7B,CAAL,EAA8C;AAC1CJ,cAAAA,UAAU,CAACH,IAAX,CAAgBO,SAAhB;AACH;AACJ,WAXS,CAaV;;;AACA,eAAK,IAAMD,OAAX,IAAqB,KAAKtI,aAA1B,EAAyC;AACrC,gBAAMuI,UAAS,GAAGD,OAAM,CAACb,KAAP,CAAa,GAAb,EAAkB,CAAlB,CAAlB;;AACA,gBAAI,CAAC;AAAA;AAAA,gCAAMM,UAAN,CAAiBK,QAAjB,EAA2BG,UAA3B,CAAL,EAA4C;AACxCH,cAAAA,QAAQ,CAACJ,IAAT,CAAcO,UAAd;AACH;AACJ,WAnBS,CAqBV;;;AACA,eAAK,IAAMD,QAAX,IAAqB,KAAK3H,mBAA1B,EAA+C;AAC3C,gBAAM4H,WAAS,GAAGD,QAAM,CAACb,KAAP,CAAa,GAAb,EAAkB,CAAlB,CAAlB;;AACA,gBAAI,CAAC;AAAA;AAAA,gCAAMM,UAAN,CAAiBM,cAAjB,EAAiCE,WAAjC,CAAL,EAAkD;AAC9CF,cAAAA,cAAc,CAACL,IAAf,CAAoBO,WAApB;AACH;AACJ,WA3BS,CA6BV;;;AACA,eAAK3G,WAAL,CAAiB6C,OAAjB,CAAyB,CAAC+D,KAAD,EAAQD,SAAR,KAAsB;AAC3C,gBACI,CAAC;AAAA;AAAA,gCAAMR,UAAN,CAAiBI,UAAjB,EAA6BI,SAA7B,CAAD,IACA,CAAC;AAAA;AAAA,gCAAMR,UAAN,CAAiBK,QAAjB,EAA2BG,SAA3B,CADD,IAEA,CAAC;AAAA;AAAA,gCAAMR,UAAN,CAAiBM,cAAjB,EAAiCE,SAAjC,CAHL,EAIE;AACE;AAAA;AAAA,sCAAQE,WAAR,CAAoBC,YAApB,CAAiCF,KAAjC;AACA,mBAAK5G,WAAL,CAAiB+G,MAAjB,CAAwBJ,SAAxB;AACH;AACJ,WATD,EA9BU,CAyCV;;AAzCU,kDA0C0B;AAChC,gBAAI,CAAC,KAAI,CAAC3G,WAAL,CAAiBgH,GAAjB,CAAqBL,WAArB,CAAL,EAAsC;AAClC,cAAA,KAAI,CAAC7F,UAAL;AACA;AAAA;AAAA,sCAAQ+F,WAAR,CAAoBI,UAApB,CAA+B,SAASN,WAAxC,EAAoDC,KAAD,IAAW;AAC1D,gBAAA,KAAI,CAAC5G,WAAL,CAAiBuD,GAAjB,CAAqBoD,WAArB,EAAgCC,KAAhC;;AACA,gBAAA,KAAI,CAACnF,eAAL;AACH,eAHD;AAIH;AACJ,WAlDS;;AA0CV,eAAK,IAAMkF,WAAX,IAAwBJ,UAAxB;AAAA;AAAA,WA1CU,CAoDV;;;AApDU,oDAqDwB;AAC9B,gBAAI,CAAC,KAAI,CAACvG,WAAL,CAAiBgH,GAAjB,CAAqBL,WAArB,CAAL,EAAsC;AAClC,cAAA,KAAI,CAAC7F,UAAL;AACA;AAAA;AAAA,sCAAQ+F,WAAR,CAAoBI,UAApB,CAA+B,SAASN,WAAxC,EAAoDC,KAAD,IAAW;AAC1D,gBAAA,KAAI,CAAC5G,WAAL,CAAiBuD,GAAjB,CAAqBoD,WAArB,EAAgCC,KAAhC;;AACA,gBAAA,KAAI,CAACnF,eAAL;AACH,eAHD;AAIH;AACJ,WA7DS;;AAqDV,eAAK,IAAMkF,WAAX,IAAwBH,QAAxB;AAAA;AAAA,WArDU,CA+DV;;;AA/DU,oDAgE8B;AACpC,gBAAI,CAAC,KAAI,CAACxG,WAAL,CAAiBgH,GAAjB,CAAqBL,WAArB,CAAL,EAAsC;AAClC,cAAA,KAAI,CAAC7F,UAAL;AACA;AAAA;AAAA,sCAAQ+F,WAAR,CAAoBI,UAApB,CAA+BN,WAA/B,EAA2CC,KAAD,IAAW;AACjD,gBAAA,KAAI,CAAC5G,WAAL,CAAiBuD,GAAjB,CAAqBoD,WAArB,EAAgCC,KAAhC;;AACA,gBAAA,KAAI,CAACnF,eAAL;AACH,eAHD;AAIH;AACJ,WAxES;;AAgEV,eAAK,IAAMkF,WAAX,IAAwBF,cAAxB;AAAA;AAAA;AASH;;AAGD3E,QAAAA,aAAa,GAAG;AACZ;AACA,eAAKvC,cAAL,CAAoBsD,OAApB,CAA6BC,KAAD,IAAW;AACnC,iBAAKoE,YAAL,CAAkBpE,KAAlB;AACH,WAFD;AAIA,eAAKtD,YAAL,CAAkBqD,OAAlB,CAA2BC,KAAD,IAAW;AACjC,iBAAKqE,aAAL,CAAmBrE,KAAnB;AACH,WAFD;AAIA,eAAKrD,aAAL,CAAmBoD,OAAnB,CAA4BC,KAAD,IAAW;AAClC,iBAAKsE,aAAL,CAAmBtE,KAAnB;AACH,WAFD,EAVY,CAcZ;;AACA,eAAKxD,YAAL,CAAkBuD,OAAlB,CAA0B,CAACwE,MAAD,EAASvE,KAAT,KAAmB;AACzC,gBAAIA,KAAK,KAAK,GAAd,EAAmB;AACfuE,cAAAA,MAAM,CAACC,CAAP,GAAW,CAAC,IAAZ;AACH;AACJ,WAJD;AAMA,eAAKvH,QAAL,GAAgB,IAAhB;AACH;;AAEDwH,QAAAA,YAAY,GAAG;AACX;AACA;AAAA;AAAA,0CAAYC,EAAZ,CAAeC,SAAf;AACH;;AAEDnE,QAAAA,KAAK,GAAG;AACJ;AACA,eAAKpD,YAAL,CAAkBwH,MAAlB,CAAyB,CAAzB;AACA,eAAK3H,QAAL,GAAgB,KAAhB;AACA,eAAKD,WAAL,GAAmB,CAAnB;AACA,eAAKF,mBAAL,GAA2B,CAA3B;AACA,eAAKA,mBAAL,GAA2B,CAA3B;AACA,eAAKT,SAAL,GAAiB,IAAjB;AACA,eAAKQ,SAAL,CAAe+H,MAAf,CAAsB,CAAtB;;AAEA,cAAI,KAAKnI,cAAL,CAAoBkG,MAApB,GAA6B,CAAjC,EAAoC;AAChC,iBAAKkC,UAAL,CAAgB,KAAKpI,cAArB;AACH;;AAED,cAAI,KAAKC,YAAL,CAAkBiG,MAAlB,GAA2B,CAA/B,EAAkC;AAC9B,iBAAKkC,UAAL,CAAgB,KAAKnI,YAArB;AACH;;AAED,cAAI,KAAKC,aAAL,CAAmBgG,MAAnB,GAA4B,CAAhC,EAAmC;AAC/B,iBAAKkC,UAAL,CAAgB,KAAKlI,aAArB;AACH;;AAED,cAAI,KAAKH,YAAL,CAAkBsI,GAAlB,CAAsB,GAAtB,CAAJ,EAAgC;AAC5B,iBAAKtI,YAAL,CAAkBsI,GAAlB,CAAsB,GAAtB,EAA2BC,OAA3B;AACA,iBAAKvI,YAAL,CAAkByH,MAAlB,CAAyB,GAAzB;AACA,iBAAK3H,SAAL,CAAewI,GAAf,CAAmB,GAAnB,EAAwBE,KAAxB;AACA,iBAAK1I,SAAL,CAAe2H,MAAf,CAAsB,GAAtB;AACH;;AAED,eAAKxH,cAAL,GAAsB,EAAtB;AACA,eAAKC,YAAL,GAAoB,EAApB;AACA,eAAKC,aAAL,GAAqB,EAArB;AACH;;AAEDkI,QAAAA,UAAU,CAACI,MAAD,EAAmB;AACzB;AACAA,UAAAA,MAAM,CAAClF,OAAP,CAAgBC,KAAD,IAAW;AACtB,gBAAMkF,SAAS,GAAG,KAAK5I,SAAL,CAAewI,GAAf,CAAmB9E,KAAnB,CAAlB;AACA,gBAAMuE,MAAM,GAAG,KAAK/H,YAAL,CAAkBsI,GAAlB,CAAsB9E,KAAtB,CAAf;;AAEA,gBAAIuE,MAAJ,EAAY;AACRA,cAAAA,MAAM,CAACY,kBAAP;AACAZ,cAAAA,MAAM,CAACC,CAAP,GAAW,CAAX;AACH;;AAED,gBAAIU,SAAJ,EAAe;AACXA,cAAAA,SAAS,CAACF,KAAV;AACH;AACJ,WAZD;AAaH;;AAEDZ,QAAAA,YAAY,CAACpE,KAAD,EAAgB;AACxB;AACA,cAAIkF,SAAS,GAAG,KAAK5I,SAAL,CAAewI,GAAf,CAAmB9E,KAAnB,KAA6B;AAAA;AAAA,2CAA7C;AACAkF,UAAAA,SAAS,CAACE,OAAV,GAAoB,KAAKhH,UAAL,GAAkB,CAAtC;AACA8G,UAAAA,SAAS,CAACG,OAAV,GAAoB,CAAC,KAAKjH,UAAN,GAAmB,CAAvC;AAEA,cAAMkH,WAAqB,GAAG,EAA9B;AACA,eAAKjK,eAAL,CAAqB0E,OAArB,CAA8B6D,MAAD,IAAY;AACrC0B,YAAAA,WAAW,CAAChC,IAAZ,CAAiBM,MAAM,CAACb,KAAP,CAAa,GAAb,EAAkB,CAAlB,CAAjB;AACH,WAFD;AAIAmC,UAAAA,SAAS,CAACK,UAAV,CAAqBxF,OAArB,CAA6B,CAACyF,CAAD,EAAIC,GAAJ,KAAY;AACrC,gBAAI,CAAC;AAAA;AAAA,gCAAMpC,UAAN,CAAiBiC,WAAjB,EAA8BG,GAA9B,CAAL,EAAyC;AACrCP,cAAAA,SAAS,CAACK,UAAV,CAAqBtB,MAArB,CAA4BwB,GAA5B;AACH;AACJ,WAJD;;AAMA,eAAK,IAAI5C,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAG,KAAKxH,eAAL,CAAqBsH,MAAzC,EAAiDE,CAAC,EAAlD,EAAsD;AAClD,gBAAM6C,IAAI,GAAG,IAAIxL,IAAJ,EAAb;AACAwL,YAAAA,IAAI,CAACC,YAAL,CAAkBtL,WAAlB;AACA,gBAAM,CAACuL,UAAD,EAAa/B,SAAb,IAA0B,KAAKxI,eAAL,CAAqBwH,CAArB,EAAwBE,KAAxB,CAA8B,GAA9B,CAAhC;AACA,gBAAM8C,WAAW,GAAG;AAAA;AAAA,oCAAQ9B,WAAR,CAAoB+B,QAApB,CAA6BF,UAA7B,EAAyC,SAAS/B,SAAlD,CAApB;AAEA6B,YAAAA,IAAI,CAACC,YAAL,CAAkBxL,MAAlB,EAA0B0L,WAA1B,GAAwCA,WAAxC;AAEA,gBAAME,WAAW,GAAG,MAAM,EAA1B;AACAb,YAAAA,SAAS,CAACc,KAAV,GAAkBD,WAAW,GAAIL,IAAI,CAACO,YAAL,CAAkB5L,WAAlB,EAA+B6L,KAAhE;AACAR,YAAAA,IAAI,CAACO,YAAL,CAAkB5L,WAAlB,EAA+B6L,KAA/B,GAAuCH,WAAvC;AACAL,YAAAA,IAAI,CAACO,YAAL,CAAkB5L,WAAlB,EAA+BiE,MAA/B,IAAyC4G,SAAS,CAACc,KAAnD;;AAEA,gBAAInD,CAAC,KAAK,CAAV,EAAa;AACT6C,cAAAA,IAAI,CAAClB,CAAL,GAASjD,IAAI,CAACC,KAAL,CAAW0D,SAAS,CAACG,OAAV,GAAoBK,IAAI,CAACO,YAAL,CAAkB5L,WAAlB,EAA+BiE,MAA/B,GAAwC,CAAvE,CAAT;AACH,aAFD,MAEO;AACHoH,cAAAA,IAAI,CAAClB,CAAL,GAASU,SAAS,CAACiB,KAAV,GAAkB5E,IAAI,CAACC,KAAL,CAAWkE,IAAI,CAACO,YAAL,CAAkB5L,WAAlB,EAA+BiE,MAA/B,GAAwC,CAAxC,GAA4C4G,SAAS,CAACkB,KAAV,GAAkB,CAAzE,CAA3B;AACH;;AAEDlB,YAAAA,SAAS,CAACiB,KAAV,GAAkBT,IAAI,CAAClB,CAAvB;AACAU,YAAAA,SAAS,CAACkB,KAAV,GAAkBV,IAAI,CAACO,YAAL,CAAkB5L,WAAlB,EAA+BiE,MAAjD;;AAEA,gBAAI,KAAK7C,UAAL,CAAgBoH,CAAhB,MAAuB7C,KAA3B,EAAkC;AAC9BkF,cAAAA,SAAS,CAACmB,KAAV,GAAkB,KAAK9K,UAAL,CAAgBsH,CAAhB,CAAlB;AACAqC,cAAAA,SAAS,CAACoB,MAAV,GAAmB,KAAK/K,UAAxB;AACH;;AAED,gBAAImK,IAAI,CAAClB,CAAL,GAASkB,IAAI,CAACO,YAAL,CAAkB5L,WAAlB,EAA+BiE,MAA/B,GAAwC,CAAjD,GAAqD4G,SAAS,CAACE,OAAnE,EAA4E;AACxE;AACH;;AAED,gBAAI,KAAK3J,UAAL,CAAgBoH,CAAhB,MAAuB7C,KAA3B,EAAkC;AAC9B,kBAAI,KAAKxD,YAAL,CAAkBsI,GAAlB,CAAsB,KAAKrJ,UAAL,CAAgBoH,CAAhB,CAAtB,CAAJ,EAA+C;AAC3C6C,gBAAAA,IAAI,CAACnB,MAAL,GAAc,KAAK/H,YAAL,CAAkBsI,GAAlB,CAAsB,KAAKrJ,UAAL,CAAgBoH,CAAhB,CAAtB,CAAd;AACH,eAFD,MAEO;AACH,oBAAM0B,MAAM,GAAG,IAAIrK,IAAJ,EAAf;AACAqK,gBAAAA,MAAM,CAACoB,YAAP,CAAoBtL,WAApB;AACAkK,gBAAAA,MAAM,CAACA,MAAP,GAAgB,KAAKmB,IAArB;AACA,qBAAKlJ,YAAL,CAAkBiE,GAAlB,CAAsB,KAAKhF,UAAL,CAAgBoH,CAAhB,CAAtB,EAA0C0B,MAA1C;AACAmB,gBAAAA,IAAI,CAACnB,MAAL,GAAcA,MAAd;AACH;AACJ;;AAEDW,YAAAA,SAAS,CAACqB,UAAV,CAAqBjD,IAArB,CAA0BoC,IAA1B;AACAR,YAAAA,SAAS,CAACK,UAAV,CAAqB9E,GAArB,CAAyBmF,UAAzB,EAAqCC,WAArC;AACAX,YAAAA,SAAS,CAAChE,KAAV,GAAkB2B,CAAlB;AACH;;AAED,eAAKvG,SAAL,CAAemE,GAAf,CAAmBT,KAAnB,EAA0BkF,SAA1B;AACH;;AAEDb,QAAAA,aAAa,CAACrE,KAAD,EAAgB;AACzB;AACA,cAAIkF,SAAsB,GAAG,KAAK5I,SAAL,CAAewI,GAAf,CAAmB9E,KAAnB,KAA6B;AAAA;AAAA,2CAA1D;AACAkF,UAAAA,SAAS,CAACE,OAAV,GAAoB,KAAKhH,UAAL,GAAkB,CAAtC;AACA8G,UAAAA,SAAS,CAACG,OAAV,GAAoB,CAAC,KAAKjH,UAAN,GAAmB,CAAvC,CAJyB,CAMzB;;AACA,cAAMoI,YAAY,GAAG,KAAK9K,QAAL,CAChB+K,GADgB,CACZ,CAAC/K,QAAD,EAAWwF,KAAX,MAAsB;AAAExF,YAAAA,QAAF;AAAYwF,YAAAA;AAAZ,WAAtB,CADY,EAEhBwF,MAFgB,CAET;AAAA,gBAAC;AAAEhL,cAAAA;AAAF,aAAD;AAAA,mBAAkBA,QAAQ,KAAKsE,KAA/B;AAAA,WAFS,CAArB,CAPyB,CAWzB;;AACAwG,UAAAA,YAAY,CAACzG,OAAb,CAAqB,SAAe;AAAA,gBAAd;AAAEmB,cAAAA;AAAF,aAAc;AAChCgE,YAAAA,SAAS,CAACU,UAAV,CAAqBtC,IAArB,CAA0B,KAAKhI,aAAL,CAAmB4F,KAAnB,CAA1B;AACAgE,YAAAA,SAAS,CAACyB,OAAV,CAAkBrD,IAAlB,CAAuB,KAAK3H,WAAL,CAAiBuF,KAAjB,CAAvB;AACAgE,YAAAA,SAAS,CAACoB,MAAV,CAAiBhD,IAAjB,CAAsB,KAAK1H,QAAL,CAAcsF,KAAd,CAAtB;AACAgE,YAAAA,SAAS,CAAC0B,SAAV,CAAoBtD,IAApB,CAAyB,KAAKvH,YAAL,CAAkBmF,KAAlB,KAA4B,CAArD;AACAgE,YAAAA,SAAS,CAAC2B,QAAV,CAAmBvD,IAAnB,CAAwB,KAAKxH,WAAL,CAAiBoF,KAAjB,KAA2B,KAAnD;AACH,WAND,EAZyB,CAoBzB;;AACA,cAAMoE,WAAW,GAAGJ,SAAS,CAACU,UAAV,CAAqBa,GAArB,CAAyB7C,MAAM,IAAIA,MAAM,CAACb,KAAP,CAAa,GAAb,EAAkB,CAAlB,CAAnC,CAApB;AACAmC,UAAAA,SAAS,CAACK,UAAV,CAAqBxF,OAArB,CAA6B,CAACyF,CAAD,EAAIC,GAAJ,KAAY;AACrC,gBAAI,CAAC;AAAA;AAAA,gCAAMpC,UAAN,CAAiBiC,WAAjB,EAA8BG,GAA9B,CAAL,EAAyC;AACrCP,cAAAA,SAAS,CAACK,UAAV,CAAqBtB,MAArB,CAA4BwB,GAA5B;AACH;AACJ,WAJD,EAtByB,CA4BzB;;AACAe,UAAAA,YAAY,CAACzG,OAAb,CAAqB,SAAe;AAAA,gBAAd;AAAEmB,cAAAA;AAAF,aAAc;AAChC,gBAAMwE,IAAI,GAAG,IAAIxL,IAAJ,EAAb;AACAwL,YAAAA,IAAI,CAACC,YAAL,CAAkBtL,WAAlB;AAEA,gBAAM,CAACuL,UAAD,EAAa/B,SAAb,IAA0B,KAAKvI,aAAL,CAAmB4F,KAAnB,EAA0B6B,KAA1B,CAAgC,GAAhC,CAAhC;AACA,gBAAM8C,WAAW,GAAG;AAAA;AAAA,oCAAQ9B,WAAR,CAAoB+B,QAApB,CAA6BF,UAA7B,WAAgD/B,SAAhD,CAApB;AAEA6B,YAAAA,IAAI,CAACC,YAAL,CAAkBxL,MAAlB,EAA0B0L,WAA1B,GAAwCA,WAAxC;AAEA,gBAAM,CAACiB,CAAD,EAAItC,CAAJ,EAAOuC,MAAP,EAAeC,MAAf,IAAyB,KAAKrL,WAAL,CAAiBuF,KAAjB,EAAwB6B,KAAxB,CAA8B,GAA9B,EAAmC0D,GAAnC,CAAuCxD,MAAvC,CAA/B;AACAyC,YAAAA,IAAI,CAACuB,WAAL,CAAiBH,CAAjB,EAAoBtC,CAApB;AACAkB,YAAAA,IAAI,CAACO,YAAL,CAAkB5L,WAAlB,EAA+B6L,KAA/B,GAAuCL,WAAW,CAACK,KAAZ,GAAoBa,MAA3D;AACArB,YAAAA,IAAI,CAACO,YAAL,CAAkB5L,WAAlB,EAA+BiE,MAA/B,GAAwCuH,WAAW,CAACvH,MAAZ,GAAqB0I,MAA7D;AACAtB,YAAAA,IAAI,CAACwB,KAAL,GAAa,KAAKnL,YAAL,CAAkBmF,KAAlB,CAAb,CAbgC,CAehC;;AACAgE,YAAAA,SAAS,CAACmB,KAAV,GAAkB,KAAKzK,QAAL,CAAcsF,KAAd,CAAlB,CAhBgC,CAkBhC;;AACA,gBAAI,CAAC,KAAK1E,YAAL,CAAkB0H,GAAlB,CAAsBlE,KAAtB,CAAL,EAAmC;AAC/B,kBAAMuE,MAAM,GAAG,IAAIrK,IAAJ,EAAf;AACAqK,cAAAA,MAAM,CAACoB,YAAP,CAAoBtL,WAApB;AACAkK,cAAAA,MAAM,CAACA,MAAP,GAAgB,KAAKmB,IAArB;AACA,mBAAKlJ,YAAL,CAAkBiE,GAAlB,CAAsBT,KAAtB,EAA6BuE,MAA7B;AACH,aAxB+B,CA0BhC;;;AACA,gBAAImB,IAAI,CAACyB,QAAL,CAAc3C,CAAd,GAAkBkB,IAAI,CAACO,YAAL,CAAkB5L,WAAlB,EAA+BiE,MAA/B,GAAwC,CAA1D,GAA8D4G,SAAS,CAACE,OAA5E,EAAqF;AACjF;AACH;;AAEDM,YAAAA,IAAI,CAACnB,MAAL,GAAc,KAAK/H,YAAL,CAAkBsI,GAAlB,CAAsB9E,KAAtB,CAAd;AACAkF,YAAAA,SAAS,CAACqB,UAAV,CAAqBjD,IAArB,CAA0BoC,IAA1B;AACAR,YAAAA,SAAS,CAACK,UAAV,CAAqB9E,GAArB,CAAyBmF,UAAzB,EAAqCC,WAArC;AACAX,YAAAA,SAAS,CAAChE,KAAV,GAAkBA,KAAlB;AACH,WAnCD;AAqCA,eAAK5E,SAAL,CAAemE,GAAf,CAAmBT,KAAnB,EAA0BkF,SAA1B;AACH;;AAEDZ,QAAAA,aAAa,CAACtE,KAAD,EAAgB;AACzB;AACA,cAAMkF,SAAS,GAAG,KAAK5I,SAAL,CAAewI,GAAf,CAAmB9E,KAAnB,KAA6B;AAAA;AAAA,2CAA/C;AACAkF,UAAAA,SAAS,CAACE,OAAV,GAAoB,KAAKhH,UAAL,GAAkB,CAAtC;AACA8G,UAAAA,SAAS,CAACG,OAAV,GAAoB,CAAC,KAAKjH,UAAN,GAAmB,CAAvC;;AAEA,eAAK,IAAIyE,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAG,KAAK1G,cAAL,CAAoBwG,MAAxC,EAAgDE,CAAC,EAAjD,EAAqD;AACjD,gBAAII,MAAM,CAAC,KAAK9G,cAAL,CAAoB0G,CAApB,CAAD,CAAN,KAAmC7C,KAAvC,EAA8C;AAC1CkF,cAAAA,SAAS,CAACU,UAAV,CAAqBtC,IAArB,CAA0B,KAAKrH,mBAAL,CAAyB4G,CAAzB,CAA1B;AACAqC,cAAAA,SAAS,CAACyB,OAAV,CAAkBrD,IAAlB,CAAuB,KAAKlH,YAAL,CAAkByG,CAAlB,CAAvB;AACAqC,cAAAA,SAAS,CAACoB,MAAV,CAAiBhD,IAAjB,CAAsBL,MAAM,CAAC,KAAK/G,cAAL,CAAoB2G,CAApB,CAAD,CAA5B;AACH;AACJ;;AAED,cAAMyC,WAAqB,GAAG,EAA9B;AACAJ,UAAAA,SAAS,CAACU,UAAV,CAAqB7F,OAArB,CAA8B6D,MAAD,IAAY;AACrC0B,YAAAA,WAAW,CAAChC,IAAZ,CAAiBM,MAAM,CAACb,KAAP,CAAa,GAAb,EAAkB,CAAlB,CAAjB;AACH,WAFD;AAIAmC,UAAAA,SAAS,CAACkC,aAAV,CAAwBrH,OAAxB,CAAgC,CAACyF,CAAD,EAAIC,GAAJ,KAAY;AACxC,gBAAI,CAAC;AAAA;AAAA,gCAAMpC,UAAN,CAAiBiC,WAAjB,EAA8BG,GAA9B,CAAL,EAAyC;AACrCP,cAAAA,SAAS,CAACkC,aAAV,CAAwBnD,MAAxB,CAA+BwB,GAA/B;AACH;AACJ,WAJD;;AAMA,eAAK,IAAI5C,GAAC,GAAG,CAAb,EAAgBA,GAAC,GAAG,KAAK5G,mBAAL,CAAyB0G,MAA7C,EAAqDE,GAAC,EAAtD,EAA0D;AACtD,gBAAII,MAAM,CAAC,KAAK9G,cAAL,CAAoB0G,GAApB,CAAD,CAAN,KAAmC7C,KAAvC,EAA8C;AAC1C,kBAAM0F,IAAI,GAAG,IAAIxL,IAAJ,EAAb;AACAwL,cAAAA,IAAI,CAACC,YAAL,CAAkBtL,WAAlB;;AACA,kBAAM,CAACuL,UAAD,EAAa/B,SAAb,EAAwBwD,UAAxB,IAAsC,KAAKpL,mBAAL,CAAyB4G,GAAzB,EAA4BE,KAA5B,CAAkC,GAAlC,CAA5C;;AACA,kBAAMuE,MAAqB,GAAG,EAA9B;;AAEA,mBAAK,IAAIpE,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAGD,MAAM,CAACoE,UAAD,CAA1B,EAAwCnE,CAAC,EAAzC,EAA6C;AACzC,oBAAMqE,SAAS,GAAM3B,UAAN,SAAoB1C,CAAnC;AACA,oBAAMsE,KAAK,GAAG;AAAA;AAAA,wCAAQzD,WAAR,CAAoB+B,QAApB,CAA6ByB,SAA7B,EAAwC,SAAS1D,SAAjD,CAAd;AACAyD,gBAAAA,MAAM,CAAChE,IAAP,CAAYkE,KAAZ;AACH;;AAED,kBAAMC,aAAa,GAAG/B,IAAI,CAACC,YAAL;AAAA;AAAA,iDAAtB;AACA8B,cAAAA,aAAa,CAACC,OAAd,CAAsBJ,MAAtB,EAA8B,KAAKpC,SAAS,CAACyC,SAA7C;AACAF,cAAAA,aAAa,CAACG,QAAd;;AAEA,kBAAM,CAACd,CAAD,EAAItC,CAAJ,IAAS,KAAKpI,YAAL,CAAkByG,GAAlB,EAAqBE,KAArB,CAA2B,GAA3B,EAAgC0D,GAAhC,CAAoCxD,MAApC,CAAf;;AACAyC,cAAAA,IAAI,CAACuB,WAAL,CAAiBH,CAAjB,EAAoBtC,CAApB;;AAEA,kBAAI,CAAC,KAAKhI,YAAL,CAAkBsI,GAAlB,CAAsB9E,KAAtB,CAAL,EAAmC;AAC/B,oBAAMuE,MAAM,GAAG,IAAIrK,IAAJ,EAAf;AACAqK,gBAAAA,MAAM,CAACoB,YAAP,CAAoBtL,WAApB;AACAkK,gBAAAA,MAAM,CAACA,MAAP,GAAgB,KAAKmB,IAArB;AACA,qBAAKlJ,YAAL,CAAkBiE,GAAlB,CAAsBT,KAAtB,EAA6BuE,MAA7B;AACH;;AAEDmB,cAAAA,IAAI,CAACnB,MAAL,GAAc,KAAK/H,YAAL,CAAkBsI,GAAlB,CAAsB9E,KAAtB,CAAd;AACAkF,cAAAA,SAAS,CAACqB,UAAV,CAAqBjD,IAArB,CAA0BoC,IAA1B;AACAR,cAAAA,SAAS,CAACkC,aAAV,CAAwB3G,GAAxB,CAA4BmF,UAA5B,EAAwC0B,MAAxC;AACApC,cAAAA,SAAS,CAAChE,KAAV,GAAkB2B,GAAlB;AACH;AACJ;;AAED,eAAKvG,SAAL,CAAemE,GAAf,CAAmBT,KAAnB,EAA0BkF,SAA1B;AACH;;AAED2C,QAAAA,gBAAgB,GAAG,CACf;AACA;AAEA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AAEA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AAEA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACH,SArsB6C,CAusB9C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AAIA;AACA;AACA;AACA;AAEA;AACA;AACA;AAEA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AAEA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AAEA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AAEA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AAEA;AACA;AAEA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AAEA;AACA;;AAEA;AACJ;AACA;AACA;AACA;;;AACI3H,QAAAA,YAAY,CAACd,SAAD,EAAoBY,KAApB,EAAyC;AACjD,cAAMkF,SAAS,GAAG,KAAK5I,SAAL,CAAewI,GAAf,CAAmB9E,KAAnB,CAAlB;AACA,cAAM8H,WAAW,GAAG,KAAKtL,YAAL,CAAkBsI,GAAlB,CAAsB9E,KAAtB,CAApB;;AAEA,cAAI,CAACkF,SAAD,IAAc,CAAC4C,WAAnB,EAAgC;AAC5B;AACH;;AAED,cAAMzB,KAAK,GAAGnB,SAAS,CAACoB,MAAV,CAAiBpB,SAAS,CAAChE,KAAV,GAAkBgE,SAAS,CAACoB,MAAV,CAAiB3D,MAApD,CAAd;AACA,cAAMoF,aAAa,GAAG1B,KAAK,GAAG,KAAKtJ,mBAAnC,CATiD,CAWjD;;AACAmI,UAAAA,SAAS,CAACE,OAAV,IAAqBhG,SAAS,GAAG2I,aAAjC;AACA7C,UAAAA,SAAS,CAACG,OAAV,GAAoBH,SAAS,CAACE,OAAV,GAAoB7K,IAAI,CAAC8D,cAAL,GAAsBC,MAA9D,CAbiD,CAejD;;AACA,cAAI0J,KAAK,GAAGF,WAAW,CAACX,QAAZ,CAAqB3C,CAArB,GAAyBpF,SAAS,GAAG2I,aAAjD;AACAD,UAAAA,WAAW,CAACb,WAAZ,CAAwBa,WAAW,CAACX,QAAZ,CAAqBL,CAA7C,EAAgDkB,KAAhD,EAjBiD,CAmBjD;;AACA,eAAKzJ,UAAL,GAAkB,KAAKxB,mBAAL,GAA2B,CAA7C;AAEA,cAAMkL,WAAW,GAAG/C,SAAS,CAACyB,OAAV,CAAkB,CAACzB,SAAS,CAAChE,KAAV,GAAkB,CAAnB,IAAwBgE,SAAS,CAACyB,OAAV,CAAkBhE,MAA5D,EAAoEI,KAApE,CAA0E,GAA1E,CAApB;AACA,cAAMmF,IAAI,GAAGjF,MAAM,CAACgF,WAAW,CAAC,CAAD,CAAZ,CAAnB;AACA,cAAME,IAAI,GAAGlF,MAAM,CAACgF,WAAW,CAAC,CAAD,CAAZ,CAAnB;AACA,cAAMlB,MAAM,GAAG9D,MAAM,CAACgF,WAAW,CAAC,CAAD,CAAZ,CAArB;AACA,cAAMjB,MAAM,GAAGzF,IAAI,CAAC6G,GAAL,CAASnF,MAAM,CAACgF,WAAW,CAAC,CAAD,CAAZ,CAAf,CAAf;AAEA,cAAMI,cAAc,GAAGnD,SAAS,CAACU,UAAV,CAAqB,CAACV,SAAS,CAAChE,KAAV,GAAkB,CAAnB,IAAwBgE,SAAS,CAACU,UAAV,CAAqBjD,MAAlE,EAA0EI,KAA1E,CAAgF,GAAhF,CAAvB;AACA,cAAM6C,UAAU,GAAGyC,cAAc,CAAC,CAAD,CAAjC;AACA,cAAMxE,SAAS,GAAGwE,cAAc,CAAC,CAAD,CAAhC;AAEA,cAAIxC,WAAW,GAAGX,SAAS,CAACK,UAAV,CAAqBT,GAArB,CAAyBc,UAAzB,CAAlB;;AACA,cAAI,CAACC,WAAL,EAAkB;AACdA,YAAAA,WAAW,GAAG;AAAA;AAAA,oCAAQ9B,WAAR,CAAoB+B,QAApB,CAA6BF,UAA7B,EAAyC,SAAS/B,SAAlD,CAAd;AACAqB,YAAAA,SAAS,CAACK,UAAV,CAAqB9E,GAArB,CAAyBmF,UAAzB,EAAqCC,WAArC;AACH,WApCgD,CAsCjD;;;AACA,cAAI,CAACX,SAAS,CAAChE,KAAV,GAAkB,CAAnB,IAAwBgE,SAAS,CAACU,UAAV,CAAqBjD,MAA7C,KAAwD,CAAxD,IAA6DuC,SAAS,CAAChE,KAAV,KAAoB,CAAjF,IAAsFgE,SAAS,CAACqB,UAAV,CAAqB5D,MAArB,GAA8B,CAAxH,EAA2H;AACvH,gBAAM2F,QAAQ,GAAGpD,SAAS,CAACqB,UAAV,CAAqBrB,SAAS,CAACqB,UAAV,CAAqB5D,MAArB,GAA8B,CAAnD,CAAjB;AACAuC,YAAAA,SAAS,CAACqD,OAAV,GAAoBD,QAAQ,CAAC9D,CAAT,GAAa8D,QAAQ,CAAChK,MAAT,GAAkB,CAA/B,GAAmCuH,WAAW,CAAC2C,OAAZ,GAAsBlK,MAAtB,GAA+B0I,MAA/B,GAAwC,CAA/F;AACH,WA1CgD,CA4CjD;;;AACA,cAAI,KAAKhL,OAAL,CAAayM,OAAb,CAAqB7C,UAArB,KAAoC,CAAC,CAAzC,EAA4C;AACxC,gBAAI,KAAKrH,UAAT,EAAqB;AACjB2G,cAAAA,SAAS,CAAChE,KAAV;AACA;AACH;;AACD,gBAAIiH,IAAI,GAAGjD,SAAS,CAACqD,OAAjB,GAA2B1C,WAAW,CAAC2C,OAAZ,GAAsBlK,MAAtB,GAA+B,CAA1D,GAA8D,IAA9D,GAAqE4G,SAAS,CAACE,OAAnF,EAA4F;AACxFF,cAAAA,SAAS,CAAChE,KAAV;AACA;AACH;AACJ,WAtDgD,CAwDjD;;;AACA,cAAIgE,SAAS,CAAChE,KAAV,GAAkB,CAAlB,GAAsB,KAAKtF,QAAL,CAAc+G,MAApC,IAA8CuC,SAAS,CAAChE,KAAV,GAAkB,CAAlB,IAAuB,CAAzE,EAA4E;AACxE,gBAAMwH,SAAS,GAAG,KAAK9M,QAAL,CAAc,CAACsJ,SAAS,CAAChE,KAAV,GAAkB,CAAnB,IAAwB,KAAKtF,QAAL,CAAc+G,MAApD,IAA8DuC,SAAS,CAACmB,KAA1F;AACAnB,YAAAA,SAAS,CAACmB,KAAV,IAAmBqC,SAAS,GAAGtJ,SAA/B;AACH,WAHD,MAGO;AACH,gBAAMsJ,UAAS,GAAG,KAAK9M,QAAL,CAAc,KAAKA,QAAL,CAAc+G,MAAd,GAAuB,CAArC,IAA0CuC,SAAS,CAACmB,KAAtE;;AACAnB,YAAAA,SAAS,CAACmB,KAAV,IAAmBqC,UAAS,GAAGtJ,SAA/B;AACH,WA/DgD,CAiEjD;;;AACA,cAAI8F,SAAS,CAACqB,UAAV,CAAqB5D,MAArB,GAA8B,CAA9B,IAAmCuC,SAAS,CAACqB,UAAV,CAAqB,CAArB,EAAwB/B,CAAxB,GAA4BU,SAAS,CAACqB,UAAV,CAAqB,CAArB,EAAwBjI,MAApD,GAA6D,IAA7D,GAAoE4G,SAAS,CAACG,OAArH,EAA8H;AAC1H,gBAAMsD,WAAW,GAAGzD,SAAS,CAACqB,UAAV,CAAqBqC,KAArB,EAApB;AACAD,YAAAA,WAAW,CAACE,MAAZ,GAAqB,KAArB;AACA3D,YAAAA,SAAS,CAAC4D,QAAV,CAAmBxF,IAAnB,CAAwBqF,WAAxB;AACH,WAtEgD,CAwEjD;;;AACA,cAAIR,IAAI,GAAGjD,SAAS,CAACqD,OAAjB,GAA2B1C,WAAW,CAAC2C,OAAZ,GAAsBlK,MAAtB,GAA+B,CAA/B,GAAmC0I,MAA9D,GAAuE9B,SAAS,CAACE,OAAV,GAAoB,IAApB,GAA2B,GAAtG,EAA2G;AACvG,gBAAI2D,OAAJ;;AACA,gBAAI7D,SAAS,CAAC4D,QAAV,CAAmBnG,MAAnB,GAA4B,CAAhC,EAAmC;AAC/BoG,cAAAA,OAAO,GAAG7D,SAAS,CAAC4D,QAAV,CAAmBF,KAAnB,EAAV;AACH,aAFD,MAEO;AACHG,cAAAA,OAAO,GAAG,IAAI7O,IAAJ,EAAV;AACA6O,cAAAA,OAAO,CAACpD,YAAR,CAAqBxL,MAArB;AACH;;AAED,iBAAK6O,eAAL,CAAqBD,OAArB,EAA8B7D,SAAS,CAAC2B,QAAV,CAAmB,CAAC3B,SAAS,CAAChE,KAAV,GAAkB,CAAnB,IAAwBgE,SAAS,CAAC2B,QAAV,CAAmBlE,MAA9D,CAA9B,EAAqG3C,KAArG;AAEA,gBAAM4D,MAAM,GAAGmF,OAAO,CAAC9C,YAAR,CAAqB9L,MAArB,CAAf;AACAyJ,YAAAA,MAAM,CAACqF,QAAP,GAAkB9O,MAAM,CAAC+O,QAAP,CAAgBC,GAAlC;AACAvF,YAAAA,MAAM,CAACiC,WAAP,GAAqBA,WAArB;;AAEA,gBAAIjC,MAAM,CAACiC,WAAX,EAAwB;AACpB;AACA;AACAkD,cAAAA,OAAO,CAAC9B,WAAR,CAAoBiB,IAApB,EAAyBC,IAAI,GAAGjD,SAAS,CAACqD,OAA1C;AACAQ,cAAAA,OAAO,CAAC7B,KAAR,GAAgBhC,SAAS,CAAC0B,SAAV,CAAoB,CAAC1B,SAAS,CAAChE,KAAV,GAAkB,CAAnB,IAAwBgE,SAAS,CAAC0B,SAAV,CAAoBjE,MAAhE,CAAhB;AACH;;AAEDoG,YAAAA,OAAO,CAACxE,MAAR,GAAiB,KAAK/H,YAAL,CAAkBsI,GAAlB,CAAsB9E,KAAtB,CAAjB;AACA+I,YAAAA,OAAO,CAACF,MAAR,GAAiB,IAAjB;AACA3D,YAAAA,SAAS,CAACqB,UAAV,CAAqBjD,IAArB,CAA0ByF,OAA1B;AACA7D,YAAAA,SAAS,CAAChE,KAAV;AACH,WAnGgD,CAqGjD;;;AACA,eAAKkI,UAAL,CAAgBpJ,KAAhB,EAAuBZ,SAAvB;AACH;AAED;AACJ;AACA;AACA;AACA;AACA;;;AACI4J,QAAAA,eAAe,CAACtD,IAAD,EAAa2D,QAAb,EAA+BrJ,KAA/B,EAAoD;AAC/D,cAAM,CAACsJ,MAAD,EAASC,MAAT,IAAmBF,QAAQ,CAACtG,KAAT,CAAe,GAAf,EAAoB0D,GAApB,CAAwBxD,MAAxB,CAAzB;AACA,cAAMuG,YAAY,GAAG9D,IAAI,CAACO,YAAL;AAAA;AAAA,uCAA+BP,IAAI,CAACC,YAAL;AAAA;AAAA,mCAApD;AACA6D,UAAAA,YAAY,CAAC9B,OAAb,CAAqB4B,MAArB,EAA6BC,MAA7B,EAAqCvJ,KAArC;AACH;;AAEDC,QAAAA,YAAY,CAACb,SAAD,EAAoBY,KAApB,EAAmC;AAC3C,cAAMkF,SAAS,GAAG,KAAK5I,SAAL,CAAewI,GAAf,CAAmB9E,KAAnB,CAAlB;AACA,cAAMyJ,UAAU,GAAG,KAAKjN,YAAL,CAAkBsI,GAAlB,CAAsB9E,KAAtB,CAAnB;AACA,cAAMqG,KAAK,GAAGnB,SAAS,CAACoB,MAAV,CAAiBpB,SAAS,CAAChE,KAAV,GAAkBgE,SAAS,CAACoB,MAAV,CAAiB3D,MAApD,CAAd;AACAuC,UAAAA,SAAS,CAACmB,KAAV,GAAkBA,KAAlB;AAEA,cAAM0B,aAAa,GAAG1B,KAAK,GAAG,KAAKvJ,mBAAnC;AACAoI,UAAAA,SAAS,CAACE,OAAV,IAAqBhG,SAAS,GAAG2I,aAAjC;AACA7C,UAAAA,SAAS,CAACG,OAAV,GAAoBH,SAAS,CAACE,OAAV,GAAoB7K,IAAI,CAAC8D,cAAL,GAAsBC,MAA9D;AACAmL,UAAAA,UAAU,CAACjF,CAAX,IAAgBpF,SAAS,GAAG2I,aAA5B;;AAEA,cAAI7C,SAAS,CAACqB,UAAV,CAAqB5D,MAArB,GAA8B,CAA9B,IAAmCuC,SAAS,CAACqB,UAAV,CAAqB,CAArB,EAAwB/B,CAAxB,GAA4BU,SAAS,CAACqB,UAAV,CAAqB,CAArB,EAAwBjI,MAAxB,GAAiC,CAA7D,GAAiE4G,SAAS,CAACG,OAAlH,EAA2H;AACvH,gBAAMK,IAAI,GAAGR,SAAS,CAACqB,UAAV,CAAqBqC,KAArB,EAAb;AACAlD,YAAAA,IAAI,CAACmD,MAAL,GAAc,KAAd;AACA3D,YAAAA,SAAS,CAAC4D,QAAV,CAAmBxF,IAAnB,CAAwBoC,IAAxB;AACH;;AAED,cAAMgE,cAAc,GAAG,KAAKrO,eAAL,CAAqB,CAAC6J,SAAS,CAAChE,KAAV,GAAkB,CAAnB,IAAwB,KAAK7F,eAAL,CAAqBsH,MAAlE,EAA0EI,KAA1E,CAAgF,GAAhF,CAAvB;AACA,cAAM6C,UAAU,GAAG8D,cAAc,CAAC,CAAD,CAAjC;AACA,cAAM7F,SAAS,GAAG6F,cAAc,CAAC,CAAD,CAAhC;AACA,cAAI7D,WAAW,GAAGX,SAAS,CAACK,UAAV,CAAqBT,GAArB,CAAyBc,UAAzB,CAAlB;;AAEA,cAAI,CAACC,WAAL,EAAkB;AACdA,YAAAA,WAAW,GAAG;AAAA;AAAA,oCAAQ9B,WAAR,CAAoB+B,QAApB,CAA6BF,UAA7B,EAAyC,SAAS/B,SAAlD,CAAd;AACAqB,YAAAA,SAAS,CAACK,UAAV,CAAqB9E,GAArB,CAAyBmF,UAAzB,EAAqCC,WAArC;AACH;;AAED,cAAMyC,QAAa,GAAGpD,SAAS,CAACqB,UAAV,CAAqBrB,SAAS,CAACqB,UAAV,CAAqB5D,MAArB,GAA8B,CAAnD,CAAtB;AACA,cAAMoG,OAAY,GAAG7D,SAAS,CAAC4D,QAAV,CAAmBnG,MAAnB,GAA4B,CAA5B,GAAgCuC,SAAS,CAAC4D,QAAV,CAAmBF,KAAnB,EAAhC,GAA6D,IAAI1O,IAAJ,EAAlF;AACA,cAAM0J,MAAM,GAAGmF,OAAO,CAAC9C,YAAR,CAAqB9L,MAArB,KAAgC4O,OAAO,CAACpD,YAAR,CAAqBxL,MAArB,CAA/C;AAEAyJ,UAAAA,MAAM,CAACiC,WAAP,GAAqBA,WAArB;AACAjC,UAAAA,MAAM,CAACqF,QAAP,GAAkB9O,MAAM,CAAC+O,QAAP,CAAgBC,GAAlC,CAhC2C,CAiC3C;AACA;;AACA,cAAIhB,IAAI,GAAGG,QAAQ,CAACnB,QAAT,CAAkB3C,CAAlB,GAAsB8D,QAAQ,CAACrC,YAAT,CAAsB5L,WAAtB,EAAmCiE,MAAnC,GAA4C,CAAlE,GAAsEyK,OAAO,CAAC9C,YAAR,CAAqB5L,WAArB,EAAkCiE,MAAlC,GAA2C,CAA5H;AACAyK,UAAAA,OAAO,CAAC9B,WAAR,CAAoB8B,OAAO,CAAC5B,QAAR,CAAiBL,CAArC,EAAwCqB,IAAxC;AACAY,UAAAA,OAAO,CAACxE,MAAR,GAAiBkF,UAAjB;AACAV,UAAAA,OAAO,CAACF,MAAR,GAAiB,IAAjB;AAEA3D,UAAAA,SAAS,CAACqB,UAAV,CAAqBjD,IAArB,CAA0ByF,OAA1B;AACA7D,UAAAA,SAAS,CAAChE,KAAV;AACA,eAAKkI,UAAL,CAAgBpJ,KAAhB,EAAuBZ,SAAvB;AACH;AAED;AACJ;AACA;AACA;AACA;;;AACIgK,QAAAA,UAAU,CAACpJ,KAAD,EAAgBZ,SAAhB,EAAyC;AAC/C,cAAM8F,SAAS,GAAG,KAAK5I,SAAL,CAAewI,GAAf,CAAmB9E,KAAnB,CAAlB;AACA,cAAM8H,WAAW,GAAG,KAAKtL,YAAL,CAAkBsI,GAAlB,CAAsB9E,KAAtB,CAApB;;AAEA,cACIkF,SAAS,CAACyE,SAAV,GAAsB,KAAK9M,SAAL,CAAe8F,MAArC,IACA,KAAK9F,SAAL,CAAeqI,SAAS,CAACyE,SAAzB,EAAoCC,SAApC,KAAkD5J,KADlD,IAEAkF,SAAS,CAACG,OAAV,GAAoB,KAAKxI,SAAL,CAAeqI,SAAS,CAACyE,SAAzB,EAAoCxG,SAH5D,EAIE;AACE,gBAAML,QAAQ,GAAG,KAAKjG,SAAL,CAAeqI,SAAS,CAACyE,SAAzB,CAAjB;AACA,gBAAIE,QAAJ;AACA,gBAAM3B,IAAI,GAAGpF,QAAQ,CAACgH,QAAtB;AACA,gBAAM3B,IAAI,GAAGrF,QAAQ,CAACiH,QAAtB;AACA,gBAAMhD,MAAM,GAAGjE,QAAQ,CAACkH,UAAxB;AACA,gBAAMhD,MAAM,GAAGlE,QAAQ,CAACmH,UAAxB;;AAEA,oBAAQnH,QAAQ,CAACoH,KAAjB;AACI,mBAAK,CAAL;AAAQ;AACJ;AAAA;AAAA,oCAAMC,GAAN,kDAAsBnK,KAAtB;AACA;;AAEJ,mBAAK,CAAL;AAAQ;AACJ6J,gBAAAA,QAAQ,GAAG;AAAA;AAAA,wCAAQO,YAAR,CAAqBC,WAArB,CACPvH,QAAQ,CAACpB,EADF,EAEPoB,QAAQ,CAACwH,KAFF,EAGPhQ,EAAE,CAAC4N,IAAD,EAAOC,IAAP,CAHK,EAIPrF,QAAQ,CAAC8G,SAJF,CAAX;AAMA;;AAEJ,mBAAK,CAAL;AAAQ;AACJ;AACA;AAhBR;;AAmBA,gBAAIC,QAAJ,EAAc;AACVA,cAAAA,QAAQ,CAACtF,MAAT,GAAkBuD,WAAlB;AACA+B,cAAAA,QAAQ,CAACU,eAAT,CAAyBzH,QAAQ,CAAC0H,MAAlC;AACAX,cAAAA,QAAQ,CAAC5C,WAAT,CAAqBiB,IAArB,EAA2BC,IAAI,GAAG,KAAKnL,WAAL,GAAmBoC,SAAnB,GAA+B0D,QAAQ,CAAC2H,GAA1E;AACAZ,cAAAA,QAAQ,CAACa,QAAT,CAAkB3D,MAAM,GAAG,KAAKzK,SAAL,CAAewI,GAAf,CAAmB,CAAnB,EAAsBkB,KAAjD,EAAwDgB,MAAM,GAAG,KAAK1K,SAAL,CAAewI,GAAf,CAAmB,CAAnB,EAAsBkB,KAAvF;AACA6D,cAAAA,QAAQ,CAAC3C,KAAT,GAAiB,CAAjB;AAEA,kBAAMsC,YAAY,GAAGK,QAAQ,CAAC5D,YAAT;AAAA;AAAA,2CAAmC4D,QAAQ,CAAClE,YAAT;AAAA;AAAA,uCAAxD;AACA6D,cAAAA,YAAY,CAAC9B,OAAb,CAAqB,CAArB,EAAwB5E,QAAQ,CAAC2H,GAAjC,EAAsCzK,KAAtC;AACH;;AAEDkF,YAAAA,SAAS,CAACyE,SAAV;AACH;AACJ,SAvsC6C,CAwsC9C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;AAEAgB,QAAAA,gBAAgB,GAAG;AACf,eAAK,IAAM3K,KAAX,IAAoB,KAAKtD,YAAzB,EAAuC;AACnC,gBAAIsD,KAAK,GAAG,EAAZ,EAAgB;AACZ,kBAAMuE,MAAM,GAAG,KAAK/H,YAAL,CAAkBsI,GAAlB,CAAsB9E,KAAtB,CAAf;;AACA,kBAAIuE,MAAJ,EAAY;AACRnK,gBAAAA,KAAK,CAACmK,MAAD,CAAL,CAAcqG,EAAd,CAAiB,GAAjB,EAAsB;AAAEC,kBAAAA,OAAO,EAAE;AAAX,iBAAtB,EAAwCC,KAAxC;AACH;AACJ;AACJ;AACJ,SA5tC6C,CA8tC9C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;AA7vC8C,O,UAEvCpM,Q,GAAuB,I;;;;;iBAGL,I;;;;;;;iBAGF,I", "sourcesContent": ["import { _decorator, Canvas, Component, Node, Sprite, SpriteFrame, tween, UITransform, v2, view } from 'cc';\r\nimport GameMapData from '../../data/GameMapData';\r\nimport MapItemData from '../../data/MapItemData';\r\nimport { GameIns } from '../../GameIns';\r\nimport ResourceList from '../../const/ResourceList';\r\nimport { Tools } from '../../utils/Tools';\r\nimport { GameFunc } from '../../GameFunc';\r\nimport ExchangeMap from '../base/ExchangeMap';\r\nimport ImageSequence from '../base/ImageSequence';\r\nimport { GameConst } from '../../const/GameConst';\r\nimport GameEnum from '../../const/GameEnum';\r\nimport NodeMove from '../base/NodeMove';\r\n\r\nconst { ccclass, property } = _decorator;\r\n\r\n@ccclass('GameMapRun')\r\nexport default class GameMapRun extends Component {\r\n\r\n    static instance: GameMapRun = null;\r\n\r\n    @property(Node)\r\n    floorLayerParent: Node = null;\r\n\r\n    @property(Node)\r\n    skyLayerParent: Node = null;\r\n\r\n    floorDataSprite = [];\r\n    skyDataSprite = [];\r\n    floorSpeed = [];\r\n    startY = 0;\r\n    floorLayer = [];\r\n    skyLayer = [];\r\n    skyLinkYDis = [];\r\n    skySpeed = [];\r\n    inMapItem = [];\r\n    skyNodeMove = [];\r\n    skyNodeAngle = [];\r\n    hideImg = [];\r\n    imageSqueDataSprite = [];\r\n    imageSqueSpeed = [];\r\n    imageSqueLayer = [];\r\n    imageSquePos = [];\r\n    randomNum = null;\r\n    LayerData = new Map();\r\n    LayerParents = new Map();\r\n    allFloorLayers = [];\r\n    allSkyLayers = [];\r\n    allSqueLayers = [];\r\n    loadedNode = new Map();\r\n    itemDatas = [];\r\n    changeMapSpeedRatio = 1;\r\n    changeSkySpeedRatio = 1;\r\n    posOffIndex = 0;\r\n    initOver = false;\r\n    loadedAtlas = new Map();\r\n    maskOver = true;\r\n    lightingPoor = [];\r\n    thunders = new Map();\r\n    nowThunder = 0;\r\n    isPlayThunder = false;\r\n    mapCanRun = true;\r\n    isInitParkour = false;\r\n    totalHeight = 0;\r\n    _lineArr = [];\r\n    delayInit = 40;\r\n    delay = 40;\r\n    m_paokuRun = false;\r\n    _loadFinish = false;\r\n    _loadTotal = 0;\r\n    _loadCount = 0;\r\n    canParkoutRun = false;\r\n    frist = true;\r\n\r\n    viewHeight = view.getVisibleSize().height\r\n    canHideImg = true;\r\n\r\n    get loadFinish(): boolean {\r\n        return this._loadFinish;\r\n    }\r\n\r\n    onLoad() {\r\n        GameMapRun.instance = this;\r\n    }\r\n\r\n    checkLoadFinish() {\r\n        this._loadCount++;\r\n        if (this._loadCount >= this._loadTotal) {\r\n            this.getAllMapLayer();\r\n            this.getAllSkyLayer();\r\n            this.getAllSqueLayer();\r\n            this.getAllBuildAndTurret();\r\n            this.initMapAndSky();\r\n            this._loadFinish = true;\r\n        }\r\n        GameIns.battleManager.addLoadingPercent(5 / this._loadTotal);\r\n    }\r\n\r\n    update(deltaTime: number) {\r\n        if (!GameConst.GameAble) {\r\n            return;\r\n        }\r\n\r\n        if (deltaTime > 0.2) {\r\n            deltaTime = 0.016666666666667;\r\n        }\r\n\r\n        const gameState = GameIns.gameRuleManager.gameState;\r\n        if (\r\n            gameState !== GameEnum.GameState.Battle &&\r\n            gameState !== GameEnum.GameState.Sortie &&\r\n            gameState !== GameEnum.GameState.Ready &&\r\n            gameState !== GameEnum.GameState.WillOver &&\r\n            gameState !== GameEnum.GameState.Idle &&\r\n            gameState !== GameEnum.GameState.Over\r\n        ) {\r\n            return;\r\n        }\r\n\r\n        if (!this.mapCanRun) {\r\n            return;\r\n        }\r\n\r\n        this.allFloorLayers.forEach((layer) => {\r\n            this.mapLayer1Run(deltaTime, layer);\r\n        });\r\n\r\n        this.allSkyLayers.forEach((layer) => {\r\n            this.skyLayer1Run(deltaTime, layer);\r\n        });\r\n\r\n        // this.allSqueLayers.forEach((layer) => {\r\n        //     this.imageSqueRun(deltaTime, layer);\r\n        // });\r\n\r\n        // if (this.LayerData.get(888) && this.m_paokuRun) {\r\n        //     this.parkourLayerRun(deltaTime);\r\n        //     this.updateSpeedLine(deltaTime);\r\n        // }\r\n    }\r\n\r\n    // clear() {\r\n    //     // 清理加载的资源\r\n    //     this.LayerData.forEach((data, key) => {\r\n    //         data.clear();\r\n    //     });\r\n    //     this.LayerData.clear();\r\n    //     this.LayerParents.clear();\r\n    //     this.itemDatas = [];\r\n    //     this.allFloorLayers = [];\r\n    //     this.allSkyLayers = [];\r\n    //     this.allSqueLayers = [];\r\n    // }\r\n\r\n    paokuRun(enable: boolean) {\r\n        this.m_paokuRun = enable;\r\n    }\r\n\r\n    // loadNode(key: string, node: Node) {\r\n    //     // this.loadedNode.set(key, node);\r\n    // }\r\n\r\n    // changeLevel(level: number, subLevel: number) {\r\n    //     this.initData(level, subLevel);\r\n    // }\r\n\r\n    initData(level: number, subLevel: number) {\r\n        this._loadFinish = false;\r\n        this._loadTotal = 0;\r\n        this._loadCount = 0;\r\n        this.reset();\r\n        this.posOffIndex = 0;\r\n\r\n        this.LayerParents.set(0, this.floorLayerParent);\r\n        this.LayerParents.set(1, this.skyLayerParent);\r\n\r\n        let randomIndex = 0;\r\n        this._loadTotal++;\r\n        GameIns.beanManager.loadTableByName1(ResourceList[`GameMap_${level}`], (table) => {\r\n            const ids = table.getAllID();\r\n            let index = 0;\r\n\r\n            do {\r\n                const record = table.getRecorder(ids[randomIndex]);\r\n                if (subLevel === record.level) {\r\n                    const totalRules = record.total_rules;\r\n                    if (this.randomNum === null) {\r\n                        this.randomNum = Math.floor(Math.random() * (totalRules - 1) + 1) + index;\r\n                    }\r\n                    if (record.id === this.randomNum) {\r\n                        this.floorDataSprite = record.floor_res;\r\n                        this.skyDataSprite = record.sky_res;\r\n                        this.floorSpeed = record.floor_speed;\r\n                        this.floorLayer = record.floor_layer;\r\n                        this.skyLayer = record.sky_layer;\r\n                        this.startY = record.start_y;\r\n                        this.skyLinkYDis = record.link_y_distance;\r\n                        this.skySpeed = record.sky_speed;\r\n                        this.inMapItem = record.in_map_item;\r\n                        this.skyNodeMove = record.skyNode_move;\r\n                        this.skyNodeAngle = record.sky_angle;\r\n                        this.hideImg = record.hide_img;\r\n                        this.imageSqueDataSprite = record.imageSque_res;\r\n                        this.imageSqueSpeed = record.imageSque_speed;\r\n                        this.imageSqueLayer = record.imageSque_layer;\r\n                        this.imageSquePos = record.imageSque_pos;\r\n                        break;\r\n                    }\r\n                } else {\r\n                    index = randomIndex + 1;\r\n                }\r\n            } while (++randomIndex < ids.length);\r\n\r\n            this.atlasManage();\r\n            this.checkLoadFinish();\r\n        }, () => {\r\n            // GameFunc.wxLoadErr();\r\n        });\r\n    }\r\n\r\n    getAllBuildAndTurret() {\r\n        for (let i = 0; i < this.inMapItem.length; i++) {\r\n            const itemData = this.inMapItem[i].split(\"!\");\r\n            const mapItem = new MapItemData(\r\n                Number(itemData[0]),\r\n                Number(itemData[1]),\r\n                Number(itemData[2]),\r\n                Number(itemData[3]),\r\n                Number(itemData[4]),\r\n                Number(itemData[5]),\r\n                Number(itemData[6]),\r\n                Number(itemData[7]),\r\n                Number(itemData[8]),\r\n                Number(itemData[9]),\r\n                itemData[10]\r\n            );\r\n            this.itemDatas[i] = mapItem;\r\n        }\r\n\r\n        // Sort items by creation position\r\n        for (let i = 0; i < this.itemDatas.length - 1; i++) {\r\n            for (let j = 0; j < this.itemDatas.length - 1 - i; j++) {\r\n                if (this.itemDatas[j].createPos > this.itemDatas[j + 1].createPos) {\r\n                    const temp = this.itemDatas[j];\r\n                    this.itemDatas[j] = this.itemDatas[j + 1];\r\n                    this.itemDatas[j + 1] = temp;\r\n                }\r\n            }\r\n        }\r\n    }\r\n\r\n    getAllSqueLayer() {\r\n        for (let i = 0; i < this.imageSqueLayer.length; i++) {\r\n            const layer = Number(this.imageSqueLayer[i]);\r\n            if (!Tools.arrContain(this.allSqueLayers, layer)) {\r\n                this.allSqueLayers.push(layer);\r\n            }\r\n        }\r\n\r\n        // Sort layers by position\r\n        for (let i = 0; i < this.imageSquePos.length - 1; i++) {\r\n            for (let j = 0; j < this.imageSquePos.length - 1 - i; j++) {\r\n                const pos1 = Number(this.imageSquePos[j].split(\",\")[1]);\r\n                const pos2 = Number(this.imageSquePos[j + 1].split(\",\")[1]);\r\n                if (pos1 > pos2) {\r\n                    // Swap positions\r\n                    [this.imageSquePos[j], this.imageSquePos[j + 1]] = [this.imageSquePos[j + 1], this.imageSquePos[j]];\r\n                    [this.imageSqueDataSprite[j], this.imageSqueDataSprite[j + 1]] = [this.imageSqueDataSprite[j + 1], this.imageSqueDataSprite[j]];\r\n                    [this.imageSqueLayer[j], this.imageSqueLayer[j + 1]] = [this.imageSqueLayer[j + 1], this.imageSqueLayer[j]];\r\n                    [this.imageSqueSpeed[j], this.imageSqueSpeed[j + 1]] = [this.imageSqueSpeed[j + 1], this.imageSqueSpeed[j]];\r\n                }\r\n            }\r\n        }\r\n    }\r\n\r\n    getAllSkyLayer() {\r\n        for (let i = 0; i < this.skyLayer.length; i++) {\r\n            if (!Tools.arrContain(this.allSkyLayers, this.skyLayer[i])) {\r\n                this.allSkyLayers.push(this.skyLayer[i]);\r\n            }\r\n        }\r\n\r\n        // Sort layers by link distance\r\n        for (let i = 0; i < this.skyLinkYDis.length - 1; i++) {\r\n            for (let j = 0; j < this.skyLinkYDis.length - 1 - i; j++) {\r\n                const pos1 = Number(this.skyLinkYDis[j].split(\",\")[1]);\r\n                const pos2 = Number(this.skyLinkYDis[j + 1].split(\",\")[1]);\r\n                if (pos1 > pos2) {\r\n                    // Swap positions\r\n                    [this.skyLinkYDis[j], this.skyLinkYDis[j + 1]] = [this.skyLinkYDis[j + 1], this.skyLinkYDis[j]];\r\n                    [this.skyDataSprite[j], this.skyDataSprite[j + 1]] = [this.skyDataSprite[j + 1], this.skyDataSprite[j]];\r\n                    [this.skyLayer[j], this.skyLayer[j + 1]] = [this.skyLayer[j + 1], this.skyLayer[j]];\r\n                    [this.skySpeed[j], this.skySpeed[j + 1]] = [this.skySpeed[j + 1], this.skySpeed[j]];\r\n                    [this.skyNodeMove[j], this.skyNodeMove[j + 1]] = [this.skyNodeMove[j + 1], this.skyNodeMove[j]];\r\n                    [this.skyNodeAngle[j], this.skyNodeAngle[j + 1]] = [this.skyNodeAngle[j + 1], this.skyNodeAngle[j]];\r\n                }\r\n            }\r\n        }\r\n    }\r\n\r\n    getAllMapLayer() {\r\n        for (let i = 0; i < this.floorLayer.length; i++) {\r\n            if (!Tools.arrContain(this.allFloorLayers, this.floorLayer[i])) {\r\n                this.allFloorLayers.push(this.floorLayer[i]);\r\n            }\r\n        }\r\n    }\r\n\r\n    atlasManage() {\r\n        const floorAtlas: string[] = [];\r\n        const skyAtlas: string[] = [];\r\n        const imageSqueAtlas: string[] = [];\r\n\r\n        // Collect unique atlas names for floor layers\r\n        for (const sprite of this.floorDataSprite) {\r\n            const atlasName = sprite.split(\",\")[1];\r\n            if (!Tools.arrContain(floorAtlas, atlasName)) {\r\n                floorAtlas.push(atlasName);\r\n            }\r\n        }\r\n\r\n        // Collect unique atlas names for sky layers\r\n        for (const sprite of this.skyDataSprite) {\r\n            const atlasName = sprite.split(\",\")[1];\r\n            if (!Tools.arrContain(skyAtlas, atlasName)) {\r\n                skyAtlas.push(atlasName);\r\n            }\r\n        }\r\n\r\n        // Collect unique atlas names for image sequence layers\r\n        for (const sprite of this.imageSqueDataSprite) {\r\n            const atlasName = sprite.split(\",\")[1];\r\n            if (!Tools.arrContain(imageSqueAtlas, atlasName)) {\r\n                imageSqueAtlas.push(atlasName);\r\n            }\r\n        }\r\n\r\n        // Release unused atlases\r\n        this.loadedAtlas.forEach((asset, atlasName) => {\r\n            if (\r\n                !Tools.arrContain(floorAtlas, atlasName) &&\r\n                !Tools.arrContain(skyAtlas, atlasName) &&\r\n                !Tools.arrContain(imageSqueAtlas, atlasName)\r\n            ) {\r\n                GameIns.loadManager.releaseAsset(asset);\r\n                this.loadedAtlas.delete(atlasName);\r\n            }\r\n        });\r\n\r\n        // Load missing atlases for floor layers\r\n        for (const atlasName of floorAtlas) {\r\n            if (!this.loadedAtlas.has(atlasName)) {\r\n                this._loadTotal++;\r\n                GameIns.loadManager.loadAtlas1(\"map/\" + atlasName, (asset) => {\r\n                    this.loadedAtlas.set(atlasName, asset);\r\n                    this.checkLoadFinish();\r\n                });\r\n            }\r\n        }\r\n\r\n        // Load missing atlases for sky layers\r\n        for (const atlasName of skyAtlas) {\r\n            if (!this.loadedAtlas.has(atlasName)) {\r\n                this._loadTotal++;\r\n                GameIns.loadManager.loadAtlas1(\"map/\" + atlasName, (asset) => {\r\n                    this.loadedAtlas.set(atlasName, asset);\r\n                    this.checkLoadFinish();\r\n                });\r\n            }\r\n        }\r\n\r\n        // Load missing atlases for image sequence layers\r\n        for (const atlasName of imageSqueAtlas) {\r\n            if (!this.loadedAtlas.has(atlasName)) {\r\n                this._loadTotal++;\r\n                GameIns.loadManager.loadAtlas1(atlasName, (asset) => {\r\n                    this.loadedAtlas.set(atlasName, asset);\r\n                    this.checkLoadFinish();\r\n                });\r\n            }\r\n        }\r\n    }\r\n\r\n\r\n    initMapAndSky() {\r\n        // 初始化地面层、天空层和队列层\r\n        this.allFloorLayers.forEach((layer) => {\r\n            this.mapLayerinit(layer);\r\n        });\r\n\r\n        this.allSkyLayers.forEach((layer) => {\r\n            this.skyLayer1init(layer);\r\n        });\r\n\r\n        this.allSqueLayers.forEach((layer) => {\r\n            this.imageSqueinit(layer);\r\n        });\r\n\r\n        // 设置所有层的初始位置\r\n        this.LayerParents.forEach((parent, layer) => {\r\n            if (layer !== 888) {\r\n                parent.y = -1880;\r\n            }\r\n        });\r\n\r\n        this.initOver = true;\r\n    }\r\n\r\n    mapEndChange() {\r\n        // 处理地图结束切换\r\n        ExchangeMap.me.endChange();\r\n    }\r\n\r\n    reset() {\r\n        // 重置地图数据\r\n        this.lightingPoor.splice(0);\r\n        this.initOver = false;\r\n        this.posOffIndex = 0;\r\n        this.changeMapSpeedRatio = 1;\r\n        this.changeMapSpeedRatio = 1;\r\n        this.randomNum = null;\r\n        this.itemDatas.splice(0);\r\n\r\n        if (this.allFloorLayers.length > 0) {\r\n            this.LayerReset(this.allFloorLayers);\r\n        }\r\n\r\n        if (this.allSkyLayers.length > 0) {\r\n            this.LayerReset(this.allSkyLayers);\r\n        }\r\n\r\n        if (this.allSqueLayers.length > 0) {\r\n            this.LayerReset(this.allSqueLayers);\r\n        }\r\n\r\n        if (this.LayerParents.get(888)) {\r\n            this.LayerParents.get(888).destroy();\r\n            this.LayerParents.delete(888);\r\n            this.LayerData.get(888).clear();\r\n            this.LayerData.delete(888);\r\n        }\r\n\r\n        this.allFloorLayers = [];\r\n        this.allSkyLayers = [];\r\n        this.allSqueLayers = [];\r\n    }\r\n\r\n    LayerReset(layers: number[]) {\r\n        // 重置指定层\r\n        layers.forEach((layer) => {\r\n            const layerData = this.LayerData.get(layer);\r\n            const parent = this.LayerParents.get(layer);\r\n\r\n            if (parent) {\r\n                parent.destroyAllChildren();\r\n                parent.y = 0;\r\n            }\r\n\r\n            if (layerData) {\r\n                layerData.clear();\r\n            }\r\n        });\r\n    }\r\n\r\n    mapLayerinit(layer: number) {\r\n        // 初始化地面层\r\n        let layerData = this.LayerData.get(layer) || new GameMapData();\r\n        layerData.ViewTop = this.viewHeight / 2;\r\n        layerData.ViewBot = -this.viewHeight / 2;\r\n\r\n        const spriteNames: string[] = [];\r\n        this.floorDataSprite.forEach((sprite) => {\r\n            spriteNames.push(sprite.split(\",\")[0]);\r\n        });\r\n\r\n        layerData.loadSprite.forEach((_, key) => {\r\n            if (!Tools.arrContain(spriteNames, key)) {\r\n                layerData.loadSprite.delete(key);\r\n            }\r\n        });\r\n\r\n        for (let i = 0; i < this.floorDataSprite.length; i++) {\r\n            const node = new Node();\r\n            node.addComponent(UITransform);\r\n            const [spriteName, atlasName] = this.floorDataSprite[i].split(\",\");\r\n            const spriteFrame = GameIns.loadManager.getImage(spriteName, \"map/\" + atlasName) as SpriteFrame;\r\n\r\n            node.addComponent(Sprite).spriteFrame = spriteFrame;\r\n\r\n            const designWidth = 720 + 20;\r\n            layerData.scale = designWidth / (node.getComponent(UITransform).width);\r\n            node.getComponent(UITransform).width = designWidth;\r\n            node.getComponent(UITransform).height *= layerData.scale;\r\n\r\n            if (i === 0) {\r\n                node.y = Math.floor(layerData.ViewBot + node.getComponent(UITransform).height / 2);\r\n            } else {\r\n                node.y = layerData.tempY + Math.floor(node.getComponent(UITransform).height / 2 + layerData.tempH / 2);\r\n            }\r\n\r\n            layerData.tempY = node.y;\r\n            layerData.tempH = node.getComponent(UITransform).height;\r\n\r\n            if (this.floorLayer[i] === layer) {\r\n                layerData.speed = this.floorSpeed[i];\r\n                layerData.speeds = this.floorSpeed;\r\n            }\r\n\r\n            if (node.y - node.getComponent(UITransform).height / 2 > layerData.ViewTop) {\r\n                break;\r\n            }\r\n\r\n            if (this.floorLayer[i] === layer) {\r\n                if (this.LayerParents.get(this.floorLayer[i])) {\r\n                    node.parent = this.LayerParents.get(this.floorLayer[i]);\r\n                } else {\r\n                    const parent = new Node();\r\n                    parent.addComponent(UITransform);\r\n                    parent.parent = this.node;\r\n                    this.LayerParents.set(this.floorLayer[i], parent);\r\n                    node.parent = parent;\r\n                }\r\n            }\r\n\r\n            layerData.nowUseNode.push(node);\r\n            layerData.loadSprite.set(spriteName, spriteFrame);\r\n            layerData.index = i;\r\n        }\r\n\r\n        this.LayerData.set(layer, layerData);\r\n    }\r\n\r\n    skyLayer1init(layer: number) {\r\n        // 初始化天空层\r\n        let layerData: GameMapData = this.LayerData.get(layer) || new GameMapData();\r\n        layerData.ViewTop = this.viewHeight / 2;\r\n        layerData.ViewBot = -this.viewHeight / 2;\r\n\r\n        // 筛选属于当前层的天空数据\r\n        const skyLayerData = this.skyLayer\r\n            .map((skyLayer, index) => ({ skyLayer, index }))\r\n            .filter(({ skyLayer }) => skyLayer === layer);\r\n\r\n        // 初始化层数据\r\n        skyLayerData.forEach(({ index }) => {\r\n            layerData.spriteName.push(this.skyDataSprite[index]);\r\n            layerData.PosInfo.push(this.skyLinkYDis[index]);\r\n            layerData.speeds.push(this.skySpeed[index]);\r\n            layerData.nodeAngle.push(this.skyNodeAngle[index] || 0);\r\n            layerData.nodeMove.push(this.skyNodeMove[index] || \"0,0\");\r\n        });\r\n\r\n        // 清理无用的精灵\r\n        const spriteNames = layerData.spriteName.map(sprite => sprite.split(\",\")[0]);\r\n        layerData.loadSprite.forEach((_, key) => {\r\n            if (!Tools.arrContain(spriteNames, key)) {\r\n                layerData.loadSprite.delete(key);\r\n            }\r\n        });\r\n\r\n        // 创建节点并初始化\r\n        skyLayerData.forEach(({ index }) => {\r\n            const node = new Node();\r\n            node.addComponent(UITransform);\r\n\r\n            const [spriteName, atlasName] = this.skyDataSprite[index].split(\",\");\r\n            const spriteFrame = GameIns.loadManager.getImage(spriteName, `map/${atlasName}`) as SpriteFrame;\r\n\r\n            node.addComponent(Sprite).spriteFrame = spriteFrame;\r\n\r\n            const [x, y, scaleX, scaleY] = this.skyLinkYDis[index].split(\",\").map(Number);\r\n            node.setPosition(x, y);\r\n            node.getComponent(UITransform).width = spriteFrame.width * scaleX;\r\n            node.getComponent(UITransform).height = spriteFrame.height * scaleY;\r\n            node.angle = this.skyNodeAngle[index];\r\n\r\n            // 设置速度\r\n            layerData.speed = this.skySpeed[index];\r\n\r\n            // 确保父节点存在\r\n            if (!this.LayerParents.has(layer)) {\r\n                const parent = new Node();\r\n                parent.addComponent(UITransform);\r\n                parent.parent = this.node;\r\n                this.LayerParents.set(layer, parent);\r\n            }\r\n\r\n            // 检查是否超出视图顶部\r\n            if (node.position.y - node.getComponent(UITransform).height / 2 > layerData.ViewTop) {\r\n                return;\r\n            }\r\n\r\n            node.parent = this.LayerParents.get(layer);\r\n            layerData.nowUseNode.push(node);\r\n            layerData.loadSprite.set(spriteName, spriteFrame);\r\n            layerData.index = index;\r\n        });\r\n\r\n        this.LayerData.set(layer, layerData);\r\n    }\r\n\r\n    imageSqueinit(layer: number) {\r\n        // 初始化队列层\r\n        const layerData = this.LayerData.get(layer) || new GameMapData();\r\n        layerData.ViewTop = this.viewHeight / 2;\r\n        layerData.ViewBot = -this.viewHeight / 2;\r\n\r\n        for (let i = 0; i < this.imageSqueLayer.length; i++) {\r\n            if (Number(this.imageSqueLayer[i]) === layer) {\r\n                layerData.spriteName.push(this.imageSqueDataSprite[i]);\r\n                layerData.PosInfo.push(this.imageSquePos[i]);\r\n                layerData.speeds.push(Number(this.imageSqueSpeed[i]));\r\n            }\r\n        }\r\n\r\n        const spriteNames: string[] = [];\r\n        layerData.spriteName.forEach((sprite) => {\r\n            spriteNames.push(sprite.split(\",\")[0]);\r\n        });\r\n\r\n        layerData.loadImageSque.forEach((_, key) => {\r\n            if (!Tools.arrContain(spriteNames, key)) {\r\n                layerData.loadImageSque.delete(key);\r\n            }\r\n        });\r\n\r\n        for (let i = 0; i < this.imageSqueDataSprite.length; i++) {\r\n            if (Number(this.imageSqueLayer[i]) === layer) {\r\n                const node = new Node();\r\n                node.addComponent(UITransform);\r\n                const [spriteName, atlasName, frameCount] = this.imageSqueDataSprite[i].split(\",\");\r\n                const frames: SpriteFrame[] = [];\r\n\r\n                for (let j = 0; j < Number(frameCount); j++) {\r\n                    const frameName = `${spriteName}_${j}`;\r\n                    const frame = GameIns.loadManager.getImage(frameName, \"map/\" + atlasName) as SpriteFrame;\r\n                    frames.push(frame);\r\n                }\r\n\r\n                const imageSequence = node.addComponent(ImageSequence);\r\n                imageSequence.setData(frames, 30 / layerData.frameTime);\r\n                imageSequence.setStart();\r\n\r\n                const [x, y] = this.imageSquePos[i].split(\",\").map(Number);\r\n                node.setPosition(x, y);\r\n\r\n                if (!this.LayerParents.get(layer)) {\r\n                    const parent = new Node();\r\n                    parent.addComponent(UITransform);\r\n                    parent.parent = this.node;\r\n                    this.LayerParents.set(layer, parent);\r\n                }\r\n\r\n                node.parent = this.LayerParents.get(layer);\r\n                layerData.nowUseNode.push(node);\r\n                layerData.loadImageSque.set(spriteName, frames);\r\n                layerData.index = i;\r\n            }\r\n        }\r\n\r\n        this.LayerData.set(layer, layerData);\r\n    }\r\n\r\n    initParkourLayer() {\r\n        //     if (!this.isInitParkour) {\r\n        //         this.canParkoutRun = false;\r\n\r\n        //         const isExpedition = _.ExpeditionMgr.isExpedition;\r\n        //         const isLJStage = isExpedition\r\n        //             ? m.StageMgr.isLJStageInExpedition(_.ExpeditionMgr.currentSection + 1)\r\n        //             : m.StageMgr.isLJStage(c.default.me.subStage);\r\n\r\n        //         if (isLJStage) {\r\n        //             let layerData = this.LayerData.get(888) || new GameMapData();\r\n        //             layerData.ViewTop = winSize.height / 2;\r\n        //             layerData.ViewBot = -winSize.height / 2;\r\n\r\n        //             let parentNode = this.LayerParents.get(888) || new Node(\"parkour\");\r\n        //             parentNode.y = 0;\r\n        //             parentNode.parent = this.node;\r\n        //             parentNode.zIndex = this.node.children.length - 3;\r\n\r\n        //             this.LayerParents.set(888, parentNode);\r\n        //             layerData.index = 0;\r\n        //             layerData.itemIndex = 0;\r\n\r\n        //             const runItemData = h.EnemyMgr._runItemData[layerData.index];\r\n        //             for (let i = 0; i < h.EnemyMgr.getRunItemDataLength(runItemData); i++) {\r\n        //                 const itemData = h.EnemyMgr.getRunItemData(runItemData, layerData.itemIndex);\r\n        //                 const id = h.EnemyMgr.getidByUid(runItemData);\r\n        //                 let height = 2560;\r\n\r\n        //                 if (id !== null) {\r\n        //                     height = h.EnemyMgr._runTable.getRecorder(id).height;\r\n        //                 }\r\n\r\n        //                 const yPos = Number(itemData.y) + layerData.index * height;\r\n        //                 const node = h.EnemyMgr.addItem(runItemData, layerData.index, layerData.itemIndex, false);\r\n        //                 node.parent = parentNode;\r\n        //                 layerData.nowUseNode.push(node);\r\n\r\n        //                 layerData.itemIndex++;\r\n        //                 if (layerData.itemIndex === h.EnemyMgr.getRunItemDataLength(runItemData)) {\r\n        //                     layerData.index++;\r\n        //                     layerData.itemIndex = 0;\r\n        //                 }\r\n\r\n        //                 if (yPos > layerData.ViewTop) {\r\n        //                     break;\r\n        //                 }\r\n        //             }\r\n\r\n        //             layerData.speeds = [];\r\n        //             const runTable = h.EnemyMgr._runTable;\r\n        //             runTable.getAllID().forEach((id) => {\r\n        //                 const speed = runTable.getRecorder(id).speed;\r\n        //                 layerData.speeds.push(speed);\r\n        //             });\r\n\r\n        //             this.LayerData.set(888, layerData);\r\n        //             this.frist = true;\r\n        //             this._createSpeedLine();\r\n        //             this.isInitParkour = true;\r\n        //             this.canParkoutRun = true;\r\n        //         }\r\n        //     }\r\n    }\r\n\r\n    // _createSpeedLine() {\r\n    //     if (this._speedLine) {\r\n    //         let yOffset = 0;\r\n    //         for (const line of this._lineArr) {\r\n    //             line.y = yOffset;\r\n    //             yOffset += 0.7 * line.height;\r\n    //         }\r\n    //     } else {\r\n    //         this._speedLine = new Node();\r\n    //         let yOffset = 0;\r\n\r\n    //         for (let i = 0; i < 2; i++) {\r\n    //             const line = new Node();\r\n    //             this._speedLine.addChild(line);\r\n    //             line.anchorY = 0;\r\n    //             line.opacity = 200;\r\n    //             line.addComponent(Sprite).spriteFrame = GameIns.loadManager.getImage(\"speedLine\", \"chapterParkour_Map\");\r\n    //             line.width = GameConst.ViewWidth;\r\n    //             line.height *= 6;\r\n    //             line.y = yOffset;\r\n    //             yOffset += line.height;\r\n    //             this._lineArr.push(line);\r\n    //         }\r\n    //     }\r\n\r\n    //     let parentNode = this.node.getChildByName(\"speedLine\");\r\n    //     if (!parentNode) {\r\n    //         parentNode = new Node(\"speedLine\");\r\n    //         parentNode.parent = this.node;\r\n    //         parentNode.zIndex = 5;\r\n    //     }\r\n\r\n    //     this._speedLine.opacity = 0;\r\n    //     this._speedLine.parent = parentNode;\r\n    //     this._speedLine.y = -GameConst.ViewHeight;\r\n    //     this._speedLineOpacity = true;\r\n    //     this.delay = this.delayInit;\r\n    // }\r\n\r\n\r\n\r\n    // parkourLayerRun(deltaTime: number) {\r\n    //     if (!this.canParkoutRun) {\r\n    //         return;\r\n    //     }\r\n\r\n    //     const layerData = this.LayerData.get(888);\r\n    //     const parentNode = this.LayerParents.get(888);\r\n    //     const speed = layerData.speeds[layerData.index % layerData.speeds.length];\r\n\r\n    //     parentNode.y -= speed * deltaTime;\r\n    //     layerData.ViewTop += deltaTime * speed;\r\n    //     layerData.ViewBot = layerData.ViewTop - winSize.height;\r\n\r\n    //     if (layerData.nowUseNode.length > 0 && layerData.nowUseNode[0].y + 200 < layerData.ViewBot) {\r\n    //         const node = layerData.nowUseNode.shift();\r\n    //         if (!node.getComponent(f.default).isEndLine) {\r\n    //             h.EnemyMgr.putItem(node, true);\r\n    //         }\r\n    //     }\r\n\r\n    //     if (layerData.index >= h.EnemyMgr._runItemData.length && layerData.index !== 1) {\r\n    //         if (this.frist) {\r\n    //             const node = h.EnemyMgr.addItem(0, layerData.index, 0, true, v2(0, this.totalHeight));\r\n    //             node.parent = parentNode;\r\n    //             layerData.nowUseNode.push(node);\r\n    //             this.frist = false;\r\n    //         }\r\n\r\n    //         const planes = h.EnemyMgr.planes;\r\n    //         if (planes.length === 1 && planes[0].getComponent(f.default).isEndLine) {\r\n    //             this.hideSpeedLineByDt(deltaTime);\r\n    //         }\r\n    //     } else {\r\n    //         const runItemData = h.EnemyMgr._runItemData[layerData.index];\r\n    //         if (runItemData) {\r\n    //             for (let i = 0; i < 5; i++) {\r\n    //                 const itemData = h.EnemyMgr.getRunItemData(runItemData, layerData.itemIndex);\r\n    //                 const id = h.EnemyMgr.getidByUid(runItemData);\r\n    //                 let height = 2560;\r\n\r\n    //                 if (id !== null) {\r\n    //                     height = h.EnemyMgr._runTable.getRecorder(id).height;\r\n    //                 }\r\n\r\n    //                 const yPos = Number(itemData.y) + this.totalHeight;\r\n    //                 if (yPos < layerData.ViewTop + 200) {\r\n    //                     const node = h.EnemyMgr.addItem(runItemData, layerData.index, layerData.itemIndex, false);\r\n    //                     node.parent = parentNode;\r\n    //                     layerData.nowUseNode.push(node);\r\n\r\n    //                     layerData.itemIndex++;\r\n    //                     if (layerData.itemIndex === h.EnemyMgr.getRunItemDataLength(runItemData)) {\r\n    //                         layerData.index++;\r\n    //                         layerData.itemIndex = 0;\r\n    //                         this.totalHeight += height;\r\n    //                     }\r\n    //                 }\r\n    //             }\r\n\r\n    //             this.LayerData.set(888, layerData);\r\n    //         }\r\n    //     }\r\n    // }\r\n\r\n    // updateSpeedLine(deltaTime: number) {\r\n    //     if (this.delay > 0) {\r\n    //         this.delay--;\r\n    //     }\r\n\r\n    //     if (this.delay === 0 && this._speedLine) {\r\n    //         this._speedLine.y -= 3000 * deltaTime;\r\n\r\n    //         const firstLine = this._lineArr[0];\r\n    //         if (firstLine.height + firstLine.y + this._speedLine.y < -GameConst.ViewHeight) {\r\n    //             this._lineArr.shift();\r\n    //             const lastLine = this._lineArr[this._lineArr.length - 1];\r\n    //             firstLine.y = lastLine.y + lastLine.height;\r\n    //             this._lineArr.push(firstLine);\r\n    //         }\r\n\r\n    //         if (this._speedLine.opacity < 255 && this._speedLineOpacity) {\r\n    //             this._speedLine.opacity += 255 * deltaTime;\r\n    //         }\r\n    //     }\r\n    // }\r\n\r\n    // thunderPlayer(sounds: string[], delay: number, loop: number = 1) {\r\n    //     if (loop === 1) {\r\n    //         for (let i = 0; i < sounds.length; i++) {\r\n    //             if (this.thunders.has(sounds[i])) {\r\n    //                 if (this.thunders.get(sounds[i]) === false) {\r\n    //                     this.thunders.set(sounds[i], true);\r\n    //                     tween(this)\r\n    //                         .delay(delay)\r\n    //                         .call(async () => {\r\n    //                             const soundId = await y.default.audioManager.playEffect(sounds[i]);\r\n    //                             audioEngine.setFinishCallback(soundId, () => {\r\n    //                                 this.thunders.set(sounds[i], false);\r\n    //                             });\r\n    //                         })\r\n    //                         .start();\r\n    //                     return;\r\n    //                 }\r\n    //             } else {\r\n    //                 this.thunders.set(sounds[i], false);\r\n    //             }\r\n    //         }\r\n    //     }\r\n    // }\r\n\r\n    // controlLighting(layerData: GameMapData) {\r\n    //     const loopTimes = layerData.loopTimes;\r\n    //     const nodes = layerData.nowUseNode;\r\n\r\n    //     nodes.forEach((node, index) => {\r\n    //         if (node.getComponent(S.default)) {\r\n    //             const interval = layerData.getTimeInterval();\r\n    //             node.getComponent(S.default).delayPlay(interval, loopTimes);\r\n\r\n    //             if (index - 1 >= 0) {\r\n    //                 this.lightningFlash(interval / 30, node, nodes[index - 1]);\r\n    //             } else {\r\n    //                 this.lightningFlash(interval / 30, node, null);\r\n    //             }\r\n    //         }\r\n    //     });\r\n    // }\r\n\r\n    // lightningFlash(delay: number, targetNode: Node, previousNode: Node | null) {\r\n    //     let lightningNode: Node;\r\n\r\n    //     if (this.lightingPoor.length > 0) {\r\n    //         lightningNode = this.lightingPoor.pop();\r\n    //     } else {\r\n    //         lightningNode = new Node();\r\n    //         lightningNode.anchorY = 1;\r\n    //         lightningNode.addComponent(Sprite).spriteFrame = y.default.loadManager.getImage(\"wuyushandian\", \"map2_sky1_stageWuyun\");\r\n    //     }\r\n\r\n    //     let height = 0;\r\n    //     let angle = 0;\r\n\r\n    //     if (targetNode) {\r\n    //         if (previousNode === null) {\r\n    //             angle = targetNode.x > 0 ? v.Tools.random_int(-90, -135) : v.Tools.random_int(90, 135);\r\n    //             height = 500;\r\n    //         } else {\r\n    //             height = v.Tools.getDisForVec2(targetNode.position, previousNode.position);\r\n    //             angle = v.Tools.getAngle(targetNode.position, previousNode.position);\r\n    //         }\r\n\r\n    //         lightningNode.parent = targetNode.parent;\r\n    //         lightningNode.position = targetNode.position;\r\n    //         lightningNode.height = height;\r\n    //         lightningNode.angle = angle;\r\n    //         lightningNode.opacity = 0;\r\n    //         lightningNode.zIndex = -1;\r\n\r\n    //         tween(lightningNode)\r\n    //             .delay(0.5 / 30)\r\n    //             .call(() => {\r\n    //                 lightningNode.opacity = 255;\r\n    //                 lightningNode.scaleX = 1;\r\n    //             })\r\n    //             .delay(0.025)\r\n    //             .call(() => {\r\n    //                 lightningNode.scaleX = -1;\r\n    //             })\r\n    //             .delay(delay)\r\n    //             .repeat(2, tween(lightningNode).to(4 / 30, { opacity: 0 }))\r\n    //             .call(() => {\r\n    //                 lightningNode.stopAllActions();\r\n    //                 lightningNode.parent = null;\r\n    //                 this.lightingPoor.push(lightningNode);\r\n    //             })\r\n    //             .start();\r\n    //     }\r\n    // }\r\n\r\n    // imageSqueRun(deltaTime: number, layer: number) {\r\n    //     const layerData = this.LayerData.get(layer);\r\n    //     const parentNode = this.LayerParents.get(layer);\r\n    //     const speed = layerData.speeds[layerData.index % layerData.speeds.length];\r\n    //     layerData.speed = speed;\r\n\r\n    //     const adjustedSpeed = speed + this.changeSkySpeedRatio;\r\n    //     layerData.ViewTop += deltaTime * adjustedSpeed;\r\n    //     layerData.ViewBot = layerData.ViewTop - winSize.height;\r\n    //     parentNode.y -= deltaTime * adjustedSpeed;\r\n\r\n    //     const nextSpriteName = layerData.spriteName[(layerData.index + 1) % layerData.spriteName.length];\r\n    //     const [spriteName, atlasName, frameCount] = nextSpriteName.split(\",\");\r\n    //     const frames: SpriteFrame[] = [];\r\n\r\n    //     if (!layerData.loadImageSque.has(spriteName)) {\r\n    //         for (let i = 0; i < Number(frameCount); i++) {\r\n    //             const frame = y.default.loadManager.getImage(`${spriteName}_${i}`, atlasName);\r\n    //             frames.push(frame);\r\n    //         }\r\n    //         layerData.loadImageSque.set(spriteName, frames);\r\n    //     } else {\r\n    //         frames.push(...layerData.loadImageSque.get(spriteName));\r\n    //     }\r\n\r\n    //     if (layerData.nowUseNode.length > 0 && layerData.nowUseNode[0].y + layerData.nowUseNode[0].height < layerData.ViewBot) {\r\n    //         const node = layerData.nowUseNode.shift();\r\n    //         if (node.getComponent(S.default)) {\r\n    //             node.getComponent(S.default).stop();\r\n    //         }\r\n    //         layerData.freeNode.push(node);\r\n    //     }\r\n\r\n    //     const nextPos = layerData.PosInfo[(layerData.index + 1) % layerData.PosInfo.length].split(\",\");\r\n    //     const x = Number(nextPos[0]);\r\n    //     const y = Number(nextPos[1]);\r\n\r\n    //     if (y + layerData.starPos - frames[0].getRect().height / 2 < layerData.ViewTop + 1880 + 500) {\r\n    //         let node: Node;\r\n\r\n    //         if (layerData.freeNode.length > 0) {\r\n    //             node = layerData.freeNode.shift();\r\n    //         } else {\r\n    //             node = new Node();\r\n    //         }\r\n\r\n    //         const imageSequence = node.getComponent(S.default) || node.addComponent(S.default);\r\n    //         node.width = frames[0].getRect().width;\r\n    //         node.height = frames[0].getRect().height;\r\n    //         node.x = x;\r\n    //         node.y = y + layerData.starPos;\r\n    //         node.parent = this.LayerParents.get(layer);\r\n    //         imageSequence.setData(frames, 30 / layerData.frameTime);\r\n    //         imageSequence.setStart();\r\n    //         layerData.nowUseNode.push(node);\r\n    //         layerData.index++;\r\n    //     }\r\n\r\n    //     if (layerData.nowTimes > layerData.getTiggerTime()) {\r\n    //         this.controlLighting(layerData);\r\n    //         layerData.nowTimes = 0;\r\n    //         layerData.nowTriggerTime = 0;\r\n    //     }\r\n\r\n    //     if ((layerData.index + 1) % layerData.spriteName.length === 0 && layerData.index !== 0 && layerData.nowUseNode.length > 0) {\r\n    //         const lastNode = layerData.nowUseNode[layerData.nowUseNode.length - 1];\r\n    //         layerData.starPos = lastNode.y + lastNode.height / 2 + frames[0].getRect().height / 2;\r\n    //     }\r\n\r\n    //     this.createItem(layer, deltaTime);\r\n    // }\r\n\r\n    /**\r\n     * 更新天空图层的运行逻辑\r\n     * @param deltaTime 时间增量\r\n     * @param layer 天空图层的索引\r\n     */\r\n    skyLayer1Run(deltaTime: number, layer: number): void {\r\n        const layerData = this.LayerData.get(layer);\r\n        const layerParent = this.LayerParents.get(layer);\r\n\r\n        if (!layerData || !layerParent) {\r\n            return;\r\n        }\r\n\r\n        const speed = layerData.speeds[layerData.index % layerData.speeds.length];\r\n        const adjustedSpeed = speed + this.changeSkySpeedRatio;\r\n\r\n        // 更新视图范围\r\n        layerData.ViewTop += deltaTime * adjustedSpeed;\r\n        layerData.ViewBot = layerData.ViewTop - view.getVisibleSize().height;\r\n\r\n        // 更新图层位置\r\n        let posY1 = layerParent.position.y - deltaTime * adjustedSpeed\r\n        layerParent.setPosition(layerParent.position.x, posY1);\r\n\r\n        // 检查隐藏图片逻辑\r\n        this.canHideImg = this.changeSkySpeedRatio > 5;\r\n\r\n        const nextPosInfo = layerData.PosInfo[(layerData.index + 1) % layerData.PosInfo.length].split(\",\");\r\n        const posX = Number(nextPosInfo[0]);\r\n        const posY = Number(nextPosInfo[1]);\r\n        const scaleX = Number(nextPosInfo[2]);\r\n        const scaleY = Math.abs(Number(nextPosInfo[3]));\r\n\r\n        const nextSpriteInfo = layerData.spriteName[(layerData.index + 1) % layerData.spriteName.length].split(\",\");\r\n        const spriteName = nextSpriteInfo[0];\r\n        const atlasName = nextSpriteInfo[1];\r\n\r\n        let spriteFrame = layerData.loadSprite.get(spriteName);\r\n        if (!spriteFrame) {\r\n            spriteFrame = GameIns.loadManager.getImage(spriteName, \"map/\" + atlasName);\r\n            layerData.loadSprite.set(spriteName, spriteFrame);\r\n        }\r\n\r\n        // 更新星星位置\r\n        if ((layerData.index + 1) % layerData.spriteName.length === 0 && layerData.index !== 0 && layerData.nowUseNode.length > 0) {\r\n            const lastNode = layerData.nowUseNode[layerData.nowUseNode.length - 1];\r\n            layerData.starPos = lastNode.y + lastNode.height / 2 + spriteFrame.getRect().height * scaleY / 2;\r\n        }\r\n\r\n        // 检查是否需要隐藏图片\r\n        if (this.hideImg.indexOf(spriteName) != -1) {\r\n            if (this.canHideImg) {\r\n                layerData.index++;\r\n                return;\r\n            }\r\n            if (posY + layerData.starPos - spriteFrame.getRect().height / 2 - 1880 < layerData.ViewTop) {\r\n                layerData.index++;\r\n                return;\r\n            }\r\n        }\r\n\r\n        // 平滑调整速度\r\n        if (layerData.index - 1 < this.skySpeed.length && layerData.index - 1 >= 0) {\r\n            const speedDiff = this.skySpeed[(layerData.index - 1) % this.skySpeed.length] - layerData.speed;\r\n            layerData.speed += speedDiff * deltaTime;\r\n        } else {\r\n            const speedDiff = this.skySpeed[this.skySpeed.length - 1] - layerData.speed;\r\n            layerData.speed += speedDiff * deltaTime;\r\n        }\r\n\r\n        // 移除超出视图范围的节点\r\n        if (layerData.nowUseNode.length > 0 && layerData.nowUseNode[0].y + layerData.nowUseNode[0].height - 1880 < layerData.ViewBot) {\r\n            const removedNode = layerData.nowUseNode.shift();\r\n            removedNode.active = false;\r\n            layerData.freeNode.push(removedNode);\r\n        }\r\n\r\n        // 创建新节点\r\n        if (posY + layerData.starPos - spriteFrame.getRect().height / 2 * scaleY < layerData.ViewTop + 1880 + 500) {\r\n            let newNode: Node;\r\n            if (layerData.freeNode.length > 0) {\r\n                newNode = layerData.freeNode.shift();\r\n            } else {\r\n                newNode = new Node();\r\n                newNode.addComponent(Sprite);\r\n            }\r\n\r\n            this.addNodeMoveComp(newNode, layerData.nodeMove[(layerData.index + 1) % layerData.nodeMove.length], layer);\r\n\r\n            const sprite = newNode.getComponent(Sprite);\r\n            sprite.sizeMode = Sprite.SizeMode.RAW;\r\n            sprite.spriteFrame = spriteFrame;\r\n\r\n            if (sprite.spriteFrame) {\r\n                // newNode.getComponent(UITransform).width = sprite.spriteFrame.getRect().width * scaleX;\r\n                // newNode.getComponent(UITransform).height = sprite.spriteFrame.height * scaleY;\r\n                newNode.setPosition(posX,posY + layerData.starPos)\r\n                newNode.angle = layerData.nodeAngle[(layerData.index + 1) % layerData.nodeAngle.length];\r\n            }\r\n\r\n            newNode.parent = this.LayerParents.get(layer);\r\n            newNode.active = true;\r\n            layerData.nowUseNode.push(newNode);\r\n            layerData.index++;\r\n        }\r\n\r\n        // 创建物品\r\n        this.createItem(layer, deltaTime);\r\n    }\r\n\r\n    /**\r\n     * 为节点添加或设置 NodeMove 组件\r\n     * @param node 要操作的节点\r\n     * @param moveData 移动数据，格式为 \"xSpeed,ySpeed\"\r\n     * @param layer 节点所属的图层\r\n     */\r\n    addNodeMoveComp(node: Node, moveData: string, layer: number): void {\r\n        const [xSpeed, ySpeed] = moveData.split(\",\").map(Number);\r\n        const nodeMoveComp = node.getComponent(NodeMove) || node.addComponent(NodeMove);\r\n        nodeMoveComp.setData(xSpeed, ySpeed, layer);\r\n    }\r\n\r\n    mapLayer1Run(deltaTime: number, layer: number) {\r\n        const layerData = this.LayerData.get(layer);\r\n        const parentNode = this.LayerParents.get(layer);\r\n        const speed = layerData.speeds[layerData.index % layerData.speeds.length];\r\n        layerData.speed = speed;\r\n\r\n        const adjustedSpeed = speed + this.changeMapSpeedRatio;\r\n        layerData.ViewTop += deltaTime * adjustedSpeed;\r\n        layerData.ViewBot = layerData.ViewTop - view.getVisibleSize().height;\r\n        parentNode.y -= deltaTime * adjustedSpeed;\r\n\r\n        if (layerData.nowUseNode.length > 0 && layerData.nowUseNode[0].y + layerData.nowUseNode[0].height / 2 < layerData.ViewBot) {\r\n            const node = layerData.nowUseNode.shift();\r\n            node.active = false;\r\n            layerData.freeNode.push(node);\r\n        }\r\n\r\n        const nextSpriteName = this.floorDataSprite[(layerData.index + 1) % this.floorDataSprite.length].split(\",\");\r\n        const spriteName = nextSpriteName[0];\r\n        const atlasName = nextSpriteName[1];\r\n        let spriteFrame = layerData.loadSprite.get(spriteName);\r\n\r\n        if (!spriteFrame) {\r\n            spriteFrame = GameIns.loadManager.getImage(spriteName, \"map/\" + atlasName);\r\n            layerData.loadSprite.set(spriteName, spriteFrame);\r\n        }\r\n\r\n        const lastNode:Node = layerData.nowUseNode[layerData.nowUseNode.length - 1];\r\n        const newNode:Node = layerData.freeNode.length > 0 ? layerData.freeNode.shift() : new Node();\r\n        const sprite = newNode.getComponent(Sprite) || newNode.addComponent(Sprite);\r\n\r\n        sprite.spriteFrame = spriteFrame;\r\n        sprite.sizeMode = Sprite.SizeMode.RAW;\r\n        // newNode.width = spriteFrame.getRect().width;\r\n        // newNode.height = spriteFrame.getRect().height;\r\n        let posY = lastNode.position.y + lastNode.getComponent(UITransform).height / 2 + newNode.getComponent(UITransform).height / 2;\r\n        newNode.setPosition(newNode.position.x, posY);\r\n        newNode.parent = parentNode;\r\n        newNode.active = true;\r\n\r\n        layerData.nowUseNode.push(newNode);\r\n        layerData.index++;\r\n        this.createItem(layer, deltaTime);\r\n    }\r\n\r\n    /**\r\n     * 创建地图中的物品（建筑、飞机、炮塔等）。\r\n     * @param layer 当前图层\r\n     * @param deltaTime 时间增量\r\n     */\r\n    createItem(layer: number, deltaTime: number): void {\r\n        const layerData = this.LayerData.get(layer);\r\n        const layerParent = this.LayerParents.get(layer);\r\n\r\n        if (\r\n            layerData.itemIndex < this.itemDatas.length &&\r\n            this.itemDatas[layerData.itemIndex].backLayer === layer &&\r\n            layerData.ViewBot > this.itemDatas[layerData.itemIndex].createPos\r\n        ) {\r\n            const itemData = this.itemDatas[layerData.itemIndex];\r\n            let itemNode: Node;\r\n            const posX = itemData.backPosX;\r\n            const posY = itemData.backPosY;\r\n            const scaleX = itemData.backScaleX;\r\n            const scaleY = itemData.backScaleY;\r\n\r\n            switch (itemData.enemy) {\r\n                case 0: // 创建建筑\r\n                    Tools.log(`创建建筑，图层: ${layer}`);\r\n                    break;\r\n\r\n                case 1: // 创建飞机\r\n                    itemNode = GameIns.enemyManager.addMapPlane(\r\n                        itemData.id,\r\n                        itemData.track,\r\n                        v2(posX, posY),\r\n                        itemData.backLayer\r\n                    );\r\n                    break;\r\n\r\n                case 2: // 创建炮塔\r\n                    // itemNode = GameIns.enemyManager.addTurret(itemData.id, itemData.backLayer).node;\r\n                    break;\r\n            }\r\n\r\n            if (itemNode) {\r\n                itemNode.parent = layerParent;\r\n                itemNode.setSiblingIndex(itemData.zIndex);\r\n                itemNode.setPosition(posX, posY - this.posOffIndex * deltaTime * itemData.Vec);\r\n                itemNode.setScale(scaleX * this.LayerData.get(0).scale, scaleY * this.LayerData.get(0).scale);\r\n                itemNode.angle = 0;\r\n\r\n                const nodeMoveComp = itemNode.getComponent(NodeMove) || itemNode.addComponent(NodeMove);\r\n                nodeMoveComp.setData(0, itemData.Vec, layer);\r\n            }\r\n\r\n            layerData.itemIndex++;\r\n        }\r\n    }\r\n    // hideLayerBylayer() {\r\n    //     for (const layer of this.allSkyLayers) {\r\n    //         if (layer > 99) {\r\n    //             const parent = this.LayerParents.get(layer);\r\n    //             if (parent) {\r\n    //                 tween(parent).to(0.5, { opacity: 0 }).start();\r\n    //             }\r\n    //         }\r\n    //     }\r\n    // }\r\n\r\n    showLayerBylayer() {\r\n        for (const layer of this.allSkyLayers) {\r\n            if (layer > 99) {\r\n                const parent = this.LayerParents.get(layer);\r\n                if (parent) {\r\n                    tween(parent).to(0.5, { opacity: 255 }).start();\r\n                }\r\n            }\r\n        }\r\n    }\r\n\r\n    // hideSpeedLine() {\r\n    //     this._speedLineOpacity = false;\r\n    //     if (this._speedLine) {\r\n    //         this._speedLine.stopAllActions();\r\n    //         tween(this._speedLine)\r\n    //             .to(1 / 30 * 22, { opacity: 0 })\r\n    //             .call(() => this.removeSpeedLine())\r\n    //             .start();\r\n    //     }\r\n    // }\r\n\r\n    // hideSpeedLineByDt(deltaTime: number) {\r\n    //     this._speedLineOpacity = false;\r\n    //     if (this._speedLine) {\r\n    //         this._speedLine.opacity -= 355 * deltaTime;\r\n    //         if (this._speedLine.opacity <= 0) {\r\n    //             this.removeSpeedLine();\r\n    //         }\r\n    //     }\r\n    // }\r\n\r\n    // removeSpeedLine() {\r\n    //     Tools.log(\"remove speedLine\");\r\n    //     if (this._speedLine) {\r\n    //         this._speedLine.destroyAllChildren();\r\n    //         this._speedLine.destroy();\r\n    //         this._speedLine = null;\r\n    //     }\r\n    //     if (this._lineArr) {\r\n    //         this._lineArr.splice(0);\r\n    //     }\r\n    // }\r\n}"]}