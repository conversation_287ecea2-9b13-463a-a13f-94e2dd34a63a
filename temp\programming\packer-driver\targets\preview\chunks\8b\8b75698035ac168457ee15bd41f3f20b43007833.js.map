{"version": 3, "sources": ["file:///E:/M2Game/Client/assets/scripts/ui/DevLoginUI.ts"], "names": ["_decorator", "<PERSON><PERSON>", "EditBox", "csproto", "GameInstance", "DevLoginData", "logDebug", "logError", "uiSelect", "BaseUI", "<PERSON><PERSON><PERSON><PERSON>", "UIMgr", "ccclass", "property", "DevLoginUI", "_onGetRoleBound", "onGetRole", "bind", "getUrl", "<PERSON><PERSON><PERSON><PERSON>", "Top", "onHide", "onShow", "loginButton", "node", "on", "EventType", "CLICK", "onLoginButtonClick", "netMgr", "registerHandler", "cs", "CS_CMD", "CS_CMD_GET_ROLE", "serverList", "for<PERSON>ach", "value", "key", "serverSelect", "itemDatas", "push", "setChooseItemData", "instance", "servername", "onChooseItem", "itemData", "usernameEditBox", "string", "user", "passwordEditBox", "password", "onClose", "unregister<PERSON><PERSON><PERSON>", "username", "platformSDK", "login", "err", "info", "msg", "closeUI"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAASA,MAAAA,U,OAAAA,U;AAAYC,MAAAA,M,OAAAA,M;AAAQC,MAAAA,O,OAAAA,O;;AACtBC,MAAAA,O;;AACEC,MAAAA,Y,iBAAAA,Y;;AACAC,MAAAA,Y,iBAAAA,Y;;AACAC,MAAAA,Q,iBAAAA,Q;AAAUC,MAAAA,Q,iBAAAA,Q;;AACVC,MAAAA,Q,iBAAAA,Q;;AACAC,MAAAA,M,iBAAAA,M;AAAQC,MAAAA,O,iBAAAA,O;AAASC,MAAAA,K,iBAAAA,K;;;;;;;;;OACpB;AAAEC,QAAAA,OAAF;AAAWC,QAAAA;AAAX,O,GAAwBb,U;;4BAGjBc,U,WADZF,OAAO,CAAC,YAAD,C,UAIHC,QAAQ,CAACZ,MAAD,C,UAERY,QAAQ,CAACX,OAAD,C,UAERW,QAAQ,CAACX,OAAD,C,UAGRW,QAAQ;AAAA;AAAA,+B,2BAXb,MACaC,UADb;AAAA;AAAA,4BACuC;AAAA;AAAA;;AAAA;;AAAA;;AAAA;;AAAA;;AAAA,eAa3BC,eAb2B,GAa0B,KAAKC,SAAL,CAAeC,IAAf,CAAoB,IAApB,CAb1B;AAAA;;AACf,eAANC,MAAM,GAAW;AAAE,iBAAO,eAAP;AAAwB;;AACnC,eAARC,QAAQ,GAAY;AAAE,iBAAO;AAAA;AAAA,kCAAQC,GAAf;AAAoB;;AAalDC,QAAAA,MAAM,GAAgC;AAAA;AAC3C;;AAEKC,QAAAA,MAAM,GAAgC;AAAA;;AAAA;AACxC,YAAA,KAAI,CAACC,WAAL,CAAiBC,IAAjB,CAAsBC,EAAtB,CAAyBxB,MAAM,CAACyB,SAAP,CAAiBC,KAA1C,EAAiD,KAAI,CAACC,kBAAtD,EAA0E,KAA1E;;AACA;AAAA;AAAA,8CAAaC,MAAb,CAAoBC,eAApB,CAAoC;AAAA;AAAA,oCAAQC,EAAR,CAAWC,MAAX,CAAkBC,eAAtD,EAAuE,KAAI,CAAClB,eAA5E;AAEA;AAAA;AAAA,8CAAamB,UAAb,CAAwBC,OAAxB,CAAgC,CAACC,KAAD,EAAQC,GAAR,KAAgB;AAC5C,cAAA,KAAI,CAACC,YAAL,CAAkBC,SAAlB,CAA4BC,IAA5B,CAAiCH,GAAjC;;AACA,cAAA,KAAI,CAACC,YAAL,CAAkBC,SAAlB,CAA4BC,IAA5B,CAAiCH,GAAG,GAAG,GAAvC;AACH,aAHD;;AAIA,YAAA,KAAI,CAACC,YAAL,CAAkBG,iBAAlB,CAAoC;AAAA;AAAA,8CAAaC,QAAb,CAAsBC,UAA1D;;AACA,YAAA,KAAI,CAACL,YAAL,CAAkBM,YAAlB,GAAkCC,QAAD,IAAsB;AACnD;AAAA;AAAA,wCAAS,SAAT,qBAAqCA,QAArC;AACA;AAAA;AAAA,gDAAaH,QAAb,CAAsBC,UAAtB,GAAmCE,QAAnC;AACH,aAHD;;AAIA,YAAA,KAAI,CAACC,eAAL,CAAqBC,MAArB,GAA8B;AAAA;AAAA,8CAAaL,QAAb,CAAsBM,IAApD;AACA,YAAA,KAAI,CAACC,eAAL,CAAqBF,MAArB,GAA8B;AAAA;AAAA,8CAAaL,QAAb,CAAsBQ,QAApD;AAdwC;AAe3C;;AAEKC,QAAAA,OAAO,GAAgC;AAAA;;AAAA;AACzC;AAAA;AAAA,8CAAatB,MAAb,CAAoBuB,iBAApB,CAAsC;AAAA;AAAA,oCAAQrB,EAAR,CAAWC,MAAX,CAAkBC,eAAxD,EAAyE,MAAI,CAAClB,eAA9E;AADyC;AAE5C;;AAEDa,QAAAA,kBAAkB,GAAG;AACjB,cAAIyB,QAAQ,GAAG,KAAKP,eAAL,CAAqBC,MAApC;AACA,cAAIG,QAAQ,GAAG,KAAKD,eAAL,CAAqBF,MAApC;AACA;AAAA;AAAA,4CAAaL,QAAb,CAAsBM,IAAtB,GAA6BK,QAA7B;AACA;AAAA;AAAA,4CAAaX,QAAb,CAAsBQ,QAAtB,GAAiCA,QAAjC;AAEA;AAAA;AAAA,4CAAaI,WAAb,CAAyBC,KAAzB,CAA+B,CAACC,GAAD,EAAMC,IAAN,KAAe;AAC1C,gBAAID,GAAJ,EAAS;AACL;AAAA;AAAA,wCAAS,YAAT,oBAAuCA,GAAvC;AACA;AACH;;AACD;AAAA;AAAA,8CAAa3B,MAAb,CAAoB0B,KAApB,CAA0BE,IAA1B;AACH,WAND;AAOH;;AAEDzC,QAAAA,SAAS,CAAC0C,GAAD,EAA0B;AAC/B;AAAA;AAAA,8BAAMC,OAAN,CAAc7C,UAAd;AACH;;AAxDkC,O;;;;;iBAIb,I;;;;;;;iBAEK,I;;;;;;;iBAEA,I;;;;;;;iBAGF,I", "sourcesContent": ["import { _decorator, But<PERSON>, EditBox } from 'cc';\nimport csproto from '../AutoGen/PB/cs_proto.js';\nimport { GameInstance } from '../GameInstance';\nimport { DevLoginData } from '../PlatformSDK/DevLoginData';\nimport { logDebug, logError } from '../Utils/Logger';\nimport { uiSelect } from './components/common/SelectList/uiSelect';\nimport { BaseUI, UILayer, UIMgr } from './UIMgr';\nconst { ccclass, property } = _decorator;\n\n@ccclass('DevLoginUI')\nexport class DevLoginUI extends BaseUI {\n    public static getUrl(): string { return \"ui/DevLoginUI\" };\n    public static getLayer(): UILayer { return UILayer.Top }\n    @property(Button)\n    loginButton: Button = null!;\n    @property(EditBox)\n    usernameEditBox: EditBox = null!;\n    @property(EditBox)\n    passwordEditBox: EditBox = null!;\n\n    @property(uiSelect)\n    serverSelect: uiSelect = null!;\n\n    private _onGetRoleBound: (msg: csproto.cs.IS2CMsg) => void = this.onGetRole.bind(this);\n\n    async onHide(...args: any[]): Promise<void> {\n    }\n\n    async onShow(...args: any[]): Promise<void> {\n        this.loginButton.node.on(Button.EventType.CLICK, this.onLoginButtonClick, this);\n        GameInstance.netMgr.registerHandler(csproto.cs.CS_CMD.CS_CMD_GET_ROLE, this._onGetRoleBound);\n\n        DevLoginData.serverList.forEach((value, key) => {\n            this.serverSelect.itemDatas.push(key)\n            this.serverSelect.itemDatas.push(key + \"1\")\n        });\n        this.serverSelect.setChooseItemData(DevLoginData.instance.servername)\n        this.serverSelect.onChooseItem = (itemData: string) => {\n            logDebug(\"LoginUI\", `choose server ${itemData}`)\n            DevLoginData.instance.servername = itemData;\n        }\n        this.usernameEditBox.string = DevLoginData.instance.user;\n        this.passwordEditBox.string = DevLoginData.instance.password;\n    }\n\n    async onClose(...args: any[]): Promise<void> {\n        GameInstance.netMgr.unregisterHandler(csproto.cs.CS_CMD.CS_CMD_GET_ROLE, this._onGetRoleBound)\n    }\n\n    onLoginButtonClick() {\n        var username = this.usernameEditBox.string;\n        var password = this.passwordEditBox.string;\n        DevLoginData.instance.user = username;\n        DevLoginData.instance.password = password;\n\n        GameInstance.platformSDK.login((err, info) => {\n            if (err) {\n                logError(\"DevLoginUI\", `login failed ${err}`);\n                return;\n            }\n            GameInstance.netMgr.login(info);\n        });\n    }\n\n    onGetRole(msg: csproto.cs.IS2CMsg) {\n        UIMgr.closeUI(DevLoginUI);\n    }\n}"]}