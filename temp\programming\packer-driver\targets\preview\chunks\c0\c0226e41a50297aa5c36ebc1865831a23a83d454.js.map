{"version": 3, "sources": ["file:///E:/M2Game/Client/assets/scripts/GameOld/world/level/LevelSystem.ts"], "names": ["_decorator", "System", "RegisterTypeID", "ccclass", "LevelSystem", "_currentLevel", "_isLevelActive", "_isLevelCompleted", "_isLevelFailed", "getCurrentLevel", "setCurrentLevel", "level", "getSystemName", "onInit", "onUnInit", "console", "log", "onUpdate", "deltaTime", "tick", "onLateUpdate", "loadLevel", "levelConfig", "warn", "levelId", "completeLevel", "failLevel", "reason", "isLevelActive", "isLevelCompleted", "isLevelFailed"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;AAASA,MAAAA,U,OAAAA,U;;AACAC,MAAAA,M,iBAAAA,M;;AACAC,MAAAA,c,iBAAAA,c;;;;;;;;;OAEH;AAAEC,QAAAA;AAAF,O,GAAcH,U;AAEpB;AACA;AACA;;6BAGaI,W,WAFZD,OAAO,CAAC,aAAD,C;;yEAAR,MAEaC,WAFb;AAAA;AAAA,4BAEwC;AAAA;AAAA;AAAA,eAE5BC,aAF4B,GAEE,IAFF;AAAA,eAG5BC,cAH4B,GAGF,KAHE;AAAA,eAI5BC,iBAJ4B,GAIC,KAJD;AAAA,eAK5BC,cAL4B,GAKF,KALE;AAAA;;AAOpC;AACJ;AACA;AACWC,QAAAA,eAAe,GAAiB;AACnC,iBAAO,KAAKJ,aAAZ;AACH;;AAEMK,QAAAA,eAAe,CAACC,KAAD,EAA4B;AAC9C,eAAKN,aAAL,GAAqBM,KAArB;AACH;AAED;AACJ;AACA;;;AACWC,QAAAA,aAAa,GAAW;AAC3B,iBAAO,aAAP;AACH;AAED;AACJ;AACA;;;AACcC,QAAAA,MAAM,GAAS;AACrB,eAAKP,cAAL,GAAsB,IAAtB;AACH;AAED;AACJ;AACA;;;AACcQ,QAAAA,QAAQ,GAAS;AACvBC,UAAAA,OAAO,CAACC,GAAR,CAAY,uCAAZ;AAEA,eAAKX,aAAL,GAAqB,IAArB;AACA,eAAKC,cAAL,GAAsB,KAAtB;AACA,eAAKC,iBAAL,GAAyB,KAAzB;AACA,eAAKC,cAAL,GAAsB,KAAtB;AAEAO,UAAAA,OAAO,CAACC,GAAR,CAAY,+BAAZ;AACH;AAED;AACJ;AACA;;;AACcC,QAAAA,QAAQ,CAACC,SAAD,EAA0B;AACxC,cAAI,CAAC,KAAKZ,cAAN,IAAwB,CAAC,KAAKD,aAAlC,EAAiD;AAC7C;AACH;;AAED,eAAKA,aAAL,CAAmBc,IAAnB,CAAwBD,SAAxB,EALwC,CAOxC;AACA;AACA;AACA;;AACH;AAED;AACJ;AACA;;;AACcE,QAAAA,YAAY,CAACF,SAAD,EAA0B,CAC5C;AACH;AAED;AACJ;AACA;AACA;AACA;;;AACWG,QAAAA,SAAS,CAACC,WAAD,EAAuB;AACnC,cAAI,KAAKhB,cAAT,EAAyB;AACrBS,YAAAA,OAAO,CAACQ,IAAR,CAAa,kEAAb;AACA,mBAAO,KAAP;AACH,WAJkC,CAMnC;AAEA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;;;AAEAR,UAAAA,OAAO,CAACC,GAAR,yBAAkCM,WAAW,CAACE,OAA9C;AACA,iBAAO,IAAP;AACH;AAED;AACJ;AACA;;;AACWC,QAAAA,aAAa,GAAS;AACzB,cAAI,CAAC,KAAKnB,cAAN,IAAwB,KAAKC,iBAA7B,IAAkD,KAAKC,cAA3D,EAA2E;AACvE;AACH;;AAED,eAAKD,iBAAL,GAAyB,IAAzB;AACA,eAAKD,cAAL,GAAsB,KAAtB;AACH;AAED;AACJ;AACA;AACA;;;AACWoB,QAAAA,SAAS,CAACC,MAAD,EAAuB;AACnC,cAAI,CAAC,KAAKrB,cAAN,IAAwB,KAAKC,iBAA7B,IAAkD,KAAKC,cAA3D,EAA2E;AACvE;AACH;;AAED,eAAKA,cAAL,GAAsB,IAAtB;AACA,eAAKF,cAAL,GAAsB,KAAtB;AACH;AAED;AACJ;AACA;;;AACWsB,QAAAA,aAAa,GAAY;AAC5B,iBAAO,KAAKtB,cAAZ;AACH;AAED;AACJ;AACA;;;AACWuB,QAAAA,gBAAgB,GAAY;AAC/B,iBAAO,KAAKtB,iBAAZ;AACH;AAED;AACJ;AACA;;;AACWuB,QAAAA,aAAa,GAAY;AAC5B,iBAAO,KAAKtB,cAAZ;AACH;;AA/JmC,O", "sourcesContent": ["import { _decorator, Node, Vec3 } from \"cc\";\nimport { System } from \"../base/System\";\nimport { RegisterTypeID } from \"../base/TypeID\";\nimport { Level } from './Level';\nconst { ccclass } = _decorator;\n\n/**\n * LevelSystem - manages level state, objectives, checkpoints, and events\n */\n@ccclass(\"LevelSystem\")\n@RegisterTypeID\nexport class LevelSystem extends System {\n    \n    private _currentLevel: Level | null = null;\n    private _isLevelActive: boolean = false;\n    private _isLevelCompleted: boolean = false;\n    private _isLevelFailed: boolean = false;\n    \n    /**\n     * Get the current level configuration\n     */\n    public getCurrentLevel(): Level | null {\n        return this._currentLevel;\n    }\n\n    public setCurrentLevel(level: Level | null): void {\n        this._currentLevel = level;\n    }\n    \n    /**\n     * Get the system name\n     */\n    public getSystemName(): string {\n        return \"LevelSystem\";\n    }\n    \n    /**\n     * Initialize the level system\n     */\n    protected onInit(): void {\n        this._isLevelActive = true;\n    }\n    \n    /**\n     * Cleanup the level system\n     */\n    protected onUnInit(): void {\n        console.log(\"LevelSystem: Cleaning up level system\");\n        \n        this._currentLevel = null;\n        this._isLevelActive = false;\n        this._isLevelCompleted = false;\n        this._isLevelFailed = false;\n        \n        console.log(\"LevelSystem: Cleanup complete\");\n    }\n    \n    /**\n     * Update the level system\n     */\n    protected onUpdate(deltaTime: number): void {\n        if (!this._isLevelActive || !this._currentLevel) {\n            return;\n        }\n        \n        this._currentLevel.tick(deltaTime);\n        \n        // // Check if all objectives are completed\n        // if (this._areAllObjectivesCompleted()) {\n        //     this.completeLevel();\n        // }\n    }\n    \n    /**\n     * Late update - handle any post-update logic\n     */\n    protected onLateUpdate(deltaTime: number): void {\n        // Could be used for UI updates, statistics, etc.\n    }\n    \n    /**\n     * Load and start a level\n     * @param levelConfig The level configuration to load\n     * @returns true if the level was loaded successfully\n     */\n    public loadLevel(levelConfig): boolean {\n        if (this._isLevelActive) {\n            console.warn(\"LevelSystem: Cannot load level - another level is already active\");\n            return false;\n        }\n        \n        // console.log(`LevelSystem: Loading level ${levelConfig.levelId}`);\n        \n        // Set current level\n        // this._currentLevel = this._cloneLevelConfig(levelConfig);\n        \n        // // Reset state\n        // this._levelStartTime = Date.now();\n        // this._levelElapsedTime = 0;\n        // this._isLevelActive = true;\n        // this._isLevelCompleted = false;\n        // this._isLevelFailed = false;\n        // this._eventHistory.length = 0;\n        \n        // // Reset checkpoints\n        // this._currentLevel.checkpoints.forEach(checkpoint => {\n        //     checkpoint.isReached = false;\n        //     checkpoint.timestamp = undefined;\n        // });\n        \n        // // Reset objectives\n        // this._currentLevel.objectives.forEach(objective => {\n        //     objective.currentValue = 0;\n        //     objective.isCompleted = false;\n        // });\n        \n        // // Emit level start event\n        // this._emitEvent(LevelEventType.LEVEL_START, {\n        //     levelId: levelConfig.levelId,\n        //     startTime: this._levelStartTime\n        // });\n        \n        console.log(`LevelSystem: Level ${levelConfig.levelId} loaded and started`);\n        return true;\n    }\n    \n    /**\n     * Complete the current level\n     */\n    public completeLevel(): void {\n        if (!this._isLevelActive || this._isLevelCompleted || this._isLevelFailed) {\n            return;\n        }\n        \n        this._isLevelCompleted = true;\n        this._isLevelActive = false;\n    }\n    \n    /**\n     * Fail the current level\n     * @param reason The reason for failure\n     */\n    public failLevel(reason: string): void {\n        if (!this._isLevelActive || this._isLevelCompleted || this._isLevelFailed) {\n            return;\n        }\n        \n        this._isLevelFailed = true;\n        this._isLevelActive = false;\n    }\n    \n    /**\n     * Check if level is active\n     */\n    public isLevelActive(): boolean {\n        return this._isLevelActive;\n    }\n    \n    /**\n     * Check if level is completed\n     */\n    public isLevelCompleted(): boolean {\n        return this._isLevelCompleted;\n    }\n    \n    /**\n     * Check if level is failed\n     */\n    public isLevelFailed(): boolean {\n        return this._isLevelFailed;\n    }\n}\n"]}