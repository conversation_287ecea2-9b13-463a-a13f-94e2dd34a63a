// const { ccclass, property } = cc._decorator;
// @ccclass
// export default class GridScreen extends BaseScreen {
//     private props: {
//         bulletNum: number;
//         beginAngle: number;
//         endAngle: number;
//         posOffset: cc.Vec2;
//     };
//     private diffDegrees: number | null = null;
//     /**
//      * 初始化网格屏幕
//      * @param config 配置数据
//      * @param mainEntity 主实体
//      */
//     constructor(config: any, mainEntity: any) {
//         super();
//         this.setData(config, mainEntity);
//         const params = this.m_config.para;
//         const bulletNum = params[0];
//         const beginAngle = params[1];
//         const endAngle = params[2];
//         const posOffset = this.m_config.offset;
//         this.props = {
//             bulletNum,
//             beginAngle,
//             endAngle,
//             posOffset,
//         };
//         this.diffDegrees = null;
//     }
//     /**
//      * 每帧更新逻辑
//      * @param deltaTime 时间增量
//      */
//     update(deltaTime: number): void {
//         // 可根据需求实现更新逻辑
//     }
//     /**
//      * 初始化
//      */
//     onInit(): void {
//         // 可根据需求实现初始化逻辑
//     }
//     /**
//      * 发射子弹
//      */
//     fire(): void {
//         let bulletNum = this.props.bulletNum;
//         // 如果计数为奇数，减少一个子弹
//         if (this.m_count % 2 !== 0) {
//             bulletNum--;
//         }
//         const attackPoint = this.getAttackPoint();
//         const x = attackPoint.x;
//         const y = attackPoint.y;
//         if (bulletNum === 1) {
//             // 单发子弹
//             const bullet = this.createBullet();
//             let angle = (this.props.beginAngle + this.props.endAngle) / 2;
//             angle -= 90;
//             if (bullet) {
//                 bullet.init(
//                     this.m_enemy,
//                     { x, y, angle },
//                     this.m_bulletState,
//                     this.m_mainEntity
//                 );
//             }
//         } else if (bulletNum > 0) {
//             // 多发子弹
//             if (this.diffDegrees === null) {
//                 this.diffDegrees = (this.props.endAngle - this.props.beginAngle) / (bulletNum - 1);
//             }
//             if (bulletNum % 2 === 0) {
//                 // 偶数子弹
//                 for (let i = 0; i < bulletNum; i++) {
//                     const bullet = this.createBullet();
//                     let angle = this.props.beginAngle + this.diffDegrees * i;
//                     angle -= 90;
//                     if (bullet) {
//                         bullet.init(
//                             this.m_enemy,
//                             { x, y, angle },
//                             this.m_bulletState,
//                             this.m_mainEntity
//                         );
//                     }
//                 }
//             } else {
//                 // 奇数子弹
//                 let offset = 0;
//                 for (let i = 0; i < bulletNum; i++) {
//                     const bullet = this.createBullet();
//                     let angle =
//                         (this.props.beginAngle + this.props.endAngle) / 2 +
//                         Math.pow(-1, i + 1) * this.diffDegrees * offset;
//                     angle -= 90;
//                     if (bullet) {
//                         bullet.init(
//                             this.m_enemy,
//                             { x, y, angle },
//                             this.m_bulletState,
//                             this.m_mainEntity
//                         );
//                     }
//                     if (i % 2 === 0) {
//                         offset++;
//                     }
//                 }
//             }
//         }
//     }
// }
System.register(["cc"], function (_export, _context) {
  "use strict";

  var _cclegacy, _crd;

  return {
    setters: [function (_cc) {
      _cclegacy = _cc.cclegacy;
    }],
    execute: function () {
      _crd = true;

      _cclegacy._RF.push({}, "bf3bcEg+PNISKgzPtadBSdA", "GridScreen", undefined);

      _cclegacy._RF.pop();

      _crd = false;
    }
  };
});
//# sourceMappingURL=46179b86c07789ca4c92515c73db7437332b1276.js.map