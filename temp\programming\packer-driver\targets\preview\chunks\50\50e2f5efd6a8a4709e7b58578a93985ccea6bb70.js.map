{"version": 3, "sources": ["file:///E:/M2Game/Client/assets/scripts/Game/utils/Helper.ts"], "names": ["Helper", "random", "seed", "min", "max", "floatToInt", "value", "parseFloat", "toPrecision", "Math", "floor", "sleep", "ms", "Promise", "resolve", "setTimeout"], "mappings": ";;;iBAAMA,M;;;;;;;;;;;AAAAA,MAAAA,M,GAAN,MAAMA,MAAN,CAAa;AACT;AACJ;AACA;AACA;AACA;AACA;AACA;AACiB,eAANC,MAAM,CAACC,IAAD,EAAWC,GAAX,EAAoBC,GAApB,EAA6B;AAAA,cAA5BF,IAA4B;AAA5BA,YAAAA,IAA4B,GAArB,CAAqB;AAAA;;AAAA,cAAlBC,GAAkB;AAAlBA,YAAAA,GAAkB,GAAZ,CAAY;AAAA;;AAAA,cAATC,GAAS;AAATA,YAAAA,GAAS,GAAH,CAAG;AAAA;;AACtCF,UAAAA,IAAI,GAAG,CAAC,OAAOA,IAAP,GAAc,KAAf,IAAwB,MAA/B;AACA,iBAAOC,GAAG,GAAID,IAAI,GAAG,MAAR,IAAmBE,GAAG,GAAGD,GAAzB,CAAb;AACH;AAED;AACJ;AACA;AACA;AACA;;;AACqB,eAAVE,UAAU,CAACC,KAAD,EAAQ;AACrBA,UAAAA,KAAK,IAAI,CAAT;AACAA,UAAAA,KAAK,GAAGC,UAAU,CAACD,KAAK,CAACE,WAAN,CAAkB,EAAlB,CAAD,CAAlB;AACA,iBAAOC,IAAI,CAACC,KAAL,CAAWJ,KAAX,CAAP;AACH;AAED;AACJ;AACA;AACA;AACA;;;AACgB,eAALK,KAAK,CAACC,EAAD,EAAK;AACb,iBAAO,IAAIC,OAAJ,CAAaC,OAAD,IAAa;AAC5BC,YAAAA,UAAU,CAAC,MAAM;AACbD,cAAAA,OAAO,CAAC,IAAD,CAAP;AACH,aAFS,EAEPF,EAFO,CAAV;AAGH,WAJM,CAAP;AAKH;;AAnCQ,O;;yBAsCEZ,M", "sourcesContent": ["class Helper {\r\n    /**\r\n     * 生成一个随机数\r\n     * @param {number} seed 随机种子\r\n     * @param {number} min 最小值\r\n     * @param {number} max 最大值\r\n     * @returns {number} 随机数\r\n     */\r\n    static random(seed = 5, min = 0, max = 1) {\r\n        seed = (9301 * seed + 49297) % 233280;\r\n        return min + (seed / 233280) * (max - min);\r\n    }\r\n\r\n    /**\r\n     * 将浮点数转换为整数\r\n     * @param {number} value 浮点数\r\n     * @returns {number} 转换后的整数\r\n     */\r\n    static floatToInt(value) {\r\n        value /= 1;\r\n        value = parseFloat(value.toPrecision(12));\r\n        return Math.floor(value);\r\n    }\r\n\r\n    /**\r\n     * 延迟指定的时间\r\n     * @param {number} ms 延迟的时间（毫秒）\r\n     * @returns {Promise<void>} Promise 对象\r\n     */\r\n    static sleep(ms) {\r\n        return new Promise((resolve) => {\r\n            setTimeout(() => {\r\n                resolve(true);\r\n            }, ms);\r\n        });\r\n    }\r\n}\r\n\r\nexport default Helper;"]}