System.register(["__unresolved_0", "cc", "__unresolved_1"], function (_export, _context) {
  "use strict";

  var _reporterNs, _cclegacy, __checkObsolete__, __checkObsoleteInNamespace__, _decorator, EnemyEntity, _dec, _class, _crd, ccclass, property, BossHurt;

  function _reportPossibleCrUseOfEnemyEntity(extras) {
    _reporterNs.report("EnemyEntity", "../enemy/EnemyEntity", _context.meta, extras);
  }

  return {
    setters: [function (_unresolved_) {
      _reporterNs = _unresolved_;
    }, function (_cc) {
      _cclegacy = _cc.cclegacy;
      __checkObsolete__ = _cc.__checkObsolete__;
      __checkObsoleteInNamespace__ = _cc.__checkObsoleteInNamespace__;
      _decorator = _cc._decorator;
    }, function (_unresolved_2) {
      EnemyEntity = _unresolved_2.default;
    }],
    execute: function () {
      _crd = true;

      _cclegacy._RF.push({}, "06723RYfglGmZQvzu6fcmGK", "BossHurt", undefined);

      __checkObsolete__(['_decorator']);

      ({
        ccclass,
        property
      } = _decorator);

      _export("default", BossHurt = (_dec = ccclass('BossHurt'), _dec(_class = class BossHurt extends (_crd && EnemyEntity === void 0 ? (_reportPossibleCrUseOfEnemyEntity({
        error: Error()
      }), EnemyEntity) : EnemyEntity) {
        getHpPercent() {
          return 1;
        }

      }) || _class));

      _cclegacy._RF.pop();

      _crd = false;
    }
  };
});
//# sourceMappingURL=76f87b53480e77fd41b992568adb93e7727335b4.js.map