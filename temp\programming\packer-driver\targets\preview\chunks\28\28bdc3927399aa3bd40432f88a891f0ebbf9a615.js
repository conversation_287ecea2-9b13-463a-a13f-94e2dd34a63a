System.register(["__unresolved_0", "cc", "__unresolved_1", "__unresolved_2", "__unresolved_3", "__unresolved_4"], function (_export, _context) {
  "use strict";

  var _reporterNs, _cclegacy, __checkObsolete__, __checkObsoleteInNamespace__, _decorator, Component, Sprite, Vec2, Node, tween, UITransform, Tween, v3, instantiate, GameConfig, GameIns, Tools, EnemyAnim, _dec, _dec2, _dec3, _class, _class2, _descriptor, _descriptor2, _descriptor3, _crd, ccclass, property, EnemyPlaneRole;

  function asyncGeneratorStep(gen, resolve, reject, _next, _throw, key, arg) { try { var info = gen[key](arg); var value = info.value; } catch (error) { reject(error); return; } if (info.done) { resolve(value); } else { Promise.resolve(value).then(_next, _throw); } }

  function _asyncToGenerator(fn) { return function () { var self = this, args = arguments; return new Promise(function (resolve, reject) { var gen = fn.apply(self, args); function _next(value) { asyncGeneratorStep(gen, resolve, reject, _next, _throw, "next", value); } function _throw(err) { asyncGeneratorStep(gen, resolve, reject, _next, _throw, "throw", err); } _next(undefined); }); }; }

  function _initializerDefineProperty(target, property, descriptor, context) { if (!descriptor) return; Object.defineProperty(target, property, { enumerable: descriptor.enumerable, configurable: descriptor.configurable, writable: descriptor.writable, value: descriptor.initializer ? descriptor.initializer.call(context) : void 0 }); }

  function _applyDecoratedDescriptor(target, property, decorators, descriptor, context) { var desc = {}; Object.keys(descriptor).forEach(function (key) { desc[key] = descriptor[key]; }); desc.enumerable = !!desc.enumerable; desc.configurable = !!desc.configurable; if ('value' in desc || desc.initializer) { desc.writable = true; } desc = decorators.slice().reverse().reduce(function (desc, decorator) { return decorator(target, property, desc) || desc; }, desc); if (context && desc.initializer !== void 0) { desc.value = desc.initializer ? desc.initializer.call(context) : void 0; desc.initializer = undefined; } if (desc.initializer === void 0) { Object.defineProperty(target, property, desc); desc = null; } return desc; }

  function _initializerWarningHelper(descriptor, context) { throw new Error('Decorating class property failed. Please ensure that ' + 'transform-class-properties is enabled and runs after the decorators transform.'); }

  function _reportPossibleCrUseOfGameConfig(extras) {
    _reporterNs.report("GameConfig", "../../../const/GameConfig", _context.meta, extras);
  }

  function _reportPossibleCrUseOfGameIns(extras) {
    _reporterNs.report("GameIns", "../../../GameIns", _context.meta, extras);
  }

  function _reportPossibleCrUseOfTools(extras) {
    _reporterNs.report("Tools", "../../../utils/Tools", _context.meta, extras);
  }

  function _reportPossibleCrUseOfEnemyAnim(extras) {
    _reporterNs.report("EnemyAnim", "./EnemyAnim", _context.meta, extras);
  }

  return {
    setters: [function (_unresolved_) {
      _reporterNs = _unresolved_;
    }, function (_cc) {
      _cclegacy = _cc.cclegacy;
      __checkObsolete__ = _cc.__checkObsolete__;
      __checkObsoleteInNamespace__ = _cc.__checkObsoleteInNamespace__;
      _decorator = _cc._decorator;
      Component = _cc.Component;
      Sprite = _cc.Sprite;
      Vec2 = _cc.Vec2;
      Node = _cc.Node;
      tween = _cc.tween;
      UITransform = _cc.UITransform;
      Tween = _cc.Tween;
      v3 = _cc.v3;
      instantiate = _cc.instantiate;
    }, function (_unresolved_2) {
      GameConfig = _unresolved_2.default;
    }, function (_unresolved_3) {
      GameIns = _unresolved_3.GameIns;
    }, function (_unresolved_4) {
      Tools = _unresolved_4.Tools;
    }, function (_unresolved_5) {
      EnemyAnim = _unresolved_5.default;
    }],
    execute: function () {
      _crd = true;

      _cclegacy._RF.push({}, "814a7K8QVRKfreLfXMsDdOE", "EnemyPlaneRole", undefined);

      __checkObsolete__(['_decorator', 'Component', 'Sprite', 'Vec2', 'Node', 'tween', 'v2', 'UITransform', 'Tween', 'v3', 'Prefab', 'instantiate']);

      ({
        ccclass,
        property
      } = _decorator);

      _export("default", EnemyPlaneRole = (_dec = property(Sprite), _dec2 = property(Sprite), _dec3 = property(Sprite), ccclass(_class = (_class2 = class EnemyPlaneRole extends Component {
        constructor() {
          super(...arguments);

          _initializerDefineProperty(this, "pedestal", _descriptor, this);

          _initializerDefineProperty(this, "role", _descriptor2, this);

          _initializerDefineProperty(this, "white", _descriptor3, this);

          this._data = null;
          this._param = "";
          this._target = null;
          this._curUId = -1;
          this._anim = null;
          this._curAnim = "";
          this._animWhiteArr = [];
          this._animWhitePool = [];
          this._animWhiteOffset = Vec2.ZERO;
          this._tailFireArr = [];
          this._rotateComp = null;
          this._warnLine = null;
          this._warnAble = false;
          this._winkCount = 0;
          this._bWinkWhite = false;
          this._winkAct = null;
          this._initOver = false;
          this._sneakGoUpAnimArr = [];
          this._sneakGoUpDelayArr = [];
          this._sneakAnim = null;
          this._cloakeAnim = null;
        }

        /**
         * 预加载 UI
         * @param {Object} data 敌机数据
         */
        preLoadUI(data) {
          if (this._data = data) {
            this._initSneak();

            this._initUI(true);
          } else {
            var fireNode = this.node.getChildByName("fire");

            if (!fireNode) {
              fireNode = new Node();
              fireNode.addComponent(UITransform);
              this.node.addChild(fireNode);
              fireNode.name = "fire";
            }

            for (var i = 0; i < 2; i++) {
              var tailFire = new Node();
              tailFire.addComponent(UITransform);
              fireNode.addChild(tailFire);
              tailFire.addComponent(Sprite); // tailFire.anchorY = 0;

              this._tailFireArr.push(tailFire);

              tailFire.active = false;
            }
          }
        }
        /**
         * 初始化敌机
         * @param {Object} data 敌机数据
         * @param {Object} target 目标对象
         * @param {string} param 参数
         */


        init(data, target, param) {
          var _this = this;

          return _asyncToGenerator(function* () {
            if (param === void 0) {
              param = "";
            }

            _this._reset();

            _this._data = data;
            _this._param = param;
            _this._target = target;

            if (!_this._data.isAm) {
              _this._initSneak();

              yield _this._initUI();
            } else {
              if (_this._anim) {
                _this._initSneak();

                _this.playAnim("idle1");

                _this._anim.node.opacity = 255;
              } else {
                _this._initOver = true;
              }
            }
          })();
        }
        /**
         * 重置敌机状态
         */


        _reset() {
          this.white.node.opacity = 0;
          this.pedestal.spriteFrame = null;
          this._curAnim = "";
          this._initOver = false;
          this.stopAnim();
          if (this._anim) this._anim.node.opacity = 0;
          if (this._sneakAnim) this._sneakAnim.node.active = false;
          if (this._rotateComp) this._rotateComp.reset();
          this._warnAble = false;
          if (this._warnLine) this._warnLine.active = false;
        }
        /**
         * 更新游戏逻辑
         * @param {number} dt 时间增量
         */


        updateGameLogic(dt) {
          if (this._rotateComp && this._rotateComp.enabled) {
            this._rotateComp.updateGameLogic(dt);
          }

          if (this._bWinkWhite) {
            this._winkCount++;

            if (this._winkCount > 8) {
              this._winkCount = 0;
              this._bWinkWhite = false;
            }
          }
        }
        /**
         * 初始化 UI
         * @param {boolean} isPreload 是否预加载
         */


        _initUI(isPreload) {
          var _this2 = this;

          return _asyncToGenerator(function* () {
            if (isPreload === void 0) {
              isPreload = false;
            }

            if (_this2._data.isAm) {
              _this2.role.spriteFrame = null;
              _this2.white.spriteFrame = null;
              _this2._winkAct = tween().to(0, {
                opacity: 204
              }).to(3 * (_crd && GameConfig === void 0 ? (_reportPossibleCrUseOfGameConfig({
                error: Error()
              }), GameConfig) : GameConfig).ActionFrameTime, {
                opacity: 0
              });

              if (_this2._curUId === _this2._data.id && _this2._anim) {
                _this2._anim.node.opacity = 255;
              } else {
                _this2._curUId = _this2._data.id;

                if (_this2._anim) {
                  _this2._anim.node.destroy();

                  _this2._anim = null;
                }

                var pf = yield (_crd && GameIns === void 0 ? (_reportPossibleCrUseOfGameIns({
                  error: Error()
                }), GameIns) : GameIns).enemyManager.getPlaneRole(_this2._data.image);
                var animNode = instantiate(pf);

                _this2.node.addChild(animNode);

                _this2._anim = animNode.getComponent(_crd && EnemyAnim === void 0 ? (_reportPossibleCrUseOfEnemyAnim({
                  error: Error()
                }), EnemyAnim) : EnemyAnim);

                _this2._anim.init(_this2._data.extraParam);
              }
            } else {
              if (_this2._anim) _this2._anim.node.active = false;
              _this2.role.node.opacity = 255;
              (_crd && GameIns === void 0 ? (_reportPossibleCrUseOfGameIns({
                error: Error()
              }), GameIns) : GameIns).enemyManager.setPlaneFrame(_this2.role, _this2._data.image);
              (_crd && GameIns === void 0 ? (_reportPossibleCrUseOfGameIns({
                error: Error()
              }), GameIns) : GameIns).enemyManager.setPlaneFrame(_this2.white, _this2._data.image);
              _this2._winkAct = tween().to(0, {
                opacity: 255
              }).to(3 * (_crd && GameConfig === void 0 ? (_reportPossibleCrUseOfGameConfig({
                error: Error()
              }), GameConfig) : GameConfig).ActionFrameTime, {
                opacity: 0
              }); // if (this._data.id >= 50000 && this._data.id < 60000) {
              //     GameIns.enemyManager.setPlaneFrame(this.pedestal, this._data.extraParam1);
              //     if (!this._rotateComp) {
              //         this._rotateComp = Tools.addScript(this.node, TurretRotateComp);
              //         this._rotateComp.init(this.role.node);
              //     }
              //     this._rotateComp.setData(this._param);
              //     this._rotateComp.enabled = true;
              //     this._warnAble = true;
              // } else 

              if (_this2._rotateComp) {
                _this2._rotateComp.enabled = false;
              }

              var fireNode = _this2.node.getChildByName("fire");

              if (!fireNode) {
                fireNode = new Node();

                _this2.node.addChild(fireNode);

                fireNode.name = "fire";
              }

              var frameTime = (_crd && GameConfig === void 0 ? (_reportPossibleCrUseOfGameConfig({
                error: Error()
              }), GameConfig) : GameConfig).ActionFrameTime;

              for (var i = 0; i < _this2._data.extraParam.length; i++) {
                var param = _this2._data.extraParam[i];
                var tailFire = _this2._tailFireArr[i];

                if (!tailFire) {
                  tailFire = new Node();
                  fireNode.addChild(tailFire);
                  tailFire.addComponent(Sprite);
                  tailFire.getComponent(UITransform).anchorY = 0;

                  _this2._tailFireArr.push(tailFire);
                }

                Tween.stopAllByTarget(tailFire);
                tailFire.active = true;
                (_crd && GameIns === void 0 ? (_reportPossibleCrUseOfGameIns({
                  error: Error()
                }), GameIns) : GameIns).enemyManager.setPlaneFrame(tailFire.getComponent(Sprite), "fire" + param[0]);
                tailFire.setPosition(param[1], param[2]);
                tailFire.getComponent(UITransform).setContentSize(param[3], param[4]);
                tailFire.angle = param[7] || 0;
                tween(tailFire).to(frameTime * param[5], {
                  scale: v3(param[6], param[6])
                }).to(frameTime * param[5], {
                  scale: v3(1, 1)
                }).repeatForever().start();
              }

              for (var _i = _this2._data.extraParam.length; _i < _this2._tailFireArr.length; _i++) {
                var _tailFire = _this2._tailFireArr[_i];
                Tween.stopAllByTarget(_tailFire);
                _tailFire.active = false;
              }
            }
          })();
        }
        /**
         * 设置动画事件回调
         * @param {string} eventName 事件名称
         * @param {Function} callback 回调函数
         */


        setEventCallback(eventName, callback) {
          this._anim && this._anim.setAnimEventCall(eventName, callback);
        }
        /**
         * 开始攻击
         */


        startAttack() {
          this._rotateComp && this._rotateComp.setAngleAble(true);
        }
        /**
         * 攻击结束
         */


        attackOver() {
          this._rotateComp && this._rotateComp.setAngleAble(false);
        }
        /**
         * 闪烁白色效果
         */


        winkWhite() {
          if (!this._bWinkWhite) {
            this._bWinkWhite = true;

            if (!this._data.isAm) {
              this._winkAct.clone(this.white.node).start();
            }
          }
        }
        /**
         * 播放动画
         * @param {string} animName 动画名称
         * @param {Function} [callback] 动画结束回调
         * @returns {boolean} 是否成功播放
         */


        playAnim(animName, callback) {
          if (callback === void 0) {
            callback = null;
          }

          if (!this._data.isAm) return false;

          if (this._curAnim === animName) {
            (_crd && Tools === void 0 ? (_reportPossibleCrUseOfTools({
              error: Error()
            }), Tools) : Tools).log("save anim", animName);
            return true;
          }

          if (this._anim) {
            this._anim.playAnim(animName, callback);

            return true;
          }

          (_crd && Tools === void 0 ? (_reportPossibleCrUseOfTools({
            error: Error()
          }), Tools) : Tools).log("anim is null", animName);
          return false;
        }
        /**
         * 暂停动画
         */


        pauseAnim() {
          this._anim && this._anim.pauseAnim();
        }
        /**
         * 恢复动画
         */


        resumeAnim() {
          this._anim && this._anim.resumeAnim();
        }
        /**
         * 停止动画
         */


        stopAnim() {
          this._anim && this._anim.stopAnim();
        }
        /**
         * 初始化潜行
         */


        _initSneak() {// 潜行初始化逻辑
        }
        /**
         * 初始化潜行动画
         */


        _initSneakAnim() {// try {
          //     if (this._sneakGoUpAnimArr.length > 0) return;
          //     for (let i = 0; i < this._data.sneakParam.length; i++) {
          //         const param = this._data.sneakParam[i];
          //         const animNode = await GameIns.enemyManager.getPlaneRole("water" + param[0]);
          //         if (!animNode) {
          //             console.error("_initSneakAnim", param);
          //             continue;
          //         }
          //         this.node.addChild(animNode);
          //         const anim = animNode.getComponent(Animation);
          //         this._sneakGoUpAnimArr.push(anim);
          //         animNode.position = v2(param[1], param[2]);
          //         animNode.zIndex = param[3];
          //         animNode.scaleX = param[4];
          //         animNode.scaleY = param[5];
          //         animNode.angle = param[6];
          //         this._sneakGoUpDelayArr.push(param[7]);
          //         animNode.active = false;
          //     }
          // } catch (error) {
          //     Tools.log("_initSneakAnim error", error);
          // }

          return _asyncToGenerator(function* () {})();
        }
        /**
         * 设置潜行方向
         * @param {number} angle 方向角度
         */


        setSneakDir(angle) {
          if (this._sneakAnim && this._sneakAnim.node.active) {
            this._sneakAnim.node.angle = angle;
          }
        }
        /**
         * 播放潜行动画
         */


        playSneakAnim() {
          if (this._sneakAnim) {
            this._sneakAnim.node.active = true;
            this.role.node.opacity = 0;

            if (this._anim) {
              this._anim.node.opacity = 0;
            }

            this._sneakAnim.reset();
          }
        }
        /**
         * 播放潜行上浮动画
         */


        playSneakGoUpAnim() {
          var _this3 = this;

          if (this._sneakAnim) {
            this._sneakAnim.node.active = false;
          }

          if (this._data.isAm) {
            if (this._anim) {
              this._anim.node.opacity = 255;
            }
          } else {
            this.role.node.opacity = 255;
          }

          var frameTime = (_crd && GameConfig === void 0 ? (_reportPossibleCrUseOfGameConfig({
            error: Error()
          }), GameConfig) : GameConfig).ActionFrameTime; // frameWork.audioManager.playEffect("sneakUp");

          var _loop = function _loop() {
            var anim = _this3._sneakGoUpAnimArr[i];

            _this3.scheduleOnce(() => {
              anim.node.active = true;
              anim.play("play");
            }, frameTime * _this3._sneakGoUpDelayArr[i]);
          };

          for (var i = 0; i < this._sneakGoUpAnimArr.length; i++) {
            _loop();
          }
        }
        /**
         * 播放蓝色特效
         * @param {Function} callback 回调函数
         */


        blueShow(callback) {// const frameTime = GameConfig.ActionFrameTime;
          // const blueColor = color(19, 233, 139);
          // const tween = tween()
          //     .to(10 * frameTime, { color: blueColor, opacity: 204 })
          //     .to(3 * frameTime, { color: Color.WHITE, opacity: 255 });
          // const endTween = tween().call(() => {
          //     if (callback) callback();
          // });
          // if (this._data.isAm && this._anim) {
          //     for (let i = 0; i < this._anim.node.childrenCount; i++) {
          //         const child = this._anim.node.children[i];
          //         child.opacity = 0;
          //         child.color = blueColor;
          //         if (i === this._anim.node.childrenCount - 1) {
          //             tween.clone(child).then(endTween).start();
          //         } else {
          //             tween.clone(child).start();
          //         }
          //     }
          // } else {
          //     this.role.node.opacity = 0;
          //     this.role.node.color = blueColor;
          //     tween.clone(this.role.node).then(endTween).start();
          // }
        }
        /**
         * 播放隐身动画
         */


        playCloakeAnim() {// if (!this._cloakeAnim) {
          //     const animNode = instantiate(GameConst.frameAnim);
          //     this.node.addChild(animNode, 11);
          //     this._cloakeAnim = animNode.getComponent(PfFrameAnim);
          //     this._cloakeAnim.init(GameIns.enemyManager.enemyAtlas, "a_", 12, GameConfig.ActionFrameTime);
          //     animNode.active = false;
          // }
          // if (this._data.sneakParam.length > 0 && this._data.sneakParam[0].length > 1) {
          //     this._cloakeAnim.node.scaleX = this._data.sneakParam[0][0];
          //     this._cloakeAnim.node.scaleY = this._data.sneakParam[0][1];
          // }
          // this._cloakeAnim.node.active = true;
          // this._cloakeAnim.reset(1);
        }
        /**
         * 播放隐身消失动画
         * @param {Function} callback 回调函数
         */


        playCloakeHideAnim(callback) {// frameWork.audioManager.playEffect("cloake");
          // this.playCloakeAnim();
          // const frameTime = GameConfig.ActionFrameTime;
          // tween(this.node)
          //     .to(5 * frameTime, { opacity: 90 })
          //     .to(2 * frameTime, { opacity: 0 })
          //     .call(() => {
          //         if (this._anim) {
          //             this._anim.node.opacity = 0;
          //         }
          //         GameIns.enemyManager.setPlaneFrame(this.role, this._data.sneakAnim);
          //         const fireNode = this.node.getChildByName("fire");
          //         if (fireNode) {
          //             fireNode.opacity = 0;
          //         }
          //     })
          //     .to(6 * frameTime, { opacity: 255 })
          //     .call(() => {
          //         if (callback) callback();
          //     })
          //     .start();

          if (callback === void 0) {
            callback = null;
          }
        }
        /**
         * 播放隐身显现动画
         * @param {Function} callback 回调函数
         */


        playCloakeShowAnim(callback) {// const frameTime = GameConfig.ActionFrameTime;
          // tween(this.node)
          //     .to(4 * frameTime, { opacity: 102 })
          //     .to(2 * frameTime, { opacity: 255 })
          //     .to(4 * frameTime, { opacity: 102 })
          //     .to(2 * frameTime, { opacity: 255 })
          //     .to(3 * frameTime, { opacity: 102 })
          //     .to(frameTime, { opacity: 0 })
          //     .call(() => {
          //         if (this._anim) {
          //             this._anim.node.opacity = 255;
          //             this.role.spriteFrame = null;
          //         } else {
          //             GameIns.enemyManager.setPlaneFrame(this.role, this._data.image);
          //             const fireNode = this.node.getChildByName("fire");
          //             if (fireNode) {
          //                 fireNode.opacity = 255;
          //             }
          //         }
          //         this.playCloakeAnim();
          //     })
          //     .to(7 * frameTime, { opacity: 255 })
          //     .call(() => {
          //         if (callback) callback();
          //     })
          //     .start();

          if (callback === void 0) {
            callback = null;
          }
        }
        /**
         * 播放攻击警告动画
         */


        playAtkWarnAnim() {
          if (!this._warnAble) return;

          if (!this._warnLine) {
            this._warnLine = new Node();

            this._warnLine.addComponent(UITransform);

            this.role.node.addChild(this._warnLine, -1);

            this._warnLine.addComponent(Sprite);

            this._warnLine.y = -72;
            this._warnLine.anchorY = 1;
          }

          (_crd && GameIns === void 0 ? (_reportPossibleCrUseOfGameIns({
            error: Error()
          }), GameIns) : GameIns).enemyManager.setPlaneFrame(this._warnLine.getComponent(Sprite), "warnLine");
          this._warnLine.active = true;
          this._warnLine.opacity = 255;
          this._warnLine.scaleY = 0;
          this._warnLine.height = 1800;
          this._warnLine.width = 10;

          this._warnLine.stopAllActions();

          tween(this._warnLine).to(0.2, {
            scaleY: 1
          }).delay(0.5).to(0.2, {
            opacity: 0
          }).call(() => {
            this._warnLine.active = false;
          }).start();
        }

      }, (_descriptor = _applyDecoratedDescriptor(_class2.prototype, "pedestal", [_dec], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function initializer() {
          return null;
        }
      }), _descriptor2 = _applyDecoratedDescriptor(_class2.prototype, "role", [_dec2], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function initializer() {
          return null;
        }
      }), _descriptor3 = _applyDecoratedDescriptor(_class2.prototype, "white", [_dec3], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function initializer() {
          return null;
        }
      })), _class2)) || _class));

      _cclegacy._RF.pop();

      _crd = false;
    }
  };
});
//# sourceMappingURL=28bdc3927399aa3bd40432f88a891f0ebbf9a615.js.map