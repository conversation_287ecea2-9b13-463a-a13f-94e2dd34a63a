{"version": 3, "sources": ["file:///E:/M2Game/Client/assets/scripts/GameOld/world/base/Object.ts"], "names": ["_decorator", "Component", "World", "ccclass", "Object", "world", "onWorldCreate", "onWorldDestroy", "onLoad", "getInstance", "onDestroy"], "mappings": ";;;;;;;;;;;;;;;;AAASA,MAAAA,U,OAAAA,U;AAAYC,MAAAA,S,OAAAA,S;;AACZC,MAAAA,K,iBAAAA,K;;;;;;;;;OACH;AAAEC,QAAAA;AAAF,O,GAAcH,U;AAEpB;AACA;AACA;AACA;;wBAEsBI,M,WADrBD,OAAO,CAAC,QAAD,C,gBAAR,MACsBC,MADtB,SACqCH,SADrC,CAC+C;AAAA;AAAA;AAAA,eAEjCI,KAFiC,GAEX,IAFW;AAAA;;AAI3C;AACJ;AACA;AACA;AACcC,QAAAA,aAAa,CAACD,KAAD,EACvB;AACI,eAAKA,KAAL,GAAaA,KAAb;AACH;AAED;AACJ;AACA;AACA;;;AACcE,QAAAA,cAAc,CAACF,KAAD,EACxB;AACI,eAAKA,KAAL,GAAa,IAAb;AACH;;AAESG,QAAAA,MAAM,GAAS;AACrB;AACA,eAAKF,aAAL,CAAmB;AAAA;AAAA,8BAAMG,WAAN,EAAnB;AACH;;AAESC,QAAAA,SAAS,GAAS;AACxB,eAAKH,cAAL,CAAoB;AAAA;AAAA,8BAAME,WAAN,EAApB;AACH;;AA7B0C,O", "sourcesContent": ["import { _decorator, Component } from 'cc';\r\nimport { World } from './World';\r\nconst { ccclass } = _decorator;\r\n\r\n/**\r\n * Abstract base class for all world Object\r\n * Inherits from Cocos Creator Component and provides common functionality\r\n */\r\n@ccclass('Object')\r\nexport abstract class Object extends Component {\r\n\r\n    protected world: World | null = null;\r\n\r\n    /**\r\n     * Called when the object is initialized\r\n     * Override this method to implement object-specific initialization logic\r\n     */\r\n    protected onWorldCreate(world: World): void\r\n    {\r\n        this.world = world;\r\n    }\r\n\r\n    /**\r\n     * Called when the object is destroyed\r\n     * Override this method to implement object-specific cleanup logic\r\n     */\r\n    protected onWorldDestroy(world: World): void\r\n    {\r\n        this.world = null;\r\n    }\r\n\r\n    protected onLoad(): void {\r\n        // TODO: 待定是否采用这种方式\r\n        this.onWorldCreate(World.getInstance());\r\n    }\r\n\r\n    protected onDestroy(): void {\r\n        this.onWorldDestroy(World.getInstance());\r\n    }\r\n}"]}