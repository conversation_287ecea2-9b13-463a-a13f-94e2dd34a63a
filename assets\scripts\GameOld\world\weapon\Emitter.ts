import { _decorator, instantiate, Node, Prefab } from 'cc';
import { Weapon } from './Weapon';
import { EmitterData } from '../../../Game/data/EmitterData';
const { ccclass, executeInEditMode, property } = _decorator;

export enum eEmitterStatus {
    None, Prewarm, Emitting, LoopEndReached, Completed
}

@ccclass('Emitter')
@executeInEditMode
export class Emitter extends Weapon {

    @property({type: Prefab, displayName: "Bullet Prefab"})
    bulletPrefab: Prefab = null;

    @property
    emitterData: EmitterData = null;

    status: eEmitterStatus = eEmitterStatus.None;
    statusElapsedTime: number = 0;
    totalElapsedTime: number = 0;
    isEmitting: boolean = false;

    /**
     * public apis
     */
    changeStatus(status: eEmitterStatus) {
        this.status = status;
        this.statusElapsedTime = 0;
    }

    protected canEmit(): boolean {
        // 检查是否可以触发发射
        // Override this method in subclasses to add custom trigger conditions
        return true;
    }

    /**
     * TODO: implement bullet emission logic in subclasses
     */
    protected emit(): void {
        for (let i = 0; i < this.emitterData.perEmitCount; i++) {
            let localIndex = i;
            if (this.emitterData.perEmitInterval > 0) {
                this.scheduleOnce(() => {
                    this.emitSingle(localIndex);
                    // Logic to emit the bullet after the interval
                }, this.emitterData.perEmitInterval * localIndex);
            }
        }

    }

    protected tryEmit(): boolean {
        if (this.canEmit()) {
            this.emit();
            return true;
        }
        return false;
    }

    protected emitSingle(index:number) {
        const direction = this.getSpawnDirection(index);
        const position = this.getSpawnPosition(index);
        this.createBullet(direction, position);
    }

    /**
     * Calculate the direction for a bullet at the given index
     * @param index The index of the bullet (0 to count-1)
     * @returns Direction vector {x, y}
     */
    getSpawnDirection(index: number): { x: number, y: number } {
        // 计算发射方向
        const angleOffset = this.emitterData.count > 1 ? (this.emitterData.arc / (this.emitterData.count - 1)) * index - this.emitterData.arc / 2 : 0;
        const radian = (this.emitterData.angle + angleOffset) * (Math.PI / 180);
        return {
            x: Math.cos(radian),
            y: Math.sin(radian)
        };
    }

    /**
     * Get the spawn position for a bullet at the given index
     * @param index The index of the bullet (0 to count-1)
     * @returns Position offset from emitter center
     */
    getSpawnPosition(index: number): { x: number, y: number } {
        if (this.emitterData.radius <= 0) {
            return { x: 0, y: 0 };
        }

        const direction = this.getSpawnDirection(index);
        return {
            x: direction.x * this.emitterData.radius,
            y: direction.y * this.emitterData.radius
        };
    }

    createBullet(direction: { x: number, y: number }, position: { x: number, y: number }) {
        if (!this.bulletPrefab) {
            console.warn("EmitterArc: No bullet prefab assigned");
            return null;
        }

        // Instantiate the bullet from prefab
        const bulletNode = instantiate(this.bulletPrefab);
        if (!bulletNode) {
            console.error("EmitterArc: Failed to instantiate bullet prefab");
            return null;
        }

        // Get the bullet component
        // const bullet = bulletNode.getComponent(Bullet);
        // if (!bullet) {
        //     console.error("EmitterArc: Bullet prefab does not have Bullet component");
        //     bulletNode.destroy();
        //     return null;
        // }

        // Set bullet position relative to emitter
        const emitterPos = this.node.getWorldPosition();
        bulletNode.setWorldPosition(
            emitterPos.x + position.x,
            emitterPos.y + position.y,
            emitterPos.z
        );

        this.node.parent?.addChild(bulletNode);

        return bulletNode;
    }

    protected startEmitting() {
        this.isEmitting = true;

        this.schedule(() => {
            this.tryEmit();
        }, this.emitterData.emitInterval);
    }

    // Implementation of CObject abstract methods
    protected onObjectInit(): void {
        // Override in subclasses if needed
    }

    protected onObjectDestroy(): void {
        // Clean up any scheduled callbacks
    }

    /**
     * Return true if this.node is in screen
     */
    protected isInScreen() : boolean {
        // TODO: Get mainCamera.containsNode(this.node)
        return true;
    }

    protected update(deltaTime: number): void {
        if (!this.emitterData) {
            return;
        }

        this.statusElapsedTime += deltaTime;
        this.totalElapsedTime += deltaTime;

        switch (this.status)
        {
            case eEmitterStatus.None:
                this.updateStatusNone();
                break;
            case eEmitterStatus.Prewarm:
                this.updateStatusPrewarm();
                break;
            case eEmitterStatus.Emitting:
                this.updateStatusEmitting(deltaTime);
                break;
            case eEmitterStatus.LoopEndReached:
                this.updateStatusLoopEndReached();
                break;
            case eEmitterStatus.Completed:
                this.updateStatusCompleted();
                break;
            default:
                break;
        }
    }

    protected updateStatusNone() {
        if (this.statusElapsedTime >= this.emitterData.initialDelay) {
            this.changeStatus(eEmitterStatus.Prewarm);
        }
    }

    protected updateStatusPrewarm() {
        if (!this.emitterData.isPreWarm)
            this.changeStatus(eEmitterStatus.Emitting);
        else {
            if (this.statusElapsedTime >= this.emitterData.preWarmDuration) {
                this.changeStatus(eEmitterStatus.Emitting);
            }
        }
    }

    protected updateStatusEmitting(deltaTime: number) {
        if (this.statusElapsedTime > this.emitterData.emitDuration) {
            if (this.emitterData.isLoop) {
                this.changeStatus(eEmitterStatus.LoopEndReached);
                
            }
            else {
                this.changeStatus(eEmitterStatus.Completed);
                return;
            }
        }

        if (this.isEmitting) {
            return;
        }

        this.startEmitting();
    }

    protected updateStatusLoopEndReached() {
        this.isEmitting = false;
    }

    protected updateStatusCompleted() {
        
    }
}
