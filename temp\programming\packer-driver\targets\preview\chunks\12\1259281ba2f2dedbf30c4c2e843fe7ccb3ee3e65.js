System.register(["__unresolved_0", "cc", "__unresolved_1"], function (_export, _context) {
  "use strict";

  var _reporterNs, _cclegacy, __checkObsolete__, __checkObsoleteInNamespace__, _decorator, Object, _dec, _class, _crd, ccclass, Entity;

  function _reportPossibleCrUseOfObject(extras) {
    _reporterNs.report("Object", "./Object", _context.meta, extras);
  }

  function _reportPossibleCrUseOfWorld(extras) {
    _reporterNs.report("World", "./World", _context.meta, extras);
  }

  return {
    setters: [function (_unresolved_) {
      _reporterNs = _unresolved_;
    }, function (_cc) {
      _cclegacy = _cc.cclegacy;
      __checkObsolete__ = _cc.__checkObsolete__;
      __checkObsoleteInNamespace__ = _cc.__checkObsoleteInNamespace__;
      _decorator = _cc._decorator;
    }, function (_unresolved_2) {
      Object = _unresolved_2.Object;
    }],
    execute: function () {
      _crd = true;

      _cclegacy._RF.push({}, "ba1d9gI6nRB3axN4UEl9iDR", "Entity", undefined);

      __checkObsolete__(['_decorator', 'Component']);

      ({
        ccclass
      } = _decorator);
      /**
       * Abstract base class for all world Entity
       * Inherits from Cocos Creator Component and provides common functionality
       */

      _export("Entity", Entity = (_dec = ccclass('Entity'), _dec(_class = class Entity extends (_crd && Object === void 0 ? (_reportPossibleCrUseOfObject({
        error: Error()
      }), Object) : Object) {
        constructor() {
          super(...arguments);
          this._eid = 0;
        }

        get eid() {
          return this._eid;
        }

        onWorldCreate(world) {
          super.onWorldCreate(world);
          this._eid = world.generateId();
          world.registerEntity(this);
        }

      }) || _class));

      _cclegacy._RF.pop();

      _crd = false;
    }
  };
});
//# sourceMappingURL=1259281ba2f2dedbf30c4c2e843fe7ccb3ee3e65.js.map