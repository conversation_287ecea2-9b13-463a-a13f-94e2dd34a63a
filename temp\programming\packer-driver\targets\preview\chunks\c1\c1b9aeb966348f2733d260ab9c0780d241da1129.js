System.register(["__unresolved_0", "cc", "__unresolved_1", "__unresolved_2", "cc/env"], function (_export, _context) {
  "use strict";

  var _reporterNs, _cclegacy, WXLogin, DevLogin, WECHAT, PlatformSDKUserInfo, _crd;

  function CreateLoginSDK() {
    if (WECHAT) {
      return new (_crd && WXLogin === void 0 ? (_reportPossibleCrUseOfWXLogin({
        error: Error()
      }), WXLogin) : WXLogin)();
    } else {
      return new (_crd && DevLogin === void 0 ? (_reportPossibleCrUseOfDevLogin({
        error: Error()
      }), DevLogin) : DevLogin)();
    }
  }

  function _reportPossibleCrUseOfWXLogin(extras) {
    _reporterNs.report("WXLogin", "./WXLogin", _context.meta, extras);
  }

  function _reportPossibleCrUseOfDevLogin(extras) {
    _reporterNs.report("DevLogin", "./DevLogin", _context.meta, extras);
  }

  function _reportPossibleCrUseOfLoginInfo(extras) {
    _reporterNs.report("LoginInfo", "../Network/NetMgr", _context.meta, extras);
  }

  _export({
    PlatformSDKUserInfo: void 0,
    CreateLoginSDK: CreateLoginSDK
  });

  return {
    setters: [function (_unresolved_) {
      _reporterNs = _unresolved_;
    }, function (_cc) {
      _cclegacy = _cc.cclegacy;
    }, function (_unresolved_2) {
      WXLogin = _unresolved_2.WXLogin;
    }, function (_unresolved_3) {
      DevLogin = _unresolved_3.DevLogin;
    }, function (_ccEnv) {
      WECHAT = _ccEnv.WECHAT;
    }],
    execute: function () {
      _crd = true;

      _cclegacy._RF.push({}, "3ca55yy+4pJNYKOWnEXMEVu", "IPlatformSDK", undefined);

      _export("PlatformSDKUserInfo", PlatformSDKUserInfo = class PlatformSDKUserInfo {
        constructor() {
          this.name = void 0;
          this.icon = void 0;
        }

      });

      _cclegacy._RF.pop();

      _crd = false;
    }
  };
});
//# sourceMappingURL=c1b9aeb966348f2733d260ab9c0780d241da1129.js.map