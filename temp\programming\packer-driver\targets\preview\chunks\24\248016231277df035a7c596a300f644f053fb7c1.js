System.register(["__unresolved_0", "cc", "__unresolved_1"], function (_export, _context) {
  "use strict";

  var _reporterNs, _cclegacy, TypeID, EntityContainer, _crd;

  function _reportPossibleCrUseOfEntity(extras) {
    _reporterNs.report("Entity", "./Entity", _context.meta, extras);
  }

  function _reportPossibleCrUseOfTypeID(extras) {
    _reporterNs.report("TypeID", "./TypeID", _context.meta, extras);
  }

  _export("EntityContainer", void 0);

  return {
    setters: [function (_unresolved_) {
      _reporterNs = _unresolved_;
    }, function (_cc) {
      _cclegacy = _cc.cclegacy;
    }, function (_unresolved_2) {
      TypeID = _unresolved_2.TypeID;
    }],
    execute: function () {
      _crd = true;

      _cclegacy._RF.push({}, "9ff2dHgAERMpKIHNTWAXgTI", "EntityContainer", undefined);

      /**
       * EntityContainer manages a collection of game entities
       */
      _export("EntityContainer", EntityContainer = class EntityContainer {
        constructor() {
          this._allEntities = [];
          this._allEntitiesById = new Map();
          // _singletonEntities are differed by type
          this._singletonEntities = new Map();
        }

        registerEntity(entity) {
          this._allEntitiesById.set(entity.eid, entity);

          this._allEntities.push(entity);
        }

        unregisterEntity(entity) {
          var index = this._allEntities.indexOf(entity);

          if (index >= 0) {
            this._allEntities.splice(index, 1);
          }
        }

        getEntities() {
          return this._allEntities;
        }

        registerSingletonEntity(entity) {
          var typeId = (_crd && TypeID === void 0 ? (_reportPossibleCrUseOfTypeID({
            error: Error()
          }), TypeID) : TypeID).getFromInstance(entity);

          this._singletonEntities.set(typeId, entity);
        }

        unregisterSingletonEntity(entity) {
          var typeId = (_crd && TypeID === void 0 ? (_reportPossibleCrUseOfTypeID({
            error: Error()
          }), TypeID) : TypeID).getFromInstance(entity);

          this._singletonEntities.delete(typeId);
        }

        getSingletonEntity(entityConstructor) {
          var typeId = (_crd && TypeID === void 0 ? (_reportPossibleCrUseOfTypeID({
            error: Error()
          }), TypeID) : TypeID).get(entityConstructor);
          return this._singletonEntities.get(typeId);
        }

      });

      _cclegacy._RF.pop();

      _crd = false;
    }
  };
});
//# sourceMappingURL=248016231277df035a7c596a300f644f053fb7c1.js.map