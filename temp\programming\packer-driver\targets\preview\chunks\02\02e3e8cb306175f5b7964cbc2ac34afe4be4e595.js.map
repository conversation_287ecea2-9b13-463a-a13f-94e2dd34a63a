{"version": 3, "sources": ["file:///E:/M2Game/Client/assets/scripts/ui/components/common/SelectList/uiSelect.ts"], "names": ["_decorator", "Color", "Component", "instantiate", "Label", "Layout", "Node", "Prefab", "Sprite", "UITransform", "uiSelectItem", "ccclass", "property", "uiSelect", "type", "tooltip", "Number", "String", "chooseItemData", "_onChooseItem", "SetOnChooseItem", "callBack", "onLoad", "setSelectShow", "btnSelect", "on", "EventType", "TOUCH_END", "onShowSelect", "setItemData", "itemDatas", "indexOf", "defaultItem", "length", "setChooseItemData", "onDestroy", "<PERSON><PERSON><PERSON><PERSON>", "off", "scrollViewSelect", "active", "show", "contentSelect", "destroyAllChildren", "itemData", "itemNode", "prefabItem", "setParent", "getComponent", "updateValue", "itemHeight", "data", "height", "spacingY", "itemMaxNum", "children", "color", "updateChooseItem", "btnLabel", "getChildByName", "string", "onChooseItem"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;AAASA,MAAAA,U,OAAAA,U;AAAYC,MAAAA,K,OAAAA,K;AAAOC,MAAAA,S,OAAAA,S;AAAWC,MAAAA,W,OAAAA,W;AAAaC,MAAAA,K,OAAAA,K;AAAOC,MAAAA,M,OAAAA,M;AAAQC,MAAAA,I,OAAAA,I;AAAMC,MAAAA,M,OAAAA,M;AAAoBC,MAAAA,M,OAAAA,M;AAAQC,MAAAA,W,OAAAA,W;;AAC5FC,MAAAA,Y,iBAAAA,Y;;;;;;;;;OACH;AAAEC,QAAAA,OAAF;AAAWC,QAAAA;AAAX,O,GAAwBZ,U;;0BAGjBa,Q,WADZF,OAAO,CAAC,UAAD,C,UAIHC,QAAQ,CAAC;AAAEE,QAAAA,IAAI,EAAER,IAAR;AAAcS,QAAAA,OAAO,EAAE;AAAvB,OAAD,C,UAGRH,QAAQ,CAAC;AAAEE,QAAAA,IAAI,EAAER,IAAR;AAAcS,QAAAA,OAAO,EAAE;AAAvB,OAAD,C,UAGRH,QAAQ,CAAC;AAAEE,QAAAA,IAAI,EAAER,IAAR;AAAcS,QAAAA,OAAO,EAAE;AAAvB,OAAD,C,UAGRH,QAAQ,CAAC;AAAEE,QAAAA,IAAI,EAAEP,MAAR;AAAgBQ,QAAAA,OAAO,EAAE;AAAzB,OAAD,C,UAGRH,QAAQ,CAAC;AAAEE,QAAAA,IAAI,EAAEE,MAAR;AAAgBD,QAAAA,OAAO,EAAE;AAAzB,OAAD,C,UAGRH,QAAQ,CAAC;AAAEE,QAAAA,IAAI,EAAEG,MAAR;AAAgBF,QAAAA,OAAO,EAAE;AAAzB,OAAD,C,UAGRH,QAAQ,CAAC;AAAEE,QAAAA,IAAI,EAAE,CAACG,MAAD,CAAR;AAAkBF,QAAAA,OAAO,EAAE;AAA3B,OAAD,C,2BAtBb,MACaF,QADb,SAC8BX,SAD9B,CACwC;AAAA;AAAA;;AAEpC;AAFoC;;AAKpC;AALoC;;AAQpC;AARoC;;AAWpC;AAXoC;;AAcpC;AAdoC;;AAiBpC;AAjBoC;;AAoBpC;AApBoC;;AAwBpC;AAxBoC,eAyB5BgB,cAzB4B,GAyBH,EAzBG;AAAA,eA0B5BC,aA1B4B;AAAA;;AA2B7BC,QAAAA,eAAe,CAACC,QAAD,EAAuC;AACzD,eAAKF,aAAL,GAAqBE,QAArB;AACH;;AAESC,QAAAA,MAAM,GAAS;AAErB,eAAKC,aAAL,CAAmB,KAAnB,EAFqB,CAIrB;;AACA,eAAKC,SAAL,CAAeC,EAAf,CAAkBnB,IAAI,CAACoB,SAAL,CAAeC,SAAjC,EAA4C,KAAKC,YAAjD,EAA+D,IAA/D;AAEA,eAAKC,WAAL,CAAiB,KAAKC,SAAtB,EAPqB,CAQrB;;AACA,cAAI,KAAKA,SAAL,CAAeC,OAAf,CAAuB,KAAKC,WAA5B,KAA4C,CAAC,CAAjD,EAAoD;AAChD,iBAAKd,cAAL,GAAsB,KAAKc,WAA3B;AACH,WAXoB,CAYrB;;;AACA,cAAI,KAAKd,cAAL,CAAoBe,MAApB,GAA6B,CAAjC,EAAoC;AAChC,iBAAKC,iBAAL,CAAuB,KAAKhB,cAA5B;AACH,WAFD,MAEO;AACH,iBAAKgB,iBAAL,CAAuB,GAAvB;AACH;AACJ;;AAESC,QAAAA,SAAS,GAAS;AACxB;AACA,cAAI,KAAKX,SAAL,IAAkB,KAAKA,SAAL,CAAeY,OAArC,EAA8C;AAC1C,iBAAKZ,SAAL,CAAea,GAAf,CAAmB/B,IAAI,CAACoB,SAAL,CAAeC,SAAlC,EAA6C,KAAKC,YAAlD,EAAgE,IAAhE;AACH;AACJ,SAxDmC,CA0DpC;;;AACQA,QAAAA,YAAY,GAAS;AACzB,cAAI,KAAKU,gBAAL,CAAsBC,MAA1B,EAAkC;AAC9B,iBAAKhB,aAAL,CAAmB,KAAnB;AACH,WAFD,MAEO;AACH,iBAAKA,aAAL,CAAmB,IAAnB;AACH;AACJ;AAED;AACJ;AACA;AACA;;;AACWA,QAAAA,aAAa,CAACiB,IAAD,EAAsB;AACtC,eAAKF,gBAAL,CAAsBC,MAAtB,GAA+BC,IAA/B;AACH;AAED;AACJ;AACA;AACA;;;AACWX,QAAAA,WAAW,CAACC,SAAD,EAA4B;AAAA;;AAC1C;AACA,eAAKW,aAAL,CAAmBC,kBAAnB,GAF0C,CAI1C;;AACA,eAAKZ,SAAL,GAAiBA,SAAjB,CAL0C,CAM1C;;AACA,eAAK,IAAIa,SAAT,IAAqB,KAAKb,SAA1B,EAAqC;AACjC,gBAAIc,QAAc,GAAGzC,WAAW,CAAC,KAAK0C,UAAN,CAAhC;AACAD,YAAAA,QAAQ,CAACE,SAAT,CAAmB,KAAKL,aAAxB,EAFiC,CAIjC;;AACAG,YAAAA,QAAQ,CAACG,YAAT;AAAA;AAAA,8CAAqCC,WAArC,CAAiD,IAAjD,EAAuDL,SAAvD;AACH,WAbyC,CAe1C;AACA;;;AACA,cAAIM,UAAkB,GAAI,KAAKJ,UAAL,CAAgBK,IAAhB,CAAqBH,YAArB,CAAkCtC,WAAlC,EAA+C0C,MAA/C,6BAAwD,KAAKV,aAAL,CAAmBM,YAAnB,CAAgC1C,MAAhC,CAAxD,qBAAwD,sBAAyC+C,QAAjG,CAA1B;AACA,eAAKd,gBAAL,CAAsBS,YAAtB,CAAmCtC,WAAnC,EAAiD0C,MAAjD,GAA0D,KAAKE,UAAL,GAAkBJ,UAA5E;AACA,eAAKR,aAAL,CAAmBM,YAAnB,CAAgCtC,WAAhC,EAA8C0C,MAA9C,GAAuDrB,SAAS,CAACG,MAAV,GAAmBgB,UAA1E;AACH;AAED;AACJ;AACA;AACA;;;AACWf,QAAAA,iBAAiB,CAACS,QAAD,EAAyB;AAC7C,eAAKzB,cAAL,GAAsByB,QAAtB,CAD6C,CAG7C;;AACA,eAAK,IAAIC,QAAT,IAAqB,KAAKH,aAAL,CAAmBa,QAAxC,EAAkD;AAC9C;AACA,gBAAIV,QAAQ,CAACG,YAAT;AAAA;AAAA,8CAAqCJ,QAArC,KAAkD,KAAKzB,cAA3D,EAA2E;AACvE0B,cAAAA,QAAQ,CAACG,YAAT,CAAsBvC,MAAtB,EAA+B+C,KAA/B,GAAuC,IAAItD,KAAJ,CAAU,GAAV,EAAe,GAAf,EAAoB,GAApB,EAAyB,GAAzB,CAAvC;AACH,aAFD,MAEO;AACH;AACA2C,cAAAA,QAAQ,CAACG,YAAT,CAAsBvC,MAAtB,EAA+B+C,KAA/B,GAAuC,IAAItD,KAAJ,CAAU,GAAV,EAAe,GAAf,EAAoB,GAApB,EAAyB,GAAzB,CAAvC;AACH;AACJ;;AACD,eAAKuD,gBAAL;AACH;AAED;AACJ;AACA;;;AACYA,QAAAA,gBAAgB,GAAS;AAC7B,cAAIC,QAAc,GAAG,KAAKjC,SAAL,CAAekC,cAAf,CAA8B,OAA9B,CAArB;AACAD,UAAAA,QAAQ,CAACV,YAAT,CAAsB3C,KAAtB,EAA8BuD,MAA9B,GAAuC,KAAKzC,cAA5C;AAEA,eAAKK,aAAL,CAAmB,KAAnB;AACH;;AAEMqC,QAAAA,YAAY,CAACjB,QAAD,EAAyB;AACxC,eAAKT,iBAAL,CAAuBS,QAAvB;;AACA,eAAKxB,aAAL,CAAoBwB,QAApB;AACH;;AAtImC,O;;;;;;;;;;;;;;;;;;;;;;;;;iBAgBf,C;;;;;;;iBAGC,E;;;;;;;iBAGA,E", "sourcesContent": ["import { _decorator, Color, Component, instantiate, Label, Layout, Node, Prefab, ScrollView, Sprite, UITransform } from 'cc';\r\nimport { uiSelectItem } from './uiSelectItem';\r\nconst { ccclass, property } = _decorator;\r\n\r\n@ccclass('uiSelect')\r\nexport class uiSelect extends Component {\r\n\r\n    // 控制选择列表显示处理的按钮\r\n    @property({ type: Node, tooltip: \"控制选择列表显示处理的按钮\" })\r\n    btnSelect!: Node;\r\n    // 选择列表节点\r\n    @property({ type: Node, tooltip: \"选择列表\" })\r\n    scrollViewSelect!: Node;\r\n    // 项目节点容器\r\n    @property({ type: Node, tooltip: \"项目节点容器\" })\r\n    contentSelect!: Node;\r\n    // 选择列表中的项目预制体\r\n    @property({ type: Prefab, tooltip: \"选择列表中的项目预制体\" })\r\n    prefabItem!: Prefab;\r\n    // 选择列表中可展示的最大数据数量超过可以滚动显示\r\n    @property({ type: Number, tooltip: \"选择列表中可展示的最大数据数量 超过可以滚动显示\" })\r\n    itemMaxNum: number = 3;\r\n    // 默认项目\r\n    @property({ type: String, tooltip: \"默认项目\" })\r\n    defaultItem: string = \"\";\r\n    // 项目数据列表\r\n    @property({ type: [String], tooltip: \"项目数据列表\" })\r\n    itemDatas: string[] = [];\r\n\r\n    // 选择的项目\r\n    private chooseItemData: string = \"\";\r\n    private _onChooseItem !: (itemData: string) => void;\r\n    public SetOnChooseItem(callBack: (itemData: string) => void) {\r\n        this._onChooseItem = callBack;\r\n    }\r\n\r\n    protected onLoad(): void {\r\n\r\n        this.setSelectShow(false);\r\n\r\n        // 注册按钮事件\r\n        this.btnSelect.on(Node.EventType.TOUCH_END, this.onShowSelect, this);\r\n\r\n        this.setItemData(this.itemDatas);\r\n        // 默认数据在数据列表当中\r\n        if (this.itemDatas.indexOf(this.defaultItem) != -1) {\r\n            this.chooseItemData = this.defaultItem;\r\n        }\r\n        // 选择项目数据\r\n        if (this.chooseItemData.length > 0) {\r\n            this.setChooseItemData(this.chooseItemData);\r\n        } else {\r\n            this.setChooseItemData(\"空\");\r\n        }\r\n    }\r\n\r\n    protected onDestroy(): void {\r\n        // 注销按钮事件\r\n        if (this.btnSelect && this.btnSelect.isValid) {\r\n            this.btnSelect.off(Node.EventType.TOUCH_END, this.onShowSelect, this);\r\n        }\r\n    }\r\n\r\n    // 处理选择列表显示\r\n    private onShowSelect(): void {\r\n        if (this.scrollViewSelect.active) {\r\n            this.setSelectShow(false);\r\n        } else {\r\n            this.setSelectShow(true);\r\n        }\r\n    }\r\n\r\n    /**\r\n     * 设置选择列表显示状态\r\n     * @param show 显示/隐藏\r\n     */\r\n    public setSelectShow(show: boolean): void {\r\n        this.scrollViewSelect.active = show;\r\n    }\r\n\r\n    /**\r\n     * 设置选择列表的数据\r\n     * @param itemDatas 项目数据\r\n     */\r\n    public setItemData(itemDatas: string[]): void {\r\n        // 移除上一次的全部子节点\r\n        this.contentSelect.destroyAllChildren();\r\n\r\n        // 更新数据\r\n        this.itemDatas = itemDatas;\r\n        // 生成新的选择节点\r\n        for (let itemData of this.itemDatas) {\r\n            let itemNode: Node = instantiate(this.prefabItem);\r\n            itemNode.setParent(this.contentSelect);\r\n\r\n            // 更新数据-可自定义\r\n            itemNode.getComponent(uiSelectItem)!.updateValue(this, itemData);\r\n        }\r\n\r\n        // 获取项目节点真实高度 node原先高度+spacingY(纵向间隔)\r\n        // 更新选择列表高度\r\n        let itemHeight: number = (this.prefabItem.data.getComponent(UITransform).height + this.contentSelect.getComponent(Layout)?.spacingY);\r\n        this.scrollViewSelect.getComponent(UITransform)!.height = this.itemMaxNum * itemHeight;\r\n        this.contentSelect.getComponent(UITransform)!.height = itemDatas.length * itemHeight;\r\n    }\r\n\r\n    /**\r\n     * 设置选择项目\r\n     * @param itemData 项目数据 \r\n     */\r\n    public setChooseItemData(itemData: string): void {\r\n        this.chooseItemData = itemData;\r\n\r\n        // 选中项目节点处理\r\n        for (let itemNode of this.contentSelect.children) {\r\n            // 选中\r\n            if (itemNode.getComponent(uiSelectItem)!.itemData === this.chooseItemData) {\r\n                itemNode.getComponent(Sprite)!.color = new Color(125, 205, 205, 255);\r\n            } else {\r\n                // 未选中\r\n                itemNode.getComponent(Sprite)!.color = new Color(105, 105, 105, 255);\r\n            }\r\n        }\r\n        this.updateChooseItem();\r\n    }\r\n\r\n    /**\r\n     * 更新选择的项目数据\r\n     */\r\n    private updateChooseItem(): void {\r\n        let btnLabel: Node = this.btnSelect.getChildByName(\"Label\")!;\r\n        btnLabel.getComponent(Label)!.string = this.chooseItemData;\r\n\r\n        this.setSelectShow(false);\r\n    }\r\n\r\n    public onChooseItem(itemData: string): void {\r\n        this.setChooseItemData(itemData);\r\n        this._onChooseItem!(itemData);\r\n    }\r\n\r\n}\r\n\r\n\r\n"]}