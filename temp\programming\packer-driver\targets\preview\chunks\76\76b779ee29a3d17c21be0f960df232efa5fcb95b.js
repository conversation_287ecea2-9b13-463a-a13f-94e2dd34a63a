System.register(["__unresolved_0", "cc", "__unresolved_1", "__unresolved_2"], function (_export, _context) {
  "use strict";

  var _reporterNs, _cclegacy, __checkObsolete__, __checkObsoleteInNamespace__, _decorator, Vec3, System, RegisterTypeID, _dec, _dec2, _class, _crd, ccclass, BulletSystem;

  function _reportPossibleCrUseOfSystem(extras) {
    _reporterNs.report("System", "../base/System", _context.meta, extras);
  }

  function _reportPossibleCrUseOfRegisterTypeID(extras) {
    _reporterNs.report("RegisterTypeID", "../base/TypeID", _context.meta, extras);
  }

  return {
    setters: [function (_unresolved_) {
      _reporterNs = _unresolved_;
    }, function (_cc) {
      _cclegacy = _cc.cclegacy;
      __checkObsolete__ = _cc.__checkObsolete__;
      __checkObsoleteInNamespace__ = _cc.__checkObsoleteInNamespace__;
      _decorator = _cc._decorator;
      Vec3 = _cc.Vec3;
    }, function (_unresolved_2) {
      System = _unresolved_2.System;
    }, function (_unresolved_3) {
      RegisterTypeID = _unresolved_3.RegisterTypeID;
    }],
    execute: function () {
      _crd = true;

      _cclegacy._RF.push({}, "7cb83oXVZVMBJYZY7lJNPxV", "BulletSystem", undefined);

      __checkObsolete__(['_decorator', 'Vec3', 'Node']);

      ({
        ccclass
      } = _decorator);
      /**
       * Bullet data structure
       */

      /**
       * Bullet configuration
       */

      /**
       * BulletSystem - manages all bullets in the game world
       * Handles bullet creation, movement, collision, and cleanup
       */
      _export("BulletSystem", BulletSystem = (_dec = ccclass("BulletSystem"), _dec2 = _crd && RegisterTypeID === void 0 ? (_reportPossibleCrUseOfRegisterTypeID({
        error: Error()
      }), RegisterTypeID) : RegisterTypeID, _dec(_class = _dec2(_class = class BulletSystem extends (_crd && System === void 0 ? (_reportPossibleCrUseOfSystem({
        error: Error()
      }), System) : System) {
        constructor() {
          super(...arguments);
          this._bullets = new Map();
          this._bulletIdCounter = 0;
          this._maxBullets = 1000;
          this._bulletPool = [];
        }

        /**
         * Get the system name
         */
        getSystemName() {
          return "BulletSystem";
        }
        /**
         * Initialize the bullet system
         */


        onInit() {
          console.log("BulletSystem: Initializing bullet system"); // Pre-allocate bullet pool for performance

          this._preallocateBulletPool();

          console.log("BulletSystem: Initialized with max bullets: " + this._maxBullets);
        }
        /**
         * Cleanup the bullet system
         */


        onUnInit() {
          console.log("BulletSystem: Cleaning up bullet system"); // Destroy all bullets

          this._bullets.forEach(bullet => {
            this._destroyBullet(bullet);
          });

          this._bullets.clear();

          this._bulletPool.length = 0;
          this._bulletIdCounter = 0;
          console.log("BulletSystem: Cleanup complete");
        }
        /**
         * Update all bullets
         */


        onUpdate(deltaTime) {
          // Update bullet positions and lifetimes
          var bulletsToRemove = [];

          this._bullets.forEach((bullet, id) => {
            // Update position
            bullet.position.add(Vec3.multiplyScalar(new Vec3(), bullet.velocity, deltaTime)); // Update lifetime

            bullet.lifetime += deltaTime; // Check if bullet should be removed

            if (bullet.lifetime >= bullet.maxLifetime) {
              bulletsToRemove.push(id);
            } // Update visual node if exists


            if (bullet.node) {
              bullet.node.setPosition(bullet.position);
            }
          }); // Remove expired bullets


          bulletsToRemove.forEach(id => {
            this.removeBullet(id);
          });
        }
        /**
         * Late update - handle any post-update logic
         */


        onLateUpdate(_deltaTime) {// Could be used for collision detection, visual effects, etc.
        }
        /**
         * Create a new bullet
         * @param position Starting position
         * @param direction Direction vector (will be normalized)
         * @param config Bullet configuration
         * @param ownerId ID of the entity that created this bullet
         * @returns The bullet ID or null if creation failed
         */


        createBullet(position, direction, config, ownerId) {
          if (this._bullets.size >= this._maxBullets) {
            console.warn("BulletSystem: Cannot create bullet - max bullets reached");
            return null;
          } // Get bullet from pool or create new one


          var bullet = this._getBulletFromPool(); // Set bullet properties


          bullet.id = this._generateBulletId();
          bullet.position.set(position);
          bullet.velocity = Vec3.multiplyScalar(new Vec3(), direction.normalize(), config.speed);
          bullet.damage = config.damage;
          bullet.lifetime = 0;
          bullet.maxLifetime = config.lifetime;
          bullet.bulletType = config.bulletType;
          bullet.ownerId = ownerId; // Add to active bullets

          this._bullets.set(bullet.id, bullet);

          console.log("BulletSystem: Created bullet " + bullet.id + " for owner " + ownerId);
          return bullet.id;
        }
        /**
         * Remove a bullet by ID
         * @param bulletId The ID of the bullet to remove
         * @returns true if the bullet was removed
         */


        removeBullet(bulletId) {
          var bullet = this._bullets.get(bulletId);

          if (!bullet) {
            return false;
          }

          this._destroyBullet(bullet);

          this._bullets.delete(bulletId);

          this._returnBulletToPool(bullet);

          return true;
        }
        /**
         * Get a bullet by ID
         * @param bulletId The ID of the bullet to get
         * @returns The bullet data or null if not found
         */


        getBullet(bulletId) {
          return this._bullets.get(bulletId) || null;
        }
        /**
         * Get all bullets
         * @returns Array of all active bullets
         */


        getAllBullets() {
          return Array.from(this._bullets.values());
        }
        /**
         * Get bullets by owner ID
         * @param ownerId The owner ID to filter by
         * @returns Array of bullets owned by the specified entity
         */


        getBulletsByOwner(ownerId) {
          return Array.from(this._bullets.values()).filter(bullet => bullet.ownerId === ownerId);
        }
        /**
         * Get the number of active bullets
         * @returns The number of active bullets
         */


        getBulletCount() {
          return this._bullets.size;
        }
        /**
         * Set the maximum number of bullets
         * @param maxBullets The new maximum number of bullets
         */


        setMaxBullets(maxBullets) {
          this._maxBullets = Math.max(1, maxBullets);
          console.log("BulletSystem: Max bullets set to " + this._maxBullets);
        }
        /**
         * Clear all bullets
         */


        clearAllBullets() {
          this._bullets.forEach(bullet => {
            this._destroyBullet(bullet);

            this._returnBulletToPool(bullet);
          });

          this._bullets.clear();

          console.log("BulletSystem: All bullets cleared");
        }
        /**
         * Pre-allocate bullet pool for performance
         */


        _preallocateBulletPool() {
          var poolSize = Math.min(100, this._maxBullets);

          for (var i = 0; i < poolSize; i++) {
            this._bulletPool.push(this._createEmptyBullet());
          }

          console.log("BulletSystem: Pre-allocated " + poolSize + " bullets in pool");
        }
        /**
         * Get a bullet from the pool or create a new one
         */


        _getBulletFromPool() {
          return this._bulletPool.pop() || this._createEmptyBullet();
        }
        /**
         * Return a bullet to the pool
         */


        _returnBulletToPool(bullet) {
          // Reset bullet data
          bullet.id = "";
          bullet.position.set(0, 0, 0);
          bullet.velocity.set(0, 0, 0);
          bullet.damage = 0;
          bullet.lifetime = 0;
          bullet.maxLifetime = 0;
          bullet.bulletType = "";
          bullet.ownerId = 0;
          bullet.node = undefined; // Return to pool if not full

          if (this._bulletPool.length < 100) {
            this._bulletPool.push(bullet);
          }
        }
        /**
         * Create an empty bullet data structure
         */


        _createEmptyBullet() {
          return {
            id: "",
            position: new Vec3(),
            velocity: new Vec3(),
            damage: 0,
            lifetime: 0,
            maxLifetime: 0,
            bulletType: "",
            ownerId: 0,
            node: undefined
          };
        }
        /**
         * Generate a unique bullet ID
         */


        _generateBulletId() {
          return "bullet_" + ++this._bulletIdCounter;
        }
        /**
         * Destroy a bullet's visual representation
         */


        _destroyBullet(bullet) {
          if (bullet.node && bullet.node.isValid) {
            bullet.node.destroy();
            bullet.node = undefined;
          }
        }

      }) || _class) || _class));

      _cclegacy._RF.pop();

      _crd = false;
    }
  };
});
//# sourceMappingURL=76b779ee29a3d17c21be0f960df232efa5fcb95b.js.map