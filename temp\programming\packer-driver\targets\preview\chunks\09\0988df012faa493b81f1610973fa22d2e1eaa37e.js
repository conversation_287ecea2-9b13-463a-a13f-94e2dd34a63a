System.register(["__unresolved_0", "cc", "__unresolved_1", "__unresolved_2"], function (_export, _context) {
  "use strict";

  var _reporterNs, _cclegacy, __checkObsolete__, __checkObsoleteInNamespace__, _decorator, v2, <PERSON><PERSON>r<PERSON>omp, BossUnitBase, _dec, _class, _crd, ccclass, property, BossCollider;

  function _reportPossibleCrUseOfColliderComp(extras) {
    _reporterNs.report("ColliderComp", "../../base/ColliderComp", _context.meta, extras);
  }

  function _reportPossibleCrUseOfBossUnitBase(extras) {
    _reporterNs.report("BossUnitBase", "./BossUnitBase", _context.meta, extras);
  }

  return {
    setters: [function (_unresolved_) {
      _reporterNs = _unresolved_;
    }, function (_cc) {
      _cclegacy = _cc.cclegacy;
      __checkObsolete__ = _cc.__checkObsolete__;
      __checkObsoleteInNamespace__ = _cc.__checkObsoleteInNamespace__;
      _decorator = _cc._decorator;
      v2 = _cc.v2;
    }, function (_unresolved_2) {
      ColliderComp = _unresolved_2.ColliderComp;
    }, function (_unresolved_3) {
      BossUnitBase = _unresolved_3.default;
    }],
    execute: function () {
      _crd = true;

      _cclegacy._RF.push({}, "88601dBGcBJgoGWU58Ah9bf", "BossCollider", undefined);

      __checkObsolete__(['_decorator', 'Component', 'v2', 'Vec3']);

      ({
        ccclass,
        property
      } = _decorator);

      _export("default", BossCollider = (_dec = ccclass('BossCollider'), _dec(_class = class BossCollider extends (_crd && BossUnitBase === void 0 ? (_reportPossibleCrUseOfBossUnitBase({
        error: Error()
      }), BossUnitBase) : BossUnitBase) {
        create(owner, data) {
          this.owner = owner;

          if (!this.collideComp) {
            this.collideComp = this.addComp(_crd && ColliderComp === void 0 ? (_reportPossibleCrUseOfColliderComp({
              error: Error()
            }), ColliderComp) : ColliderComp, new (_crd && ColliderComp === void 0 ? (_reportPossibleCrUseOfColliderComp({
              error: Error()
            }), ColliderComp) : ColliderComp)());
          }

          this.collideComp.init(this);
          this.collideComp.setData(data, this.owner, v2(this.node.position.x, this.node.position.y));
          this.m_comps.forEach(comp => {
            comp.init(this);
          });
        }

        setCollidePosition(x, y) {
          var _this$collideComp;

          (_this$collideComp = this.collideComp) == null || _this$collideComp.setPos(x, y);
        }

        setCollideAble(enabled) {
          if (this.collideComp) {
            this.collideComp.enabled = enabled;
          }
        }

        updateGameLogic(deltaTime) {
          this.m_comps.forEach(comp => {
            comp.update(deltaTime);
          });
        }

      }) || _class));

      _cclegacy._RF.pop();

      _crd = false;
    }
  };
});
//# sourceMappingURL=0988df012faa493b81f1610973fa22d2e1eaa37e.js.map