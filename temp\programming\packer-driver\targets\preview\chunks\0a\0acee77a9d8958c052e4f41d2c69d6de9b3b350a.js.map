{"version": 3, "sources": ["file:///E:/M2Game/Client/assets/scripts/ui/main/ShopUI.ts"], "names": ["_decorator", "BaseUI", "<PERSON><PERSON><PERSON><PERSON>", "ccclass", "property", "ShopUI", "getUrl", "<PERSON><PERSON><PERSON><PERSON>", "Background", "onLoad", "onShow", "onHide", "onClose", "update", "dt"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;AAASA,MAAAA,U,OAAAA,U;;AACAC,MAAAA,M,iBAAAA,M;AAAQC,MAAAA,O,iBAAAA,O;;;;;;;;;OACX;AAAEC,QAAAA,OAAF;AAAWC,QAAAA;AAAX,O,GAAwBJ,U;;wBAGjBK,M,WADZF,OAAO,CAAC,QAAD,C,gBAAR,MACaE,MADb;AAAA;AAAA,4BACmC;AACX,eAANC,MAAM,GAAW;AAAE,iBAAO,gBAAP;AAA0B;;AACrC,eAARC,QAAQ,GAAY;AAAE,iBAAO;AAAA;AAAA,kCAAQC,UAAf;AAA2B;;AAErDC,QAAAA,MAAM,GAAS,CAExB;;AAEKC,QAAAA,MAAM,GAAgC;AAAA;AAC3C;;AACKC,QAAAA,MAAM,GAAgC;AAAA;AAC3C;;AACKC,QAAAA,OAAO,GAAgC;AAAA;AAC5C;;AACSC,QAAAA,MAAM,CAACC,EAAD,EAAmB,CAClC;;AAf8B,O", "sourcesContent": ["import { _decorator } from 'cc';\nimport { BaseUI, UILayer } from '../UIMgr';\nconst { ccclass, property } = _decorator;\n\n@ccclass('ShopUI')\nexport class ShopUI extends BaseUI {\n    public static getUrl(): string { return \"ui/main/ShopUI\"; }\n    public static getLayer(): UILayer { return UILayer.Background }\n\n    protected onLoad(): void {\n\n    }\n\n    async onShow(...args: any[]): Promise<void> {\n    }\n    async onHide(...args: any[]): Promise<void> {\n    }\n    async onClose(...args: any[]): Promise<void> {\n    }\n    protected update(dt: number): void {\n    }\n\n}\n\n"]}