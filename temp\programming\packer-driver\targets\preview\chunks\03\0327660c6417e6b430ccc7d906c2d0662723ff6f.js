System.register(["__unresolved_0", "cc", "__unresolved_1", "__unresolved_2", "__unresolved_3", "__unresolved_4", "__unresolved_5", "__unresolved_6", "__unresolved_7", "__unresolved_8", "__unresolved_9", "__unresolved_10", "__unresolved_11", "__unresolved_12", "__unresolved_13"], function (_export, _context) {
  "use strict";

  var _reporterNs, _cclegacy, __checkObsolete__, __checkObsoleteInNamespace__, _decorator, Sprite, tween, game, ParticleSystem2D, Tween, v3, GameIns, Tools, Entity, AngleComp, BulletFly, GameFunc, UIAnimMethods, ScaleComp, XYFly, ParticleComponent, ColliderComp, MainPlane, GameConst, _dec, _dec2, _dec3, _dec4, _dec5, _class, _class2, _descriptor, _descriptor2, _descriptor3, _descriptor4, _class3, _crd, ccclass, property, Bullet;

  function asyncGeneratorStep(gen, resolve, reject, _next, _throw, key, arg) { try { var info = gen[key](arg); var value = info.value; } catch (error) { reject(error); return; } if (info.done) { resolve(value); } else { Promise.resolve(value).then(_next, _throw); } }

  function _asyncToGenerator(fn) { return function () { var self = this, args = arguments; return new Promise(function (resolve, reject) { var gen = fn.apply(self, args); function _next(value) { asyncGeneratorStep(gen, resolve, reject, _next, _throw, "next", value); } function _throw(err) { asyncGeneratorStep(gen, resolve, reject, _next, _throw, "throw", err); } _next(undefined); }); }; }

  function _initializerDefineProperty(target, property, descriptor, context) { if (!descriptor) return; Object.defineProperty(target, property, { enumerable: descriptor.enumerable, configurable: descriptor.configurable, writable: descriptor.writable, value: descriptor.initializer ? descriptor.initializer.call(context) : void 0 }); }

  function _applyDecoratedDescriptor(target, property, decorators, descriptor, context) { var desc = {}; Object.keys(descriptor).forEach(function (key) { desc[key] = descriptor[key]; }); desc.enumerable = !!desc.enumerable; desc.configurable = !!desc.configurable; if ('value' in desc || desc.initializer) { desc.writable = true; } desc = decorators.slice().reverse().reduce(function (desc, decorator) { return decorator(target, property, desc) || desc; }, desc); if (context && desc.initializer !== void 0) { desc.value = desc.initializer ? desc.initializer.call(context) : void 0; desc.initializer = undefined; } if (desc.initializer === void 0) { Object.defineProperty(target, property, desc); desc = null; } return desc; }

  function _initializerWarningHelper(descriptor, context) { throw new Error('Decorating class property failed. Please ensure that ' + 'transform-class-properties is enabled and runs after the decorators transform.'); }

  function _reportPossibleCrUseOfGameIns(extras) {
    _reporterNs.report("GameIns", "../../GameIns", _context.meta, extras);
  }

  function _reportPossibleCrUseOfTools(extras) {
    _reporterNs.report("Tools", "../../utils/Tools", _context.meta, extras);
  }

  function _reportPossibleCrUseOfEntity(extras) {
    _reporterNs.report("Entity", "../base/Entity", _context.meta, extras);
  }

  function _reportPossibleCrUseOfAngleComp(extras) {
    _reporterNs.report("AngleComp", "../base/AngleComp", _context.meta, extras);
  }

  function _reportPossibleCrUseOfBulletFly(extras) {
    _reporterNs.report("BulletFly", "./BulletFly", _context.meta, extras);
  }

  function _reportPossibleCrUseOfGameFunc(extras) {
    _reporterNs.report("GameFunc", "../../GameFunc", _context.meta, extras);
  }

  function _reportPossibleCrUseOfUIAnimMethods(extras) {
    _reporterNs.report("UIAnimMethods", "../base/UIAnimMethods", _context.meta, extras);
  }

  function _reportPossibleCrUseOfScaleComp(extras) {
    _reporterNs.report("ScaleComp", "../base/ScaleComp", _context.meta, extras);
  }

  function _reportPossibleCrUseOfXYFly(extras) {
    _reporterNs.report("XYFly", "../base/XYFly", _context.meta, extras);
  }

  function _reportPossibleCrUseOfParticleComponent(extras) {
    _reporterNs.report("ParticleComponent", "../ParticleComponent", _context.meta, extras);
  }

  function _reportPossibleCrUseOfColliderComp(extras) {
    _reporterNs.report("ColliderComp", "../base/ColliderComp", _context.meta, extras);
  }

  function _reportPossibleCrUseOfMainPlane(extras) {
    _reporterNs.report("MainPlane", "../plane/mainPlane/MainPlane", _context.meta, extras);
  }

  function _reportPossibleCrUseOfGameConst(extras) {
    _reporterNs.report("GameConst", "../../const/GameConst", _context.meta, extras);
  }

  return {
    setters: [function (_unresolved_) {
      _reporterNs = _unresolved_;
    }, function (_cc) {
      _cclegacy = _cc.cclegacy;
      __checkObsolete__ = _cc.__checkObsolete__;
      __checkObsoleteInNamespace__ = _cc.__checkObsoleteInNamespace__;
      _decorator = _cc._decorator;
      Sprite = _cc.Sprite;
      tween = _cc.tween;
      game = _cc.game;
      ParticleSystem2D = _cc.ParticleSystem2D;
      Tween = _cc.Tween;
      v3 = _cc.v3;
    }, function (_unresolved_2) {
      GameIns = _unresolved_2.GameIns;
    }, function (_unresolved_3) {
      Tools = _unresolved_3.Tools;
    }, function (_unresolved_4) {
      Entity = _unresolved_4.default;
    }, function (_unresolved_5) {
      AngleComp = _unresolved_5.default;
    }, function (_unresolved_6) {
      BulletFly = _unresolved_6.default;
    }, function (_unresolved_7) {
      GameFunc = _unresolved_7.GameFunc;
    }, function (_unresolved_8) {
      UIAnimMethods = _unresolved_8.default;
    }, function (_unresolved_9) {
      ScaleComp = _unresolved_9.default;
    }, function (_unresolved_10) {
      XYFly = _unresolved_10.default;
    }, function (_unresolved_11) {
      ParticleComponent = _unresolved_11.default;
    }, function (_unresolved_12) {
      ColliderComp = _unresolved_12.ColliderComp;
    }, function (_unresolved_13) {
      MainPlane = _unresolved_13.MainPlane;
    }, function (_unresolved_14) {
      GameConst = _unresolved_14.GameConst;
    }],
    execute: function () {
      _crd = true;

      _cclegacy._RF.push({}, "771e3z4lZJCFI7xLAXh7DiF", "Bullet", undefined);

      __checkObsolete__(['_decorator', 'Sprite', 'ParticleSystem', 'Node', 'tween', 'v2', 'misc', 'director', 'game', 'ParticleSystem2D', 'Tween', 'v3']);

      ({
        ccclass,
        property
      } = _decorator);

      _export("default", Bullet = (_dec = ccclass('Bullet'), _dec2 = property(Sprite), _dec3 = property(Sprite), _dec4 = property(Sprite), _dec5 = property(ParticleSystem2D), _dec(_class = (_class2 = (_class3 = class Bullet extends (_crd && Entity === void 0 ? (_reportPossibleCrUseOfEntity({
        error: Error()
      }), Entity) : Entity) {
        constructor() {
          super(...arguments);

          _initializerDefineProperty(this, "skinImg", _descriptor, this);

          _initializerDefineProperty(this, "fire", _descriptor2, this);

          _initializerDefineProperty(this, "enemyFire", _descriptor3, this);

          _initializerDefineProperty(this, "burstFire", _descriptor4, this);

          this.enemy = false;
          this.bulletID = 1;
          this.m_collideComp = null;
          this.playHurt = true;
          this.isCirt = false;
          this.m_createTime = 0;
          this.m_lifeTime = 0;
          this.aliveTime = 0;
          this.m_fireTween = null;
          this.collideDun = false;
          this._catapultCount = 0;
          this._catapultAtkRatio = 1;
          this._collideEntity = null;
          this._catapultTargets = [];
          this.streakCall = null;
          this.m_throughArr = [];
          this.m_config = void 0;
          this.m_mainEntity = void 0;
          this.bulletState = void 0;
        }

        /**
         * 初始化子弹
         * @param {number} bulletID 子弹的唯一标识符
         */
        create(bulletID) {
          this.bulletID = bulletID;
          this.m_config = (_crd && GameIns === void 0 ? (_reportPossibleCrUseOfGameIns({
            error: Error()
          }), GameIns) : GameIns).bulletManager.getConfig(this.bulletID);
          this.removeAllComp(); // 添加子弹飞行组件（如旋转角度）

          if (this.m_config.rs && this.m_config.rs !== 0) {
            this.addComp(_crd && AngleComp === void 0 ? (_reportPossibleCrUseOfAngleComp({
              error: Error()
            }), AngleComp) : AngleComp, new (_crd && AngleComp === void 0 ? (_reportPossibleCrUseOfAngleComp({
              error: Error()
            }), AngleComp) : AngleComp)({
              angleSpeed: this.m_config.rs
            }, this.m_config.bustyle));
          } // try {
          //     this.node.opacity = 255;
          // } catch (error) {
          //     Tools.error('Error setting opacity:', error);
          // }
          // // 判断是否为跟踪子弹
          // if (FireShells.isFollowBullet(this.m_config.bustyle)) {
          //     const hideTime = this.m_config.para[this.m_config.para.length - 1];
          //     this.addComp(TargetFly, new TargetFly({ speed: this.m_config.initialve, hideTime }));
          // } else {
          //     // 根据子弹类型添加不同的组件
          //     switch (this.m_config.bustyle) {
          //         case 7:
          //             this.addComp(BulletParabolic, new BulletParabolic(this.m_config));
          //             break;
          //         case 25:
          //             this.addComp(CircleZoomFly, new CircleZoomFly(this.m_config));
          //             break;
          //         case 29:
          //             const speedFactor = PilotManager.getCurSkill(PilotSkillType.ArondBallSpeedFactor);
          //             const radius = PilotManager.getCurSkill(PilotSkillType.ArondBallRadius);
          //             const adjustedSpeed = speedFactor ? this.m_config.para[2] * (1 + speedFactor[0]) : this.m_config.para[2];
          //             const adjustedRadius = radius ? this.m_config.para[0] + radius[0] : this.m_config.para[0];
          //             const imageName = `${this.m_config.image.split('_')[0]}_${this.m_config.image.split('_')[1]}_tw`;
          //             this.addComp(CircleFly, new CircleFly(adjustedSpeed, adjustedRadius));
          //             this.addComp(ParticleComponent, new ParticleComponent(imageName, 0, this.getType(), this.m_config.id));
          //             this.addComp(OnceCollideComp, new OnceCollideComp());
          //             this.addComp(ResistBulletComp, new ResistBulletComp());
          //             break;
          //         case 32:
          //             const hideTime32 = this.m_config.para[this.m_config.para.length - 1];
          //             this.addComp(TargetAroundFly, new TargetAroundFly({ speed: this.m_config.initialve, hideTime: hideTime32 }));
          //             break;
          //         case 33:
          //             const hideTime33 = this.m_config.para[this.m_config.para.length - 1];
          //             this.addComp(TargetAroundFly2, new TargetAroundFly2({ speed: this.m_config.initialve, hideTime: hideTime33 }));
          //             break;
          //         case 36:
          //             this.addComp(FlyThenTurnComp, new FlyThenTurnComp(this.m_config));
          //             break;
          //         case 37:
          //             this.addComp(BoomerangFly, new BoomerangFly(this.m_config));
          //             break;
          //         case 43:
          //             this.addComp(BatSkillFly, new BatSkillFly(this.m_config));
          //             break;
          //         case 44:
          //             this.addComp(HarmonicFly, new HarmonicFly(this.m_config, this.m_config.para));
          //             break;
          //         case 47:
          //             this.addComp(WaveRotateComp, new WaveRotateComp(this.m_config, this.m_config.para));
          //             break;
          //         case 48:
          //             const knifeSpeed = this.m_config.para[1] * (1 + MainPlaneManager.data.knifeSpeedUp / 100);
          //             const knifeRadius = this.m_config.para[2];
          //             this.addComp(CircleFly1, new CircleFly1(knifeSpeed, knifeRadius));
          //             this.addComp(OnceCollideComp, new OnceCollideComp());
          //             break;
          //         case 53:
          //             this.addComp(AngleDeltaFly, new AngleDeltaFly(this.m_config));
          //             break;
          //         case 54:
          //             this.addComp(CircleChangeFly, new CircleChangeFly(this.m_config));
          //             break;
          //         case 56:
          //             this.addComp(RefractionFly, new RefractionFly(this.m_config));
          //             break;
          //         default:


          this.addComp(_crd && BulletFly === void 0 ? (_reportPossibleCrUseOfBulletFly({
            error: Error()
          }), BulletFly) : BulletFly, new (_crd && BulletFly === void 0 ? (_reportPossibleCrUseOfBulletFly({
            error: Error()
          }), BulletFly) : BulletFly)(this.m_config));

          if (this.m_config.bustyle === 50 || this.m_config.bustyle === 59) {
            this.addComp(_crd && ScaleComp === void 0 ? (_reportPossibleCrUseOfScaleComp({
              error: Error()
            }), ScaleComp) : ScaleComp, new (_crd && ScaleComp === void 0 ? (_reportPossibleCrUseOfScaleComp({
              error: Error()
            }), ScaleComp) : ScaleComp)());
          } else if (this.getComp(_crd && ScaleComp === void 0 ? (_reportPossibleCrUseOfScaleComp({
            error: Error()
          }), ScaleComp) : ScaleComp)) {
            this.removeComp(_crd && ScaleComp === void 0 ? (_reportPossibleCrUseOfScaleComp({
              error: Error()
            }), ScaleComp) : ScaleComp);
          }

          if (this.m_config.zdyd.length > 0) {
            this.addComp(_crd && XYFly === void 0 ? (_reportPossibleCrUseOfXYFly({
              error: Error()
            }), XYFly) : XYFly, new (_crd && XYFly === void 0 ? (_reportPossibleCrUseOfXYFly({
              error: Error()
            }), XYFly) : XYFly)(this.m_config.zdyd, this.m_config.waittime, this.m_config.waittime.length > 0));
          } else if (this.getComp(_crd && XYFly === void 0 ? (_reportPossibleCrUseOfXYFly({
            error: Error()
          }), XYFly) : XYFly)) {
            this.removeComp(_crd && XYFly === void 0 ? (_reportPossibleCrUseOfXYFly({
              error: Error()
            }), XYFly) : XYFly);
          } // }
          // }
          // 添加粒子效果


          var particleEffect = this.m_config.zdwy1;

          if (particleEffect && particleEffect !== '') {
            this.addComp(_crd && ParticleComponent === void 0 ? (_reportPossibleCrUseOfParticleComponent({
              error: Error()
            }), ParticleComponent) : ParticleComponent, new (_crd && ParticleComponent === void 0 ? (_reportPossibleCrUseOfParticleComponent({
              error: Error()
            }), ParticleComponent) : ParticleComponent)(particleEffect, 0, this.getType(), this.m_config.id));
          } // 添加碰撞组件


          this.m_collideComp = this.addComp(_crd && ColliderComp === void 0 ? (_reportPossibleCrUseOfColliderComp({
            error: Error()
          }), ColliderComp) : ColliderComp, new (_crd && ColliderComp === void 0 ? (_reportPossibleCrUseOfColliderComp({
            error: Error()
          }), ColliderComp) : ColliderComp)()); // 设置子弹皮肤

          this.setSkin(); // 设置子弹缩放

          this.node.setScale(this.m_config.scale, this.m_config.scale); // 初始化发射效果

          if (this.fire) {
            this.fire.node.active = false;
            Tween.stopAllByTarget(this.fire.node);
          }
        }
        /**
        * 设置子弹的皮肤
        */


        setSkin() {
          var _this = this;

          return _asyncToGenerator(function* () {
            // 移除旧的子弹特效节点
            (_crd && Tools === void 0 ? (_reportPossibleCrUseOfTools({
              error: Error()
            }), Tools) : Tools).removeChildByName(_this.skinImg.node.parent, 'fist');
            (_crd && Tools === void 0 ? (_reportPossibleCrUseOfTools({
              error: Error()
            }), Tools) : Tools).removeChildByName(_this.skinImg.node.parent, 'gatlin');
            (_crd && Tools === void 0 ? (_reportPossibleCrUseOfTools({
              error: Error()
            }), Tools) : Tools).removeChildByName(_this.skinImg.node.parent, 'tail'); // 重置子弹透明度

            _this.skinImg.node.opacity = 255; // // 根据子弹类型设置特殊皮肤
            // switch (this.m_config.bustyle) {
            //     case 51:
            //         this.setBossBoxingFistSkin();
            //         return;
            //     case 52:
            //         this.setBossBoxingGatlinSkin();
            //         return;
            //     case 55:
            //         this.addTail();
            //         break;
            // }
            // 设置子弹的默认皮肤

            _this.skinImg.spriteFrame = null;

            if (_this.bulletID > 1000) {
              // 敌方子弹
              (_crd && GameFunc === void 0 ? (_reportPossibleCrUseOfGameFunc({
                error: Error()
              }), GameFunc) : GameFunc).setImage(_this.skinImg, _this.m_config.image, (_crd && GameIns === void 0 ? (_reportPossibleCrUseOfGameIns({
                error: Error()
              }), GameIns) : GameIns).bulletManager.enemyBulletAtlas);
            } else {
              // 主角子弹
              (_crd && GameFunc === void 0 ? (_reportPossibleCrUseOfGameFunc({
                error: Error()
              }), GameFunc) : GameFunc).setImage(_this.skinImg, _this.m_config.image, (_crd && GameIns === void 0 ? (_reportPossibleCrUseOfGameIns({
                error: Error()
              }), GameIns) : GameIns).bulletManager.mainBulletAtlas); // 如果未加载到图片，尝试异步加载

              if (!_this.skinImg.spriteFrame) {
                yield (_crd && GameIns === void 0 ? (_reportPossibleCrUseOfGameIns({
                  error: Error()
                }), GameIns) : GameIns).loadManager.setImage(_this.skinImg, _this.m_config.image, 'mainBullet');
              }
            } // 检查图片是否加载成功


            if (!_this.skinImg.spriteFrame) {
              (_crd && Tools === void 0 ? (_reportPossibleCrUseOfTools({
                error: Error()
              }), Tools) : Tools).error('Bullet image error:', _this.m_config.image);
            } else {// // 设置子弹的宽高比例
              // if (!GameConfig.isHD) {
              //     this.skinImg.node.width = 0.666666666667 * this.skinImg.spriteFrame.getOriginalSize().width;
              //     this.skinImg.node.height = 0.666666666667 * this.skinImg.spriteFrame.getOriginalSize().height;
              // }
            }
          })();
        } //     /**
        //  * 初始化跟踪子弹
        //  * @param {boolean} isEnemy 是否为敌方子弹
        //  * @param {Object} position 子弹的初始位置
        //  * @param {number} attack 子弹的攻击力
        //  */
        //     initFollow(isEnemy, position, attack) {
        //         this.init(isEnemy, position, {
        //             attack: attack,
        //             through: false,
        //         });
        //     }

        /**
         * 初始化子弹
         * @param {boolean} isEnemy 是否为敌方子弹
         * @param {Object} position 子弹的初始位置
         * @param {Object} state 子弹的状态
         * @param {Entity} mainEntity 子弹的发射实体
         */


        init(isEnemy, position, state, mainEntity) {
          if (state === void 0) {
            state = null;
          }

          if (mainEntity === void 0) {
            mainEntity = null;
          }

          var intensifyImage, opacity; // 设置强化子弹的皮肤

          if (state.fireIntensify && state.fireIntensify.length > 0 && state.fireIntensify[0] > 0) {
            intensifyImage = this.m_config.image + (_crd && GameIns === void 0 ? (_reportPossibleCrUseOfGameIns({
              error: Error()
            }), GameIns) : GameIns).bulletManager.intensifyName;
            (_crd && GameIns === void 0 ? (_reportPossibleCrUseOfGameIns({
              error: Error()
            }), GameIns) : GameIns).loadManager.setImage(this.skinImg, intensifyImage, 'mainBullet');
            this.skinImg.node.setScale((_crd && GameIns === void 0 ? (_reportPossibleCrUseOfGameIns({
              error: Error()
            }), GameIns) : GameIns).bulletManager.intensifyScale, (_crd && GameIns === void 0 ? (_reportPossibleCrUseOfGameIns({
              error: Error()
            }), GameIns) : GameIns).bulletManager.intensifyScale);
          }

          this.m_mainEntity = mainEntity;
          this.node.setPosition(position.x, position.y);
          this.skinImg.node.angle = 0;
          this.m_lifeTime = this.m_config.retrieve;

          if (this.m_lifeTime > 0) {
            this.m_createTime = game.totalTime;
          }

          this.node.angle = isEnemy ? 180 - position.angle : -position.angle;
          this.enemy = isEnemy;

          if (!state.clone) {
            state.through = this.m_config.disappear === 1;
          }

          this.bulletState = state; // if (mainEntity && mainEntity instanceof ShadowPlane) {
          //     opacity = mainEntity.bulletOpacity;
          //     this.node.opacity = opacity;
          // } else {
          //     this.node.opacity = 255;
          // }
          // if (this.bulletState.through) {
          //     this.addComp(OnceCollideComp, new OnceCollideComp());
          // }
          // 初始化子弹的所有组件

          this.m_comps.forEach(comp => {
            comp.init(this);
          }); // 设置子弹飞行组件

          var bulletFlyComp = this.getComp(_crd && BulletFly === void 0 ? (_reportPossibleCrUseOfBulletFly({
            error: Error()
          }), BulletFly) : BulletFly);

          if (bulletFlyComp) {
            bulletFlyComp.setData(-position.angle, this.m_mainEntity, this.enemy);
          } // // 设置特殊飞行组件
          // const flyThenTurnComp = this.getComp(FlyThenTurnComp);
          // if (flyThenTurnComp) {
          //     const [param0, param4, param5, param6] = this.m_config.para;
          //     flyThenTurnComp.setData(-position.angle, this.m_mainEntity, param0, param4, param5, param6);
          // }
          // const circleFlyComp = this.getComp(CircleFly);
          // if (circleFlyComp) {
          //     circleFlyComp.setData({ x: position.x, y: position.y }, position.angle, this.m_mainEntity);
          // }
          // const circleFly1Comp = this.getComp(CircleFly1);
          // if (circleFly1Comp) {
          //     circleFly1Comp.setData({ x: position.x, y: position.y }, position.angle, this.m_mainEntity);
          // }
          // const bulletParabolicComp = this.getComp(BulletParabolic);
          // if (bulletParabolicComp) {
          //     bulletParabolicComp.setData(this.node.angle);
          // }
          // const batSkillFlyComp = this.getComp(BatSkillFly);
          // if (batSkillFlyComp) {
          //     const batIndex = MainPlaneManager.plane.skillBatDis[0]++ % MainPlaneManager.plane.skillTargetNdoes.length;
          //     batSkillFlyComp.setData(MainPlaneManager.plane.skillTargetNdoes[batIndex]);
          // }
          // // 设置碰撞组件


          var colliderComp = this.getComp(_crd && ColliderComp === void 0 ? (_reportPossibleCrUseOfColliderComp({
            error: Error()
          }), ColliderComp) : ColliderComp);
          var shiftingBody = this.m_config.shiftingbody;
          var bodyWidth = shiftingBody.length > 0 ? shiftingBody[0] : 0;
          var bodyHeight = shiftingBody.length > 1 ? shiftingBody[1] : 0; // if (this.m_config.bustyle === 50) {
          //     colliderComp.setData([1, bodyWidth, bodyHeight, 300, 50]);
          // } else {

          colliderComp.setData([0, bodyWidth, bodyHeight, this.m_config.body, this.m_config.body]); // }
          // // 设置缩放组件
          // const scaleComp = this.getComp(ScaleComp);
          // if (scaleComp) {
          //     scaleComp.setData(this.m_config, this.m_collideComp);
          // }
          // 设置子弹的发射效果

          if (this.fire && this.enemyFire) {
            var fireEffect = this.m_config.zdwy1;

            if (fireEffect && fireEffect !== '') {
              if (this.m_config.id < 1000) {
                this.fire.node.active = true;
                this.fire.node.opacity = 255;
                this.enemyFire.node.active = false;
                Tween.stopAllByTarget(this.fire.node);
                var fireTween = tween(this.fire.node).to((_crd && UIAnimMethods === void 0 ? (_reportPossibleCrUseOfUIAnimMethods({
                  error: Error()
                }), UIAnimMethods) : UIAnimMethods).fromTo(0, 1), {
                  scale: v3(1, 1)
                }).to((_crd && UIAnimMethods === void 0 ? (_reportPossibleCrUseOfUIAnimMethods({
                  error: Error()
                }), UIAnimMethods) : UIAnimMethods).fromTo(1, 3), {
                  scale: v3(0.447, 0.56)
                }).to((_crd && UIAnimMethods === void 0 ? (_reportPossibleCrUseOfUIAnimMethods({
                  error: Error()
                }), UIAnimMethods) : UIAnimMethods).fromTo(3, 5), {
                  scale: v3(1, 1)
                });
                tween(this.fire.node).repeatForever(fireTween).start();
              } else {
                this.fire.node.active = false;
                this.enemyFire.node.active = true;
                this.enemyFire.node.opacity = 255;
                Tween.stopAllByTarget(this.enemyFire.node);
                var enemyFireTween = tween(this.enemyFire.node).to((_crd && UIAnimMethods === void 0 ? (_reportPossibleCrUseOfUIAnimMethods({
                  error: Error()
                }), UIAnimMethods) : UIAnimMethods).fromTo(0, 1), {
                  scale: v3(0.6, 0.6)
                }).to((_crd && UIAnimMethods === void 0 ? (_reportPossibleCrUseOfUIAnimMethods({
                  error: Error()
                }), UIAnimMethods) : UIAnimMethods).fromTo(1, 3), {
                  scale: v3(0.3, 0.35)
                }).to((_crd && UIAnimMethods === void 0 ? (_reportPossibleCrUseOfUIAnimMethods({
                  error: Error()
                }), UIAnimMethods) : UIAnimMethods).fromTo(3, 5), {
                  scale: v3(0.6, 0.6)
                });
                tween(this.enemyFire.node).repeatForever(enemyFireTween).start();
              }
            } else {
              this.fire.node.active = false;
              this.enemyFire.node.active = false;
              Tween.stopAllByTarget(this.fire.node);
            }
          } // // 设置子弹的拖尾效果
          // const streakEffect = this.m_config.zdwy2;
          // if (streakEffect && streakEffect !== '') {
          //     const streakNode = GameIns.bulletManager.getStreak(streakEffect);
          //     const streakComp = streakNode.getComponent(StreakComp);
          //     streakComp.enabled = true;
          //     GameIns.bulletManager.addStreak(streakNode);
          //     this.streakCall = streakComp.destroySelf.bind(streakComp);
          //     streakComp.setData(this.m_config.wycs[0] || 0.3, this.skinImg.node.width * this.node.scale, this.node);
          // }
          // 设置爆炸效果


          if (this.burstFire) {
            this.burstFire.node.active = this.m_config.bustyle === 38;
          }
        }
        /**
         * 获取子弹的基础攻击力
         * @returns {number} 子弹的攻击力
         */


        _getAttack() {
          var randomValue = Math.random();
          var critChance = this.bulletState.cirt ? this.bulletState.cirt[0] : 0;
          var atkChallenge = this.bulletState.atkChallenge || 0;
          var attack = this.bulletState.attack; // 特殊处理主机类型子弹

          if (this.m_mainEntity instanceof (_crd && MainPlane === void 0 ? (_reportPossibleCrUseOfMainPlane({
            error: Error()
          }), MainPlane) : MainPlane)) {
            var config = this.m_mainEntity.m_config;

            if (config && config.type === 702) {
              if ([27, 28, 29, 37].indexOf(this.getType()) != -1) {
                attack /= 5;
              }
            }
          } // 判断是否暴击


          if (randomValue <= critChance) {
            var critMultiplier = this.bulletState.cirt ? this.bulletState.cirt[1] : 1;
            this.isCirt = true;
            return (attack + atkChallenge) * critMultiplier;
          } else {
            this.isCirt = false;
            return attack + atkChallenge;
          }
        }
        /**
         * 获取子弹对目标的实际攻击力
         * @param {Entity} target 目标实体
         * @returns {number} 实际攻击力
         */


        getAttack(target) {
          if (target === void 0) {
            target = null;
          }

          var attack = this._getAttack(); // if (target) {
          //     // 主机子弹的额外加成
          //     if (this.m_mainEntity instanceof MainPlane) {
          //         attack *= MainPlaneManager.getSkillAddAtkRatio();
          //         // 血量低时的额外攻击力加成
          //         if (MainPlaneManager.data.hp < MainPlaneManager.data.maxhp) {
          //             attack += MainPlaneManager.attributePromote.get(Attribute.HurtAddAtk) || 0;
          //         }
          //         // 高血量时的额外攻击力加成
          //         if (MainPlaneManager.skillMap.get(Attribute.MainHighHpAtk)) {
          //             const skillData = Tools.stringToNumber(MainPlaneManager.skillMap.get(Attribute.MainHighHpAtk), ',');
          //             if (MainPlaneManager.data.hp > MainPlaneManager.data.maxhp * skillData[1] / 100) {
          //                 attack *= 1 + skillData[0] / 100;
          //                 MainPlaneManager.checkSkill(Attribute.MainHighHpAtk);
          //             }
          //         }
          //         // 针对 Boss 的额外攻击力加成
          //         if (target instanceof BossHurt) {
          //             const bossAtkBonus = MainPlaneManager.attributePromote.get(Attribute.BossAtk);
          //             if (bossAtkBonus) {
          //                 attack *= 1 + bossAtkBonus / 100;
          //             }
          //         }
          //         // 针对炮塔的额外攻击力加成
          //         if (target instanceof EnemyTurret) {
          //             const turretAtkBonus = MainPlaneManager.attributePromote.get(Attribute.TurretAtk);
          //             if (turretAtkBonus) {
          //                 attack *= 1 + turretAtkBonus / 100;
          //             }
          //         }
          //         // 低血量敌人的额外攻击力加成
          //         if (MainPlaneManager.skillMap.get(Attribute.EnemyLowHpAtk)) {
          //             const skillData = Tools.stringToNumber(MainPlaneManager.skillMap.get(Attribute.EnemyLowHpAtk), ',');
          //             if ((target instanceof EnemyBase || target instanceof BossHurt) && target.getHpPercent() < skillData[1] / 100) {
          //                 attack *= 1 + skillData[0] / 100;
          //                 MainPlaneManager.checkSkill(Attribute.EnemyLowHpAtk);
          //             }
          //         }
          //     }
          //     // 炮塔技能的额外加成
          //     if (this.m_mainEntity instanceof WinePlane) {
          //         if (this.m_config.bustyle === 40) {
          //             if (this.m_config.id >= 511 && this.m_config.id <= 516) {
          //                 attack += WinePlaneManager.getEquipLvPromote(TurretSkill.Ballistic);
          //             }
          //         } else if (this.m_config.bustyle === 22) {
          //             attack += WinePlaneManager.getEquipLvPromote(TurretSkill.Missile);
          //         }
          //     }
          // }
          // // 处理一次性碰撞组件的攻击力
          // if (this.getComp(OnceCollideComp)) {
          //     return this.getComp(OnceCollideComp).getAttack(target, attack);
          // }
          // // 处理弹射攻击力
          // if (this._catapultCount > 0) {
          //     attack *= this._catapultAtkRatio;
          // }


          return attack;
        }
        /**
         * 获取子弹的类型
         * @returns {number} 子弹的类型
         */


        getType() {
          return this.m_config.bustyle;
        }
        /**
         * 播放子弹命中音效
         */


        playHurtAudio() {// if (this.m_config.hit.length > 0) {
          //     Bullet.playAudio('hit2');
          // }
        }
        /**
         * 子弹碰撞处理
         * @param {ColliderComp} target 碰撞目标
         */


        onCollide(target) {
          // if (this.getComp(ResistBulletComp)) {
          //     this.getComp(ResistBulletComp).onCollide(target);
          // }
          // if (this.getComp(OnceCollideComp)) {
          //     if (this.getComp(OnceCollideComp).onCollide(target)) {
          //         this.m_throughArr.push(target);
          //         if (this.getType() === 48) {
          //             HurtEffectManager.me.createDefaultEffect(target, this, this.m_config.exstyle1, this.m_config.exstyle2);
          //         } else {
          //             HurtEffectManager.me.createHurtEffect(target, this, this.m_config.exstyle1, this.m_config.exstyle2);
          //             if (this.getType() === 26) {
          //                 SpecialBulletManager.addBulletCollide(this, target.entity);
          //             } else if (this.getType() === 59 && this.bulletState.through && this.m_throughArr.length === 1) {
          //                 this._collideEntity = target.entity;
          //                 this.checkCatapultForThrough();
          //             }
          //         }
          //     }
          // } else 
          if (this.m_config.exstyle1) {
            // if (this.getType() === 26) {
            //     HurtEffectManager.me.createHurtEffect(this.m_collideComp, this, this.m_config.exstyle1, this.m_config.exstyle2);
            // } else {
            (_crd && GameIns === void 0 ? (_reportPossibleCrUseOfGameIns({
              error: Error()
            }), GameIns) : GameIns).hurtEffectManager.createHurtEffect(target, this, this.m_config.exstyle1, this.m_config.exstyle2); // }
          } // if (!this.bulletState.through) {
          //     this._collideEntity = target.entity;
          //     this.playHurtAudio();
          //     switch (this.getType()) {
          //         case 26:
          //         case 29:
          //         case 48:
          //             break;
          //         default:
          //             this.remove(true);
          //     }
          //     if (this.getType() === 28) {
          //         const radius = this.bulletState.extra[1];
          //         EnemyManager.EnemyMgr.planes.forEach((enemy) => {
          //             const bulletPos = this.m_collideComp.getScreenPos();
          //             const enemyPos = enemy.getComp(ColliderComp).getScreenPos();
          //             if (Tools.getPosDis(bulletPos, enemyPos) < radius) {
          //                 HurtEffectManager.me.createHurtNumByType(
          //                     enemy.getComp(ColliderComp),
          //                     this,
          //                     this.getAttack(enemy) * this.bulletState.extra[0],
          //                     { x: 0, y: 0 }
          //                 );
          //                 enemy.hurt(this.getAttack(enemy) * this.bulletState.extra[0]);
          //             }
          //         });
          //     }
          //     if (this.getType() === 26) {
          //         // GameIns.audioManager.playEffect('ele');
          //     }
          // }

        }
        /**
         * 子弹超出屏幕处理
         */


        onOutScreen() {
          if (this.m_lifeTime > 0) {
            var currentTime = game.totalTime;
            this.aliveTime = (currentTime - this.m_createTime) / 1000;
          }

          switch (this.getType()) {
            case 26:
            case 29:
            case 48:
            case 54:
            case 55:
              break;

            default:
              if (this.aliveTime >= this.m_lifeTime) {
                this.remove();
              }

          }
        }
        /**
         * 移除子弹
         * @param {boolean} force 是否强制移除
         */


        remove(force) {
          if (force === void 0) {
            force = false;
          }

          if (!force || !this.checkCatapult()) {
            this.willRemove();

            if (this.collideDun) {
              (_crd && GameIns === void 0 ? (_reportPossibleCrUseOfGameIns({
                error: Error()
              }), GameIns) : GameIns).hurtEffectManager.playBulletCollideDunAnim(this.node.position);
            }

            (_crd && GameIns === void 0 ? (_reportPossibleCrUseOfGameIns({
              error: Error()
            }), GameIns) : GameIns).bulletManager.removeBullet(this);

            try {
              if (this.streakCall) {
                this.streakCall();
                this.streakCall = null;
              }
            } catch (error) {
              console.error('Error during streakCall:', error);
            }

            this.m_mainEntity = null;
          }

          if (this.getType() === 37) {
            this.skinImg.node.stopAllActions();
          }
        }
        /**
         * 子弹死亡移除
         */


        dieRemove() {
          this.willRemove();

          try {
            (_crd && GameIns === void 0 ? (_reportPossibleCrUseOfGameIns({
              error: Error()
            }), GameIns) : GameIns).hurtEffectManager.playBulletDieAnim(this.node.position);
          } catch (error) {
            console.error('Error during dieRemove:', error);
          }

          (_crd && GameIns === void 0 ? (_reportPossibleCrUseOfGameIns({
            error: Error()
          }), GameIns) : GameIns).bulletManager.removeBullet(this, false);

          try {
            if (this.streakCall) {
              this.streakCall();
              this.streakCall = null;
            }
          } catch (error) {
            console.error('Error during streakCall:', error);
          }

          this.m_mainEntity = null;
        }
        /**
         * 子弹移除前的清理操作
         */


        willRemove() {
          if (this.m_collideComp) {
            this.m_collideComp.enabled = false;
          }

          if (this.skinImg) {
            this.skinImg.spriteFrame = null;
          }

          if (this.fire && this.fire.node.active) {
            this.fire.node.opacity = 0;
            this.fire.node.stopAllActions();
          }

          if (this.enemyFire && this.enemyFire.node.active) {
            this.enemyFire.node.opacity = 0;
            this.enemyFire.node.stopAllActions();
          }

          this.m_throughArr.splice(0);
        } //     /**
        //      * 检查子弹是否可以弹射
        //      * @returns {boolean} 是否成功弹射
        //      */


        checkCatapult() {
          var isCatapulted = false; //         if (
          //             this.bulletState.catapult &&
          //             this.bulletState.catapult.length > 0 &&
          //             this._catapultCount < this.bulletState.catapult[0]
          //         ) {
          //             this._catapultTargets.push(this._collideEntity);
          //             const rangeEnemies = GameFunc.GFunc.getRangeEnemyParam(
          //                 this._collideEntity,
          //                 this.node.position,
          //                 this.bulletState.catapult[1]
          //             );
          //             for (let i = 0; i < rangeEnemies.length; i++) {
          //                 const enemyData = rangeEnemies[i];
          //                 if (
          //                     enemyData.enemy &&
          //                     !enemyData.enemy.isDead &&
          //                     !Tools.arrContain(this._catapultTargets, enemyData.enemy)
          //                 ) {
          //                     this.node.angle = enemyData.angle;
          //                     const bulletFlyComp = this.getComp(BulletFly);
          //                     if (bulletFlyComp) {
          //                         bulletFlyComp.setData(enemyData.angle, this.m_mainEntity, this.enemy);
          //                     }
          //                     if (this.m_collideComp) {
          //                         const newPos = this.m_collideComp.initPos.rotate(
          //                             misc.degreesToRadians(enemyData.angle)
          //                         );
          //                         this.m_collideComp.setPos(newPos.x, newPos.y);
          //                     }
          //                     this._catapultCount++;
          //                     this._catapultAtkRatio *= this.bulletState.catapult[2];
          //                     isCatapulted = true;
          //                     break;
          //                 }
          //             }
          //         }

          return isCatapulted;
        } //     /**
        //      * 检查穿透子弹是否可以弹射
        //      * @returns {boolean} 是否成功弹射
        //      */
        //     checkCatapultForThrough() {
        //         if (
        //             this.bulletState.catapult &&
        //             this.bulletState.catapult.length > 0 &&
        //             this._catapultCount < this.bulletState.catapult[0]
        //         ) {
        //             const newBullet = GameIns.bulletManager.getBullet(this.bulletID, this.enemy);
        //             if (newBullet) {
        //                 BattleLayer.me.addBullet(newBullet);
        //                 const newState = {
        //                     attack: this.bulletState.attack,
        //                     extra: this.bulletState.extra,
        //                     cirt: this.bulletState.cirt,
        //                     fireIntensify: this.bulletState.fireIntensify,
        //                     catapult: this.bulletState.catapult,
        //                     atkChallenge: this.bulletState.atkChallenge,
        //                     clone: true,
        //                 };
        //                 newBullet.init(
        //                     this.enemy,
        //                     {
        //                         x: this.node.x,
        //                         y: this.node.y,
        //                         angle: 180 - this.node.angle,
        //                     },
        //                     newState,
        //                     this.m_mainEntity
        //                 );
        //                 newBullet.onCatapultForThrough(this._collideEntity);
        //             }
        //         }
        //         return false;
        //     }
        //     /**
        //      * 处理穿透子弹的弹射逻辑
        //      * @param {Entity} target 碰撞目标
        //      * @returns {boolean} 是否成功弹射
        //      */
        //     onCatapultForThrough(target) {
        //         this._collideEntity = target;
        //         const isCatapulted = this.checkCatapult();
        //         if (!isCatapulted) {
        //             this.remove();
        //         }
        //         return isCatapulted;
        //     }
        //     /**
        //      * 设置碰撞组件的启用状态
        //      * @param {boolean} enabled 是否启用
        //      */


        setColliderEnable(enabled) {
          if (this.m_collideComp) {
            this.m_collideComp.enabled = enabled;
          }
        }
        /**
         * 更新子弹逻辑
         * @param {number} deltaTime 帧间隔时间
         */


        update(deltaTime) {
          if (!(_crd && GameConst === void 0 ? (_reportPossibleCrUseOfGameConst({
            error: Error()
          }), GameConst) : GameConst).GameAble) {
            return;
          }

          this.m_comps.forEach(comp => {
            comp.update(deltaTime);
          }); // if (
          //     (MainPlaneManager.isShow || GameIns.bulletManager.fireShellUINode) &&
          //     this.node.y > 1000
          // ) {
          //     this.remove();
          // }

          if (this.m_config.bustyle === 55 && this.node.y < -2000) {
            this.remove();
          }
        }
        /**
         * 更新子弹的组件逻辑
         * @param {number} deltaTime 帧间隔时间
         */


        updateComp(deltaTime) {
          this.m_comps.forEach(comp => {
            if (comp.updateComp) {
              comp.updateComp(deltaTime);
            }
          });
        }
        /**
         * 刷新子弹状态
         */


        refresh() {
          this.collideDun = false;
          this._catapultCount = 0;
          this._catapultAtkRatio = 1;
          this._collideEntity = null;

          this._catapultTargets.splice(0);
        } //     /**
        //      * 检查子弹是否可以与目标碰撞
        //      * @param {Entity} target 碰撞目标
        //      * @returns {boolean} 是否可以碰撞
        //      */
        //     canCollideEntity(target) {
        //         return (
        //             this._catapultTargets.length === 0 ||
        //             this._catapultTargets.indexOf(target) < 0
        //         );
        //     }
        //     /**
        //      * 处理蝙蝠技能的逻辑
        //      */
        //     batSkill() {
        //         if (this.m_config.exstyle1) {
        //             const randomScale = Tools.random_int(18, 30) / 100;
        //             HurtEffectManager.me.createHurtEffect(
        //                 this.m_collideComp,
        //                 this,
        //                 this.m_config.exstyle1,
        //                 randomScale
        //             );
        //             this.remove();
        //         }
        //     }
        //     /**
        //      * 设置 Boss 拳头子弹的皮肤
        //      */
        //     setBossBoxingFistSkin() {
        //         this.skinImg.node.opacity = 0;
        //         const parent = this.skinImg.node.parent;
        //         // 创建拳头节点
        //         const fistNode = new Node("fist");
        //         fistNode.parent = parent;
        //         // 创建尾部节点
        //         const tailNode = new Node("tail");
        //         tailNode.parent = fistNode;
        //         tailNode.anchorY = 0.05;
        //         tailNode.y = 34;
        //         GameFunc.setImage(tailNode.addComponent(Sprite), "fist_zd_51_0", GameIns.bulletManager.enemyBulletAtlas);
        //         // 创建底部节点
        //         const bottomNode = new Node("6");
        //         bottomNode.parent = fistNode;
        //         bottomNode.y = -35;
        //         GameFunc.setImage(bottomNode.addComponent(Sprite), "fist_zd_51_6", GameIns.bulletManager.enemyBulletAtlas);
        //         // 创建左上节点
        //         const leftTopNode = new Node("nbl");
        //         leftTopNode.parent = fistNode;
        //         leftTopNode.setAnchorPoint(1, 1);
        //         leftTopNode.y = 90;
        //         GameFunc.setImage(leftTopNode.addComponent(Sprite), "fist_zd_51_4", GameIns.bulletManager.enemyBulletAtlas);
        //         // 创建右上节点
        //         const rightTopNode = new Node("nbr");
        //         rightTopNode.parent = fistNode;
        //         rightTopNode.setAnchorPoint(1, 1);
        //         rightTopNode.y = 90;
        //         rightTopNode.scaleX = -1;
        //         GameFunc.setImage(rightTopNode.addComponent(Sprite), "fist_zd_51_4", GameIns.bulletManager.enemyBulletAtlas);
        //         // 创建左下节点
        //         const leftBottomNode = new Node("nrl");
        //         leftBottomNode.parent = fistNode;
        //         leftBottomNode.setAnchorPoint(0.73, 0.97);
        //         leftBottomNode.x = -23;
        //         leftBottomNode.y = -3;
        //         leftBottomNode.scaleX = -1;
        //         GameFunc.setImage(leftBottomNode.addComponent(Sprite), "fist_zd_51_3", GameIns.bulletManager.enemyBulletAtlas);
        //         // 创建右下节点
        //         const rightBottomNode = new Node("nrr");
        //         rightBottomNode.parent = fistNode;
        //         rightBottomNode.setAnchorPoint(0.73, 0.97);
        //         rightBottomNode.x = 23;
        //         rightBottomNode.y = -3;
        //         GameFunc.setImage(rightBottomNode.addComponent(Sprite), "fist_zd_51_3", GameIns.bulletManager.enemyBulletAtlas);
        //         // 创建主体节点
        //         const bodyNode = new Node("body");
        //         bodyNode.parent = fistNode;
        //         GameFunc.setImage(bodyNode.addComponent(Sprite), "fist_zd_51_1", GameIns.bulletManager.enemyBulletAtlas);
        //         // 动画时间
        //         const frameTime = GameConfig.ActionFrameTime;
        //         // 左下节点动画
        //         const leftBottomX = leftBottomNode.x;
        //         const leftBottomY = leftBottomNode.y;
        //         tween(leftBottomNode)
        //             .to(5 * frameTime, { angle: 0.8, x: leftBottomX + 4, y: leftBottomY + 25.6 })
        //             .to(3 * frameTime, { angle: -33.8, x: leftBottomX + 19.4, y: leftBottomY + 57.8, scaleX: -1.2, scaleY: 1.2 })
        //             .call(() => {
        //                 leftBottomNode.scaleX = -1;
        //                 leftBottomNode.scaleY = 1;
        //             })
        //             .start();
        //         // 右下节点动画
        //         const rightBottomX = rightBottomNode.x;
        //         const rightBottomY = rightBottomNode.y;
        //         tween(rightBottomNode)
        //             .to(5 * frameTime, { angle: -0.8, x: rightBottomX - 4, y: rightBottomY + 25.6 })
        //             .to(3 * frameTime, { angle: 33.8, x: rightBottomX - 19.4, y: rightBottomY + 57.8, scale: 1.2 })
        //             .call(() => {
        //                 rightBottomnode.setScale(1;
        //             })
        //             .start();
        //         // 主体节点动画
        //         const bodyY = bodyNode.y;
        //         tween(bodyNode)
        //             .to(5 * frameTime, { y: bodyY + 21.5 })
        //             .start();
        //         // 左上节点动画
        //         const leftTopY = leftTopNode.y;
        //         tween(leftTopNode)
        //             .to(5 * frameTime, { angle: 12.85, y: leftTopY - 4 })
        //             .start();
        //         // 右上节点动画
        //         const rightTopY = rightTopNode.y;
        //         tween(rightTopNode)
        //             .to(5 * frameTime, { angle: -12.85, y: rightTopY - 4 })
        //             .start();
        //         // 底部节点动画
        //         const bottomY = bottomNode.y;
        //         tween(bottomNode)
        //             .delay(10 * frameTime)
        //             .to(3 * frameTime, { scaleX: 1, scaleY: 1.3, y: bottomY - 26.3 })
        //             .start();
        //         // 尾部节点动画
        //         tailNode.scaleX = 0.8;
        //         tailNode.scaleY = 0.5;
        //         tailNode.opacity = 0;
        //         tween(tailNode)
        //             .to(5 * frameTime, { scaleX: 0.5, scaleY: 0.7, opacity: 255 })
        //             .to(20 * frameTime, { scaleX: 0.7, scaleY: 1.2 })
        //             .start();
        //     }
        //     /**
        //      * 设置 Boss 加特林子弹的皮肤
        //      */
        //     setBossBoxingGatlinSkin() {
        //         this.skinImg.node.opacity = 0;
        //         const parent = this.skinImg.node.parent;
        //         // 创建加特林节点
        //         const gatlinNode = new Node("gatlin");
        //         gatlinNode.parent = parent;
        //         // 创建尾部节点
        //         const tailNode = new Node("tail");
        //         tailNode.parent = gatlinNode;
        //         tailNode.anchorY = 0.04;
        //         tailNode.y = -50;
        //         GameFunc.setImage(tailNode.addComponent(Sprite), "fist_zd_52_0", GameIns.bulletManager.enemyBulletAtlas);
        //         // 创建子弹节点
        //         const bulletNode = new Node("bullet");
        //         bulletNode.parent = gatlinNode;
        //         GameFunc.setImage(bulletNode.addComponent(Sprite), "fist_zd_52_1", GameIns.bulletManager.enemyBulletAtlas);
        //         // 创建顶部节点
        //         const topNode = new Node("top");
        //         topNode.parent = gatlinNode;
        //         GameFunc.setImage(topNode.addComponent(Sprite), "fist_zd_52_2", GameIns.bulletManager.enemyBulletAtlas);
        //         topNode.getComponent(Sprite).dstBlendFactor = macro.ONE;
        //         // 动画时间
        //         const frameTime = GameConfig.ActionFrameTime;
        //         // 顶部节点动画
        //         tween(topNode)
        //             .to(3 * frameTime, { scaleX: 1.25, scaleY: 0.68, y: -20 })
        //             .to(5 * frameTime, { scaleX: 1, scaleY: 1 })
        //             .start();
        //         // 子弹节点动画
        //         tween(bulletNode)
        //             .to(3 * frameTime, { scale: 1.15 })
        //             .to(5 * frameTime, { scaleX: 1, scaleY: 1 })
        //             .start();
        //         // 尾部节点动画
        //         tween(tailNode)
        //             .to(2 * frameTime, { scale: 0.85 })
        //             .to(2 * frameTime, { scale: 1 })
        //             .to(2 * frameTime, { scale: 0.85 })
        //             .to(2 * frameTime, { scale: 1 })
        //             .start();
        //     }
        //     /**
        //      * 为子弹添加尾部效果
        //      */
        //     addTail() {
        //         const tailNode = new Node("tail");
        //         GameFunc.setImage(
        //             tailNode.addComponent(Sprite),
        //             "gatlin_missle_tail",
        //             GameIns.bulletManager.enemyComAtlas
        //         );
        //         this.skinImg.node.parent.addChild(tailNode);
        //         tailnode.setScale(4;
        //         tailNode.anchorY = 0;
        //     }

        /**
         * 子弹音效的最大播放数量
         */
        //     /**
        //      * 子弹音效的播放状态
        //      */
        //     static audios = new Map();
        //     /**
        //      * 播放子弹音效
        //      * @param {string} audioName 音效名称
        //      */
        //     static playAudio(audioName) {
        //         const maxNum = Bullet.audioMaxNum[audioName];
        //         let currentNum = Bullet.audios.get(audioName) || 0;
        //         if (currentNum < maxNum) {
        //             Bullet.audios.set(audioName, ++currentNum);
        //             // const audioId = GameIns.audioManager.playEffectSync(audioName);
        //             // audioEngine.setFinishCallback(audioId, () => {
        //             //     Bullet.onAudioFinish(audioName);
        //             // });
        //         }
        //     }
        //     /**
        //      * 子弹音效播放完成的回调
        //      * @param {string} audioName 音效名称
        //      */
        //     static onAudioFinish(audioName) {
        //         const currentNum = Bullet.audios.get(audioName);
        //         Bullet.audios.set(audioName, Math.max(0, currentNum - 1));
        //     }


      }, _class3.PrefabName = "Bullet", _class3.audioMaxNum = {
        hit1: 5,
        hit2: 3,
        hit3: 2,
        hit4: 5,
        hit5: 5,
        hit6: 5,
        swordHit: 5
      }, _class3), (_descriptor = _applyDecoratedDescriptor(_class2.prototype, "skinImg", [_dec2], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function initializer() {
          return null;
        }
      }), _descriptor2 = _applyDecoratedDescriptor(_class2.prototype, "fire", [_dec3], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function initializer() {
          return null;
        }
      }), _descriptor3 = _applyDecoratedDescriptor(_class2.prototype, "enemyFire", [_dec4], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function initializer() {
          return null;
        }
      }), _descriptor4 = _applyDecoratedDescriptor(_class2.prototype, "burstFire", [_dec5], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function initializer() {
          return null;
        }
      })), _class2)) || _class));

      _cclegacy._RF.pop();

      _crd = false;
    }
  };
});
//# sourceMappingURL=0327660c6417e6b430ccc7d906c2d0662723ff6f.js.map