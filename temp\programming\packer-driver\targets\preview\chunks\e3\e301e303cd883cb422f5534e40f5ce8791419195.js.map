{"version": 3, "sources": ["file:///E:/M2Game/Client/assets/scripts/Game/ui/plane/enemy/EnemyAttrComponent.ts"], "names": ["_decorator", "Component", "rect", "EnemyAttrData", "GameEnum", "Tools", "EnemyAttrDoctorCom", "EnemyAttrShieldCom", "ccclass", "property", "EnemyAttrComponent", "entity", "attr", "attrData", "awakeRect", "comArr", "comDict", "reset", "init", "attrString", "attributes", "split", "length", "parts", "type", "parseInt", "param", "push", "components", "component", "hasAttribution", "EnemyAttr", "Doctor", "addScript", "node", "Shield", "i", "existingComponent", "arrC<PERSON>ain", "destroy", "splice", "newComponent", "updateGameLogic", "deltaTime", "attrType", "die", "showAttrShield"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAASA,MAAAA,U,OAAAA,U;AAAYC,MAAAA,S,OAAAA,S;AAAWC,MAAAA,I,OAAAA,I;;AACvBC,MAAAA,a,iBAAAA,a;;AACFC,MAAAA,Q;;AACEC,MAAAA,K,iBAAAA,K;;AACFC,MAAAA,kB;;AACAC,MAAAA,kB;;;;;;;;;OAID;AAAEC,QAAAA,OAAF;AAAWC,QAAAA;AAAX,O,GAAwBT,U;;yBAGTU,kB,GADpBF,O,UAAD,MACqBE,kBADrB,SACgDT,SADhD,CAC0D;AAAA;AAAA;AAAA,eAEtDU,MAFsD,GAE7C,IAF6C;AAEvC;AAFuC,eAGtDC,IAHsD,GAG/C,EAH+C;AAG3C;AAH2C,eAItDC,QAJsD,GAI3C,EAJ2C;AAIvC;AAJuC,eAKtDC,SALsD,GAK1CZ,IAAI,EALsC;AAKlC;AALkC,eAMtDa,MANsD,GAM7C,EAN6C;AAMzC;AANyC,eAOtDC,OAPsD,GAO5C,EAP4C;AAAA;;AAOxC;;AAGd;AACJ;AACA;AACIC,QAAAA,KAAK,GAAG;AACJ,eAAKN,MAAL,GAAc,IAAd;AACA,eAAKC,IAAL,GAAY,EAAZ;AACA,eAAKC,QAAL,GAAgB,EAAhB;AACA,eAAKC,SAAL,GAAiBZ,IAAI,EAArB;AACA,eAAKa,MAAL,GAAc,EAAd;AACA,eAAKC,OAAL,GAAe,EAAf;AACH;AAED;AACJ;AACA;AACA;AACA;;;AACIE,QAAAA,IAAI,CAACP,MAAD,EAASQ,UAAT,EAAqB;AACrB,eAAKR,MAAL,GAAcA,MAAd;AACA,eAAKC,IAAL,GAAY,EAAZ;AACA,eAAKC,QAAL,GAAgB,EAAhB,CAHqB,CAKrB;;AACA,cAAMO,UAAU,GAAGD,UAAU,CAACE,KAAX,CAAiB,GAAjB,CAAnB;;AACA,eAAK,IAAMT,IAAX,IAAmBQ,UAAnB,EAA+B;AAC3B,gBAAIR,IAAI,CAACU,MAAL,GAAc,CAAlB,EAAqB;AACjB,kBAAMC,KAAK,GAAGX,IAAI,CAACS,KAAL,CAAW,GAAX,CAAd;;AACA,kBAAIE,KAAK,CAACD,MAAN,GAAe,CAAnB,EAAsB;AAClB,oBAAMT,QAAQ,GAAG;AAAA;AAAA,qDAAjB;AACAA,gBAAAA,QAAQ,CAACW,IAAT,GAAgBC,QAAQ,CAACF,KAAK,CAAC,CAAD,CAAN,CAAxB;AACAV,gBAAAA,QAAQ,CAACa,KAAT,GAAiBH,KAAK,CAAC,CAAD,CAAtB;AACA,qBAAKV,QAAL,CAAcA,QAAQ,CAACW,IAAvB,IAA+BX,QAA/B;AACA,qBAAKD,IAAL,CAAUe,IAAV,CAAed,QAAQ,CAACW,IAAxB;AACH;AACJ;AACJ;;AAED,cAAMI,UAAU,GAAG,EAAnB;AACA,cAAIC,SAAJ,CArBqB,CAuBrB;;AACA,cAAI,KAAKC,cAAL,CAAoB;AAAA;AAAA,oCAASC,SAAT,CAAmBC,MAAvC,CAAJ,EAAoD;AAChDH,YAAAA,SAAS,GAAG;AAAA;AAAA,gCAAMI,SAAN,CAAgB,KAAKC,IAArB;AAAA;AAAA,yDAAZ;AACAL,YAAAA,SAAS,CAACX,IAAV,CAAe,KAAKL,QAAL,CAAc;AAAA;AAAA,sCAASkB,SAAT,CAAmBC,MAAjC,CAAf;AACAJ,YAAAA,UAAU,CAACD,IAAX,CAAgBE,SAAhB;AACH,WA5BoB,CA8BrB;;;AACA,cAAI,KAAKC,cAAL,CAAoB;AAAA;AAAA,oCAASC,SAAT,CAAmBI,MAAvC,CAAJ,EAAoD;AAChDN,YAAAA,SAAS,GAAG;AAAA;AAAA,gCAAMI,SAAN,CAAgB,KAAKC,IAArB;AAAA;AAAA,yDAAZ;AACAL,YAAAA,SAAS,CAACX,IAAV,CAAeP,MAAf,EAAuB,KAAKE,QAAL,CAAc;AAAA;AAAA,sCAASkB,SAAT,CAAmBI,MAAjC,CAAvB;AACAP,YAAAA,UAAU,CAACD,IAAX,CAAgBE,SAAhB;AACH,WAnCoB,CAqCrB;;;AACA,eAAK,IAAIO,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAG,KAAKrB,MAAL,CAAYO,MAAhC,EAAwCc,CAAC,EAAzC,EAA6C;AACzC,gBAAMC,iBAAiB,GAAG,KAAKtB,MAAL,CAAYqB,CAAZ,CAA1B;;AACA,gBAAI,CAAC;AAAA;AAAA,gCAAME,UAAN,CAAiBV,UAAjB,EAA6BS,iBAA7B,CAAL,EAAsD;AAClDA,cAAAA,iBAAiB,CAACE,OAAlB;AACA,mBAAKxB,MAAL,CAAYyB,MAAZ,CAAmBJ,CAAnB,EAAsB,CAAtB;AACA,mBAAKpB,OAAL,CAAaqB,iBAAiB,CAACxB,QAAlB,CAA2BW,IAAxC,IAAgD,IAAhD;AACAY,cAAAA,CAAC;AACJ;AACJ,WA9CoB,CAgDrB;;;AACA,eAAK,IAAMK,YAAX,IAA2Bb,UAA3B,EAAuC;AACnC,gBAAI,CAAC;AAAA;AAAA,gCAAMU,UAAN,CAAiB,KAAKvB,MAAtB,EAA8B0B,YAA9B,CAAL,EAAkD;AAC9C,mBAAK1B,MAAL,CAAYY,IAAZ,CAAiBc,YAAjB;AACA,mBAAKzB,OAAL,CAAayB,YAAY,CAAC5B,QAAb,CAAsBW,IAAnC,IAA2CiB,YAA3C;AACH;AACJ;AACJ;AAED;AACJ;AACA;AACA;;;AACIC,QAAAA,eAAe,CAACC,SAAD,EAAY;AACvB,eAAK,IAAMd,SAAX,IAAwB,KAAKd,MAA7B,EAAqC;AACjCc,YAAAA,SAAS,CAACa,eAAV,CAA0BC,SAA1B;AACH;AACJ;AAED;AACJ;AACA;AACA;AACA;;;AACIb,QAAAA,cAAc,CAACc,QAAD,EAAW;AACrB,iBAAO;AAAA;AAAA,8BAAMN,UAAN,CAAiB,KAAK1B,IAAtB,EAA4BgC,QAA5B,CAAP;AACH;AAED;AACJ;AACA;;;AACIC,QAAAA,GAAG,GAAG;AACF,eAAK,IAAMhB,SAAX,IAAwB,KAAKd,MAA7B,EAAqC;AACjCc,YAAAA,SAAS,CAACgB,GAAV;AACH;AACJ;AAED;AACJ;AACA;;;AACIC,QAAAA,cAAc,GAAG;AACb,eAAK,IAAMjB,SAAX,IAAwB,KAAKd,MAA7B,EAAqC;AACjC,gBAAIc,SAAS,CAACiB,cAAd,EAA8B;AAC1BjB,cAAAA,SAAS,CAACiB,cAAV;AACH;AACJ;AACJ;;AAzHqD,O", "sourcesContent": ["import { _decorator, Component, rect } from \"cc\";\r\nimport { EnemyAttrData } from \"../../../data/EnemyData\";\r\nimport GameEnum from \"../../../const/GameEnum\";\r\nimport { Tools } from \"../../../utils/Tools\";\r\nimport EnemyAttrDoctorCom from \"./EnemyAttrDoctorCom\";\r\nimport EnemyAttrShieldCom from \"./EnemyAttrShieldCom\";\r\n\r\n\r\n\r\nconst { ccclass, property } = _decorator;\r\n\r\n@ccclass\r\nexport default class EnemyAttrComponent extends Component {\r\n\r\n    entity = null; // 敌人实体\r\n    attr = []; // 属性列表\r\n    attrData = {}; // 属性数据映射\r\n    awakeRect = rect(); // 唤醒区域\r\n    comArr = []; // 属性组件数组\r\n    comDict = {}; // 属性组件字典\r\n\r\n\r\n    /**\r\n     * 重置组件状态\r\n     */\r\n    reset() {\r\n        this.entity = null;\r\n        this.attr = [];\r\n        this.attrData = {};\r\n        this.awakeRect = rect();\r\n        this.comArr = [];\r\n        this.comDict = {};\r\n    }\r\n\r\n    /**\r\n     * 初始化属性组件\r\n     * @param {Node} entity 敌人实体\r\n     * @param {string} attrString 属性字符串\r\n     */\r\n    init(entity, attrString) {\r\n        this.entity = entity;\r\n        this.attr = [];\r\n        this.attrData = {};\r\n\r\n        // 解析属性字符串\r\n        const attributes = attrString.split('#');\r\n        for (const attr of attributes) {\r\n            if (attr.length > 0) {\r\n                const parts = attr.split(';');\r\n                if (parts.length > 1) {\r\n                    const attrData = new EnemyAttrData();\r\n                    attrData.type = parseInt(parts[0]);\r\n                    attrData.param = parts[1];\r\n                    this.attrData[attrData.type] = attrData;\r\n                    this.attr.push(attrData.type);\r\n                }\r\n            }\r\n        }\r\n\r\n        const components = [];\r\n        let component;\r\n\r\n        // 初始化医生属性组件\r\n        if (this.hasAttribution(GameEnum.EnemyAttr.Doctor)) {\r\n            component = Tools.addScript(this.node, EnemyAttrDoctorCom);\r\n            component.init(this.attrData[GameEnum.EnemyAttr.Doctor]);\r\n            components.push(component);\r\n        }\r\n\r\n        // 初始化护盾属性组件\r\n        if (this.hasAttribution(GameEnum.EnemyAttr.Shield)) {\r\n            component = Tools.addScript(this.node, EnemyAttrShieldCom);\r\n            component.init(entity, this.attrData[GameEnum.EnemyAttr.Shield]);\r\n            components.push(component);\r\n        }\r\n\r\n        // 清理无效组件\r\n        for (let i = 0; i < this.comArr.length; i++) {\r\n            const existingComponent = this.comArr[i];\r\n            if (!Tools.arrContain(components, existingComponent)) {\r\n                existingComponent.destroy();\r\n                this.comArr.splice(i, 1);\r\n                this.comDict[existingComponent.attrData.type] = null;\r\n                i--;\r\n            }\r\n        }\r\n\r\n        // 添加新组件\r\n        for (const newComponent of components) {\r\n            if (!Tools.arrContain(this.comArr, newComponent)) {\r\n                this.comArr.push(newComponent);\r\n                this.comDict[newComponent.attrData.type] = newComponent;\r\n            }\r\n        }\r\n    }\r\n\r\n    /**\r\n     * 更新游戏逻辑\r\n     * @param {number} deltaTime 帧间隔时间\r\n     */\r\n    updateGameLogic(deltaTime) {\r\n        for (const component of this.comArr) {\r\n            component.updateGameLogic(deltaTime);\r\n        }\r\n    }\r\n\r\n    /**\r\n     * 检查是否具有指定属性\r\n     * @param {number} attrType 属性类型\r\n     * @returns {boolean} 是否具有该属性\r\n     */\r\n    hasAttribution(attrType) {\r\n        return Tools.arrContain(this.attr, attrType);\r\n    }\r\n\r\n    /**\r\n     * 处理敌人死亡逻辑\r\n     */\r\n    die() {\r\n        for (const component of this.comArr) {\r\n            component.die();\r\n        }\r\n    }\r\n\r\n    /**\r\n     * 显示护盾属性\r\n     */\r\n    showAttrShield() {\r\n        for (const component of this.comArr) {\r\n            if (component.showAttrShield) {\r\n                component.showAttrShield();\r\n            }\r\n        }\r\n    }\r\n}"]}