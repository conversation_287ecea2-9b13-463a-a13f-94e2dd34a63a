{"version": 3, "sources": ["file:///E:/M2Game/Client/assets/scripts/Game/ui/plane/boss/BossCollider.ts"], "names": ["_decorator", "v2", "ColliderComp", "BossUnitBase", "ccclass", "property", "BossCollider", "create", "owner", "data", "collide<PERSON>omp", "addComp", "init", "setData", "node", "position", "x", "y", "m_comps", "for<PERSON>ach", "comp", "setCollidePosition", "setPos", "setCollideAble", "enabled", "updateGameLogic", "deltaTime", "update"], "mappings": ";;;;;;;;;;;;;;;;;;;;AAASA,MAAAA,U,OAAAA,U;AAAuBC,MAAAA,E,OAAAA,E;;AACvBC,MAAAA,Y,iBAAAA,Y;;AACFC,MAAAA,Y;;;;;;;;;OAED;AAAEC,QAAAA,OAAF;AAAWC,QAAAA;AAAX,O,GAAwBL,U;;yBAGTM,Y,WADpBF,OAAO,CAAC,cAAD,C,gBAAR,MACqBE,YADrB;AAAA;AAAA,wCACuD;AAEnDC,QAAAA,MAAM,CAACC,KAAD,EAAaC,IAAb,EAA8B;AAChC,eAAKD,KAAL,GAAaA,KAAb;;AACA,cAAI,CAAC,KAAKE,WAAV,EAAuB;AACnB,iBAAKA,WAAL,GAAmB,KAAKC,OAAL;AAAA;AAAA,8CAA2B;AAAA;AAAA,+CAA3B,CAAnB;AACH;;AACD,eAAKD,WAAL,CAAiBE,IAAjB,CAAsB,IAAtB;AACA,eAAKF,WAAL,CAAiBG,OAAjB,CAAyBJ,IAAzB,EAA+B,KAAKD,KAApC,EAA2CP,EAAE,CAAC,KAAKa,IAAL,CAAUC,QAAV,CAAmBC,CAApB,EAAuB,KAAKF,IAAL,CAAUC,QAAV,CAAmBE,CAA1C,CAA7C;AAEA,eAAKC,OAAL,CAAaC,OAAb,CAAsBC,IAAD,IAAU;AAC3BA,YAAAA,IAAI,CAACR,IAAL,CAAU,IAAV;AACH,WAFD;AAGH;;AAEDS,QAAAA,kBAAkB,CAACL,CAAD,EAAYC,CAAZ,EAA6B;AAAA;;AAC3C,oCAAKP,WAAL,+BAAkBY,MAAlB,CAAyBN,CAAzB,EAA4BC,CAA5B;AACH;;AAEDM,QAAAA,cAAc,CAACC,OAAD,EAAyB;AACnC,cAAI,KAAKd,WAAT,EAAsB;AAClB,iBAAKA,WAAL,CAAiBc,OAAjB,GAA2BA,OAA3B;AACH;AACJ;;AAEDC,QAAAA,eAAe,CAACC,SAAD,EAA0B;AACrC,eAAKR,OAAL,CAAaC,OAAb,CAAsBC,IAAD,IAAU;AAC3BA,YAAAA,IAAI,CAACO,MAAL,CAAYD,SAAZ;AACH,WAFD;AAGH;;AA7BkD,O", "sourcesContent": ["import { _decorator, Component, v2, Vec3 } from 'cc';\r\nimport { ColliderComp } from '../../base/ColliderComp';\r\nimport BossUnitBase from './BossUnitBase';\r\n\r\nconst { ccclass, property } = _decorator;\r\n\r\n@ccclass('Boss<PERSON>ollider')\r\nexport default class BossCollider extends BossUnitBase {\r\n\r\n    create(owner: any, data: any): void {\r\n        this.owner = owner;\r\n        if (!this.collideComp) {\r\n            this.collideComp = this.addComp(ColliderComp, new ColliderComp());\r\n        }\r\n        this.collideComp.init(this);\r\n        this.collideComp.setData(data, this.owner, v2(this.node.position.x, this.node.position.y));\r\n\r\n        this.m_comps.forEach((comp) => {\r\n            comp.init(this);\r\n        });\r\n    }\r\n\r\n    setCollidePosition(x: number, y: number): void {\r\n        this.collideComp?.setPos(x, y);\r\n    }\r\n\r\n    setCollideAble(enabled: boolean): void {\r\n        if (this.collideComp) {\r\n            this.collideComp.enabled = enabled;\r\n        }\r\n    }\r\n\r\n    updateGameLogic(deltaTime: number): void {\r\n        this.m_comps.forEach((comp) => {\r\n            comp.update(deltaTime);\r\n        });\r\n    }\r\n}"]}