System.register(["__unresolved_0", "cc", "__unresolved_1", "__unresolved_2", "__unresolved_3"], function (_export, _context) {
  "use strict";

  var _reporterNs, _cclegacy, __checkObsolete__, __checkObsoleteInNamespace__, _decorator, Component, Label, Node, randomRangeInt, UITransform, logDebug, List, BagSortType, TabStatus, _dec, _dec2, _dec3, _class, _class2, _descriptor, _descriptor2, _crd, ccclass, property, mockItems, mockPlaneParts, BagGrid;

  function _initializerDefineProperty(target, property, descriptor, context) { if (!descriptor) return; Object.defineProperty(target, property, { enumerable: descriptor.enumerable, configurable: descriptor.configurable, writable: descriptor.writable, value: descriptor.initializer ? descriptor.initializer.call(context) : void 0 }); }

  function _applyDecoratedDescriptor(target, property, decorators, descriptor, context) { var desc = {}; Object.keys(descriptor).forEach(function (key) { desc[key] = descriptor[key]; }); desc.enumerable = !!desc.enumerable; desc.configurable = !!desc.configurable; if ('value' in desc || desc.initializer) { desc.writable = true; } desc = decorators.slice().reverse().reduce(function (desc, decorator) { return decorator(target, property, desc) || desc; }, desc); if (context && desc.initializer !== void 0) { desc.value = desc.initializer ? desc.initializer.call(context) : void 0; desc.initializer = undefined; } if (desc.initializer === void 0) { Object.defineProperty(target, property, desc); desc = null; } return desc; }

  function _initializerWarningHelper(descriptor, context) { throw new Error('Decorating class property failed. Please ensure that ' + 'transform-class-properties is enabled and runs after the decorators transform.'); }

  function _reportPossibleCrUseOflogDebug(extras) {
    _reporterNs.report("logDebug", "db://assets/scripts/Utils/Logger", _context.meta, extras);
  }

  function _reportPossibleCrUseOfList(extras) {
    _reporterNs.report("List", "../../../../components/common/list/List", _context.meta, extras);
  }

  function _reportPossibleCrUseOfBagSortType(extras) {
    _reporterNs.report("BagSortType", "../../PlaneTypes", _context.meta, extras);
  }

  function _reportPossibleCrUseOfTabStatus(extras) {
    _reporterNs.report("TabStatus", "../../PlaneTypes", _context.meta, extras);
  }

  return {
    setters: [function (_unresolved_) {
      _reporterNs = _unresolved_;
    }, function (_cc) {
      _cclegacy = _cc.cclegacy;
      __checkObsolete__ = _cc.__checkObsolete__;
      __checkObsoleteInNamespace__ = _cc.__checkObsoleteInNamespace__;
      _decorator = _cc._decorator;
      Component = _cc.Component;
      Label = _cc.Label;
      Node = _cc.Node;
      randomRangeInt = _cc.randomRangeInt;
      UITransform = _cc.UITransform;
    }, function (_unresolved_2) {
      logDebug = _unresolved_2.logDebug;
    }, function (_unresolved_3) {
      List = _unresolved_3.default;
    }, function (_unresolved_4) {
      BagSortType = _unresolved_4.BagSortType;
      TabStatus = _unresolved_4.TabStatus;
    }],
    execute: function () {
      _crd = true;

      _cclegacy._RF.push({}, "1ce0bH/NfdBLL19SPqwUpSI", "BagGrid", undefined);

      __checkObsolete__(['_decorator', 'Component', 'Label', 'Node', 'randomRangeInt', 'UITransform']);

      ({
        ccclass,
        property
      } = _decorator);
      mockItems = [];
      mockPlaneParts = [];

      _export("BagGrid", BagGrid = (_dec = ccclass('BagGrid'), _dec2 = property(_crd && List === void 0 ? (_reportPossibleCrUseOfList({
        error: Error()
      }), List) : List), _dec3 = property(Node), _dec(_class = (_class2 = class BagGrid extends Component {
        constructor() {
          super(...arguments);

          _initializerDefineProperty(this, "bagList", _descriptor, this);

          _initializerDefineProperty(this, "separator", _descriptor2, this);

          this._sortedItems = [];
          this._sortedPlaneParts = [];
          this._lineGridNum = 5;
        }

        onLoad() {
          this.separator.removeFromParent();
        }

        setGridStatus(tabStatus, sortType) {
          if (mockItems.length < 100) {
            for (var i = 0; i < 501; i++) {
              mockItems.push({
                name: "\u9053\u5177" + i
              });
            }
          }

          if (mockPlaneParts.length < 100) {
            for (var _i = 0; _i < 501; _i++) {
              var partyID = randomRangeInt(1, 4);
              mockPlaneParts.push({
                part: partyID,
                name: "\u96F6\u4EF6" + partyID,
                quality: randomRangeInt(1, 4)
              });
            }
          }

          switch (tabStatus) {
            case (_crd && TabStatus === void 0 ? (_reportPossibleCrUseOfTabStatus({
              error: Error()
            }), TabStatus) : TabStatus).Bag:
              this._sortedItems = mockItems;
              this._sortedPlaneParts = [...mockPlaneParts].sort((a, b) => {
                if (sortType === (_crd && BagSortType === void 0 ? (_reportPossibleCrUseOfBagSortType({
                  error: Error()
                }), BagSortType) : BagSortType).Part) {
                  return b.part - a.part || b.quality - a.quality;
                } else {
                  return b.quality - a.quality || b.part - a.part;
                }
              });
              var planePartRowNum = Math.ceil(this._sortedPlaneParts.length / this._lineGridNum);
              var itemRowNum = Math.ceil(this._sortedItems.length / this._lineGridNum);
              this.bagList.numItems = planePartRowNum + itemRowNum + 1;
              (_crd && logDebug === void 0 ? (_reportPossibleCrUseOflogDebug({
                error: Error()
              }), logDebug) : logDebug)("PlaneUI", "setGridStatus plane_parts_len:" + this._sortedPlaneParts.length + " item_num:" + this._sortedItems.length + " planePartRowNum:" + planePartRowNum + " itemRowNum:" + itemRowNum);
              break;

            case (_crd && TabStatus === void 0 ? (_reportPossibleCrUseOfTabStatus({
              error: Error()
            }), TabStatus) : TabStatus).Merge:
              this._sortedItems = mockPlaneParts;
              this._sortedPlaneParts = this.sortWithMergePriority(mockPlaneParts, 3);
              break;
          }

          this.bagList.scrollTo(0, 1);
        }

        onBagListRender(item, row) {
          var labels = item.getComponentsInChildren(Label);
          labels.forEach(label => {
            label.node.parent.active = true;
          });

          if (labels.length > this._lineGridNum) {
            this.separator.removeFromParent();
            this.separator.active = false;
          }

          var a = Math.ceil(this._sortedPlaneParts.length / this._lineGridNum);

          if (row < a) {
            this.onRenderPartPlane(item, row);
          } else if (row == a) {
            this.onRenderSeparator(item, row);
          } else {
            this.onRenderItem(item, row - a - 1);
          }
        }

        onRenderPartPlane(item, row) {
          var beginIndex = row * this._lineGridNum;
          var endIndex = (row + 1) * this._lineGridNum;
          var childLabels = item.getComponentsInChildren(Label);

          for (var index = beginIndex; index < endIndex; index++) {
            var label = childLabels[index - beginIndex];

            if (index < this._sortedPlaneParts.length) {
              var planePart = this._sortedPlaneParts[index];
              label.string = planePart.name + "(\u54C1\u8D28:" + planePart.quality + ")";
            } else {
              label.node.parent.active = false;
            }
          }
        }

        onRenderSeparator(item, row) {
          var normalSize = this.bagList.tmpNode.getComponent(UITransform).contentSize;
          var itemUITrans = item.getComponent(UITransform);
          itemUITrans.setContentSize(normalSize.width, normalSize.height / 2);
          item.getComponentsInChildren(Label).forEach(label => {
            label.node.parent.active = false;
          });
          this.separator.active = true;
          item.addChild(this.separator);
        }

        onRenderItem(item, item_row) {
          var beginIndex = item_row * this._lineGridNum;
          var endIndex = (item_row + 1) * this._lineGridNum;
          var childLabels = item.getComponentsInChildren(Label);

          for (var index = beginIndex; index < endIndex; index++) {
            var label = childLabels[index - beginIndex];

            if (index < this._sortedItems.length) {
              var itemData = this._sortedItems[index];
              label.string = itemData.name;
            } else {
              label.node.parent.active = false;
            }
          }
        }

        sortWithMergePriority(data, mergeNum) {
          // 统计每个part-quality组合的数量
          var countMap = new Map(); // 第一次遍历：统计数量

          data.forEach(item => {
            var key = item.part + "-" + item.quality;
            countMap.set(key, (countMap.get(key) || 0) + 1);
          }); // 第二次遍历：排序

          return [...data].sort((a, b) => {
            var aKey = a.part + "-" + a.quality;
            var bKey = b.part + "-" + b.quality;
            var aCanMerge = (countMap.get(aKey) || 0) >= mergeNum;
            var bCanMerge = (countMap.get(bKey) || 0) >= mergeNum; // 可合成的优先

            if (aCanMerge !== bCanMerge) return aCanMerge ? -1 : 1; // 最后按quality排序

            return b.quality - a.quality;
          });
        }

      }, (_descriptor = _applyDecoratedDescriptor(_class2.prototype, "bagList", [_dec2], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function initializer() {
          return null;
        }
      }), _descriptor2 = _applyDecoratedDescriptor(_class2.prototype, "separator", [_dec3], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function initializer() {
          return null;
        }
      })), _class2)) || _class));

      _cclegacy._RF.pop();

      _crd = false;
    }
  };
});
//# sourceMappingURL=b25bebc8e31b603d5682b297bfda0c8fa52f943f.js.map