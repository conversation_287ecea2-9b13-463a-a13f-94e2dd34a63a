System.register(["__unresolved_0", "cc", "__unresolved_1", "__unresolved_2", "__unresolved_3", "__unresolved_4"], function (_export, _context) {
  "use strict";

  var _reporterNs, _cclegacy, __checkObsolete__, __checkObsoleteInNamespace__, _decorator, instantiate, Emitter, DefaultMovable, DefaultMoveModifier, eSolverTarget, eEasing, _dec, _class, _crd, ccclass, property, EmitterArc;

  function _reportPossibleCrUseOfEmitter(extras) {
    _reporterNs.report("Emitter", "./Emitter", _context.meta, extras);
  }

  function _reportPossibleCrUseOfDefaultMovable(extras) {
    _reporterNs.report("DefaultMovable", "../move/DefaultMovable", _context.meta, extras);
  }

  function _reportPossibleCrUseOfDefaultMoveModifier(extras) {
    _reporterNs.report("DefaultMoveModifier", "../move/DefaultMoveModifier", _context.meta, extras);
  }

  function _reportPossibleCrUseOfeSolverTarget(extras) {
    _reporterNs.report("eSolverTarget", "../move/IMovable", _context.meta, extras);
  }

  function _reportPossibleCrUseOfeEasing(extras) {
    _reporterNs.report("eEasing", "../move/IMovable", _context.meta, extras);
  }

  return {
    setters: [function (_unresolved_) {
      _reporterNs = _unresolved_;
    }, function (_cc) {
      _cclegacy = _cc.cclegacy;
      __checkObsolete__ = _cc.__checkObsolete__;
      __checkObsoleteInNamespace__ = _cc.__checkObsoleteInNamespace__;
      _decorator = _cc._decorator;
      instantiate = _cc.instantiate;
    }, function (_unresolved_2) {
      Emitter = _unresolved_2.Emitter;
    }, function (_unresolved_3) {
      DefaultMovable = _unresolved_3.DefaultMovable;
    }, function (_unresolved_4) {
      DefaultMoveModifier = _unresolved_4.DefaultMoveModifier;
    }, function (_unresolved_5) {
      eSolverTarget = _unresolved_5.eSolverTarget;
      eEasing = _unresolved_5.eEasing;
    }],
    execute: function () {
      _crd = true;

      _cclegacy._RF.push({}, "93e575huyBPT6bORtc6obQb", "EmitterArc", undefined);

      __checkObsolete__(['_decorator', 'instantiate']);

      ({
        ccclass,
        property
      } = _decorator);

      _export("EmitterArc", EmitterArc = (_dec = ccclass('EmitterArc'), _dec(_class = class EmitterArc extends (_crd && Emitter === void 0 ? (_reportPossibleCrUseOfEmitter({
        error: Error()
      }), Emitter) : Emitter) {
        canEmit() {
          if (!this.emitterData) return false;

          if (!this.bulletPrefab || this.emitterData.count <= 0) {
            return false;
          }

          return true;
        }
        /**
         * Implementation of emitBullet for arc-based emission
         */


        emit() {
          for (var i = 0; i < this.emitterData.count; i++) {
            var direction = this.getSpawnDirection(i);
            var position = this.getSpawnPosition(i);
            this.createBullet(direction, position);
          }
        }
        /**
         * Calculate the direction for a bullet at the given index
         * @param index The index of the bullet (0 to count-1)
         * @returns Direction vector {x, y}
         */


        getSpawnDirection(index) {
          // 计算发射方向
          var angleOffset = this.emitterData.count > 1 ? this.emitterData.arc / (this.emitterData.count - 1) * index - this.emitterData.arc / 2 : 0;
          var radian = (this.emitterData.angle + angleOffset) * (Math.PI / 180);
          return {
            x: Math.cos(radian),
            y: Math.sin(radian)
          };
        }
        /**
         * Get the spawn position for a bullet at the given index
         * @param index The index of the bullet (0 to count-1)
         * @returns Position offset from emitter center
         */


        getSpawnPosition(index) {
          if (this.emitterData.radius <= 0) {
            return {
              x: 0,
              y: 0
            };
          }

          var direction = this.getSpawnDirection(index);
          return {
            x: direction.x * this.emitterData.radius,
            y: direction.y * this.emitterData.radius
          };
        }

        createBullet(direction, position) {
          var _this$node$parent;

          if (!this.bulletPrefab) {
            console.warn("EmitterArc: No bullet prefab assigned");
            return null;
          } // Instantiate the bullet from prefab


          var bulletNode = instantiate(this.bulletPrefab);

          if (!bulletNode) {
            console.error("EmitterArc: Failed to instantiate bullet prefab");
            return null;
          } // Get the bullet component
          // const bullet = bulletNode.getComponent(Bullet);
          // if (!bullet) {
          //     console.error("EmitterArc: Bullet prefab does not have Bullet component");
          //     bulletNode.destroy();
          //     return null;
          // }
          // Set bullet position relative to emitter


          var emitterPos = this.node.getWorldPosition();
          bulletNode.setWorldPosition(emitterPos.x + position.x, emitterPos.y + position.y, emitterPos.z); // Add or get the DefaultMovable component for movement

          var movable = bulletNode.getComponent(_crd && DefaultMovable === void 0 ? (_reportPossibleCrUseOfDefaultMovable({
            error: Error()
          }), DefaultMovable) : DefaultMovable);

          if (!movable) {
            movable = bulletNode.addComponent(_crd && DefaultMovable === void 0 ? (_reportPossibleCrUseOfDefaultMovable({
              error: Error()
            }), DefaultMovable) : DefaultMovable);
          } // Set initial velocity based on direction and speed multiplier


          movable.vx = direction.x * this.emitterData.emitPower;
          movable.vy = direction.y * this.emitterData.emitPower;
          movable.angle = Math.atan2(direction.y, direction.x) * 180 / Math.PI;
          movable.addSolver(new (_crd && DefaultMoveModifier === void 0 ? (_reportPossibleCrUseOfDefaultMoveModifier({
            error: Error()
          }), DefaultMoveModifier) : DefaultMoveModifier)((_crd && eSolverTarget === void 0 ? (_reportPossibleCrUseOfeSolverTarget({
            error: Error()
          }), eSolverTarget) : eSolverTarget).Angle, 0, 180, (_crd && eEasing === void 0 ? (_reportPossibleCrUseOfeEasing({
            error: Error()
          }), eEasing) : eEasing).Linear));
          movable.addSolver(new (_crd && DefaultMoveModifier === void 0 ? (_reportPossibleCrUseOfDefaultMoveModifier({
            error: Error()
          }), DefaultMoveModifier) : DefaultMoveModifier)((_crd && eSolverTarget === void 0 ? (_reportPossibleCrUseOfeSolverTarget({
            error: Error()
          }), eSolverTarget) : eSolverTarget).Vx, 0, 2, (_crd && eEasing === void 0 ? (_reportPossibleCrUseOfeEasing({
            error: Error()
          }), eEasing) : eEasing).Linear));
          movable.addSolver(new (_crd && DefaultMoveModifier === void 0 ? (_reportPossibleCrUseOfDefaultMoveModifier({
            error: Error()
          }), DefaultMoveModifier) : DefaultMoveModifier)((_crd && eSolverTarget === void 0 ? (_reportPossibleCrUseOfeSolverTarget({
            error: Error()
          }), eSolverTarget) : eSolverTarget).Vy, -500, 2, (_crd && eEasing === void 0 ? (_reportPossibleCrUseOfeEasing({
            error: Error()
          }), eEasing) : eEasing).Linear));
          (_this$node$parent = this.node.parent) == null || _this$node$parent.addChild(bulletNode);
          return bulletNode;
        }

      }) || _class));

      _cclegacy._RF.pop();

      _crd = false;
    }
  };
});
//# sourceMappingURL=90aa7d18c9197eb5a7757840c8435a57a0c81d14.js.map