System.register(["__unresolved_0", "cc", "__unresolved_1", "__unresolved_2", "__unresolved_3"], function (_export, _context) {
  "use strict";

  var _reporterNs, _cclegacy, __checkObsolete__, __checkObsoleteInNamespace__, _decorator, Component, Node, Prefab, NodePool, sp, instantiate, UIOpacity, UITransform, Tools, GameIns, BlastComp, _dec, _class, _class2, _crd, ccclass, property, EnemyEffectLayer;

  function asyncGeneratorStep(gen, resolve, reject, _next, _throw, key, arg) { try { var info = gen[key](arg); var value = info.value; } catch (error) { reject(error); return; } if (info.done) { resolve(value); } else { Promise.resolve(value).then(_next, _throw); } }

  function _asyncToGenerator(fn) { return function () { var self = this, args = arguments; return new Promise(function (resolve, reject) { var gen = fn.apply(self, args); function _next(value) { asyncGeneratorStep(gen, resolve, reject, _next, _throw, "next", value); } function _throw(err) { asyncGeneratorStep(gen, resolve, reject, _next, _throw, "throw", err); } _next(undefined); }); }; }

  function _reportPossibleCrUseOfTools(extras) {
    _reporterNs.report("Tools", "../../../utils/Tools", _context.meta, extras);
  }

  function _reportPossibleCrUseOfGameIns(extras) {
    _reporterNs.report("GameIns", "../../../GameIns", _context.meta, extras);
  }

  function _reportPossibleCrUseOfBlastComp(extras) {
    _reporterNs.report("BlastComp", "../../base/BlastComp", _context.meta, extras);
  }

  return {
    setters: [function (_unresolved_) {
      _reporterNs = _unresolved_;
    }, function (_cc) {
      _cclegacy = _cc.cclegacy;
      __checkObsolete__ = _cc.__checkObsolete__;
      __checkObsoleteInNamespace__ = _cc.__checkObsoleteInNamespace__;
      _decorator = _cc._decorator;
      Component = _cc.Component;
      Node = _cc.Node;
      Prefab = _cc.Prefab;
      NodePool = _cc.NodePool;
      sp = _cc.sp;
      instantiate = _cc.instantiate;
      UIOpacity = _cc.UIOpacity;
      UITransform = _cc.UITransform;
    }, function (_unresolved_2) {
      Tools = _unresolved_2.Tools;
    }, function (_unresolved_3) {
      GameIns = _unresolved_3.GameIns;
    }, function (_unresolved_4) {
      BlastComp = _unresolved_4.default;
    }],
    execute: function () {
      _crd = true;

      _cclegacy._RF.push({}, "f1f27R3mHJBAKwCxZtWns2R", "EnemyEffectLayer", undefined);

      __checkObsolete__(['_decorator', 'Component', 'Node', 'Prefab', 'NodePool', 'Vec3', 'sp', 'SpriteFrame', 'instantiate', 'UIOpacity', 'UITransform']);

      ({
        ccclass,
        property
      } = _decorator);

      _export("default", EnemyEffectLayer = (_dec = ccclass('EnemyEffectLayer'), _dec(_class = (_class2 = class EnemyEffectLayer extends Component {
        constructor() {
          super(...arguments);
          this.hurtEffectLayer = null;
          this.hurtNumLayer = null;
          this._blastPool = new Map();
          this._blastPF = new Map();
          this._blastArr = [];
          this.m_blastPool = new Map();
          this.m_blastData = new Map();
          this.m_dieBomb = new NodePool();
          this.m_dieBombData = null;
          this.m_blastArr = [];
        }

        onLoad() {
          EnemyEffectLayer.me = this;
        }

        start() {
          this.hurtEffectLayer = new Node();
          this.hurtEffectLayer.addComponent(UITransform);
          this.hurtEffectLayer.parent = this.node;
          this.hurtEffectLayer.setPosition(0, 0);
          this.hurtNumLayer = new Node();
          this.hurtNumLayer.addComponent(UITransform);
          this.hurtNumLayer.parent = this.node;
          this.hurtNumLayer.setPosition(0, 0);
        }

        reset() {
          for (var i = 0; i < this.node.children.length; i++) {
            var child = this.node.children[i];

            if (child !== this.hurtEffectLayer && child !== this.hurtNumLayer) {
              child.parent = null;
              child.destroy();
              i--;
            } else {
              child.destroyAllChildren();
            }
          }

          this._blastPool.forEach(pool => {
            (_crd && Tools === void 0 ? (_reportPossibleCrUseOfTools({
              error: Error()
            }), Tools) : Tools).clearNodePool(pool);
          });

          this._blastPool.clear();

          this._blastPF.clear();

          (_crd && Tools === void 0 ? (_reportPossibleCrUseOfTools({
            error: Error()
          }), Tools) : Tools).clearArrayForNode(this._blastArr);
          (_crd && Tools === void 0 ? (_reportPossibleCrUseOfTools({
            error: Error()
          }), Tools) : Tools).clearMapForNodeArr(this.m_blastPool);
          this.m_blastData.clear();
          (_crd && Tools === void 0 ? (_reportPossibleCrUseOfTools({
            error: Error()
          }), Tools) : Tools).clearArrayForNode(this._blastArr);
          (_crd && Tools === void 0 ? (_reportPossibleCrUseOfTools({
            error: Error()
          }), Tools) : Tools).clearNodePool(this.m_dieBomb);
          this.m_dieBombData = null;
        }

        init(blastTypes) {
          if (blastTypes === void 0) {
            blastTypes = null;
          }

          var blastNames = ['blast0', 'blast1', 'blast2', 'blast3', 'blast4', 'blast5', 'blast7', 'blast8'];

          for (var name of blastNames) {
            var prefab = (_crd && GameIns === void 0 ? (_reportPossibleCrUseOfGameIns({
              error: Error()
            }), GameIns) : GameIns).loadManager.getRes(name, Prefab);

            if (prefab) {
              this._blastPF.set(name, prefab);

              if (!this._blastPool.has(name)) {
                this._blastPool.set(name, new NodePool());
              }

              var _pool = this._blastPool.get(name);

              if (blastTypes && blastTypes.length > 0) {
                var type = parseInt(name.split('blast')[1]);

                if ((_crd && Tools === void 0 ? (_reportPossibleCrUseOfTools({
                  error: Error()
                }), Tools) : Tools).arrContain(blastTypes, type)) {
                  if (type === 0) {
                    var node = instantiate(prefab);

                    _pool.put(node);
                  } else {
                    var count = type === 1 ? 12 : 3;

                    for (var i = 0; i < count; i++) {
                      var _node = instantiate(prefab);

                      _pool.put(_node);
                    }
                  }
                }
              } else {
                for (var _i = 0; _i < 5; _i++) {
                  var _node2 = instantiate(prefab);

                  _pool.put(_node2);
                }
              }
            }
          }

          var skeletonNames = ['skel_blast9', 'skel_bFire_blast', 'skel_bIce_blast'];

          for (var _name of skeletonNames) {
            var _skeletonData = (_crd && GameIns === void 0 ? (_reportPossibleCrUseOfGameIns({
              error: Error()
            }), GameIns) : GameIns).loadManager.getRes(_name, sp.SkeletonData);

            if (_skeletonData) {
              this.m_blastData.set(_name, _skeletonData);
            }
          }

          if (!this.m_blastPool.has('skel_blast9')) {
            this.m_blastPool.set('skel_blast9', []);
          }

          var pool = this.m_blastPool.get('skel_blast9');
          var skeletonData = this.m_blastData.get('skel_blast9');

          if (skeletonData) {
            for (var _i2 = 0; _i2 < 10; _i2++) {
              var _node3 = new Node();

              _node3.addComponent(UITransform);

              var skeleton = _node3.addComponent(sp.Skeleton);

              skeleton.skeletonData = skeletonData;
              skeleton.premultipliedAlpha = false;
              skeleton.enableBatch = true;
              skeleton.setAnimationCacheMode(sp.Skeleton.AnimationCacheMode.SHARED_CACHE);
              pool.push(_node3);
            }
          }

          this.m_dieBombData = (_crd && GameIns === void 0 ? (_reportPossibleCrUseOfGameIns({
            error: Error()
          }), GameIns) : GameIns).loadManager.getRes('skel_diebomb', sp.SkeletonData);
          var bombCount = 8;

          for (var _i3 = 0; _i3 < bombCount; _i3++) {
            var _node4 = new Node();

            _node4.addComponent(UITransform);

            var _skeleton = _node4.addComponent(sp.Skeleton);

            _skeleton.skeletonData = this.m_dieBombData;
            _skeleton.premultipliedAlpha = false;

            _skeleton.setAnimationCacheMode(sp.Skeleton.AnimationCacheMode.SHARED_CACHE);

            _skeleton.enableBatch = true;
            this.m_dieBomb.put(_node4);
          }
        }

        _loadRes(name, type) {
          var _this = this;

          return _asyncToGenerator(function* () {
            if (type === Prefab) {
              var prefab = yield (_crd && GameIns === void 0 ? (_reportPossibleCrUseOfGameIns({
                error: Error()
              }), GameIns) : GameIns).loadManager.loadPrefab(name);

              _this._blastPF.set(name, prefab);
            } else if (type === sp.SkeletonData) {
              var skeletonData = yield (_crd && GameIns === void 0 ? (_reportPossibleCrUseOfGameIns({
                error: Error()
              }), GameIns) : GameIns).loadManager.loadSpine(name);

              _this.m_blastData.set(name, skeletonData);
            }
          })();
        }

        getBlastNode(name) {
          if (!this.m_blastData.get(name)) {
            (_crd && Tools === void 0 ? (_reportPossibleCrUseOfTools({
              error: Error()
            }), Tools) : Tools).error("No blast skeleton: " + name);

            this._loadRes(name, sp.SkeletonData);

            return null;
          }

          if (!this.m_blastPool.has(name)) {
            this.m_blastPool.set(name, []);
          }

          var pool = this.m_blastPool.get(name);
          var node = pool.pop();

          if (!node) {
            node = new Node();
            node.addComponent(UITransform);
            var skeleton = node.addComponent(sp.Skeleton);
            skeleton.skeletonData = this.m_blastData.get(name);
            skeleton.premultipliedAlpha = false;
            skeleton.enableBatch = true;
            skeleton.setAnimationCacheMode(sp.Skeleton.AnimationCacheMode.SHARED_CACHE);
          }

          return node;
        }

        recycleBlastNode(name, node) {
          if (!this.m_blastPool.has(name)) {
            this.m_blastPool.set(name, []);
          }

          var pool = this.m_blastPool.get(name);
          node.getComponent(UIOpacity).opacity = 0;
          pool.push(node);
          (_crd && Tools === void 0 ? (_reportPossibleCrUseOfTools({
            error: Error()
          }), Tools) : Tools).arrRemove(this._blastArr, node);
        }

        addBlastEffectByName(target, effectName, effectData, callback) {
          if (callback === void 0) {
            callback = null;
          }

          this.playBlastEffect(target, effectName, effectData);

          if (callback) {
            callback();
          }
        }

        addBlastEffect(target, type, effectData, callback) {
          if (callback === void 0) {
            callback = null;
          }

          if (type !== 9) {
            if (type > 9) {
              callback && callback();
              (_crd && Tools === void 0 ? (_reportPossibleCrUseOfTools({
                error: Error()
              }), Tools) : Tools).error('爆炸类型不对:', type);
              return null;
            }

            var _node5 = this.playBlastAnimation(target, "blast" + type, effectData, callback);

            if (_node5) {
              _node5.setSiblingIndex(type);
            }

            return _node5;
          }

          var node = this.playBlastEffect(target, "skel_blast" + type, effectData, callback);

          if (node) {
            node.setSiblingIndex(type);
          }

          return node;
        }

        playBlastEffect(target, effectName, effectData, callback) {
          if (callback === void 0) {
            callback = null;
          }

          var node = this.getBlastNode(effectName);

          if (!node) {
            (_crd && Tools === void 0 ? (_reportPossibleCrUseOfTools({
              error: Error()
            }), Tools) : Tools).error("\u672A\u627E\u5230\u7206\u70B8\u52A8\u753B\u8D44\u6E90: " + effectName);
            callback && callback();
            return null;
          }

          this.m_blastArr.push(node);
          var skeleton = node.getComponent(sp.Skeleton);

          if (!skeleton) {
            (_crd && Tools === void 0 ? (_reportPossibleCrUseOfTools({
              error: Error()
            }), Tools) : Tools).error("\u8282\u70B9\u672A\u5305\u542B Skeleton \u7EC4\u4EF6: " + effectName);
            return null;
          }

          skeleton.node.setScale(effectData.scale, effectData.scale);
          skeleton.node.parent = this.node;
          skeleton.node.getComponent(UIOpacity).opacity = 255;
          var scenePos = {
            x: 0,
            y: 0
          };

          if (target) {
            scenePos = (_crd && GameIns === void 0 ? (_reportPossibleCrUseOfGameIns({
              error: Error()
            }), GameIns) : GameIns).sceneManager.getScenePos(target);
          }

          skeleton.node.setPosition(effectData.x + scenePos.x, effectData.y + scenePos.y);
          skeleton.node.angle = effectData.angle;
          skeleton.setAnimation(0, 'play', false);
          skeleton.setCompleteListener(() => {
            skeleton.setCompleteListener(null);
            callback && callback();
            this.recycleBlastNode(effectName, node);
          });
          return node;
        }

        playBlastAnimation(target, effectName, effectData, callback) {
          if (callback === void 0) {
            callback = null;
          }

          var node = this.getAnimationNode(effectName);

          if (!node) {
            (_crd && Tools === void 0 ? (_reportPossibleCrUseOfTools({
              error: Error()
            }), Tools) : Tools).error("\u672A\u627E\u5230\u7206\u70B8\u52A8\u753B\u8D44\u6E90: " + effectName);
            callback && callback();
            return null;
          }

          this.m_blastArr.push(node.node);
          node.node.setScale(effectData.scale, effectData.scale);
          node.node.parent = this.node;
          var scenePos = {
            x: 0,
            y: 0
          };

          if (target) {
            scenePos = (_crd && GameIns === void 0 ? (_reportPossibleCrUseOfGameIns({
              error: Error()
            }), GameIns) : GameIns).sceneManager.getScenePos(target);
          }

          node.node.setPosition(effectData.x + scenePos.x, effectData.y + scenePos.y);
          node.node.angle = effectData.angle;
          node.play(() => {
            callback && callback();
            this.recycleBlastAnimation(effectName, node.node);
          });
          return node.node;
        }

        getAnimationNode(name) {
          if (!this._blastPool.has(name)) {
            this._blastPool.set(name, new NodePool());
          }

          var pool = this._blastPool.get(name);

          var node = pool.get();

          if (!node) {
            var prefab = this._blastPF.get(name);

            if (prefab) {
              node = instantiate(prefab);
            } else {
              this._loadRes(name, Prefab);
            }
          }

          return node ? node.getComponent(_crd && BlastComp === void 0 ? (_reportPossibleCrUseOfBlastComp({
            error: Error()
          }), BlastComp) : BlastComp) : null;
        }

        recycleBlastAnimation(name, node) {
          if (!this._blastPool.has(name)) {
            this._blastPool.set(name, new NodePool());
          }

          var pool = this._blastPool.get(name);

          pool.put(node);
          (_crd && Tools === void 0 ? (_reportPossibleCrUseOfTools({
            error: Error()
          }), Tools) : Tools).arrRemove(this._blastArr, node);
        }

        playDieBombAnim(position) {
          var _this2 = this;

          return _asyncToGenerator(function* () {
            if (!_this2.m_dieBombData) {
              _this2.m_dieBombData = yield (_crd && GameIns === void 0 ? (_reportPossibleCrUseOfGameIns({
                error: Error()
              }), GameIns) : GameIns).loadManager.loadSpine('skel_diebomb');
            }

            if (_this2.m_dieBombData) {
              var node = _this2.m_dieBomb.get();

              if (!node) {
                node = new Node();
                node.addComponent(UITransform);

                var _skeleton2 = node.addComponent(sp.Skeleton);

                _skeleton2.skeletonData = _this2.m_dieBombData;
                _skeleton2.premultipliedAlpha = false;

                _skeleton2.setAnimationCacheMode(sp.Skeleton.AnimationCacheMode.SHARED_CACHE);

                _skeleton2.enableBatch = true;
              }

              node.parent = _this2.hurtEffectLayer;
              node.setPosition(position);
              var skeleton = node.getComponent(sp.Skeleton);
              skeleton.setCompleteListener(() => {
                skeleton.setCompleteListener(null);

                _this2.m_dieBomb.put(skeleton.node);
              });
              skeleton.setAnimation(0, 'play', false);
            }
          })();
        }

      }, _class2.me = void 0, _class2)) || _class));

      _cclegacy._RF.pop();

      _crd = false;
    }
  };
});
//# sourceMappingURL=72d7d790f5979df91593b1ddf30310844ca05773.js.map