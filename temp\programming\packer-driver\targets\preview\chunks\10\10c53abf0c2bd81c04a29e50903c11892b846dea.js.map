{"version": 3, "sources": ["file:///E:/M2Game/Client/assets/scripts/ui/main/plane/PlaneTypes.ts"], "names": ["TabStatus", "BagSortType"], "mappings": ";;;;;;;;;;;;;;2BAAYA,S,0BAAAA,S;AAAAA,QAAAA,S;AAAAA,QAAAA,S;eAAAA,S;;;6BAKAC,W,0BAAAA,W;AAAAA,QAAAA,W;AAAAA,QAAAA,W;AAAAA,QAAAA,W;eAAAA,W", "sourcesContent": ["export enum TabStatus {\n    Bag = 'Bag',\n    Merge = 'Merge',\n}\n\nexport enum BagSortType {\n    Quality = 'Quality',\n    Part = 'Part',\n    Merge = 'Merge',\n}"]}