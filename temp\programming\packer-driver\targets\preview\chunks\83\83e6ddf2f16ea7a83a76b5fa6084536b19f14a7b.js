System.register(["__unresolved_0", "cc", "__unresolved_1", "__unresolved_2", "__unresolved_3"], function (_export, _context) {
  "use strict";

  var _reporterNs, _cclegacy, __checkObsolete__, __checkObsoleteInNamespace__, _decorator, Component, LevelEventGroup, World, LevelSystem, _dec, _class, _crd, ccclass, property, Level;

  function _reportPossibleCrUseOfLevelEventGroup(extras) {
    _reporterNs.report("LevelEventGroup", "./LevelEventGroup", _context.meta, extras);
  }

  function _reportPossibleCrUseOfWorld(extras) {
    _reporterNs.report("World", "../base/World", _context.meta, extras);
  }

  function _reportPossibleCrUseOfLevelSystem(extras) {
    _reporterNs.report("LevelSystem", "./LevelSystem", _context.meta, extras);
  }

  return {
    setters: [function (_unresolved_) {
      _reporterNs = _unresolved_;
    }, function (_cc) {
      _cclegacy = _cc.cclegacy;
      __checkObsolete__ = _cc.__checkObsolete__;
      __checkObsoleteInNamespace__ = _cc.__checkObsoleteInNamespace__;
      _decorator = _cc._decorator;
      Component = _cc.Component;
    }, function (_unresolved_2) {
      LevelEventGroup = _unresolved_2.LevelEventGroup;
    }, function (_unresolved_3) {
      World = _unresolved_3.World;
    }, function (_unresolved_4) {
      LevelSystem = _unresolved_4.LevelSystem;
    }],
    execute: function () {
      _crd = true;

      _cclegacy._RF.push({}, "fa9af2oUN1DV5nauyTFyUBj", "Level", undefined);

      __checkObsolete__(['_decorator', 'Component', 'Node']);

      ({
        ccclass,
        property
      } = _decorator);

      _export("Level", Level = (_dec = ccclass('Level'), _dec(_class = class Level extends Component {
        constructor() {
          super(...arguments);
          this._levelElapsedTime = 0;
          //@property({type:[LevelEventGroup]})
          this.levelEvents = [];
        }

        get levelElapsedTime() {
          return this._levelElapsedTime;
        }

        onLoad() {
          // 非正式代码
          var levelSys = (_crd && World === void 0 ? (_reportPossibleCrUseOfWorld({
            error: Error()
          }), World) : World).getInstance().getSystem(_crd && LevelSystem === void 0 ? (_reportPossibleCrUseOfLevelSystem({
            error: Error()
          }), LevelSystem) : LevelSystem);
          levelSys.setCurrentLevel(this); // get all LevelEventGroup from children

          this.levelEvents = this.getComponentsInChildren(_crd && LevelEventGroup === void 0 ? (_reportPossibleCrUseOfLevelEventGroup({
            error: Error()
          }), LevelEventGroup) : LevelEventGroup);
          this.levelEvents.forEach(event => {
            event.init(this);
          });
          this._levelElapsedTime = 0;
        }

        tick(deltaTime) {
          this._levelElapsedTime += deltaTime;
          this.levelEvents.forEach(event => {
            event.tryExecute();
          });
        }

      }) || _class));

      _cclegacy._RF.pop();

      _crd = false;
    }
  };
});
//# sourceMappingURL=83e6ddf2f16ea7a83a76b5fa6084536b19f14a7b.js.map