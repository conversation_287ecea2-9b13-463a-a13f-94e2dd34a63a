{"version": 3, "sources": ["file:///E:/M2Game/Client/assets/scripts/Game/ui/base/Controller.ts"], "names": ["_decorator", "Component", "NodeEventType", "GameIns", "GameEnum", "ccclass", "property", "Controller", "target", "onLoad", "instance", "planeManager", "mainPlane", "start", "node", "on", "TOUCH_START", "onTouchStart", "TOUCH_END", "onTouchEnd", "TOUCH_MOVE", "onTouchMove", "event", "gameRuleManager", "gameState", "GameState", "Battle", "deltaX", "getDeltaX", "deltaY", "getDeltaY", "onControl"], "mappings": ";;;;;;;;;;;;;;;;;;;;AAASA,MAAAA,U,OAAAA,U;AAAYC,MAAAA,S,OAAAA,S;AAAsBC,MAAAA,a,OAAAA,a;;AAClCC,MAAAA,O,iBAAAA,O;;AACFC,MAAAA,Q;;;;;;;;;OAED;AAAEC,QAAAA,OAAF;AAAWC,QAAAA;AAAX,O,GAAwBN,U;;4BAGjBO,U,WADZF,OAAO,CAAC,YAAD,C,2BAAR,MACaE,UADb,SACgCN,SADhC,CAC0C;AAAA;AAAA;AAAA,eAEtCO,MAFsC,GAE7B,IAF6B;AAAA;;AAGd;;AAExB;AACJ;AACA;AACIC,QAAAA,MAAM,GAAG;AACLF,UAAAA,UAAU,CAACG,QAAX,GAAsB,IAAtB,CADK,CACuB;;AAC5B,eAAKF,MAAL,GAAc;AAAA;AAAA,kCAAQG,YAAR,CAAqBC,SAAnC,CAFK,CAEyC;AACjD;AAED;AACJ;AACA;;;AACIC,QAAAA,KAAK,GAAG;AACJ,eAAKC,IAAL,CAAUC,EAAV,CAAab,aAAa,CAACc,WAA3B,EAAwC,KAAKC,YAA7C,EAA2D,IAA3D;AACA,eAAKH,IAAL,CAAUC,EAAV,CAAab,aAAa,CAACgB,SAA3B,EAAsC,KAAKC,UAA3C,EAAuD,IAAvD;AACA,eAAKL,IAAL,CAAUC,EAAV,CAAab,aAAa,CAACkB,UAA3B,EAAuC,KAAKC,WAA5C,EAAyD,IAAzD;AACH;AAED;AACJ;AACA;AACA;;;AACIJ,QAAAA,YAAY,CAACK,KAAD,EAAQ;AAChB,cAAI,KAAKd,MAAL,IAAe,IAAnB,EAAyB,CACrB;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACH;AACJ;AAED;AACJ;AACA;AACA;;;AACIa,QAAAA,WAAW,CAACC,KAAD,EAAQ;AACf,cAAI,KAAKd,MAAL,IAAe,IAAnB,EAAyB;AACrB,gBAAI;AAAA;AAAA,oCAAQe,eAAR,CAAwBC,SAAxB,GAAoC;AAAA;AAAA,sCAASC,SAAT,CAAmBC,MAA3D,EAAmE;AAC/D,qBAD+D,CACvD;AACX;;AACD,gBAAMC,MAAM,GAAGL,KAAK,CAACM,SAAN,EAAf;AACA,gBAAMC,MAAM,GAAGP,KAAK,CAACQ,SAAN,EAAf;AACA,iBAAKtB,MAAL,CAAYuB,SAAZ,CAAsBJ,MAAtB,EAA8BE,MAA9B,EANqB,CAMkB;AAC1C;AACJ;AAED;AACJ;AACA;AACA;;;AACIV,QAAAA,UAAU,CAACG,KAAD,EAAQ;AACd;AACA,cAAI,KAAKd,MAAT,EAAiB,CACb;AACH;AACJ;;AAhEqC,O,UAG/BE,Q,GAAW,I", "sourcesContent": ["import { _decorator, Component, Animation, NodeEventType } from 'cc';\r\nimport { GameIns } from '../../GameIns';\r\nimport GameEnum from '../../const/GameEnum';\r\n\r\nconst { ccclass, property } = _decorator;\r\n\r\n@ccclass('Controller')\r\nexport class Controller extends Component {\r\n\r\n    target = null; // 目标对象（主飞机）\r\n    static instance = null; // 单例实例\r\n\r\n    /**\r\n     * 加载时初始化\r\n     */\r\n    onLoad() {\r\n        Controller.instance = this; // 单例模式\r\n        this.target = GameIns.planeManager.mainPlane; // 获取主飞机\r\n    }\r\n\r\n    /**\r\n     * 开始时绑定触摸事件\r\n     */\r\n    start() {\r\n        this.node.on(NodeEventType.TOUCH_START, this.onTouchStart, this);\r\n        this.node.on(NodeEventType.TOUCH_END, this.onTouchEnd, this);\r\n        this.node.on(NodeEventType.TOUCH_MOVE, this.onTouchMove, this);\r\n    }\r\n\r\n    /**\r\n     * 触摸开始事件\r\n     * @param {Event.EventTouch} event 触摸事件\r\n     */\r\n    onTouchStart(event) {\r\n        if (this.target != null) {\r\n            // const handGuiderDialog = GameIns.uiManager.getDialogByName(\"handGuider\");\r\n            // if (handGuiderDialog) {\r\n            //     handGuiderDialog.OnRemove(); // 移除引导手势\r\n            // }\r\n\r\n            // const bossEnterDialog = GameIns.uiManager.getDialogByName(\"BossEnterDialog\");\r\n            // if (bossEnterDialog) {\r\n            //     bossEnterDialog.onCloseBtnTap(); // 关闭 Boss 进入对话框\r\n            // }\r\n        }\r\n    }\r\n\r\n    /**\r\n     * 触摸移动事件\r\n     * @param {Event.EventTouch} event 触摸事件\r\n     */\r\n    onTouchMove(event) {\r\n        if (this.target != null) {\r\n            if (GameIns.gameRuleManager.gameState < GameEnum.GameState.Battle) {\r\n                return; // 游戏未进入战斗状态时不处理\r\n            }\r\n            const deltaX = event.getDeltaX();\r\n            const deltaY = event.getDeltaY();\r\n            this.target.onControl(deltaX, deltaY); // 控制主飞机移动\r\n        }\r\n    }\r\n\r\n    /**\r\n     * 触摸结束事件\r\n     * @param {Event.EventTouch} event 触摸事件\r\n     */\r\n    onTouchEnd(event) {\r\n        // 当前实现中，触摸结束事件未处理任何逻辑\r\n        if (this.target) {\r\n            // 可扩展逻辑\r\n        }\r\n    }\r\n}"]}