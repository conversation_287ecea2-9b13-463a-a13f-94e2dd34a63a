{"version": 3, "sources": ["file:///E:/M2Game/Client/assets/scripts/Game/ui/plane/mainPlane/MainSkillBase.ts"], "names": ["_decorator", "BaseComp", "GameIns", "GameConst", "ccclass", "property", "MainSkillBase", "m_plane", "m_record", "m_isFire", "m_CDTime", "m_isEnd", "m_keepTime", "m_inGame", "onInit", "getProgress", "cd", "cdTime", "value", "setData", "record", "mainPlaneManager", "plane", "gameDataManager", "skillCD", "kd", "createUI", "isPlane", "setCanFire", "canFire", "isCooldown", "isSkillUnlocked", "checkLockSkill", "isInGame", "isNotLJStage", "isFireEnabled", "fireEnable", "fire", "onFire", "fireFinish", "onFireFinish", "onUpdate", "deltaTime", "removeSkill", "update", "GameAble", "quiteBattle", "startBattle", "resetCD", "beginBattle", "endBattle", "crazyAnim", "isCrazy", "setSkinActive", "skinAnim", "setAnimation", "setCompleteListener", "isActive", "mechaAnimNode", "active", "node", "opacity"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;AAASA,MAAAA,U,OAAAA,U;;AACFC,MAAAA,Q;;AACEC,MAAAA,O,iBAAAA,O;;AACAC,MAAAA,S,iBAAAA,S;;;;;;;;;OAGH;AAAEC,QAAAA,OAAF;AAAWC,QAAAA;AAAX,O,GAAwBL,U;;yBAGTM,a,WADpBF,OAAO,CAAC,eAAD,C,gBAAR,MACqBE,aADrB;AAAA;AAAA,gCACoD;AAAA;AAAA;AAAA,eAChDC,OADgD,GACjC,IADiC;AAAA,eAEhDC,QAFgD,GAEhC,IAFgC;AAAA,eAGhDC,QAHgD,GAG5B,KAH4B;AAAA,eAIhDC,QAJgD,GAI7B,CAJ6B;AAAA,eAKhDC,OALgD,GAK7B,KAL6B;AAAA,eAMhDC,UANgD,GAM3B,CAN2B;AAAA,eAOhDC,QAPgD,GAO5B,KAP4B;AAAA;;AAShDC,QAAAA,MAAM,GAAG,CAAE;;AAEXC,QAAAA,WAAW,GAAW;AAClB,iBAAO,KAAKL,QAAL,GAAgB,KAAKF,QAAL,CAAcQ,EAArC;AACH;;AAES,YAANC,MAAM,GAAW;AACjB,iBAAO,KAAKP,QAAZ;AACH;;AAES,YAANO,MAAM,CAACC,KAAD,EAAgB;AACtB,eAAKR,QAAL,GAAgBQ,KAAhB;AACH;;AAEDC,QAAAA,OAAO,CAACC,MAAD,EAAc;AACjB,eAAKb,OAAL,GAAe;AAAA;AAAA,kCAAQc,gBAAR,CAAyBC,KAAxC;AACA,eAAKd,QAAL,GAAgBY,MAAhB;AACA,eAAKH,MAAL,GAAc;AAAA;AAAA,kCAAQM,eAAR,CAAwBC,OAAxB,IAAmC,CAAjD;AACA,eAAKZ,UAAL,GAAkB,KAAKJ,QAAL,CAAciB,EAAhC;AACH;;AAEKC,QAAAA,QAAQ,GAAG;AAAA;AACb,gBAAI;AAAA;AAAA,oCAAQL,gBAAR,CAAyBM,OAAzB,CAAiC,GAAjC,CAAJ,EAA2C;AACvC;AACH,aAHY,CAKb;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;;AAjBa;AAkBhB;;AAEDC,QAAAA,UAAU,GAAG;AACT,eAAKnB,QAAL,GAAgB,KAAhB;AACA,eAAKQ,MAAL,GAAc,CAAd;AACH;;AAEDY,QAAAA,OAAO,GAAY;AACf,cAAMA,OAAO,GAAG,CAAC,KAAKpB,QAAtB;AACA,cAAMqB,UAAU,GAAG,KAAKb,MAAL,IAAe,CAAlC;AACA,cAAMc,eAAe,GAAG;AAAA;AAAA,kCAAQV,gBAAR,CAAyBW,cAAzB,EAAxB;AACA,cAAMC,QAAQ,GAAG,KAAKpB,QAAtB;AACA,cAAMqB,YAAY,GAAG,IAArB,CALe,CAKU;;AACzB,cAAMC,aAAa,GAAG;AAAA;AAAA,kCAAQd,gBAAR,CAAyBe,UAA/C;AAEA,iBAAOP,OAAO,IAAIC,UAAX,IAAyBC,eAAzB,IAA4CE,QAA5C,IAAwDC,YAAxD,IAAwEC,aAA/E;AACH;;AAEDE,QAAAA,IAAI,GAAG;AACH,cAAI,KAAKR,OAAL,EAAJ,EAAoB;AAChB,iBAAKpB,QAAL,GAAgB,IAAhB;AACA,iBAAKG,UAAL,GAAkB,KAAKJ,QAAL,CAAciB,EAAhC;AACA,iBAAKR,MAAL,GAAc,KAAKT,QAAL,CAAcQ,EAA5B;AACA,iBAAKsB,MAAL;AACH;AACJ;;AAEDC,QAAAA,UAAU,GAAG;AACT,cAAI,CAAC,KAAKV,OAAL,EAAL,EAAqB;AACjB,iBAAKpB,QAAL,GAAgB,KAAhB;AACA,iBAAK+B,YAAL;AACH;AACJ;;AAEDF,QAAAA,MAAM,GAAE,CAEP;;AAEDE,QAAAA,YAAY,GAAE,CAEb;;AAEDC,QAAAA,QAAQ,CAACC,SAAD,EAAmB,CAE1B;;AAEDC,QAAAA,WAAW,GAAE,CAEZ;;AAEDC,QAAAA,MAAM,CAACF,SAAD,EAAoB;AACtB,cAAI;AAAA;AAAA,sCAAUG,QAAV,IAAsB,KAAKhC,QAA/B,EAAyC;AACrC,iBAAK4B,QAAL,CAAcC,SAAd;;AAEA,gBAAI,CAAC,KAAKb,OAAL,EAAL,EAAqB;AACjB,mBAAKZ,MAAL,IAAeyB,SAAf;AACH;;AAED,gBAAI,KAAKjC,QAAT,EAAmB;AACf,mBAAKG,UAAL,IAAmB8B,SAAnB;;AACA,kBAAI,KAAK9B,UAAL,IAAmB,CAAvB,EAA0B;AACtB,qBAAK2B,UAAL;AACH;AACJ;AACJ;AACJ;;AAEDO,QAAAA,WAAW,GAAG;AACV,eAAKjC,QAAL,GAAgB,KAAhB;;AACA,cAAI,KAAKJ,QAAT,EAAmB;AACf,iBAAK8B,UAAL;AACH;AACJ;;AAEDQ,QAAAA,WAAW,CAACC,OAAD,EAA2B;AAAA,cAA1BA,OAA0B;AAA1BA,YAAAA,OAA0B,GAAP,KAAO;AAAA;;AAClC,eAAKnC,QAAL,GAAgB,IAAhB;;AACA,cAAI,CAACmC,OAAL,EAAc;AACV;AAAA;AAAA,oCAAQzB,eAAR,CAAwBC,OAAxB,GAAkC,KAAKP,MAAvC;AACH;AACJ;;AAEDgC,QAAAA,WAAW,GAAG;AACV,eAAKvB,QAAL;AACH;;AAEDwB,QAAAA,SAAS,GAAG;AACR,eAAKP,WAAL,GADQ,CAER;AACH;;AAEDQ,QAAAA,SAAS,CAACC,OAAD,EAAmB;AACxB,cAAIA,OAAJ,EAAa;AACT,iBAAKC,aAAL,CAAmB,IAAnB;AACA,iBAAK9C,OAAL,CAAa+C,QAAb,CAAsBC,YAAtB,CAAmC,CAAnC,EAAsC,eAAtC,EAAuD,KAAvD;AACA,iBAAKhD,OAAL,CAAa+C,QAAb,CAAsBE,mBAAtB,CAA0C,MAAM;AAC5C,mBAAKjD,OAAL,CAAa+C,QAAb,CAAsBE,mBAAtB,CAA0C,IAA1C;AACA,mBAAKjD,OAAL,CAAa+C,QAAb,CAAsBC,YAAtB,CAAmC,CAAnC,EAAsC,WAAtC,EAAmD,IAAnD;AACH,aAHD;AAIH,WAPD,MAOO;AACH,iBAAKhD,OAAL,CAAa+C,QAAb,CAAsBC,YAAtB,CAAmC,CAAnC,EAAsC,cAAtC,EAAsD,KAAtD;AACA,iBAAKhD,OAAL,CAAa+C,QAAb,CAAsBE,mBAAtB,CAA0C,MAAM;AAC5C,mBAAKjD,OAAL,CAAa+C,QAAb,CAAsBE,mBAAtB,CAA0C,IAA1C;AACA,mBAAKH,aAAL,CAAmB,KAAnB;AACH,aAHD;AAIH;AACJ;;AAEDA,QAAAA,aAAa,CAACI,QAAD,EAAoB;AAC7B,eAAKlD,OAAL,CAAamD,aAAb,CAA2BC,MAA3B,GAAoC,CAACF,QAArC;AACA,eAAKlD,OAAL,CAAa+C,QAAb,CAAsBM,IAAtB,CAA2BC,OAA3B,GAAqCJ,QAAQ,GAAG,GAAH,GAAS,CAAtD;AACH;;AA9J+C,O", "sourcesContent": ["import { _decorator, Component } from 'cc';\r\nimport BaseComp from '../../base/BaseComp';\r\nimport { GameIns } from '../../../GameIns';\r\nimport { GameConst } from '../../../const/GameConst';\r\n\r\n\r\nconst { ccclass, property } = _decorator;\r\n\r\n@ccclass('MainSkillBase')\r\nexport default class MainSkillBase extends BaseComp {\r\n    m_plane: any = null;\r\n    m_record: any = null;\r\n    m_isFire: boolean = false;\r\n    m_CDTime: number = 0;\r\n    m_isEnd: boolean = false;\r\n    m_keepTime: number = 0;\r\n    m_inGame: boolean = false;\r\n\r\n    onInit() {}\r\n\r\n    getProgress(): number {\r\n        return this.m_CDTime / this.m_record.cd;\r\n    }\r\n\r\n    get cdTime(): number {\r\n        return this.m_CDTime;\r\n    }\r\n\r\n    set cdTime(value: number) {\r\n        this.m_CDTime = value;\r\n    }\r\n\r\n    setData(record: any) {\r\n        this.m_plane = GameIns.mainPlaneManager.plane;\r\n        this.m_record = record;\r\n        this.cdTime = GameIns.gameDataManager.skillCD || 0;\r\n        this.m_keepTime = this.m_record.kd;\r\n    }\r\n\r\n    async createUI() {\r\n        if (GameIns.mainPlaneManager.isPlane(710)) {\r\n            return;\r\n        }\r\n\r\n        // let dialog = uiManager.getDialog(MechaUI);\r\n        // if (!dialog) {\r\n        //     try {\r\n        //         await loadManager.loadPrefab('MechaUI');\r\n        //         dialog = await uiManager.createDialog(MechaUI);\r\n        //     } catch (error) {\r\n        //         GFunc.wxLoadErr();\r\n        //     }\r\n        // }\r\n\r\n        // if (dialog) {\r\n        //     dialog.onActive();\r\n        // }\r\n    }\r\n\r\n    setCanFire() {\r\n        this.m_isFire = false;\r\n        this.cdTime = 0;\r\n    }\r\n\r\n    canFire(): boolean {\r\n        const canFire = !this.m_isFire;\r\n        const isCooldown = this.cdTime <= 0;\r\n        const isSkillUnlocked = GameIns.mainPlaneManager.checkLockSkill();\r\n        const isInGame = this.m_inGame;\r\n        const isNotLJStage = true//!StageMgr.isLJStage(GameIns.battleManager.subStage);\r\n        const isFireEnabled = GameIns.mainPlaneManager.fireEnable;\r\n\r\n        return canFire && isCooldown && isSkillUnlocked && isInGame && isNotLJStage && isFireEnabled;\r\n    }\r\n\r\n    fire() {\r\n        if (this.canFire()) {\r\n            this.m_isFire = true;\r\n            this.m_keepTime = this.m_record.kd;\r\n            this.cdTime = this.m_record.cd;\r\n            this.onFire();\r\n        }\r\n    }\r\n\r\n    fireFinish() {\r\n        if (!this.canFire()) {\r\n            this.m_isFire = false;\r\n            this.onFireFinish();\r\n        }\r\n    }\r\n\r\n    onFire(){\r\n\r\n    }\r\n\r\n    onFireFinish(){\r\n\r\n    }\r\n\r\n    onUpdate(deltaTime: number){\r\n\r\n    }\r\n\r\n    removeSkill(){\r\n\r\n    }\r\n\r\n    update(deltaTime: number) {\r\n        if (GameConst.GameAble && this.m_inGame) {\r\n            this.onUpdate(deltaTime);\r\n\r\n            if (!this.canFire()) {\r\n                this.cdTime -= deltaTime;\r\n            }\r\n\r\n            if (this.m_isFire) {\r\n                this.m_keepTime -= deltaTime;\r\n                if (this.m_keepTime <= 0) {\r\n                    this.fireFinish();\r\n                }\r\n            }\r\n        }\r\n    }\r\n\r\n    quiteBattle() {\r\n        this.m_inGame = false;\r\n        if (this.m_isFire) {\r\n            this.fireFinish();\r\n        }\r\n    }\r\n\r\n    startBattle(resetCD: boolean = false) {\r\n        this.m_inGame = true;\r\n        if (!resetCD) {\r\n            GameIns.gameDataManager.skillCD = this.cdTime;\r\n        }\r\n    }\r\n\r\n    beginBattle() {\r\n        this.createUI();\r\n    }\r\n\r\n    endBattle() {\r\n        this.removeSkill();\r\n        // uiManager.removeDialog(MechaUI);\r\n    }\r\n\r\n    crazyAnim(isCrazy: boolean) {\r\n        if (isCrazy) {\r\n            this.setSkinActive(true);\r\n            this.m_plane.skinAnim.setAnimation(0, 'tocrazyingame', false);\r\n            this.m_plane.skinAnim.setCompleteListener(() => {\r\n                this.m_plane.skinAnim.setCompleteListener(null);\r\n                this.m_plane.skinAnim.setAnimation(0, 'crazyidle', true);\r\n            });\r\n        } else {\r\n            this.m_plane.skinAnim.setAnimation(0, 'ingametoidle', false);\r\n            this.m_plane.skinAnim.setCompleteListener(() => {\r\n                this.m_plane.skinAnim.setCompleteListener(null);\r\n                this.setSkinActive(false);\r\n            });\r\n        }\r\n    }\r\n\r\n    setSkinActive(isActive: boolean) {\r\n        this.m_plane.mechaAnimNode.active = !isActive;\r\n        this.m_plane.skinAnim.node.opacity = isActive ? 255 : 0;\r\n    }\r\n}"]}