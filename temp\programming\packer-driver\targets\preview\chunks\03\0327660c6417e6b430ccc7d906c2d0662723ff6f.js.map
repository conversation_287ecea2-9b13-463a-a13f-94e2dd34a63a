{"version": 3, "sources": ["file:///E:/M2Game/Client/assets/scripts/Game/ui/bullet/Bullet.ts"], "names": ["_decorator", "Sprite", "tween", "game", "ParticleSystem2D", "Tween", "v3", "GameIns", "Tools", "Entity", "AngleComp", "Bullet<PERSON>ly", "GameFunc", "UIAnimMethods", "ScaleComp", "XYFly", "ParticleComponent", "ColliderComp", "MainPlane", "GameConst", "ccclass", "property", "Bullet", "enemy", "bulletID", "m_collideComp", "playHurt", "isCirt", "m_createTime", "m_lifeTime", "aliveTime", "m_fireTween", "collide<PERSON>un", "_catapultCount", "_catapultAtkRatio", "_collideEntity", "_catapultTargets", "streakCall", "m_throughArr", "m_config", "m_mainEntity", "bulletState", "create", "bulletManager", "getConfig", "removeAllComp", "rs", "addComp", "angleSpeed", "bustyle", "getComp", "removeComp", "zdyd", "length", "waittime", "particleEffect", "zdwy1", "getType", "id", "<PERSON><PERSON><PERSON>", "node", "setScale", "scale", "fire", "active", "stopAllByTarget", "removeChildByName", "skinImg", "parent", "opacity", "spriteFrame", "setImage", "image", "enemyBulletAtlas", "mainBulletAtlas", "loadManager", "error", "init", "isEnemy", "position", "state", "mainEntity", "intensifyImage", "fireIntensify", "intensifyName", "intensifyScale", "setPosition", "x", "y", "angle", "retrieve", "totalTime", "clone", "through", "disappear", "m_comps", "for<PERSON>ach", "comp", "bulletFlyComp", "setData", "colliderComp", "shiftingBody", "shiftingbody", "bodyWidth", "bodyHeight", "body", "enemyFire", "fireEffect", "fireTween", "to", "fromTo", "repeatF<PERSON><PERSON>", "start", "enemyFireTween", "burstFire", "_getAttack", "randomValue", "Math", "random", "crit<PERSON><PERSON>ce", "cirt", "atkChallenge", "attack", "config", "type", "indexOf", "critMultiplier", "getAttack", "target", "playHurtAudio", "onCollide", "exstyle1", "hurtEffectManager", "createHurtEffect", "exstyle2", "onOutScreen", "currentTime", "remove", "force", "checkCatapult", "will<PERSON><PERSON><PERSON>", "playBulletCollideDunAnim", "removeBullet", "console", "stopAllActions", "dieRemove", "playBulletDieAnim", "enabled", "splice", "isCatapulted", "setColliderEnable", "update", "deltaTime", "GameAble", "updateComp", "refresh", "PrefabName", "audioMaxNum", "hit1", "hit2", "hit3", "hit4", "hit5", "hit6", "swordHit"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAASA,MAAAA,U,OAAAA,U;AAAYC,MAAAA,M,OAAAA,M;AAA8BC,MAAAA,K,OAAAA,K;AAA2BC,MAAAA,I,OAAAA,I;AAAMC,MAAAA,gB,OAAAA,gB;AAAkBC,MAAAA,K,OAAAA,K;AAAOC,MAAAA,E,OAAAA,E;;AACpGC,MAAAA,O,iBAAAA,O;;AACAC,MAAAA,K,iBAAAA,K;;AACFC,MAAAA,M;;AACAC,MAAAA,S;;AACAC,MAAAA,S;;AACEC,MAAAA,Q,iBAAAA,Q;;AAEFC,MAAAA,a;;AACAC,MAAAA,S;;AACAC,MAAAA,K;;AACAC,MAAAA,iB;;AACEC,MAAAA,Y,kBAAAA,Y;;AACAC,MAAAA,S,kBAAAA,S;;AACAC,MAAAA,S,kBAAAA,S;;;;;;;;;OAGH;AAAEC,QAAAA,OAAF;AAAWC,QAAAA;AAAX,O,GAAwBrB,U;;yBAGTsB,M,WADpBF,OAAO,CAAC,QAAD,C,UAIHC,QAAQ,CAACpB,MAAD,C,UAGRoB,QAAQ,CAACpB,MAAD,C,UAGRoB,QAAQ,CAACpB,MAAD,C,UAGRoB,QAAQ,CAACjB,gBAAD,C,sCAbb,MACqBkB,MADrB;AAAA;AAAA,4BAC2C;AAAA;AAAA;;AAAA;;AAAA;;AAAA;;AAAA;;AAAA,eAgBvCC,KAhBuC,GAgB/B,KAhB+B;AAAA,eAiBvCC,QAjBuC,GAiB5B,CAjB4B;AAAA,eAkBvCC,aAlBuC,GAkBvB,IAlBuB;AAAA,eAmBvCC,QAnBuC,GAmB5B,IAnB4B;AAAA,eAoBvCC,MApBuC,GAoB9B,KApB8B;AAAA,eAqBvCC,YArBuC,GAqBxB,CArBwB;AAAA,eAsBvCC,UAtBuC,GAsB1B,CAtB0B;AAAA,eAuBvCC,SAvBuC,GAuB3B,CAvB2B;AAAA,eAwBvCC,WAxBuC,GAwBzB,IAxByB;AAAA,eAyBvCC,UAzBuC,GAyB1B,KAzB0B;AAAA,eA0BvCC,cA1BuC,GA0BtB,CA1BsB;AAAA,eA2BvCC,iBA3BuC,GA2BnB,CA3BmB;AAAA,eA4BvCC,cA5BuC,GA4BtB,IA5BsB;AAAA,eA6BvCC,gBA7BuC,GA6BpB,EA7BoB;AAAA,eA8BvCC,UA9BuC,GA8B1B,IA9B0B;AAAA,eA+BvCC,YA/BuC,GA+BxB,EA/BwB;AAAA,eAgCvCC,QAhCuC;AAAA,eAiCvCC,YAjCuC;AAAA,eAkCvCC,WAlCuC;AAAA;;AAqCvC;AACJ;AACA;AACA;AACIC,QAAAA,MAAM,CAAClB,QAAD,EAAW;AACb,eAAKA,QAAL,GAAgBA,QAAhB;AACA,eAAKe,QAAL,GAAgB;AAAA;AAAA,kCAAQI,aAAR,CAAsBC,SAAtB,CAAgC,KAAKpB,QAArC,CAAhB;AACA,eAAKqB,aAAL,GAHa,CAKb;;AACA,cAAI,KAAKN,QAAL,CAAcO,EAAd,IAAoB,KAAKP,QAAL,CAAcO,EAAd,KAAqB,CAA7C,EAAgD;AAC5C,iBAAKC,OAAL;AAAA;AAAA,wCAAwB;AAAA;AAAA,wCAAc;AAAEC,cAAAA,UAAU,EAAE,KAAKT,QAAL,CAAcO;AAA5B,aAAd,EAAgD,KAAKP,QAAL,CAAcU,OAA9D,CAAxB;AACH,WARY,CAUb;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AAEA;AACA;AACA;AAEA;AACA;AACA;AAEA;AACA;AACA;AAEA;AACA;AACA;AAEA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AAEA;AACA;AACA;AAEA;AACA;AACA;AAEA;;;AACY,eAAKF,OAAL;AAAA;AAAA,sCAAwB;AAAA;AAAA,sCAAc,KAAKR,QAAnB,CAAxB;;AACA,cAAI,KAAKA,QAAL,CAAcU,OAAd,KAA0B,EAA1B,IAAgC,KAAKV,QAAL,CAAcU,OAAd,KAA0B,EAA9D,EAAkE;AAC9D,iBAAKF,OAAL;AAAA;AAAA,wCAAwB;AAAA;AAAA,yCAAxB;AACH,WAFD,MAEO,IAAI,KAAKG,OAAL;AAAA;AAAA,qCAAJ,EAA6B;AAChC,iBAAKC,UAAL;AAAA;AAAA;AACH;;AAED,cAAI,KAAKZ,QAAL,CAAca,IAAd,CAAmBC,MAAnB,GAA4B,CAAhC,EAAmC;AAC/B,iBAAKN,OAAL;AAAA;AAAA,gCAAoB;AAAA;AAAA,gCAAU,KAAKR,QAAL,CAAca,IAAxB,EAA8B,KAAKb,QAAL,CAAce,QAA5C,EAAsD,KAAKf,QAAL,CAAce,QAAd,CAAuBD,MAAvB,GAAgC,CAAtF,CAApB;AACH,WAFD,MAEO,IAAI,KAAKH,OAAL;AAAA;AAAA,6BAAJ,EAAyB;AAC5B,iBAAKC,UAAL;AAAA;AAAA;AACH,WAxGA,CAyGT;AACJ;AAEA;;;AACA,cAAMI,cAAc,GAAG,KAAKhB,QAAL,CAAciB,KAArC;;AACA,cAAID,cAAc,IAAIA,cAAc,KAAK,EAAzC,EAA6C;AACzC,iBAAKR,OAAL;AAAA;AAAA,wDAAgC;AAAA;AAAA,wDAAsBQ,cAAtB,EAAsC,CAAtC,EAAyC,KAAKE,OAAL,EAAzC,EAAyD,KAAKlB,QAAL,CAAcmB,EAAvE,CAAhC;AACH,WAhHY,CAkHb;;;AACA,eAAKjC,aAAL,GAAqB,KAAKsB,OAAL;AAAA;AAAA,4CAA2B;AAAA;AAAA,6CAA3B,CAArB,CAnHa,CAqHb;;AACA,eAAKY,OAAL,GAtHa,CAwHb;;AACA,eAAKC,IAAL,CAAUC,QAAV,CAAmB,KAAKtB,QAAL,CAAcuB,KAAjC,EAAuC,KAAKvB,QAAL,CAAcuB,KAArD,EAzHa,CA2Hb;;AACA,cAAI,KAAKC,IAAT,EAAe;AACX,iBAAKA,IAAL,CAAUH,IAAV,CAAeI,MAAf,GAAwB,KAAxB;AACA3D,YAAAA,KAAK,CAAC4D,eAAN,CAAsB,KAAKF,IAAL,CAAUH,IAAhC;AACH;AACJ;AACD;AACJ;AACA;;;AACUD,QAAAA,OAAO,GAAG;AAAA;;AAAA;AACZ;AACA;AAAA;AAAA,gCAAMO,iBAAN,CAAwB,KAAI,CAACC,OAAL,CAAaP,IAAb,CAAkBQ,MAA1C,EAAkD,MAAlD;AACA;AAAA;AAAA,gCAAMF,iBAAN,CAAwB,KAAI,CAACC,OAAL,CAAaP,IAAb,CAAkBQ,MAA1C,EAAkD,QAAlD;AACA;AAAA;AAAA,gCAAMF,iBAAN,CAAwB,KAAI,CAACC,OAAL,CAAaP,IAAb,CAAkBQ,MAA1C,EAAkD,MAAlD,EAJY,CAMZ;;AACA,YAAA,KAAI,CAACD,OAAL,CAAaP,IAAb,CAAkBS,OAAlB,GAA4B,GAA5B,CAPY,CASZ;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AAEA;AACA;AACA;AACA;AAEA;;AACA,YAAA,KAAI,CAACF,OAAL,CAAaG,WAAb,GAA2B,IAA3B;;AAEA,gBAAI,KAAI,CAAC9C,QAAL,GAAgB,IAApB,EAA0B;AACtB;AACA;AAAA;AAAA,wCAAS+C,QAAT,CAAkB,KAAI,CAACJ,OAAvB,EAAgC,KAAI,CAAC5B,QAAL,CAAciC,KAA9C,EAAqD;AAAA;AAAA,sCAAQ7B,aAAR,CAAsB8B,gBAA3E;AACH,aAHD,MAGO;AACH;AACA;AAAA;AAAA,wCAASF,QAAT,CAAkB,KAAI,CAACJ,OAAvB,EAAgC,KAAI,CAAC5B,QAAL,CAAciC,KAA9C,EAAqD;AAAA;AAAA,sCAAQ7B,aAAR,CAAsB+B,eAA3E,EAFG,CAIH;;AACA,kBAAI,CAAC,KAAI,CAACP,OAAL,CAAaG,WAAlB,EAA+B;AAC3B,sBAAM;AAAA;AAAA,wCAAQK,WAAR,CAAoBJ,QAApB,CAA6B,KAAI,CAACJ,OAAlC,EAA2C,KAAI,CAAC5B,QAAL,CAAciC,KAAzD,EAAgE,YAAhE,CAAN;AACH;AACJ,aAtCW,CAwCZ;;;AACA,gBAAI,CAAC,KAAI,CAACL,OAAL,CAAaG,WAAlB,EAA+B;AAC3B;AAAA;AAAA,kCAAMM,KAAN,CAAY,qBAAZ,EAAmC,KAAI,CAACrC,QAAL,CAAciC,KAAjD;AACH,aAFD,MAEO,CACH;AACA;AACA;AACA;AACA;AACH;AAjDW;AAkDf,SA/NsC,CAgO3C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEI;AACJ;AACA;AACA;AACA;AACA;AACA;;;AACIK,QAAAA,IAAI,CAACC,OAAD,EAAUC,QAAV,EAAoBC,KAApB,EAAkCC,UAAlC,EAAqD;AAAA,cAAjCD,KAAiC;AAAjCA,YAAAA,KAAiC,GAAzB,IAAyB;AAAA;;AAAA,cAAnBC,UAAmB;AAAnBA,YAAAA,UAAmB,GAAN,IAAM;AAAA;;AACrD,cAAIC,cAAJ,EAAoBb,OAApB,CADqD,CAGrD;;AACA,cAAIW,KAAK,CAACG,aAAN,IAAuBH,KAAK,CAACG,aAAN,CAAoB9B,MAApB,GAA6B,CAApD,IAAyD2B,KAAK,CAACG,aAAN,CAAoB,CAApB,IAAyB,CAAtF,EAAyF;AACrFD,YAAAA,cAAc,GAAG,KAAK3C,QAAL,CAAciC,KAAd,GAAsB;AAAA;AAAA,oCAAQ7B,aAAR,CAAsByC,aAA7D;AACA;AAAA;AAAA,oCAAQT,WAAR,CAAoBJ,QAApB,CAA6B,KAAKJ,OAAlC,EAA2Ce,cAA3C,EAA2D,YAA3D;AACA,iBAAKf,OAAL,CAAaP,IAAb,CAAkBC,QAAlB,CAA2B;AAAA;AAAA,oCAAQlB,aAAR,CAAsB0C,cAAjD,EAAgE;AAAA;AAAA,oCAAQ1C,aAAR,CAAsB0C,cAAtF;AACH;;AAED,eAAK7C,YAAL,GAAoByC,UAApB;AACA,eAAKrB,IAAL,CAAU0B,WAAV,CAAsBP,QAAQ,CAACQ,CAA/B,EAAkCR,QAAQ,CAACS,CAA3C;AACA,eAAKrB,OAAL,CAAaP,IAAb,CAAkB6B,KAAlB,GAA0B,CAA1B;AACA,eAAK5D,UAAL,GAAkB,KAAKU,QAAL,CAAcmD,QAAhC;;AAEA,cAAI,KAAK7D,UAAL,GAAkB,CAAtB,EAAyB;AACrB,iBAAKD,YAAL,GAAoBzB,IAAI,CAACwF,SAAzB;AACH;;AAED,eAAK/B,IAAL,CAAU6B,KAAV,GAAkBX,OAAO,GAAG,MAAMC,QAAQ,CAACU,KAAlB,GAA0B,CAACV,QAAQ,CAACU,KAA7D;AACA,eAAKlE,KAAL,GAAauD,OAAb;;AAEA,cAAI,CAACE,KAAK,CAACY,KAAX,EAAkB;AACdZ,YAAAA,KAAK,CAACa,OAAN,GAAgB,KAAKtD,QAAL,CAAcuD,SAAd,KAA4B,CAA5C;AACH;;AAED,eAAKrD,WAAL,GAAmBuC,KAAnB,CA1BqD,CA4BrD;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AAEA;;AACA,eAAKe,OAAL,CAAaC,OAAb,CAAsBC,IAAD,IAAU;AAC3BA,YAAAA,IAAI,CAACpB,IAAL,CAAU,IAAV;AACH,WAFD,EAxCqD,CA4CrD;;AACA,cAAMqB,aAAa,GAAG,KAAKhD,OAAL;AAAA;AAAA,qCAAtB;;AACA,cAAIgD,aAAJ,EAAmB;AACfA,YAAAA,aAAa,CAACC,OAAd,CAAsB,CAACpB,QAAQ,CAACU,KAAhC,EAAuC,KAAKjD,YAA5C,EAA0D,KAAKjB,KAA/D;AACH,WAhDoD,CAkDrD;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AAEA;;;AACA,cAAM6E,YAAY,GAAG,KAAKlD,OAAL;AAAA;AAAA,2CAArB;AACA,cAAMmD,YAAY,GAAG,KAAK9D,QAAL,CAAc+D,YAAnC;AACA,cAAMC,SAAS,GAAGF,YAAY,CAAChD,MAAb,GAAsB,CAAtB,GAA0BgD,YAAY,CAAC,CAAD,CAAtC,GAA4C,CAA9D;AACA,cAAMG,UAAU,GAAGH,YAAY,CAAChD,MAAb,GAAsB,CAAtB,GAA0BgD,YAAY,CAAC,CAAD,CAAtC,GAA4C,CAA/D,CAlFqD,CAoFrD;AACA;AACA;;AACID,UAAAA,YAAY,CAACD,OAAb,CAAqB,CAAC,CAAD,EAAII,SAAJ,EAAeC,UAAf,EAA2B,KAAKjE,QAAL,CAAckE,IAAzC,EAA+C,KAAKlE,QAAL,CAAckE,IAA7D,CAArB,EAvFiD,CAwFrD;AAEA;AACA;AACA;AACA;AACA;AAEA;;AACA,cAAI,KAAK1C,IAAL,IAAa,KAAK2C,SAAtB,EAAiC;AAC7B,gBAAMC,UAAU,GAAG,KAAKpE,QAAL,CAAciB,KAAjC;;AACA,gBAAImD,UAAU,IAAIA,UAAU,KAAK,EAAjC,EAAqC;AACjC,kBAAI,KAAKpE,QAAL,CAAcmB,EAAd,GAAmB,IAAvB,EAA6B;AACzB,qBAAKK,IAAL,CAAUH,IAAV,CAAeI,MAAf,GAAwB,IAAxB;AACA,qBAAKD,IAAL,CAAUH,IAAV,CAAeS,OAAf,GAAyB,GAAzB;AACA,qBAAKqC,SAAL,CAAe9C,IAAf,CAAoBI,MAApB,GAA6B,KAA7B;AACA3D,gBAAAA,KAAK,CAAC4D,eAAN,CAAsB,KAAKF,IAAL,CAAUH,IAAhC;AAEA,oBAAMgD,SAAS,GAAG1G,KAAK,CAAC,KAAK6D,IAAL,CAAUH,IAAX,CAAL,CACbiD,EADa,CACV;AAAA;AAAA,oDAAcC,MAAd,CAAqB,CAArB,EAAwB,CAAxB,CADU,EACkB;AAAEhD,kBAAAA,KAAK,EAACxD,EAAE,CAAC,CAAD,EAAG,CAAH;AAAV,iBADlB,EAEbuG,EAFa,CAEV;AAAA;AAAA,oDAAcC,MAAd,CAAqB,CAArB,EAAwB,CAAxB,CAFU,EAEkB;AAAEhD,kBAAAA,KAAK,EAACxD,EAAE,CAAC,KAAD,EAAO,IAAP;AAAV,iBAFlB,EAGbuG,EAHa,CAGV;AAAA;AAAA,oDAAcC,MAAd,CAAqB,CAArB,EAAwB,CAAxB,CAHU,EAGkB;AAAEhD,kBAAAA,KAAK,EAACxD,EAAE,CAAC,CAAD,EAAG,CAAH;AAAV,iBAHlB,CAAlB;AAKAJ,gBAAAA,KAAK,CAAC,KAAK6D,IAAL,CAAUH,IAAX,CAAL,CAAsBmD,aAAtB,CAAoCH,SAApC,EAA+CI,KAA/C;AACH,eAZD,MAYO;AACH,qBAAKjD,IAAL,CAAUH,IAAV,CAAeI,MAAf,GAAwB,KAAxB;AACA,qBAAK0C,SAAL,CAAe9C,IAAf,CAAoBI,MAApB,GAA6B,IAA7B;AACA,qBAAK0C,SAAL,CAAe9C,IAAf,CAAoBS,OAApB,GAA8B,GAA9B;AACAhE,gBAAAA,KAAK,CAAC4D,eAAN,CAAsB,KAAKyC,SAAL,CAAe9C,IAArC;AAEA,oBAAMqD,cAAc,GAAG/G,KAAK,CAAC,KAAKwG,SAAL,CAAe9C,IAAhB,CAAL,CAClBiD,EADkB,CACf;AAAA;AAAA,oDAAcC,MAAd,CAAqB,CAArB,EAAwB,CAAxB,CADe,EACa;AAAEhD,kBAAAA,KAAK,EAACxD,EAAE,CAAC,GAAD,EAAK,GAAL;AAAV,iBADb,EAElBuG,EAFkB,CAEf;AAAA;AAAA,oDAAcC,MAAd,CAAqB,CAArB,EAAwB,CAAxB,CAFe,EAEa;AAAEhD,kBAAAA,KAAK,EAACxD,EAAE,CAAC,GAAD,EAAK,IAAL;AAAV,iBAFb,EAGlBuG,EAHkB,CAGf;AAAA;AAAA,oDAAcC,MAAd,CAAqB,CAArB,EAAwB,CAAxB,CAHe,EAGa;AAAEhD,kBAAAA,KAAK,EAACxD,EAAE,CAAC,GAAD,EAAK,GAAL;AAAV,iBAHb,CAAvB;AAKAJ,gBAAAA,KAAK,CAAC,KAAKwG,SAAL,CAAe9C,IAAhB,CAAL,CAA2BmD,aAA3B,CAAyCE,cAAzC,EAAyDD,KAAzD;AACH;AACJ,aA1BD,MA0BO;AACH,mBAAKjD,IAAL,CAAUH,IAAV,CAAeI,MAAf,GAAwB,KAAxB;AACA,mBAAK0C,SAAL,CAAe9C,IAAf,CAAoBI,MAApB,GAA6B,KAA7B;AACA3D,cAAAA,KAAK,CAAC4D,eAAN,CAAsB,KAAKF,IAAL,CAAUH,IAAhC;AACH;AACJ,WAlIoD,CAoIrD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;;;AACA,cAAI,KAAKsD,SAAT,EAAoB;AAChB,iBAAKA,SAAL,CAAetD,IAAf,CAAoBI,MAApB,GAA6B,KAAKzB,QAAL,CAAcU,OAAd,KAA0B,EAAvD;AACH;AACJ;AACD;AACJ;AACA;AACA;;;AACIkE,QAAAA,UAAU,GAAG;AACT,cAAMC,WAAW,GAAGC,IAAI,CAACC,MAAL,EAApB;AACA,cAAMC,UAAU,GAAG,KAAK9E,WAAL,CAAiB+E,IAAjB,GAAwB,KAAK/E,WAAL,CAAiB+E,IAAjB,CAAsB,CAAtB,CAAxB,GAAmD,CAAtE;AACA,cAAMC,YAAY,GAAG,KAAKhF,WAAL,CAAiBgF,YAAjB,IAAiC,CAAtD;AACA,cAAIC,MAAM,GAAG,KAAKjF,WAAL,CAAiBiF,MAA9B,CAJS,CAMT;;AACA,cAAI,KAAKlF,YAAL;AAAA;AAAA,qCAAJ,EAA4C;AACxC,gBAAMmF,MAAM,GAAG,KAAKnF,YAAL,CAAkBD,QAAjC;;AACA,gBAAIoF,MAAM,IAAIA,MAAM,CAACC,IAAP,KAAgB,GAA9B,EAAmC;AAC/B,kBAAI,CAAC,EAAD,EAAK,EAAL,EAAS,EAAT,EAAa,EAAb,EAAiBC,OAAjB,CAAyB,KAAKpE,OAAL,EAAzB,KAA4C,CAAC,CAAjD,EAAoD;AAChDiE,gBAAAA,MAAM,IAAI,CAAV;AACH;AACJ;AACJ,WAdQ,CAgBT;;;AACA,cAAIN,WAAW,IAAIG,UAAnB,EAA+B;AAC3B,gBAAMO,cAAc,GAAG,KAAKrF,WAAL,CAAiB+E,IAAjB,GAAwB,KAAK/E,WAAL,CAAiB+E,IAAjB,CAAsB,CAAtB,CAAxB,GAAmD,CAA1E;AACA,iBAAK7F,MAAL,GAAc,IAAd;AACA,mBAAO,CAAC+F,MAAM,GAAGD,YAAV,IAA0BK,cAAjC;AACH,WAJD,MAIO;AACH,iBAAKnG,MAAL,GAAc,KAAd;AACA,mBAAO+F,MAAM,GAAGD,YAAhB;AACH;AACJ;AAED;AACJ;AACA;AACA;AACA;;;AACIM,QAAAA,SAAS,CAACC,MAAD,EAAgB;AAAA,cAAfA,MAAe;AAAfA,YAAAA,MAAe,GAAN,IAAM;AAAA;;AACrB,cAAIN,MAAM,GAAG,KAAKP,UAAL,EAAb,CADqB,CAGrB;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;;;AAEA,iBAAOO,MAAP;AACH;AAED;AACJ;AACA;AACA;;;AACIjE,QAAAA,OAAO,GAAG;AACN,iBAAO,KAAKlB,QAAL,CAAcU,OAArB;AACH;AACD;AACJ;AACA;;;AACIgF,QAAAA,aAAa,GAAG,CACZ;AACA;AACA;AACH;AAED;AACJ;AACA;AACA;;;AACIC,QAAAA,SAAS,CAACF,MAAD,EAAS;AACd;AACA;AACA;AAEA;AACA;AACA;AAEA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,cAAI,KAAKzF,QAAL,CAAc4F,QAAlB,EAA4B;AACxB;AACA;AACA;AACI;AAAA;AAAA,oCAAQC,iBAAR,CAA0BC,gBAA1B,CAA2CL,MAA3C,EAAmD,IAAnD,EAAyD,KAAKzF,QAAL,CAAc4F,QAAvE,EAAiF,KAAK5F,QAAL,CAAc+F,QAA/F,EAJoB,CAKxB;AACH,WA7Ba,CA+Bd;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;;AACH;AAED;AACJ;AACA;;;AACIC,QAAAA,WAAW,GAAG;AACV,cAAI,KAAK1G,UAAL,GAAkB,CAAtB,EAAyB;AACrB,gBAAM2G,WAAW,GAAGrI,IAAI,CAACwF,SAAzB;AACA,iBAAK7D,SAAL,GAAiB,CAAC0G,WAAW,GAAG,KAAK5G,YAApB,IAAoC,IAArD;AACH;;AAED,kBAAQ,KAAK6B,OAAL,EAAR;AACI,iBAAK,EAAL;AACA,iBAAK,EAAL;AACA,iBAAK,EAAL;AACA,iBAAK,EAAL;AACA,iBAAK,EAAL;AACI;;AACJ;AACI,kBAAI,KAAK3B,SAAL,IAAkB,KAAKD,UAA3B,EAAuC;AACnC,qBAAK4G,MAAL;AACH;;AAVT;AAYH;AAED;AACJ;AACA;AACA;;;AACIA,QAAAA,MAAM,CAACC,KAAD,EAAgB;AAAA,cAAfA,KAAe;AAAfA,YAAAA,KAAe,GAAP,KAAO;AAAA;;AAClB,cAAI,CAACA,KAAD,IAAU,CAAC,KAAKC,aAAL,EAAf,EAAqC;AACjC,iBAAKC,UAAL;;AAEA,gBAAI,KAAK5G,UAAT,EAAqB;AACjB;AAAA;AAAA,sCAAQoG,iBAAR,CAA0BS,wBAA1B,CAAmD,KAAKjF,IAAL,CAAUmB,QAA7D;AACH;;AAED;AAAA;AAAA,oCAAQpC,aAAR,CAAsBmG,YAAtB,CAAmC,IAAnC;;AAEA,gBAAI;AACA,kBAAI,KAAKzG,UAAT,EAAqB;AACjB,qBAAKA,UAAL;AACA,qBAAKA,UAAL,GAAkB,IAAlB;AACH;AACJ,aALD,CAKE,OAAOuC,KAAP,EAAc;AACZmE,cAAAA,OAAO,CAACnE,KAAR,CAAc,0BAAd,EAA0CA,KAA1C;AACH;;AAED,iBAAKpC,YAAL,GAAoB,IAApB;AACH;;AAED,cAAI,KAAKiB,OAAL,OAAmB,EAAvB,EAA2B;AACvB,iBAAKU,OAAL,CAAaP,IAAb,CAAkBoF,cAAlB;AACH;AACJ;AAED;AACJ;AACA;;;AACIC,QAAAA,SAAS,GAAG;AACR,eAAKL,UAAL;;AAEA,cAAI;AACA;AAAA;AAAA,oCAAQR,iBAAR,CAA0Bc,iBAA1B,CAA4C,KAAKtF,IAAL,CAAUmB,QAAtD;AACH,WAFD,CAEE,OAAOH,KAAP,EAAc;AACZmE,YAAAA,OAAO,CAACnE,KAAR,CAAc,yBAAd,EAAyCA,KAAzC;AACH;;AAED;AAAA;AAAA,kCAAQjC,aAAR,CAAsBmG,YAAtB,CAAmC,IAAnC,EAAyC,KAAzC;;AAEA,cAAI;AACA,gBAAI,KAAKzG,UAAT,EAAqB;AACjB,mBAAKA,UAAL;AACA,mBAAKA,UAAL,GAAkB,IAAlB;AACH;AACJ,WALD,CAKE,OAAOuC,KAAP,EAAc;AACZmE,YAAAA,OAAO,CAACnE,KAAR,CAAc,0BAAd,EAA0CA,KAA1C;AACH;;AAED,eAAKpC,YAAL,GAAoB,IAApB;AACH;AAED;AACJ;AACA;;;AACIoG,QAAAA,UAAU,GAAG;AACT,cAAI,KAAKnH,aAAT,EAAwB;AACpB,iBAAKA,aAAL,CAAmB0H,OAAnB,GAA6B,KAA7B;AACH;;AAED,cAAI,KAAKhF,OAAT,EAAkB;AACd,iBAAKA,OAAL,CAAaG,WAAb,GAA2B,IAA3B;AACH;;AAED,cAAI,KAAKP,IAAL,IAAa,KAAKA,IAAL,CAAUH,IAAV,CAAeI,MAAhC,EAAwC;AACpC,iBAAKD,IAAL,CAAUH,IAAV,CAAeS,OAAf,GAAyB,CAAzB;AACA,iBAAKN,IAAL,CAAUH,IAAV,CAAeoF,cAAf;AACH;;AAED,cAAI,KAAKtC,SAAL,IAAkB,KAAKA,SAAL,CAAe9C,IAAf,CAAoBI,MAA1C,EAAkD;AAC9C,iBAAK0C,SAAL,CAAe9C,IAAf,CAAoBS,OAApB,GAA8B,CAA9B;AACA,iBAAKqC,SAAL,CAAe9C,IAAf,CAAoBoF,cAApB;AACH;;AAED,eAAK1G,YAAL,CAAkB8G,MAAlB,CAAyB,CAAzB;AACH,SAnrBsC,CAorB3C;AACA;AACA;AACA;;;AACIT,QAAAA,aAAa,GAAG;AACZ,cAAIU,YAAY,GAAG,KAAnB,CADY,CAGpB;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEQ,iBAAOA,YAAP;AACH,SAtuBsC,CAwuB3C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AAEA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;;;AACIC,QAAAA,iBAAiB,CAACH,OAAD,EAAU;AACvB,cAAI,KAAK1H,aAAT,EAAwB;AACpB,iBAAKA,aAAL,CAAmB0H,OAAnB,GAA6BA,OAA7B;AACH;AACJ;AAED;AACJ;AACA;AACA;;;AACII,QAAAA,MAAM,CAACC,SAAD,EAAY;AACd,cAAI,CAAC;AAAA;AAAA,sCAAUC,QAAf,EAAyB;AACrB;AACH;;AAED,eAAK1D,OAAL,CAAaC,OAAb,CAAsBC,IAAD,IAAU;AAC3BA,YAAAA,IAAI,CAACsD,MAAL,CAAYC,SAAZ;AACH,WAFD,EALc,CASd;AACA;AACA;AACA;AACA;AACA;;AAEA,cAAI,KAAKjH,QAAL,CAAcU,OAAd,KAA0B,EAA1B,IAAgC,KAAKW,IAAL,CAAU4B,CAAV,GAAc,CAAC,IAAnD,EAAyD;AACrD,iBAAKiD,MAAL;AACH;AACJ;AAED;AACJ;AACA;AACA;;;AACIiB,QAAAA,UAAU,CAACF,SAAD,EAAY;AAClB,eAAKzD,OAAL,CAAaC,OAAb,CAAsBC,IAAD,IAAU;AAC3B,gBAAIA,IAAI,CAACyD,UAAT,EAAqB;AACjBzD,cAAAA,IAAI,CAACyD,UAAL,CAAgBF,SAAhB;AACH;AACJ,WAJD;AAKH;AAED;AACJ;AACA;;;AACIG,QAAAA,OAAO,GAAG;AACN,eAAK3H,UAAL,GAAkB,KAAlB;AACA,eAAKC,cAAL,GAAsB,CAAtB;AACA,eAAKC,iBAAL,GAAyB,CAAzB;AACA,eAAKC,cAAL,GAAsB,IAAtB;;AACA,eAAKC,gBAAL,CAAsBgH,MAAtB,CAA6B,CAA7B;AACH,SAx1BsC,CA01B3C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AAEA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AAEA;AACA;AAEA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAGI;AACJ;AACA;AAWA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;AA7lC2C,O,UAChCQ,U,GAAa,Q,UAqjCbC,W,GAAc;AACjBC,QAAAA,IAAI,EAAE,CADW;AAEjBC,QAAAA,IAAI,EAAE,CAFW;AAGjBC,QAAAA,IAAI,EAAE,CAHW;AAIjBC,QAAAA,IAAI,EAAE,CAJW;AAKjBC,QAAAA,IAAI,EAAE,CALW;AAMjBC,QAAAA,IAAI,EAAE,CANW;AAOjBC,QAAAA,QAAQ,EAAE;AAPO,O;;;;;iBAljCX,I;;;;;;;iBAGH,I;;;;;;;iBAGK,I;;;;;;;iBAGA,I", "sourcesContent": ["import { _decorator, Sprite, ParticleSystem, Node, tween, v2, misc, director, game, ParticleSystem2D, Tween, v3 } from 'cc';\r\nimport { GameIns } from '../../GameIns';\r\nimport { Tools } from '../../utils/Tools';\r\nimport Entity from '../base/Entity';\r\nimport AngleComp from '../base/AngleComp';\r\nimport BulletFly from './BulletFly';\r\nimport { GameFunc } from '../../GameFunc';\r\nimport GameConfig from '../../const/GameConfig';\r\nimport UIAnimMethods from '../base/UIAnimMethods';\r\nimport ScaleComp from '../base/ScaleComp';\r\nimport XYFly from '../base/XYFly';\r\nimport ParticleComponent from '../ParticleComponent';\r\nimport { ColliderComp } from '../base/ColliderComp';\r\nimport { MainPlane } from '../plane/mainPlane/MainPlane';\r\nimport { GameConst } from '../../const/GameConst';\r\n\r\n\r\nconst { ccclass, property } = _decorator;\r\n\r\n@ccclass('Bullet')\r\nexport default class Bullet extends Entity {\r\n    static PrefabName = \"Bullet\";\r\n\r\n    @property(Sprite)\r\n    skinImg = null;\r\n\r\n    @property(Sprite)\r\n    fire = null;\r\n\r\n    @property(Sprite)\r\n    enemyFire = null;\r\n\r\n    @property(ParticleSystem2D)\r\n    burstFire = null;\r\n\r\n\r\n    enemy = false;\r\n    bulletID = 1;\r\n    m_collideComp = null;\r\n    playHurt = true;\r\n    isCirt = false;\r\n    m_createTime = 0;\r\n    m_lifeTime = 0;\r\n    aliveTime = 0;\r\n    m_fireTween = null;\r\n    collideDun = false;\r\n    _catapultCount = 0;\r\n    _catapultAtkRatio = 1;\r\n    _collideEntity = null;\r\n    _catapultTargets = [];\r\n    streakCall = null;\r\n    m_throughArr = [];\r\n    m_config: any;\r\n    m_mainEntity: any;\r\n    bulletState: any;\r\n\r\n\r\n    /**\r\n     * 初始化子弹\r\n     * @param {number} bulletID 子弹的唯一标识符\r\n     */\r\n    create(bulletID) {\r\n        this.bulletID = bulletID;\r\n        this.m_config = GameIns.bulletManager.getConfig(this.bulletID);\r\n        this.removeAllComp();\r\n\r\n        // 添加子弹飞行组件（如旋转角度）\r\n        if (this.m_config.rs && this.m_config.rs !== 0) {\r\n            this.addComp(AngleComp, new AngleComp({ angleSpeed: this.m_config.rs }, this.m_config.bustyle));\r\n        }\r\n\r\n        // try {\r\n        //     this.node.opacity = 255;\r\n        // } catch (error) {\r\n        //     Tools.error('Error setting opacity:', error);\r\n        // }\r\n\r\n        // // 判断是否为跟踪子弹\r\n        // if (FireShells.isFollowBullet(this.m_config.bustyle)) {\r\n        //     const hideTime = this.m_config.para[this.m_config.para.length - 1];\r\n        //     this.addComp(TargetFly, new TargetFly({ speed: this.m_config.initialve, hideTime }));\r\n        // } else {\r\n        //     // 根据子弹类型添加不同的组件\r\n        //     switch (this.m_config.bustyle) {\r\n        //         case 7:\r\n        //             this.addComp(BulletParabolic, new BulletParabolic(this.m_config));\r\n        //             break;\r\n\r\n        //         case 25:\r\n        //             this.addComp(CircleZoomFly, new CircleZoomFly(this.m_config));\r\n        //             break;\r\n\r\n        //         case 29:\r\n        //             const speedFactor = PilotManager.getCurSkill(PilotSkillType.ArondBallSpeedFactor);\r\n        //             const radius = PilotManager.getCurSkill(PilotSkillType.ArondBallRadius);\r\n        //             const adjustedSpeed = speedFactor ? this.m_config.para[2] * (1 + speedFactor[0]) : this.m_config.para[2];\r\n        //             const adjustedRadius = radius ? this.m_config.para[0] + radius[0] : this.m_config.para[0];\r\n        //             const imageName = `${this.m_config.image.split('_')[0]}_${this.m_config.image.split('_')[1]}_tw`;\r\n        //             this.addComp(CircleFly, new CircleFly(adjustedSpeed, adjustedRadius));\r\n        //             this.addComp(ParticleComponent, new ParticleComponent(imageName, 0, this.getType(), this.m_config.id));\r\n        //             this.addComp(OnceCollideComp, new OnceCollideComp());\r\n        //             this.addComp(ResistBulletComp, new ResistBulletComp());\r\n        //             break;\r\n\r\n        //         case 32:\r\n        //             const hideTime32 = this.m_config.para[this.m_config.para.length - 1];\r\n        //             this.addComp(TargetAroundFly, new TargetAroundFly({ speed: this.m_config.initialve, hideTime: hideTime32 }));\r\n        //             break;\r\n\r\n        //         case 33:\r\n        //             const hideTime33 = this.m_config.para[this.m_config.para.length - 1];\r\n        //             this.addComp(TargetAroundFly2, new TargetAroundFly2({ speed: this.m_config.initialve, hideTime: hideTime33 }));\r\n        //             break;\r\n\r\n        //         case 36:\r\n        //             this.addComp(FlyThenTurnComp, new FlyThenTurnComp(this.m_config));\r\n        //             break;\r\n\r\n        //         case 37:\r\n        //             this.addComp(BoomerangFly, new BoomerangFly(this.m_config));\r\n        //             break;\r\n\r\n        //         case 43:\r\n        //             this.addComp(BatSkillFly, new BatSkillFly(this.m_config));\r\n        //             break;\r\n\r\n        //         case 44:\r\n        //             this.addComp(HarmonicFly, new HarmonicFly(this.m_config, this.m_config.para));\r\n        //             break;\r\n\r\n        //         case 47:\r\n        //             this.addComp(WaveRotateComp, new WaveRotateComp(this.m_config, this.m_config.para));\r\n        //             break;\r\n\r\n        //         case 48:\r\n        //             const knifeSpeed = this.m_config.para[1] * (1 + MainPlaneManager.data.knifeSpeedUp / 100);\r\n        //             const knifeRadius = this.m_config.para[2];\r\n        //             this.addComp(CircleFly1, new CircleFly1(knifeSpeed, knifeRadius));\r\n        //             this.addComp(OnceCollideComp, new OnceCollideComp());\r\n        //             break;\r\n\r\n        //         case 53:\r\n        //             this.addComp(AngleDeltaFly, new AngleDeltaFly(this.m_config));\r\n        //             break;\r\n\r\n        //         case 54:\r\n        //             this.addComp(CircleChangeFly, new CircleChangeFly(this.m_config));\r\n        //             break;\r\n\r\n        //         case 56:\r\n        //             this.addComp(RefractionFly, new RefractionFly(this.m_config));\r\n        //             break;\r\n\r\n        //         default:\r\n                    this.addComp(BulletFly, new BulletFly(this.m_config));\r\n                    if (this.m_config.bustyle === 50 || this.m_config.bustyle === 59) {\r\n                        this.addComp(ScaleComp, new ScaleComp());\r\n                    } else if (this.getComp(ScaleComp)) {\r\n                        this.removeComp(ScaleComp);\r\n                    }\r\n\r\n                    if (this.m_config.zdyd.length > 0) {\r\n                        this.addComp(XYFly, new XYFly(this.m_config.zdyd, this.m_config.waittime, this.m_config.waittime.length > 0));\r\n                    } else if (this.getComp(XYFly)) {\r\n                        this.removeComp(XYFly);\r\n                    }\r\n            // }\r\n        // }\r\n\r\n        // 添加粒子效果\r\n        const particleEffect = this.m_config.zdwy1;\r\n        if (particleEffect && particleEffect !== '') {\r\n            this.addComp(ParticleComponent, new ParticleComponent(particleEffect, 0, this.getType(), this.m_config.id));\r\n        }\r\n\r\n        // 添加碰撞组件\r\n        this.m_collideComp = this.addComp(ColliderComp, new ColliderComp());\r\n\r\n        // 设置子弹皮肤\r\n        this.setSkin();\r\n\r\n        // 设置子弹缩放\r\n        this.node.setScale(this.m_config.scale,this.m_config.scale);\r\n\r\n        // 初始化发射效果\r\n        if (this.fire) {\r\n            this.fire.node.active = false;\r\n            Tween.stopAllByTarget(this.fire.node);\r\n        }\r\n    }\r\n    /**\r\n * 设置子弹的皮肤\r\n */\r\n    async setSkin() {\r\n        // 移除旧的子弹特效节点\r\n        Tools.removeChildByName(this.skinImg.node.parent, 'fist');\r\n        Tools.removeChildByName(this.skinImg.node.parent, 'gatlin');\r\n        Tools.removeChildByName(this.skinImg.node.parent, 'tail');\r\n\r\n        // 重置子弹透明度\r\n        this.skinImg.node.opacity = 255;\r\n\r\n        // // 根据子弹类型设置特殊皮肤\r\n        // switch (this.m_config.bustyle) {\r\n        //     case 51:\r\n        //         this.setBossBoxingFistSkin();\r\n        //         return;\r\n\r\n        //     case 52:\r\n        //         this.setBossBoxingGatlinSkin();\r\n        //         return;\r\n\r\n        //     case 55:\r\n        //         this.addTail();\r\n        //         break;\r\n        // }\r\n\r\n        // 设置子弹的默认皮肤\r\n        this.skinImg.spriteFrame = null;\r\n\r\n        if (this.bulletID > 1000) {\r\n            // 敌方子弹\r\n            GameFunc.setImage(this.skinImg, this.m_config.image, GameIns.bulletManager.enemyBulletAtlas);\r\n        } else {\r\n            // 主角子弹\r\n            GameFunc.setImage(this.skinImg, this.m_config.image, GameIns.bulletManager.mainBulletAtlas);\r\n\r\n            // 如果未加载到图片，尝试异步加载\r\n            if (!this.skinImg.spriteFrame) {\r\n                await GameIns.loadManager.setImage(this.skinImg, this.m_config.image, 'mainBullet');\r\n            }\r\n        }\r\n\r\n        // 检查图片是否加载成功\r\n        if (!this.skinImg.spriteFrame) {\r\n            Tools.error('Bullet image error:', this.m_config.image);\r\n        } else {\r\n            // // 设置子弹的宽高比例\r\n            // if (!GameConfig.isHD) {\r\n            //     this.skinImg.node.width = 0.666666666667 * this.skinImg.spriteFrame.getOriginalSize().width;\r\n            //     this.skinImg.node.height = 0.666666666667 * this.skinImg.spriteFrame.getOriginalSize().height;\r\n            // }\r\n        }\r\n    }\r\n//     /**\r\n//  * 初始化跟踪子弹\r\n//  * @param {boolean} isEnemy 是否为敌方子弹\r\n//  * @param {Object} position 子弹的初始位置\r\n//  * @param {number} attack 子弹的攻击力\r\n//  */\r\n//     initFollow(isEnemy, position, attack) {\r\n//         this.init(isEnemy, position, {\r\n//             attack: attack,\r\n//             through: false,\r\n//         });\r\n//     }\r\n\r\n    /**\r\n     * 初始化子弹\r\n     * @param {boolean} isEnemy 是否为敌方子弹\r\n     * @param {Object} position 子弹的初始位置\r\n     * @param {Object} state 子弹的状态\r\n     * @param {Entity} mainEntity 子弹的发射实体\r\n     */\r\n    init(isEnemy, position, state = null, mainEntity = null) {\r\n        let intensifyImage, opacity;\r\n\r\n        // 设置强化子弹的皮肤\r\n        if (state.fireIntensify && state.fireIntensify.length > 0 && state.fireIntensify[0] > 0) {\r\n            intensifyImage = this.m_config.image + GameIns.bulletManager.intensifyName;\r\n            GameIns.loadManager.setImage(this.skinImg, intensifyImage, 'mainBullet');\r\n            this.skinImg.node.setScale(GameIns.bulletManager.intensifyScale,GameIns.bulletManager.intensifyScale);\r\n        }\r\n\r\n        this.m_mainEntity = mainEntity;\r\n        this.node.setPosition(position.x, position.y);\r\n        this.skinImg.node.angle = 0;\r\n        this.m_lifeTime = this.m_config.retrieve;\r\n\r\n        if (this.m_lifeTime > 0) {\r\n            this.m_createTime = game.totalTime;\r\n        }\r\n\r\n        this.node.angle = isEnemy ? 180 - position.angle : -position.angle;\r\n        this.enemy = isEnemy;\r\n\r\n        if (!state.clone) {\r\n            state.through = this.m_config.disappear === 1;\r\n        }\r\n\r\n        this.bulletState = state;\r\n\r\n        // if (mainEntity && mainEntity instanceof ShadowPlane) {\r\n        //     opacity = mainEntity.bulletOpacity;\r\n        //     this.node.opacity = opacity;\r\n        // } else {\r\n        //     this.node.opacity = 255;\r\n        // }\r\n\r\n        // if (this.bulletState.through) {\r\n        //     this.addComp(OnceCollideComp, new OnceCollideComp());\r\n        // }\r\n\r\n        // 初始化子弹的所有组件\r\n        this.m_comps.forEach((comp) => {\r\n            comp.init(this);\r\n        });\r\n\r\n        // 设置子弹飞行组件\r\n        const bulletFlyComp = this.getComp(BulletFly);\r\n        if (bulletFlyComp) {\r\n            bulletFlyComp.setData(-position.angle, this.m_mainEntity, this.enemy);\r\n        }\r\n\r\n        // // 设置特殊飞行组件\r\n        // const flyThenTurnComp = this.getComp(FlyThenTurnComp);\r\n        // if (flyThenTurnComp) {\r\n        //     const [param0, param4, param5, param6] = this.m_config.para;\r\n        //     flyThenTurnComp.setData(-position.angle, this.m_mainEntity, param0, param4, param5, param6);\r\n        // }\r\n\r\n        // const circleFlyComp = this.getComp(CircleFly);\r\n        // if (circleFlyComp) {\r\n        //     circleFlyComp.setData({ x: position.x, y: position.y }, position.angle, this.m_mainEntity);\r\n        // }\r\n\r\n        // const circleFly1Comp = this.getComp(CircleFly1);\r\n        // if (circleFly1Comp) {\r\n        //     circleFly1Comp.setData({ x: position.x, y: position.y }, position.angle, this.m_mainEntity);\r\n        // }\r\n\r\n        // const bulletParabolicComp = this.getComp(BulletParabolic);\r\n        // if (bulletParabolicComp) {\r\n        //     bulletParabolicComp.setData(this.node.angle);\r\n        // }\r\n\r\n        // const batSkillFlyComp = this.getComp(BatSkillFly);\r\n        // if (batSkillFlyComp) {\r\n        //     const batIndex = MainPlaneManager.plane.skillBatDis[0]++ % MainPlaneManager.plane.skillTargetNdoes.length;\r\n        //     batSkillFlyComp.setData(MainPlaneManager.plane.skillTargetNdoes[batIndex]);\r\n        // }\r\n\r\n        // // 设置碰撞组件\r\n        const colliderComp = this.getComp(ColliderComp) as ColliderComp;\r\n        const shiftingBody = this.m_config.shiftingbody;\r\n        const bodyWidth = shiftingBody.length > 0 ? shiftingBody[0] : 0;\r\n        const bodyHeight = shiftingBody.length > 1 ? shiftingBody[1] : 0;\r\n\r\n        // if (this.m_config.bustyle === 50) {\r\n        //     colliderComp.setData([1, bodyWidth, bodyHeight, 300, 50]);\r\n        // } else {\r\n            colliderComp.setData([0, bodyWidth, bodyHeight, this.m_config.body, this.m_config.body]);\r\n        // }\r\n\r\n        // // 设置缩放组件\r\n        // const scaleComp = this.getComp(ScaleComp);\r\n        // if (scaleComp) {\r\n        //     scaleComp.setData(this.m_config, this.m_collideComp);\r\n        // }\r\n\r\n        // 设置子弹的发射效果\r\n        if (this.fire && this.enemyFire) {\r\n            const fireEffect = this.m_config.zdwy1;\r\n            if (fireEffect && fireEffect !== '') {\r\n                if (this.m_config.id < 1000) {\r\n                    this.fire.node.active = true;\r\n                    this.fire.node.opacity = 255;\r\n                    this.enemyFire.node.active = false;\r\n                    Tween.stopAllByTarget(this.fire.node);\r\n\r\n                    const fireTween = tween(this.fire.node)\r\n                        .to(UIAnimMethods.fromTo(0, 1), { scale:v3(1,1)})\r\n                        .to(UIAnimMethods.fromTo(1, 3), { scale:v3(0.447,0.56)})\r\n                        .to(UIAnimMethods.fromTo(3, 5), { scale:v3(1,1)});\r\n\r\n                    tween(this.fire.node).repeatForever(fireTween).start();\r\n                } else {\r\n                    this.fire.node.active = false;\r\n                    this.enemyFire.node.active = true;\r\n                    this.enemyFire.node.opacity = 255;\r\n                    Tween.stopAllByTarget(this.enemyFire.node);\r\n\r\n                    const enemyFireTween = tween(this.enemyFire.node)\r\n                        .to(UIAnimMethods.fromTo(0, 1), { scale:v3(0.6,0.6)})\r\n                        .to(UIAnimMethods.fromTo(1, 3), { scale:v3(0.3,0.35)})\r\n                        .to(UIAnimMethods.fromTo(3, 5), { scale:v3(0.6,0.6)});\r\n\r\n                    tween(this.enemyFire.node).repeatForever(enemyFireTween).start();\r\n                }\r\n            } else {\r\n                this.fire.node.active = false;\r\n                this.enemyFire.node.active = false;\r\n                Tween.stopAllByTarget(this.fire.node);\r\n            }\r\n        }\r\n\r\n        // // 设置子弹的拖尾效果\r\n        // const streakEffect = this.m_config.zdwy2;\r\n        // if (streakEffect && streakEffect !== '') {\r\n        //     const streakNode = GameIns.bulletManager.getStreak(streakEffect);\r\n        //     const streakComp = streakNode.getComponent(StreakComp);\r\n        //     streakComp.enabled = true;\r\n        //     GameIns.bulletManager.addStreak(streakNode);\r\n        //     this.streakCall = streakComp.destroySelf.bind(streakComp);\r\n        //     streakComp.setData(this.m_config.wycs[0] || 0.3, this.skinImg.node.width * this.node.scale, this.node);\r\n        // }\r\n\r\n        // 设置爆炸效果\r\n        if (this.burstFire) {\r\n            this.burstFire.node.active = this.m_config.bustyle === 38;\r\n        }\r\n    }\r\n    /**\r\n     * 获取子弹的基础攻击力\r\n     * @returns {number} 子弹的攻击力\r\n     */\r\n    _getAttack() {\r\n        const randomValue = Math.random();\r\n        const critChance = this.bulletState.cirt ? this.bulletState.cirt[0] : 0;\r\n        const atkChallenge = this.bulletState.atkChallenge || 0;\r\n        let attack = this.bulletState.attack;\r\n\r\n        // 特殊处理主机类型子弹\r\n        if (this.m_mainEntity instanceof MainPlane) {\r\n            const config = this.m_mainEntity.m_config;\r\n            if (config && config.type === 702) {\r\n                if ([27, 28, 29, 37].indexOf(this.getType()) != -1) {\r\n                    attack /= 5;\r\n                }\r\n            }\r\n        }\r\n\r\n        // 判断是否暴击\r\n        if (randomValue <= critChance) {\r\n            const critMultiplier = this.bulletState.cirt ? this.bulletState.cirt[1] : 1;\r\n            this.isCirt = true;\r\n            return (attack + atkChallenge) * critMultiplier;\r\n        } else {\r\n            this.isCirt = false;\r\n            return attack + atkChallenge;\r\n        }\r\n    }\r\n\r\n    /**\r\n     * 获取子弹对目标的实际攻击力\r\n     * @param {Entity} target 目标实体\r\n     * @returns {number} 实际攻击力\r\n     */\r\n    getAttack(target = null) {\r\n        let attack = this._getAttack();\r\n\r\n        // if (target) {\r\n        //     // 主机子弹的额外加成\r\n        //     if (this.m_mainEntity instanceof MainPlane) {\r\n        //         attack *= MainPlaneManager.getSkillAddAtkRatio();\r\n\r\n        //         // 血量低时的额外攻击力加成\r\n        //         if (MainPlaneManager.data.hp < MainPlaneManager.data.maxhp) {\r\n        //             attack += MainPlaneManager.attributePromote.get(Attribute.HurtAddAtk) || 0;\r\n        //         }\r\n\r\n        //         // 高血量时的额外攻击力加成\r\n        //         if (MainPlaneManager.skillMap.get(Attribute.MainHighHpAtk)) {\r\n        //             const skillData = Tools.stringToNumber(MainPlaneManager.skillMap.get(Attribute.MainHighHpAtk), ',');\r\n        //             if (MainPlaneManager.data.hp > MainPlaneManager.data.maxhp * skillData[1] / 100) {\r\n        //                 attack *= 1 + skillData[0] / 100;\r\n        //                 MainPlaneManager.checkSkill(Attribute.MainHighHpAtk);\r\n        //             }\r\n        //         }\r\n\r\n        //         // 针对 Boss 的额外攻击力加成\r\n        //         if (target instanceof BossHurt) {\r\n        //             const bossAtkBonus = MainPlaneManager.attributePromote.get(Attribute.BossAtk);\r\n        //             if (bossAtkBonus) {\r\n        //                 attack *= 1 + bossAtkBonus / 100;\r\n        //             }\r\n        //         }\r\n\r\n        //         // 针对炮塔的额外攻击力加成\r\n        //         if (target instanceof EnemyTurret) {\r\n        //             const turretAtkBonus = MainPlaneManager.attributePromote.get(Attribute.TurretAtk);\r\n        //             if (turretAtkBonus) {\r\n        //                 attack *= 1 + turretAtkBonus / 100;\r\n        //             }\r\n        //         }\r\n\r\n        //         // 低血量敌人的额外攻击力加成\r\n        //         if (MainPlaneManager.skillMap.get(Attribute.EnemyLowHpAtk)) {\r\n        //             const skillData = Tools.stringToNumber(MainPlaneManager.skillMap.get(Attribute.EnemyLowHpAtk), ',');\r\n        //             if ((target instanceof EnemyBase || target instanceof BossHurt) && target.getHpPercent() < skillData[1] / 100) {\r\n        //                 attack *= 1 + skillData[0] / 100;\r\n        //                 MainPlaneManager.checkSkill(Attribute.EnemyLowHpAtk);\r\n        //             }\r\n        //         }\r\n        //     }\r\n\r\n        //     // 炮塔技能的额外加成\r\n        //     if (this.m_mainEntity instanceof WinePlane) {\r\n        //         if (this.m_config.bustyle === 40) {\r\n        //             if (this.m_config.id >= 511 && this.m_config.id <= 516) {\r\n        //                 attack += WinePlaneManager.getEquipLvPromote(TurretSkill.Ballistic);\r\n        //             }\r\n        //         } else if (this.m_config.bustyle === 22) {\r\n        //             attack += WinePlaneManager.getEquipLvPromote(TurretSkill.Missile);\r\n        //         }\r\n        //     }\r\n        // }\r\n\r\n        // // 处理一次性碰撞组件的攻击力\r\n        // if (this.getComp(OnceCollideComp)) {\r\n        //     return this.getComp(OnceCollideComp).getAttack(target, attack);\r\n        // }\r\n\r\n        // // 处理弹射攻击力\r\n        // if (this._catapultCount > 0) {\r\n        //     attack *= this._catapultAtkRatio;\r\n        // }\r\n\r\n        return attack;\r\n    }\r\n\r\n    /**\r\n     * 获取子弹的类型\r\n     * @returns {number} 子弹的类型\r\n     */\r\n    getType() {\r\n        return this.m_config.bustyle;\r\n    }\r\n    /**\r\n     * 播放子弹命中音效\r\n     */\r\n    playHurtAudio() {\r\n        // if (this.m_config.hit.length > 0) {\r\n        //     Bullet.playAudio('hit2');\r\n        // }\r\n    }\r\n\r\n    /**\r\n     * 子弹碰撞处理\r\n     * @param {ColliderComp} target 碰撞目标\r\n     */\r\n    onCollide(target) {\r\n        // if (this.getComp(ResistBulletComp)) {\r\n        //     this.getComp(ResistBulletComp).onCollide(target);\r\n        // }\r\n\r\n        // if (this.getComp(OnceCollideComp)) {\r\n        //     if (this.getComp(OnceCollideComp).onCollide(target)) {\r\n        //         this.m_throughArr.push(target);\r\n\r\n        //         if (this.getType() === 48) {\r\n        //             HurtEffectManager.me.createDefaultEffect(target, this, this.m_config.exstyle1, this.m_config.exstyle2);\r\n        //         } else {\r\n        //             HurtEffectManager.me.createHurtEffect(target, this, this.m_config.exstyle1, this.m_config.exstyle2);\r\n\r\n        //             if (this.getType() === 26) {\r\n        //                 SpecialBulletManager.addBulletCollide(this, target.entity);\r\n        //             } else if (this.getType() === 59 && this.bulletState.through && this.m_throughArr.length === 1) {\r\n        //                 this._collideEntity = target.entity;\r\n        //                 this.checkCatapultForThrough();\r\n        //             }\r\n        //         }\r\n        //     }\r\n        // } else \r\n        if (this.m_config.exstyle1) {\r\n            // if (this.getType() === 26) {\r\n            //     HurtEffectManager.me.createHurtEffect(this.m_collideComp, this, this.m_config.exstyle1, this.m_config.exstyle2);\r\n            // } else {\r\n                GameIns.hurtEffectManager.createHurtEffect(target, this, this.m_config.exstyle1, this.m_config.exstyle2);\r\n            // }\r\n        }\r\n\r\n        // if (!this.bulletState.through) {\r\n        //     this._collideEntity = target.entity;\r\n        //     this.playHurtAudio();\r\n\r\n        //     switch (this.getType()) {\r\n        //         case 26:\r\n        //         case 29:\r\n        //         case 48:\r\n        //             break;\r\n        //         default:\r\n        //             this.remove(true);\r\n        //     }\r\n\r\n        //     if (this.getType() === 28) {\r\n        //         const radius = this.bulletState.extra[1];\r\n        //         EnemyManager.EnemyMgr.planes.forEach((enemy) => {\r\n        //             const bulletPos = this.m_collideComp.getScreenPos();\r\n        //             const enemyPos = enemy.getComp(ColliderComp).getScreenPos();\r\n        //             if (Tools.getPosDis(bulletPos, enemyPos) < radius) {\r\n        //                 HurtEffectManager.me.createHurtNumByType(\r\n        //                     enemy.getComp(ColliderComp),\r\n        //                     this,\r\n        //                     this.getAttack(enemy) * this.bulletState.extra[0],\r\n        //                     { x: 0, y: 0 }\r\n        //                 );\r\n        //                 enemy.hurt(this.getAttack(enemy) * this.bulletState.extra[0]);\r\n        //             }\r\n        //         });\r\n        //     }\r\n\r\n        //     if (this.getType() === 26) {\r\n        //         // GameIns.audioManager.playEffect('ele');\r\n        //     }\r\n        // }\r\n    }\r\n\r\n    /**\r\n     * 子弹超出屏幕处理\r\n     */\r\n    onOutScreen() {\r\n        if (this.m_lifeTime > 0) {\r\n            const currentTime = game.totalTime;\r\n            this.aliveTime = (currentTime - this.m_createTime) / 1000;\r\n        }\r\n\r\n        switch (this.getType()) {\r\n            case 26:\r\n            case 29:\r\n            case 48:\r\n            case 54:\r\n            case 55:\r\n                break;\r\n            default:\r\n                if (this.aliveTime >= this.m_lifeTime) {\r\n                    this.remove();\r\n                }\r\n        }\r\n    }\r\n\r\n    /**\r\n     * 移除子弹\r\n     * @param {boolean} force 是否强制移除\r\n     */\r\n    remove(force = false) {\r\n        if (!force || !this.checkCatapult()) {\r\n            this.willRemove();\r\n\r\n            if (this.collideDun) {\r\n                GameIns.hurtEffectManager.playBulletCollideDunAnim(this.node.position);\r\n            }\r\n\r\n            GameIns.bulletManager.removeBullet(this);\r\n\r\n            try {\r\n                if (this.streakCall) {\r\n                    this.streakCall();\r\n                    this.streakCall = null;\r\n                }\r\n            } catch (error) {\r\n                console.error('Error during streakCall:', error);\r\n            }\r\n\r\n            this.m_mainEntity = null;\r\n        }\r\n\r\n        if (this.getType() === 37) {\r\n            this.skinImg.node.stopAllActions();\r\n        }\r\n    }\r\n\r\n    /**\r\n     * 子弹死亡移除\r\n     */\r\n    dieRemove() {\r\n        this.willRemove();\r\n\r\n        try {\r\n            GameIns.hurtEffectManager.playBulletDieAnim(this.node.position);\r\n        } catch (error) {\r\n            console.error('Error during dieRemove:', error);\r\n        }\r\n\r\n        GameIns.bulletManager.removeBullet(this, false);\r\n\r\n        try {\r\n            if (this.streakCall) {\r\n                this.streakCall();\r\n                this.streakCall = null;\r\n            }\r\n        } catch (error) {\r\n            console.error('Error during streakCall:', error);\r\n        }\r\n\r\n        this.m_mainEntity = null;\r\n    }\r\n\r\n    /**\r\n     * 子弹移除前的清理操作\r\n     */\r\n    willRemove() {\r\n        if (this.m_collideComp) {\r\n            this.m_collideComp.enabled = false;\r\n        }\r\n\r\n        if (this.skinImg) {\r\n            this.skinImg.spriteFrame = null;\r\n        }\r\n\r\n        if (this.fire && this.fire.node.active) {\r\n            this.fire.node.opacity = 0;\r\n            this.fire.node.stopAllActions();\r\n        }\r\n\r\n        if (this.enemyFire && this.enemyFire.node.active) {\r\n            this.enemyFire.node.opacity = 0;\r\n            this.enemyFire.node.stopAllActions();\r\n        }\r\n\r\n        this.m_throughArr.splice(0);\r\n    }\r\n//     /**\r\n//      * 检查子弹是否可以弹射\r\n//      * @returns {boolean} 是否成功弹射\r\n//      */\r\n    checkCatapult() {\r\n        let isCatapulted = false;\r\n\r\n//         if (\r\n//             this.bulletState.catapult &&\r\n//             this.bulletState.catapult.length > 0 &&\r\n//             this._catapultCount < this.bulletState.catapult[0]\r\n//         ) {\r\n//             this._catapultTargets.push(this._collideEntity);\r\n\r\n//             const rangeEnemies = GameFunc.GFunc.getRangeEnemyParam(\r\n//                 this._collideEntity,\r\n//                 this.node.position,\r\n//                 this.bulletState.catapult[1]\r\n//             );\r\n\r\n//             for (let i = 0; i < rangeEnemies.length; i++) {\r\n//                 const enemyData = rangeEnemies[i];\r\n//                 if (\r\n//                     enemyData.enemy &&\r\n//                     !enemyData.enemy.isDead &&\r\n//                     !Tools.arrContain(this._catapultTargets, enemyData.enemy)\r\n//                 ) {\r\n//                     this.node.angle = enemyData.angle;\r\n\r\n//                     const bulletFlyComp = this.getComp(BulletFly);\r\n//                     if (bulletFlyComp) {\r\n//                         bulletFlyComp.setData(enemyData.angle, this.m_mainEntity, this.enemy);\r\n//                     }\r\n\r\n//                     if (this.m_collideComp) {\r\n//                         const newPos = this.m_collideComp.initPos.rotate(\r\n//                             misc.degreesToRadians(enemyData.angle)\r\n//                         );\r\n//                         this.m_collideComp.setPos(newPos.x, newPos.y);\r\n//                     }\r\n\r\n//                     this._catapultCount++;\r\n//                     this._catapultAtkRatio *= this.bulletState.catapult[2];\r\n//                     isCatapulted = true;\r\n//                     break;\r\n//                 }\r\n//             }\r\n//         }\r\n\r\n        return isCatapulted;\r\n    }\r\n\r\n//     /**\r\n//      * 检查穿透子弹是否可以弹射\r\n//      * @returns {boolean} 是否成功弹射\r\n//      */\r\n//     checkCatapultForThrough() {\r\n//         if (\r\n//             this.bulletState.catapult &&\r\n//             this.bulletState.catapult.length > 0 &&\r\n//             this._catapultCount < this.bulletState.catapult[0]\r\n//         ) {\r\n//             const newBullet = GameIns.bulletManager.getBullet(this.bulletID, this.enemy);\r\n//             if (newBullet) {\r\n//                 BattleLayer.me.addBullet(newBullet);\r\n\r\n//                 const newState = {\r\n//                     attack: this.bulletState.attack,\r\n//                     extra: this.bulletState.extra,\r\n//                     cirt: this.bulletState.cirt,\r\n//                     fireIntensify: this.bulletState.fireIntensify,\r\n//                     catapult: this.bulletState.catapult,\r\n//                     atkChallenge: this.bulletState.atkChallenge,\r\n//                     clone: true,\r\n//                 };\r\n\r\n//                 newBullet.init(\r\n//                     this.enemy,\r\n//                     {\r\n//                         x: this.node.x,\r\n//                         y: this.node.y,\r\n//                         angle: 180 - this.node.angle,\r\n//                     },\r\n//                     newState,\r\n//                     this.m_mainEntity\r\n//                 );\r\n\r\n//                 newBullet.onCatapultForThrough(this._collideEntity);\r\n//             }\r\n//         }\r\n\r\n//         return false;\r\n//     }\r\n\r\n//     /**\r\n//      * 处理穿透子弹的弹射逻辑\r\n//      * @param {Entity} target 碰撞目标\r\n//      * @returns {boolean} 是否成功弹射\r\n//      */\r\n//     onCatapultForThrough(target) {\r\n//         this._collideEntity = target;\r\n//         const isCatapulted = this.checkCatapult();\r\n//         if (!isCatapulted) {\r\n//             this.remove();\r\n//         }\r\n//         return isCatapulted;\r\n//     }\r\n\r\n//     /**\r\n//      * 设置碰撞组件的启用状态\r\n//      * @param {boolean} enabled 是否启用\r\n//      */\r\n    setColliderEnable(enabled) {\r\n        if (this.m_collideComp) {\r\n            this.m_collideComp.enabled = enabled;\r\n        }\r\n    }\r\n\r\n    /**\r\n     * 更新子弹逻辑\r\n     * @param {number} deltaTime 帧间隔时间\r\n     */\r\n    update(deltaTime) {\r\n        if (!GameConst.GameAble) {\r\n            return;\r\n        }\r\n\r\n        this.m_comps.forEach((comp) => {\r\n            comp.update(deltaTime);\r\n        });\r\n\r\n        // if (\r\n        //     (MainPlaneManager.isShow || GameIns.bulletManager.fireShellUINode) &&\r\n        //     this.node.y > 1000\r\n        // ) {\r\n        //     this.remove();\r\n        // }\r\n\r\n        if (this.m_config.bustyle === 55 && this.node.y < -2000) {\r\n            this.remove();\r\n        }\r\n    }\r\n\r\n    /**\r\n     * 更新子弹的组件逻辑\r\n     * @param {number} deltaTime 帧间隔时间\r\n     */\r\n    updateComp(deltaTime) {\r\n        this.m_comps.forEach((comp) => {\r\n            if (comp.updateComp) {\r\n                comp.updateComp(deltaTime);\r\n            }\r\n        });\r\n    }\r\n\r\n    /**\r\n     * 刷新子弹状态\r\n     */\r\n    refresh() {\r\n        this.collideDun = false;\r\n        this._catapultCount = 0;\r\n        this._catapultAtkRatio = 1;\r\n        this._collideEntity = null;\r\n        this._catapultTargets.splice(0);\r\n    }\r\n\r\n//     /**\r\n//      * 检查子弹是否可以与目标碰撞\r\n//      * @param {Entity} target 碰撞目标\r\n//      * @returns {boolean} 是否可以碰撞\r\n//      */\r\n//     canCollideEntity(target) {\r\n//         return (\r\n//             this._catapultTargets.length === 0 ||\r\n//             this._catapultTargets.indexOf(target) < 0\r\n//         );\r\n//     }\r\n\r\n//     /**\r\n//      * 处理蝙蝠技能的逻辑\r\n//      */\r\n//     batSkill() {\r\n//         if (this.m_config.exstyle1) {\r\n//             const randomScale = Tools.random_int(18, 30) / 100;\r\n//             HurtEffectManager.me.createHurtEffect(\r\n//                 this.m_collideComp,\r\n//                 this,\r\n//                 this.m_config.exstyle1,\r\n//                 randomScale\r\n//             );\r\n//             this.remove();\r\n//         }\r\n//     }\r\n//     /**\r\n//      * 设置 Boss 拳头子弹的皮肤\r\n//      */\r\n//     setBossBoxingFistSkin() {\r\n//         this.skinImg.node.opacity = 0;\r\n//         const parent = this.skinImg.node.parent;\r\n\r\n//         // 创建拳头节点\r\n//         const fistNode = new Node(\"fist\");\r\n//         fistNode.parent = parent;\r\n\r\n//         // 创建尾部节点\r\n//         const tailNode = new Node(\"tail\");\r\n//         tailNode.parent = fistNode;\r\n//         tailNode.anchorY = 0.05;\r\n//         tailNode.y = 34;\r\n//         GameFunc.setImage(tailNode.addComponent(Sprite), \"fist_zd_51_0\", GameIns.bulletManager.enemyBulletAtlas);\r\n\r\n//         // 创建底部节点\r\n//         const bottomNode = new Node(\"6\");\r\n//         bottomNode.parent = fistNode;\r\n//         bottomNode.y = -35;\r\n//         GameFunc.setImage(bottomNode.addComponent(Sprite), \"fist_zd_51_6\", GameIns.bulletManager.enemyBulletAtlas);\r\n\r\n//         // 创建左上节点\r\n//         const leftTopNode = new Node(\"nbl\");\r\n//         leftTopNode.parent = fistNode;\r\n//         leftTopNode.setAnchorPoint(1, 1);\r\n//         leftTopNode.y = 90;\r\n//         GameFunc.setImage(leftTopNode.addComponent(Sprite), \"fist_zd_51_4\", GameIns.bulletManager.enemyBulletAtlas);\r\n\r\n//         // 创建右上节点\r\n//         const rightTopNode = new Node(\"nbr\");\r\n//         rightTopNode.parent = fistNode;\r\n//         rightTopNode.setAnchorPoint(1, 1);\r\n//         rightTopNode.y = 90;\r\n//         rightTopNode.scaleX = -1;\r\n//         GameFunc.setImage(rightTopNode.addComponent(Sprite), \"fist_zd_51_4\", GameIns.bulletManager.enemyBulletAtlas);\r\n\r\n//         // 创建左下节点\r\n//         const leftBottomNode = new Node(\"nrl\");\r\n//         leftBottomNode.parent = fistNode;\r\n//         leftBottomNode.setAnchorPoint(0.73, 0.97);\r\n//         leftBottomNode.x = -23;\r\n//         leftBottomNode.y = -3;\r\n//         leftBottomNode.scaleX = -1;\r\n//         GameFunc.setImage(leftBottomNode.addComponent(Sprite), \"fist_zd_51_3\", GameIns.bulletManager.enemyBulletAtlas);\r\n\r\n//         // 创建右下节点\r\n//         const rightBottomNode = new Node(\"nrr\");\r\n//         rightBottomNode.parent = fistNode;\r\n//         rightBottomNode.setAnchorPoint(0.73, 0.97);\r\n//         rightBottomNode.x = 23;\r\n//         rightBottomNode.y = -3;\r\n//         GameFunc.setImage(rightBottomNode.addComponent(Sprite), \"fist_zd_51_3\", GameIns.bulletManager.enemyBulletAtlas);\r\n\r\n//         // 创建主体节点\r\n//         const bodyNode = new Node(\"body\");\r\n//         bodyNode.parent = fistNode;\r\n//         GameFunc.setImage(bodyNode.addComponent(Sprite), \"fist_zd_51_1\", GameIns.bulletManager.enemyBulletAtlas);\r\n\r\n//         // 动画时间\r\n//         const frameTime = GameConfig.ActionFrameTime;\r\n\r\n//         // 左下节点动画\r\n//         const leftBottomX = leftBottomNode.x;\r\n//         const leftBottomY = leftBottomNode.y;\r\n//         tween(leftBottomNode)\r\n//             .to(5 * frameTime, { angle: 0.8, x: leftBottomX + 4, y: leftBottomY + 25.6 })\r\n//             .to(3 * frameTime, { angle: -33.8, x: leftBottomX + 19.4, y: leftBottomY + 57.8, scaleX: -1.2, scaleY: 1.2 })\r\n//             .call(() => {\r\n//                 leftBottomNode.scaleX = -1;\r\n//                 leftBottomNode.scaleY = 1;\r\n//             })\r\n//             .start();\r\n\r\n//         // 右下节点动画\r\n//         const rightBottomX = rightBottomNode.x;\r\n//         const rightBottomY = rightBottomNode.y;\r\n//         tween(rightBottomNode)\r\n//             .to(5 * frameTime, { angle: -0.8, x: rightBottomX - 4, y: rightBottomY + 25.6 })\r\n//             .to(3 * frameTime, { angle: 33.8, x: rightBottomX - 19.4, y: rightBottomY + 57.8, scale: 1.2 })\r\n//             .call(() => {\r\n//                 rightBottomnode.setScale(1;\r\n//             })\r\n//             .start();\r\n\r\n//         // 主体节点动画\r\n//         const bodyY = bodyNode.y;\r\n//         tween(bodyNode)\r\n//             .to(5 * frameTime, { y: bodyY + 21.5 })\r\n//             .start();\r\n\r\n//         // 左上节点动画\r\n//         const leftTopY = leftTopNode.y;\r\n//         tween(leftTopNode)\r\n//             .to(5 * frameTime, { angle: 12.85, y: leftTopY - 4 })\r\n//             .start();\r\n\r\n//         // 右上节点动画\r\n//         const rightTopY = rightTopNode.y;\r\n//         tween(rightTopNode)\r\n//             .to(5 * frameTime, { angle: -12.85, y: rightTopY - 4 })\r\n//             .start();\r\n\r\n//         // 底部节点动画\r\n//         const bottomY = bottomNode.y;\r\n//         tween(bottomNode)\r\n//             .delay(10 * frameTime)\r\n//             .to(3 * frameTime, { scaleX: 1, scaleY: 1.3, y: bottomY - 26.3 })\r\n//             .start();\r\n\r\n//         // 尾部节点动画\r\n//         tailNode.scaleX = 0.8;\r\n//         tailNode.scaleY = 0.5;\r\n//         tailNode.opacity = 0;\r\n//         tween(tailNode)\r\n//             .to(5 * frameTime, { scaleX: 0.5, scaleY: 0.7, opacity: 255 })\r\n//             .to(20 * frameTime, { scaleX: 0.7, scaleY: 1.2 })\r\n//             .start();\r\n//     }\r\n\r\n//     /**\r\n//      * 设置 Boss 加特林子弹的皮肤\r\n//      */\r\n//     setBossBoxingGatlinSkin() {\r\n//         this.skinImg.node.opacity = 0;\r\n//         const parent = this.skinImg.node.parent;\r\n\r\n//         // 创建加特林节点\r\n//         const gatlinNode = new Node(\"gatlin\");\r\n//         gatlinNode.parent = parent;\r\n\r\n//         // 创建尾部节点\r\n//         const tailNode = new Node(\"tail\");\r\n//         tailNode.parent = gatlinNode;\r\n//         tailNode.anchorY = 0.04;\r\n//         tailNode.y = -50;\r\n//         GameFunc.setImage(tailNode.addComponent(Sprite), \"fist_zd_52_0\", GameIns.bulletManager.enemyBulletAtlas);\r\n\r\n//         // 创建子弹节点\r\n//         const bulletNode = new Node(\"bullet\");\r\n//         bulletNode.parent = gatlinNode;\r\n//         GameFunc.setImage(bulletNode.addComponent(Sprite), \"fist_zd_52_1\", GameIns.bulletManager.enemyBulletAtlas);\r\n\r\n//         // 创建顶部节点\r\n//         const topNode = new Node(\"top\");\r\n//         topNode.parent = gatlinNode;\r\n//         GameFunc.setImage(topNode.addComponent(Sprite), \"fist_zd_52_2\", GameIns.bulletManager.enemyBulletAtlas);\r\n//         topNode.getComponent(Sprite).dstBlendFactor = macro.ONE;\r\n\r\n//         // 动画时间\r\n//         const frameTime = GameConfig.ActionFrameTime;\r\n\r\n//         // 顶部节点动画\r\n//         tween(topNode)\r\n//             .to(3 * frameTime, { scaleX: 1.25, scaleY: 0.68, y: -20 })\r\n//             .to(5 * frameTime, { scaleX: 1, scaleY: 1 })\r\n//             .start();\r\n\r\n//         // 子弹节点动画\r\n//         tween(bulletNode)\r\n//             .to(3 * frameTime, { scale: 1.15 })\r\n//             .to(5 * frameTime, { scaleX: 1, scaleY: 1 })\r\n//             .start();\r\n\r\n//         // 尾部节点动画\r\n//         tween(tailNode)\r\n//             .to(2 * frameTime, { scale: 0.85 })\r\n//             .to(2 * frameTime, { scale: 1 })\r\n//             .to(2 * frameTime, { scale: 0.85 })\r\n//             .to(2 * frameTime, { scale: 1 })\r\n//             .start();\r\n//     }\r\n//     /**\r\n//      * 为子弹添加尾部效果\r\n//      */\r\n//     addTail() {\r\n//         const tailNode = new Node(\"tail\");\r\n//         GameFunc.setImage(\r\n//             tailNode.addComponent(Sprite),\r\n//             \"gatlin_missle_tail\",\r\n//             GameIns.bulletManager.enemyComAtlas\r\n//         );\r\n//         this.skinImg.node.parent.addChild(tailNode);\r\n//         tailnode.setScale(4;\r\n//         tailNode.anchorY = 0;\r\n//     }\r\n\r\n\r\n    /**\r\n     * 子弹音效的最大播放数量\r\n     */\r\n    static audioMaxNum = {\r\n        hit1: 5,\r\n        hit2: 3,\r\n        hit3: 2,\r\n        hit4: 5,\r\n        hit5: 5,\r\n        hit6: 5,\r\n        swordHit: 5,\r\n    };\r\n\r\n//     /**\r\n//      * 子弹音效的播放状态\r\n//      */\r\n//     static audios = new Map();\r\n\r\n//     /**\r\n//      * 播放子弹音效\r\n//      * @param {string} audioName 音效名称\r\n//      */\r\n//     static playAudio(audioName) {\r\n//         const maxNum = Bullet.audioMaxNum[audioName];\r\n//         let currentNum = Bullet.audios.get(audioName) || 0;\r\n\r\n//         if (currentNum < maxNum) {\r\n//             Bullet.audios.set(audioName, ++currentNum);\r\n//             // const audioId = GameIns.audioManager.playEffectSync(audioName);\r\n//             // audioEngine.setFinishCallback(audioId, () => {\r\n//             //     Bullet.onAudioFinish(audioName);\r\n//             // });\r\n//         }\r\n//     }\r\n\r\n//     /**\r\n//      * 子弹音效播放完成的回调\r\n//      * @param {string} audioName 音效名称\r\n//      */\r\n//     static onAudioFinish(audioName) {\r\n//         const currentNum = Bullet.audios.get(audioName);\r\n//         Bullet.audios.set(audioName, Math.max(0, currentNum - 1));\r\n//     }\r\n}"]}