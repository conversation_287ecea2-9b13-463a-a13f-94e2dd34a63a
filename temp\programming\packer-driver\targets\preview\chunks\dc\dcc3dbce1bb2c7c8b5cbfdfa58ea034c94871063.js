System.register(["cc"], function (_export, _context) {
  "use strict";

  var _cclegacy, __checkObsolete__, __checkObsoleteInNamespace__, _decorator, Component, _dec, _class, _crd, ccclass, property, EnemyAttrBaseCom;

  return {
    setters: [function (_cc) {
      _cclegacy = _cc.cclegacy;
      __checkObsolete__ = _cc.__checkObsolete__;
      __checkObsoleteInNamespace__ = _cc.__checkObsoleteInNamespace__;
      _decorator = _cc._decorator;
      Component = _cc.Component;
    }],
    execute: function () {
      _crd = true;

      _cclegacy._RF.push({}, "572bdZ0lr5ObJvgMcSCWp2y", "EnemyAttrBaseCom", undefined);

      __checkObsolete__(['_decorator', 'Component']);

      ({
        ccclass,
        property
      } = _decorator);

      _export("default", EnemyAttrBaseCom = (_dec = ccclass('EnemyAttrBaseCom'), _dec(_class = class EnemyAttrBaseCom extends Component {
        constructor() {
          super(...arguments);
          this.enemyEntity = null;
          // 敌人实体
          this.attrData = null;
          // 属性数据
          this.attrMgr = null;
        }

        // 属性管理器

        /**
         * 更新游戏逻辑
         * @param {number} deltaTime 帧间隔时间
         */
        updateGameLogic(deltaTime) {// 子类实现具体逻辑
        }
        /**
         * 处理敌人死亡逻辑
         */


        die() {// 子类实现具体逻辑
        }

      }) || _class));

      _cclegacy._RF.pop();

      _crd = false;
    }
  };
});
//# sourceMappingURL=dcc3dbce1bb2c7c8b5cbfdfa58ea034c94871063.js.map