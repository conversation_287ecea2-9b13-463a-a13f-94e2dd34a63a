{"version": 3, "sources": ["file:///E:/M2Game/Client/assets/scripts/Game/ui/bullet/LJLaserBullet.ts"], "names": [], "mappings": "AAAA;AAEA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AAEA;AACA;AACA;AACA;AAEA;AACA;AACA;AAEA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AAEA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AAEA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AAEA;AAEA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AAEA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "sourcesContent": ["// const { ccclass, property } = cc._decorator;\r\n\r\n// enum LaserState {\r\n//     idle = 0,\r\n//     launch = 1,\r\n//     fire = 2,\r\n//     remove = 3,\r\n// }\r\n\r\n// @ccclass\r\n// export default class LJLaserBullet extends LaserBullet {\r\n//     private m_target: any = null;\r\n//     private m_targetUuid: number = 0;\r\n//     private attacked: boolean = false;\r\n//     private sceneEntity: any = null;\r\n//     private lifePos: cc.Vec2 = cc.Vec2.ZERO;\r\n//     private time: number = 0;\r\n//     private m_state: LaserState = LaserState.idle;\r\n//     private m_hurtEffect: cc.Node = null;\r\n\r\n//     /**\r\n//      * 初始化激光子弹\r\n//      * @param enemy 敌人\r\n//      * @param position 子弹初始位置\r\n//      * @param bulletState 子弹状态\r\n//      * @param mainEntity 主实体\r\n//      */\r\n//     init(enemy: any, position: cc.Vec2, bulletState: any, mainEntity: any): void {\r\n//         this.node.x = position.x;\r\n//         this.node.y = position.y;\r\n//         this.enemy = enemy;\r\n//         this.bulletState = bulletState;\r\n//         this.attacked = false;\r\n//         this.laserImg.node.parent.x = 0;\r\n//         this.m_state = LaserState.idle;\r\n\r\n//         this.m_comps.forEach((comp) => {\r\n//             comp.init(this);\r\n//         });\r\n\r\n//         this.playHurt = false;\r\n//         this.node.angle = 180;\r\n//         this.launchImg.node.angle = 180;\r\n//         this.laserImg.node.parent.scaleX = this.props.scale;\r\n\r\n//         const sceneEntity = SceneManager.me.getSceneEntity(mainEntity);\r\n//         this.m_flyComp.setTarget(sceneEntity, mainEntity, { x: 0, y: 0 }, true);\r\n//         this.sceneEntity = sceneEntity;\r\n\r\n//         this.m_hurtEffect = cc.instantiate(GameConst.LJLaserPoint);\r\n//         this.launchImg.setSpeed(15);\r\n\r\n//         this.launch();\r\n//         this.scheduleOnce(() => {\r\n//             this.fireLaser();\r\n//             this.time = 0;\r\n//         }, this.props.launchTime);\r\n//     }\r\n\r\n//     /**\r\n//      * 激光发射阶段\r\n//      */\r\n//     launch(): void {\r\n//         if (this.sceneEntity instanceof WinePlane) {\r\n//             this.sceneEntity.updateAngle();\r\n//         }\r\n//         this.m_state = LaserState.launch;\r\n//         this.m_collideComp.enabled = false;\r\n//         this.launchImg.play(0);\r\n//     }\r\n\r\n//     /**\r\n//      * 设置目标\r\n//      * @param target 目标实体\r\n//      * @param targetCollider 目标碰撞器\r\n//      */\r\n//     setTarget(target: any, targetCollider: any): void {\r\n//         this.m_target = target;\r\n//         this.m_targetCollider = targetCollider;\r\n\r\n//         if (target) {\r\n//             this.m_targetUuid = target.new_uuid;\r\n//             if (!this.m_hurtEffect) {\r\n//                 this.m_hurtEffect = cc.instantiate(GameConst.LJLaserPoint);\r\n//             }\r\n//             this.node.opacity = 255;\r\n//         }\r\n//     }\r\n\r\n//     /**\r\n//      * 播放激光发射音效\r\n//      */\r\n//     async playFireAudio(): Promise<void> {\r\n//         await frameWork.audioManager.playEffect(\"pt_jg\");\r\n//     }\r\n\r\n//     /**\r\n//      * 激光发射逻辑\r\n//      */\r\n//     fireLaser(): void {\r\n//         if (this.m_state === LaserState.remove) {\r\n//             return;\r\n//         }\r\n\r\n//         if (this.sceneEntity instanceof WinePlane) {\r\n//             this.sceneEntity.updateAngle();\r\n//             const angle = this.sceneEntity.getFireBulletAngle();\r\n//             const topY = this.sceneEntity.top.y;\r\n//             const topX = this.sceneEntity.top.x;\r\n\r\n//             cc.tween(this.sceneEntity.top)\r\n//                 .to(UIAnimMethods.fromTo(1, 4), {\r\n//                     y: topY - 24 * Math.cos((angle / 180) * Math.PI),\r\n//                     x: topX + 24 * Math.sin((angle / 180) * Math.PI),\r\n//                 })\r\n//                 .to(UIAnimMethods.fromTo(4, 9), {\r\n//                     y: topY + 2 * Math.cos((angle / 180) * Math.PI),\r\n//                     x: topX - 2 * Math.sin((angle / 180) * Math.PI),\r\n//                 })\r\n//                 .to(UIAnimMethods.fromTo(9, 12), {\r\n//                     y: topY,\r\n//                     x: topX,\r\n//                 })\r\n//                 .call(this.sceneEntity.top.stopAllActions.bind(this.sceneEntity.top))\r\n//                 .start();\r\n//         }\r\n\r\n//         this.playFireAudio();\r\n//         this.m_state = LaserState.fire;\r\n//         this.laserImg.node.parent.height = 0;\r\n//         this.laserImg.node.parent.scaleX = this.props.scale;\r\n\r\n//         cc.tween(this.laserImg.node.parent)\r\n//             .delay(this.props.laserTime)\r\n//             .to(0.2, { scaleX: 0 })\r\n//             .start();\r\n\r\n//         this.laserImg.play(0);\r\n//         this.playHurtEffect();\r\n\r\n//         this.scheduleOnce(() => {\r\n//             this.m_collideComp.enabled = true;\r\n//             if (this.m_state === LaserState.fire) {\r\n//                 this.remove();\r\n//             }\r\n//         }, this.props.laserTime + 0.2);\r\n//     }\r\n\r\n//     /**\r\n//      * 播放受击效果\r\n//      */\r\n//     playHurtEffect(): void {\r\n//         if (this.m_hurtEffect) {\r\n//             this.m_hurtEffect.parent = EnemyEffectLayer.me.hurtEffectLayer;\r\n//             this.m_hurtEffect.opacity = 0;\r\n//         }\r\n//     }\r\n\r\n//     /**\r\n//      * 每帧更新逻辑\r\n//      * @param deltaTime 时间增量\r\n//      */\r\n//     update(deltaTime: number): void {\r\n//         if (!GameConst.GameAble || this.m_state === LaserState.remove) {\r\n//             return;\r\n//         }\r\n\r\n//         // 限制 deltaTime 的最大值\r\n//         if (deltaTime > 0.2) {\r\n//             deltaTime = 0.016666666666667; // 约等于 1/60 秒\r\n//         }\r\n\r\n//         // 如果目标碰撞器为空且处于发射阶段\r\n//         if (this.m_targetCollider == null && this.m_state === LaserState.launch) {\r\n//             if (this.sceneEntity instanceof WinePlane) {\r\n//                 this.sceneEntity.stopActions();\r\n//                 this.launchImg.node.angle = 180 - this.sceneEntity.angleChange(this.lifePos);\r\n//             }\r\n//             this.remove();\r\n//             return;\r\n//         }\r\n\r\n//         let targetPos: cc.Vec2;\r\n//         let targetHeight = 0;\r\n\r\n//         // 如果目标碰撞器存在\r\n//         if (this.m_targetCollider != null) {\r\n//             targetPos = this.m_targetCollider.getScreenPos();\r\n//             this.lifePos.x = targetPos.x;\r\n//             this.lifePos.y = targetPos.y;\r\n\r\n//             // 检查目标是否有效\r\n//             if (\r\n//                 this.m_target == null ||\r\n//                 this.m_target.new_uuid !== this.m_targetUuid ||\r\n//                 this.m_target.node == null ||\r\n//                 this.m_target.node.parent == null ||\r\n//                 !this.m_target.node.active ||\r\n//                 !this.m_targetCollider.enabled ||\r\n//                 targetPos.x < -360 ||\r\n//                 targetPos.x > 360 ||\r\n//                 targetPos.y > 0 ||\r\n//                 targetPos.y < 300 - GameConst.ViewHeight\r\n//             ) {\r\n//                 if (this.sceneEntity instanceof WinePlane) {\r\n//                     this.launchImg.node.angle = 180 - this.sceneEntity.angleChange(this.lifePos);\r\n//                 }\r\n//             } else {\r\n//                 targetHeight = this.m_targetCollider.data.height / 2;\r\n//                 if (this.sceneEntity instanceof WinePlane) {\r\n//                     this.launchImg.node.angle = 180 - this.sceneEntity.angleChange();\r\n//                 }\r\n//             }\r\n//         } else {\r\n//             targetPos = this.lifePos || cc.Vec2.ZERO;\r\n//             if (this.sceneEntity instanceof WinePlane) {\r\n//                 this.launchImg.node.angle = 180 - this.sceneEntity.angleChange(this.lifePos);\r\n//             }\r\n//         }\r\n\r\n//         // 更新组件\r\n//         this.m_comps.forEach((comp) => {\r\n//             comp.update(deltaTime);\r\n//         });\r\n\r\n//         this.node.opacity = 255;\r\n\r\n//         // 更新激光位置和角度\r\n//         const currentPos = this.node.position;\r\n//         this.m_hurtEffect.x = targetPos.x;\r\n//         this.m_hurtEffect.y = targetPos.y - targetHeight;\r\n//         this.m_hurtEffect.opacity = 255;\r\n\r\n//         if (\r\n//             this.m_targetCollider != null &&\r\n//             \"onCollide\" in this.m_targetCollider.entity &&\r\n//             this.m_state === LaserState.fire &&\r\n//             !this.attacked\r\n//         ) {\r\n//             this.m_targetCollider.entity.onCollide(this.m_collideComp);\r\n//         }\r\n\r\n//         const distance = this.m_target == null ||\r\n//             this.m_target.new_uuid !== this.m_targetUuid ||\r\n//             this.m_target.node == null ||\r\n//             this.m_target.node.parent == null ||\r\n//             !this.m_target.node.active ||\r\n//             !this.m_targetCollider.enabled\r\n//             ? 2000\r\n//             : Tools.DYTools.getPosDis(currentPos, targetPos);\r\n\r\n//         const angle = Tools.DYTools.getAngle(currentPos, targetPos);\r\n//         const progress = Math.min(1, 5 * this.time);\r\n\r\n//         this.laserImg.node.parent.height = cc.misc.lerp(0, distance, progress);\r\n//         this.time += deltaTime;\r\n//         this.laserImg.node.parent.angle = -angle;\r\n//         this.fireImg.node.angle = -angle;\r\n\r\n//         if (this.sceneEntity instanceof WinePlane) {\r\n//             this.launchImg.node.angle = 180 - this.sceneEntity.angleChange(targetPos);\r\n//         }\r\n//     }\r\n\r\n//     /**\r\n//      * 移除激光子弹\r\n//      */\r\n//     remove(): void {\r\n//         this.m_state = LaserState.remove;\r\n//         this.launchImg.clear();\r\n//         this.fireImg.clear();\r\n//         this.m_collideComp.enabled = false;\r\n//         this.toremove();\r\n//     }\r\n\r\n//     /**\r\n//      * 清理逻辑\r\n//      */\r\n//     toremove(): void {\r\n//         if (this.sceneEntity instanceof WinePlane) {\r\n//             this.sceneEntity.setAngle0();\r\n//         }\r\n//         this.m_targets = [];\r\n//         this.launchImg.clear();\r\n//         this.fireImg.clear();\r\n//         this.laserImg.clear();\r\n//         this.m_collideComp && (this.m_collideComp.enabled = false);\r\n//         BulletManager.me.removeBullet(this);\r\n//         if (this.m_hurtEffect) {\r\n//             this.m_hurtEffect.opacity = 0;\r\n//         }\r\n//     }\r\n\r\n//     /**\r\n//      * 销毁逻辑\r\n//      */\r\n//     willDestroy(): void {\r\n//         this.m_targets = [];\r\n//         this.launchImg.clear();\r\n//         this.fireImg.clear();\r\n//         this.laserImg.clear();\r\n//         this.m_collideComp && (this.m_collideComp.enabled = false);\r\n//         if (this.m_hurtEffect) {\r\n//             this.m_hurtEffect.opacity = 0;\r\n//         }\r\n//     }\r\n// }"]}