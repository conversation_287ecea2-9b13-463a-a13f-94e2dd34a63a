{"version": 3, "sources": ["file:///E:/M2Game/Client/assets/scripts/GameOld/factroy/GoodsFactory.ts"], "names": ["GoodsFactory", "_decorator", "instantiate", "Global", "Goods", "GamePersistNode", "GameFactory", "ccclass", "property", "createProduct", "productType", "goodsTemp", "productPool", "size", "get", "persistNode", "getComponent", "goodsPreb", "BLOOD_GOODS", "init", "bloodGoods", "LIGHT_GOODS", "lightGoods", "MISSILE_GOODS", "missileGoods"], "mappings": ";;;qJAOaA,Y;;;;;;;;;;;;;;;;;;;;;;;;;;;AAPJC,MAAAA,U,OAAAA,U;AAA6BC,MAAAA,W,OAAAA,W;;AAC7BC,MAAAA,M,iBAAAA,M;;AACAC,MAAAA,K,iBAAAA,K;;AACAC,MAAAA,e,iBAAAA,e;;AACAC,MAAAA,W,iBAAAA,W;;;;;;;;;OACH;AAAEC,QAAAA,OAAF;AAAWC,QAAAA;AAAX,O,GAAwBP,U;;8BAEjBD,Y,GAAN,MAAMA,YAAN;AAAA;AAAA,sCAAuC;AAGnCS,QAAAA,aAAa,CAACC,WAAD,EAA4B;AAC5C,cAAIC,SAAe,GAAG,IAAtB;;AAEA,cAAG,KAAKC,WAAL,CAAiBC,IAAjB,KAA0B,CAA7B,EAAgC;AAC5BF,YAAAA,SAAS,GAAG,KAAKC,WAAL,CAAiBE,GAAjB,EAAZ,CAD4B,CACS;AACxC,WAFD,MAEO;AACHH,YAAAA,SAAS,GAAGT,WAAW,CAAC,KAAKa,WAAL,CAAiBC,YAAjB;AAAA;AAAA,oDAA+CC,SAAhD,CAAvB,CADG,CACiF;AACvF;;AAED,kBAAOP,WAAP;AACI,iBAAK;AAAA;AAAA,kCAAOQ,WAAZ;AACIP,cAAAA,SAAS,CAACK,YAAV;AAAA;AAAA,kCAA8BG,IAA9B,CAAmCT,WAAnC,EAAgD,KAAKK,WAAL,CAAiBC,YAAjB;AAAA;AAAA,sDAA+CI,UAA/F;AACA;;AACJ,iBAAK;AAAA;AAAA,kCAAOC,WAAZ;AACIV,cAAAA,SAAS,CAACK,YAAV;AAAA;AAAA,kCAA8BG,IAA9B,CAAmCT,WAAnC,EAAgD,KAAKK,WAAL,CAAiBC,YAAjB;AAAA;AAAA,sDAA+CM,UAA/F;AACA;;AACJ,iBAAK;AAAA;AAAA,kCAAOC,aAAZ;AACIZ,cAAAA,SAAS,CAACK,YAAV;AAAA;AAAA,kCAA8BG,IAA9B,CAAmCT,WAAnC,EAAgD,KAAKK,WAAL,CAAiBC,YAAjB;AAAA;AAAA,sDAA+CQ,YAA/F;AACA;AATR;;AAYA,iBAAOb,SAAP;AACH;;AAzByC,O", "sourcesContent": ["import { _decorator, Component, Node, instantiate } from 'cc';\nimport { Global } from '../Global';\nimport { Goods } from '../Goods';\nimport { GamePersistNode } from '../GamePersistNode';\nimport { GameFactory } from './GameFactory';\nconst { ccclass, property } = _decorator;\n\nexport class GoodsFactory extends GameFactory {\n\n\n    public createProduct(productType: string): Node {\n        let goodsTemp: Node = null;\n\n        if(this.productPool.size() > 0) {\n            goodsTemp = this.productPool.get();  //如果池里有物资，就直接拿来用\n        } else {\n            goodsTemp = instantiate(this.persistNode.getComponent(GamePersistNode).goodsPreb);  //从常驻节点拿到预制体原料\n        }\n\n        switch(productType) {\n            case Global.BLOOD_GOODS:\n                goodsTemp.getComponent(Goods).init(productType, this.persistNode.getComponent(GamePersistNode).bloodGoods);\n                break;\n            case Global.LIGHT_GOODS:\n                goodsTemp.getComponent(Goods).init(productType, this.persistNode.getComponent(GamePersistNode).lightGoods);\n                break;\n            case Global.MISSILE_GOODS:\n                goodsTemp.getComponent(Goods).init(productType, this.persistNode.getComponent(GamePersistNode).missileGoods);\n                break;\n        }\n\n        return goodsTemp;\n    }\n\n}\n\n"]}