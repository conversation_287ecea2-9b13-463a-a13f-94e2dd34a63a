{"version": 3, "sources": ["file:///E:/M2Game/Client/assets/scripts/PlatformSDK/WXLogin.ts"], "names": ["WXLogin", "logInfo", "csproto", "authButton", "login", "cb", "wx", "complete", "res", "errMsg", "code", "accountType", "cs", "AccountType", "ACCOUNT_TYPE_WXMINIGAME", "serverAddr", "showUserInfoButton", "show", "hideUserInfoButton", "hide", "getUserInfo", "param", "THIS", "getSetting", "success", "console", "error", "authSetting", "sysInfo", "getSystemInfoSync", "button", "createUserInfoButton", "type", "text", "plain", "style", "left", "screenWidth", "x", "top", "screenHeight", "y", "safeArea", "width", "w", "height", "h", "lineHeight", "backgroundColor", "color", "textAlign", "fontSize", "borderRadius", "onTap", "userInfo", "destroy", "log", "getUserInfo__", "getUserInfo_", "hasTap", "name", "nick<PERSON><PERSON>", "icon", "avatarUrl"], "mappings": ";;;gDAMaA,O;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAJJC,MAAAA,O,iBAAAA,O;;AAEFC,MAAAA,O;;;;;;;yBAEMF,O,GAAN,MAAMA,OAAN,CAAsC;AAAA;AAAA,eACjCG,UADiC,GACpB,IADoB;AAAA;;AAEzCC,QAAAA,KAAK,CAACC,EAAD,EAA4C;AAC7C;AAAA;AAAA,kCAAQ,OAAR,EAAiB,gBAAjB;AACAC,UAAAA,EAAE,CAACF,KAAH,CAAS;AACLG,YAAAA,QAAQ,CAACC,GAAD,EAAM;AACV;AAAA;AAAA,sCAAQ,OAAR,yBAAsCA,GAAG,CAACC,MAA1C,SAAoDD,GAAG,CAACE,IAAxD;AACAL,cAAAA,EAAE,CAACG,GAAG,CAACE,IAAJ,GAAW,IAAX,GAAkBF,GAAG,CAACC,MAAvB,EAA+B;AAC7BE,gBAAAA,WAAW,EAAE;AAAA;AAAA,wCAAQC,EAAR,CAAWC,WAAX,CAAuBC,uBADP;AAE7BJ,gBAAAA,IAAI,EAAEF,GAAG,CAACE,IAFmB;AAG7BK,gBAAAA,UAAU,EAAE;AAHiB,eAA/B,CAAF;AAKH;;AARI,WAAT;AAUH;;AAEDC,QAAAA,kBAAkB,GAAG;AACjB,cAAI,KAAKb,UAAT,EAAqB;AACjB,iBAAKA,UAAL,CAAgBc,IAAhB;AACH;AACJ;;AAEDC,QAAAA,kBAAkB,GAAG;AACjB,cAAI,KAAKf,UAAT,EAAqB;AACjB,iBAAKA,UAAL,CAAgBgB,IAAhB;AACH;AACJ;;AAEDC,QAAAA,WAAW,CAACf,EAAD,EAA8DgB,KAA9D,EAA0E;AACjF,cAAIC,IAAI,GAAG,IAAX;AACAhB,UAAAA,EAAE,CAACiB,UAAH,CAAc;AACVC,YAAAA,OAAO,CAAChB,GAAD,EAAM;AACTiB,cAAAA,OAAO,CAACC,KAAR,CAAc,gCAAd,EAAgDlB,GAAG,CAACmB,WAApD;;AACA,kBAAI,CAACnB,GAAG,CAACmB,WAAJ,CAAgB,gBAAhB,CAAL,EAAwC;AACpCF,gBAAAA,OAAO,CAACC,KAAR,CAAc,+BAAd;AACA,oBAAIE,OAAO,GAAGtB,EAAE,CAACuB,iBAAH,EAAd;AACA,oBAAIC,MAAM,GAAGxB,EAAE,CAACyB,oBAAH,CAAwB;AACjCC,kBAAAA,IAAI,EAAE,MAD2B;AAEjCC,kBAAAA,IAAI,EAAE,EAF2B;AAGjC;AACAC,kBAAAA,KAAK,EAAE,IAJ0B;AAKjCC,kBAAAA,KAAK,EAAE;AACHC,oBAAAA,IAAI,EAAER,OAAO,CAACS,WAAR,GAAsBhB,KAAK,CAACiB,CAD/B;AACiC;AACpCC,oBAAAA,GAAG,EAAEX,OAAO,CAACY,YAAR,IAAwB,IAAInB,KAAK,CAACoB,CAAlC,KAAwCb,OAAO,CAACc,QAAR,CAAiBH,GAAjB,GAAuB,EAA/D,CAFF;AAEqE;AACxEI,oBAAAA,KAAK,EAAEf,OAAO,CAACS,WAAR,GAAsBhB,KAAK,CAACuB,CAHhC;AAGkC;AACrCC,oBAAAA,MAAM,EAAEjB,OAAO,CAACY,YAAR,GAAuBnB,KAAK,CAACyB,CAJlC;AAIoC;AACvCC,oBAAAA,UAAU,EAAE,EALT;AAMHC,oBAAAA,eAAe,EAAE,SANd;AAOHC,oBAAAA,KAAK,EAAE,SAPJ;AAQHC,oBAAAA,SAAS,EAAE,QARR;AASHC,oBAAAA,QAAQ,EAAE,EATP;AAUHC,oBAAAA,YAAY,EAAE;AAVX;AAL0B,iBAAxB,CAAb;AAkBAtB,gBAAAA,MAAM,CAACb,IAAP;AACAa,gBAAAA,MAAM,CAACuB,KAAP,CAAc7C,GAAD,IAAS;AAClB,sBAAIA,GAAG,CAAC8C,QAAR,EAAkB;AACdxB,oBAAAA,MAAM,CAACyB,OAAP;AACAjC,oBAAAA,IAAI,CAACnB,UAAL,GAAkB,IAAlB;AACAsB,oBAAAA,OAAO,CAAC+B,GAAR,CAAY,8BAAZ,EAA4ChD,GAAG,CAAC8C,QAAhD;AACAhC,oBAAAA,IAAI,CAACmC,aAAL,CAAmBjD,GAAG,CAAC8C,QAAvB,EAAiCjD,EAAjC,EAAqC,IAArC;AACH;AACJ,iBAPD;AAQAiB,gBAAAA,IAAI,CAACnB,UAAL,GAAkB2B,MAAlB;AACH,eA/BD,MAgCK;AACDR,gBAAAA,IAAI,CAACoC,YAAL,CAAkBrD,EAAlB,EAAsB,KAAtB;AACH;AACJ;;AAtCS,WAAd;AAwCH;;AACOoD,QAAAA,aAAa,CAACH,QAAD,EAAgBjD,EAAhB,EAAsFsD,MAAtF,EAAuG;AACxHtD,UAAAA,EAAE,CAAC,IAAD,EAAO;AACLuD,YAAAA,IAAI,EAAEN,QAAQ,CAACO,QADV;AAELC,YAAAA,IAAI,EAAER,QAAQ,CAACS;AAFV,WAAP,EAGC,IAHD,CAAF;AAIH;;AACOL,QAAAA,YAAY,CAACrD,EAAD,EAAuEsD,MAAvE,EAAwF;AACxG,cAAIrC,IAAI,GAAG,IAAX;AACAhB,UAAAA,EAAE,CAACc,WAAH,CAAe;AACXb,YAAAA,QAAQ,CAACC,GAAD,EAAM;AACViB,cAAAA,OAAO,CAACC,KAAR,CAAc,gCAAd;;AACA,kBAAIlB,GAAG,CAAC8C,QAAR,EAAkB;AACdhC,gBAAAA,IAAI,CAACmC,aAAL,CAAmBjD,GAAG,CAAC8C,QAAvB,EAAiCjD,EAAjC,EAAqCsD,MAArC;AACH,eAFD,MAGK;AACDtD,gBAAAA,EAAE,CAAC,oBAAD,EAAuB,IAAvB,EAA6B,KAA7B,CAAF;AACH;AACJ;;AATU,WAAf;AAWH;;AA1FwC,O", "sourcesContent": ["\r\nimport { LoginInfo } from '../Network/NetMgr';\r\nimport { logInfo } from '../Utils/Logger';\r\nimport { IPlatformSDK, PlatformSDKUserInfo } from './IPlatformSDK'\r\nimport csproto from '../AutoGen/PB/cs_proto.js';\r\n\r\nexport class WXLogin implements IPlatformSDK {\r\n    private authButton = null;\r\n    login(cb: (err: string, req: LoginInfo) => void) {\r\n        logInfo(\"Login\", \"start wx login\")\r\n        wx.login({\r\n            complete(res) {\r\n                logInfo(\"Login\", `complete wx login ${res.errMsg} ${res.code}`)\r\n                cb(res.code ? null : res.errMsg, {\r\n                    accountType: csproto.cs.AccountType.ACCOUNT_TYPE_WXMINIGAME,\r\n                    code: res.code,\r\n                    serverAddr: \"wss://m2test.5600.online:9101/\",\r\n                })\r\n            }\r\n        })\r\n    }\r\n\r\n    showUserInfoButton() {\r\n        if (this.authButton) {\r\n            this.authButton.show();\r\n        }\r\n    }\r\n\r\n    hideUserInfoButton() {\r\n        if (this.authButton) {\r\n            this.authButton.hide();\r\n        }\r\n    }\r\n\r\n    getUserInfo(cb: (err: string, req: PlatformSDKUserInfo, hasTap) => void, param: any) {\r\n        let THIS = this;\r\n        wx.getSetting({\r\n            success(res) {\r\n                console.error('ybgg get userinfo auth setting', res.authSetting)\r\n                if (!res.authSetting['scope.userInfo']) {\r\n                    console.error('ybgg start authorize userinfo')\r\n                    let sysInfo = wx.getSystemInfoSync();\r\n                    var button = wx.createUserInfoButton({\r\n                        type: 'text',\r\n                        text: '',\r\n                        //image : \"SDK/WXGetUserInfo.png\",\r\n                        plain: true,\r\n                        style: {\r\n                            left: sysInfo.screenWidth * param.x,//0,//buttonPosition.x,\r\n                            top: sysInfo.screenHeight * (1 - param.y) + (sysInfo.safeArea.top - 20),//1334-100,//buttonPosition.y+buttonPosition.height,\r\n                            width: sysInfo.screenWidth * param.w,//750,//buttonPosition.width,\r\n                            height: sysInfo.screenHeight * param.h,//100,//buttonPosition.height,\r\n                            lineHeight: 40,\r\n                            backgroundColor: '#ff0000',\r\n                            color: '#ffffff',\r\n                            textAlign: 'center',\r\n                            fontSize: 16,\r\n                            borderRadius: 4\r\n                        }\r\n                    })\r\n                    button.show();\r\n                    button.onTap((res) => {\r\n                        if (res.userInfo) {\r\n                            button.destroy();\r\n                            THIS.authButton = null;\r\n                            console.log('ybgg get wx userinfo success', res.userInfo)\r\n                            THIS.getUserInfo__(res.userInfo, cb, true)\r\n                        }\r\n                    })\r\n                    THIS.authButton = button;\r\n                }\r\n                else {\r\n                    THIS.getUserInfo_(cb, false);\r\n                }\r\n            }\r\n        })\r\n    }\r\n    private getUserInfo__(userInfo: any, cb: (err: string, req: PlatformSDKUserInfo, hasTap: boolean) => void, hasTap: boolean) {\r\n        cb(null, {\r\n            name: userInfo.nickName,\r\n            icon: userInfo.avatarUrl,\r\n        }, true)\r\n    }\r\n    private getUserInfo_(cb: (err: string, req: PlatformSDKUserInfo, hasTap: boolean) => void, hasTap: boolean) {\r\n        let THIS = this\r\n        wx.getUserInfo({\r\n            complete(res) {\r\n                console.error(\"ybgg get user info wx complete\")\r\n                if (res.userInfo) {\r\n                    THIS.getUserInfo__(res.userInfo, cb, hasTap)\r\n                }\r\n                else {\r\n                    cb(\"get userinfo error\", null, false)\r\n                }\r\n            }\r\n        })\r\n    }\r\n}"]}