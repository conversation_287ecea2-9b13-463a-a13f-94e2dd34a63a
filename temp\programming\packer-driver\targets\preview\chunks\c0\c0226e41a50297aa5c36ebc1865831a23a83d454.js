System.register(["__unresolved_0", "cc", "__unresolved_1", "__unresolved_2"], function (_export, _context) {
  "use strict";

  var _reporterNs, _cclegacy, __checkObsolete__, __checkObsoleteInNamespace__, _decorator, System, RegisterTypeID, _dec, _dec2, _class, _crd, ccclass, LevelSystem;

  function _reportPossibleCrUseOfSystem(extras) {
    _reporterNs.report("System", "../base/System", _context.meta, extras);
  }

  function _reportPossibleCrUseOfRegisterTypeID(extras) {
    _reporterNs.report("RegisterTypeID", "../base/TypeID", _context.meta, extras);
  }

  function _reportPossibleCrUseOfLevel(extras) {
    _reporterNs.report("Level", "./Level", _context.meta, extras);
  }

  return {
    setters: [function (_unresolved_) {
      _reporterNs = _unresolved_;
    }, function (_cc) {
      _cclegacy = _cc.cclegacy;
      __checkObsolete__ = _cc.__checkObsolete__;
      __checkObsoleteInNamespace__ = _cc.__checkObsoleteInNamespace__;
      _decorator = _cc._decorator;
    }, function (_unresolved_2) {
      System = _unresolved_2.System;
    }, function (_unresolved_3) {
      RegisterTypeID = _unresolved_3.RegisterTypeID;
    }],
    execute: function () {
      _crd = true;

      _cclegacy._RF.push({}, "21f78LVBE9F67JdxikRg7Gt", "LevelSystem", undefined);

      __checkObsolete__(['_decorator', 'Node', 'Vec3']);

      ({
        ccclass
      } = _decorator);
      /**
       * LevelSystem - manages level state, objectives, checkpoints, and events
       */

      _export("LevelSystem", LevelSystem = (_dec = ccclass("LevelSystem"), _dec2 = _crd && RegisterTypeID === void 0 ? (_reportPossibleCrUseOfRegisterTypeID({
        error: Error()
      }), RegisterTypeID) : RegisterTypeID, _dec(_class = _dec2(_class = class LevelSystem extends (_crd && System === void 0 ? (_reportPossibleCrUseOfSystem({
        error: Error()
      }), System) : System) {
        constructor() {
          super(...arguments);
          this._currentLevel = null;
          this._isLevelActive = false;
          this._isLevelCompleted = false;
          this._isLevelFailed = false;
        }

        /**
         * Get the current level configuration
         */
        getCurrentLevel() {
          return this._currentLevel;
        }

        setCurrentLevel(level) {
          this._currentLevel = level;
        }
        /**
         * Get the system name
         */


        getSystemName() {
          return "LevelSystem";
        }
        /**
         * Initialize the level system
         */


        onInit() {
          this._isLevelActive = true;
        }
        /**
         * Cleanup the level system
         */


        onUnInit() {
          console.log("LevelSystem: Cleaning up level system");
          this._currentLevel = null;
          this._isLevelActive = false;
          this._isLevelCompleted = false;
          this._isLevelFailed = false;
          console.log("LevelSystem: Cleanup complete");
        }
        /**
         * Update the level system
         */


        onUpdate(deltaTime) {
          if (!this._isLevelActive || !this._currentLevel) {
            return;
          }

          this._currentLevel.tick(deltaTime); // // Check if all objectives are completed
          // if (this._areAllObjectivesCompleted()) {
          //     this.completeLevel();
          // }

        }
        /**
         * Late update - handle any post-update logic
         */


        onLateUpdate(deltaTime) {// Could be used for UI updates, statistics, etc.
        }
        /**
         * Load and start a level
         * @param levelConfig The level configuration to load
         * @returns true if the level was loaded successfully
         */


        loadLevel(levelConfig) {
          if (this._isLevelActive) {
            console.warn("LevelSystem: Cannot load level - another level is already active");
            return false;
          } // console.log(`LevelSystem: Loading level ${levelConfig.levelId}`);
          // Set current level
          // this._currentLevel = this._cloneLevelConfig(levelConfig);
          // // Reset state
          // this._levelStartTime = Date.now();
          // this._levelElapsedTime = 0;
          // this._isLevelActive = true;
          // this._isLevelCompleted = false;
          // this._isLevelFailed = false;
          // this._eventHistory.length = 0;
          // // Reset checkpoints
          // this._currentLevel.checkpoints.forEach(checkpoint => {
          //     checkpoint.isReached = false;
          //     checkpoint.timestamp = undefined;
          // });
          // // Reset objectives
          // this._currentLevel.objectives.forEach(objective => {
          //     objective.currentValue = 0;
          //     objective.isCompleted = false;
          // });
          // // Emit level start event
          // this._emitEvent(LevelEventType.LEVEL_START, {
          //     levelId: levelConfig.levelId,
          //     startTime: this._levelStartTime
          // });


          console.log("LevelSystem: Level " + levelConfig.levelId + " loaded and started");
          return true;
        }
        /**
         * Complete the current level
         */


        completeLevel() {
          if (!this._isLevelActive || this._isLevelCompleted || this._isLevelFailed) {
            return;
          }

          this._isLevelCompleted = true;
          this._isLevelActive = false;
        }
        /**
         * Fail the current level
         * @param reason The reason for failure
         */


        failLevel(reason) {
          if (!this._isLevelActive || this._isLevelCompleted || this._isLevelFailed) {
            return;
          }

          this._isLevelFailed = true;
          this._isLevelActive = false;
        }
        /**
         * Check if level is active
         */


        isLevelActive() {
          return this._isLevelActive;
        }
        /**
         * Check if level is completed
         */


        isLevelCompleted() {
          return this._isLevelCompleted;
        }
        /**
         * Check if level is failed
         */


        isLevelFailed() {
          return this._isLevelFailed;
        }

      }) || _class) || _class));

      _cclegacy._RF.pop();

      _crd = false;
    }
  };
});
//# sourceMappingURL=c0226e41a50297aa5c36ebc1865831a23a83d454.js.map