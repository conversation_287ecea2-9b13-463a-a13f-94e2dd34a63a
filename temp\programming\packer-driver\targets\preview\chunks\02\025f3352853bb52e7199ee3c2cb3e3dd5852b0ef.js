System.register(["__unresolved_0", "cc", "__unresolved_1"], function (_export, _context) {
  "use strict";

  var _reporterNs, _cclegacy, __checkObsolete__, __checkObsoleteInNamespace__, _decorator, Component, find, GamePersistNode, _dec, _class, _crd, ccclass, property, Anim;

  function _reportPossibleCrUseOfAnimFactory(extras) {
    _reporterNs.report("AnimFactory", "./factroy/AnimFactory", _context.meta, extras);
  }

  function _reportPossibleCrUseOfGamePersistNode(extras) {
    _reporterNs.report("GamePersistNode", "./GamePersistNode", _context.meta, extras);
  }

  return {
    setters: [function (_unresolved_) {
      _reporterNs = _unresolved_;
    }, function (_cc) {
      _cclegacy = _cc.cclegacy;
      __checkObsolete__ = _cc.__checkObsolete__;
      __checkObsoleteInNamespace__ = _cc.__checkObsoleteInNamespace__;
      _decorator = _cc._decorator;
      Component = _cc.Component;
      find = _cc.find;
    }, function (_unresolved_2) {
      GamePersistNode = _unresolved_2.GamePersistNode;
    }],
    execute: function () {
      _crd = true;

      _cclegacy._RF.push({}, "5348aRrJnxAI4uEM4LrSPPI", "Anim", undefined);

      __checkObsolete__(['_decorator', 'Component', 'Node', 'find']);

      ({
        ccclass,
        property
      } = _decorator);

      _export("Anim", Anim = (_dec = ccclass('Anim'), _dec(_class = class Anim extends Component {
        constructor() {
          super(...arguments);
          this.animFactory = null;
        }

        onLoad() {
          this.animFactory = find("GamePersistNode").getComponent(_crd && GamePersistNode === void 0 ? (_reportPossibleCrUseOfGamePersistNode({
            error: Error()
          }), GamePersistNode) : GamePersistNode).animFactory;
        }

        recycle() {
          this.animFactory.recycleProduct(this.node);
        }

      }) || _class));

      _cclegacy._RF.pop();

      _crd = false;
    }
  };
});
//# sourceMappingURL=025f3352853bb52e7199ee3c2cb3e3dd5852b0ef.js.map