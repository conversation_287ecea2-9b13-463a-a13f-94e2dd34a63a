System.register(["cc"], function (_export, _context) {
  "use strict";

  var _cclegacy, __checkObsolete__, __checkObsoleteInNamespace__, _decorator, NodePool, find, GameFactory, _crd, ccclass, property;

  _export("GameFactory", void 0);

  return {
    setters: [function (_cc) {
      _cclegacy = _cc.cclegacy;
      __checkObsolete__ = _cc.__checkObsolete__;
      __checkObsoleteInNamespace__ = _cc.__checkObsoleteInNamespace__;
      _decorator = _cc._decorator;
      NodePool = _cc.NodePool;
      find = _cc.find;
    }],
    execute: function () {
      _crd = true;

      _cclegacy._RF.push({}, "7c1a9WmxypIsoLH3AcrENAO", "GameFactory", undefined);

      __checkObsolete__(['_decorator', 'Component', 'Node', 'NodePool', 'find']);

      ({
        ccclass,
        property
      } = _decorator);

      _export("GameFactory", GameFactory = class GameFactory {
        constructor() {
          this.productPool = null;
          this.persistNode = null;
          this.productPool = new NodePool(); //建立节点池

          this.persistNode = find("GamePersistNode"); //获取常驻节点，常驻节点上会有我们制造产品的原料
        }

        createProduct(productType) {
          //具体实现交给子类
          return null;
        }

        recycleProduct(product) {
          this.productPool.put(product); //回收产品，即放回节点池
        }

      });

      _cclegacy._RF.pop();

      _crd = false;
    }
  };
});
//# sourceMappingURL=5d8cc311c9d86a98a06fcd30b34f37ee4e15e9f1.js.map