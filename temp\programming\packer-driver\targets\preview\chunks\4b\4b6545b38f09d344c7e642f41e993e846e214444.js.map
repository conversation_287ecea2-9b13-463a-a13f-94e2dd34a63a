{"version": 3, "sources": ["file:///E:/M2Game/Client/assets/scripts/GameOld/factroy/AnimFactory.ts"], "names": ["AnimFactory", "_decorator", "instantiate", "GamePersistNode", "GameFactory", "ccclass", "property", "createAnim", "anim<PERSON>emp", "productPool", "size", "get", "persistNode", "getComponent", "animPreb"], "mappings": ";;;sIAMaA,W;;;;;;;;;;;;;;;;;;;AANJC,MAAAA,U,OAAAA,U;AAA6BC,MAAAA,W,OAAAA,W;;AAC7BC,MAAAA,e,iBAAAA,e;;AACAC,MAAAA,W,iBAAAA,W;;;;;;;;;OACH;AAAEC,QAAAA,OAAF;AAAWC,QAAAA;AAAX,O,GAAwBL,U;;6BAGjBD,W,GAAN,MAAMA,WAAN;AAAA;AAAA,sCAAsC;AAClCO,QAAAA,UAAU,GAAS;AACtB,cAAIC,QAAc,GAAG,IAArB;;AAEA,cAAG,KAAKC,WAAL,CAAiBC,IAAjB,KAA0B,CAA7B,EAAgC;AAC5BF,YAAAA,QAAQ,GAAG,KAAKC,WAAL,CAAiBE,GAAjB,EAAX,CAD4B,CACQ;AACvC,WAFD,MAEO;AACHH,YAAAA,QAAQ,GAAGN,WAAW,CAAC,KAAKU,WAAL,CAAiBC,YAAjB;AAAA;AAAA,oDAA+CC,QAAhD,CAAtB,CADG,CAC+E;AACrF;;AAED,iBAAON,QAAP;AACH;;AAXwC,O", "sourcesContent": ["import { _decorator, Component, Node, instantiate } from 'cc';\nimport { GamePersistNode } from '../GamePersistNode';\nimport { GameFactory } from './GameFactory';\nconst { ccclass, property } = _decorator;\n\n\nexport class AnimFactory extends GameFactory {\n    public createAnim(): Node {\n        let animTemp: Node = null;\n\n        if(this.productPool.size() > 0) {\n            animTemp = this.productPool.get();  //如果池里有敌机，就直接拿来用\n        } else {\n            animTemp = instantiate(this.persistNode.getComponent(GamePersistNode).animPreb);  //从常驻节点拿到预制体原料\n        }\n\n        return animTemp;\n    } \n}\n\n"]}