System.register(["cc"], function (_export, _context) {
  "use strict";

  var _cclegacy, Helper, _crd;

  return {
    setters: [function (_cc) {
      _cclegacy = _cc.cclegacy;
    }],
    execute: function () {
      _crd = true;

      _cclegacy._RF.push({}, "d4ed1OQIchLVZsWr1vB+L8I", "Helper", undefined);

      Helper = class Helper {
        /**
         * 生成一个随机数
         * @param {number} seed 随机种子
         * @param {number} min 最小值
         * @param {number} max 最大值
         * @returns {number} 随机数
         */
        static random(seed, min, max) {
          if (seed === void 0) {
            seed = 5;
          }

          if (min === void 0) {
            min = 0;
          }

          if (max === void 0) {
            max = 1;
          }

          seed = (9301 * seed + 49297) % 233280;
          return min + seed / 233280 * (max - min);
        }
        /**
         * 将浮点数转换为整数
         * @param {number} value 浮点数
         * @returns {number} 转换后的整数
         */


        static floatToInt(value) {
          value /= 1;
          value = parseFloat(value.toPrecision(12));
          return Math.floor(value);
        }
        /**
         * 延迟指定的时间
         * @param {number} ms 延迟的时间（毫秒）
         * @returns {Promise<void>} Promise 对象
         */


        static sleep(ms) {
          return new Promise(resolve => {
            setTimeout(() => {
              resolve(true);
            }, ms);
          });
        }

      };

      _export("default", Helper);

      _cclegacy._RF.pop();

      _crd = false;
    }
  };
});
//# sourceMappingURL=50e2f5efd6a8a4709e7b58578a93985ccea6bb70.js.map