System.register(["__unresolved_0", "cc", "__unresolved_1"], function (_export, _context) {
  "use strict";

  var _reporterNs, _cclegacy, __checkObsolete__, __checkObsoleteInNamespace__, v2, Vec2, misc, Size, size, sp, resources, SpriteFrame, GameConst, DYTools, _crd, Tools;

  function _reportPossibleCrUseOfGameConst(extras) {
    _reporterNs.report("GameConst", "../const/GameConst", _context.meta, extras);
  }

  return {
    setters: [function (_unresolved_) {
      _reporterNs = _unresolved_;
    }, function (_cc) {
      _cclegacy = _cc.cclegacy;
      __checkObsolete__ = _cc.__checkObsolete__;
      __checkObsoleteInNamespace__ = _cc.__checkObsoleteInNamespace__;
      v2 = _cc.v2;
      Vec2 = _cc.Vec2;
      misc = _cc.misc;
      Size = _cc.Size;
      size = _cc.size;
      sp = _cc.sp;
      resources = _cc.resources;
      SpriteFrame = _cc.SpriteFrame;
    }, function (_unresolved_2) {
      GameConst = _unresolved_2.GameConst;
    }],
    execute: function () {
      _crd = true;

      _cclegacy._RF.push({}, "836a4KfuzNEJoGo3LRAtuST", "Tools", undefined);

      __checkObsolete__(['Component', 'NodePool', 'v2', 'Node', 'Vec2', 'misc', 'Size', 'size', 'Sprite', 'sp', 'resources', 'SpriteFrame']);

      DYTools = class DYTools {
        constructor() {
          this._btnAble = true;
          this.charArr = ["0", "1", "2", "3", "4", "5", "6", "7", "8", "9", "a", "b", "c", "d", "e", "f", "g", "h", "i", "j", "k", "l", "m", "n", "o", "p", "q", "r", "s", "t", "u", "v", "w", "x", "y", "z", "A", "B", "C", "D", "E", "F", "G", "H", "I", "J", "K", "L", "M", "N", "O", "P", "Q", "R", "S", "T", "U", "V", "W", "X", "Y", "Z"];
        }

        /**
         * 获取当前时间戳（毫秒）
         */
        getPhoneTimestamp() {
          var date = new Date();
          return Date.parse(date.toString());
        }
        /**
         * 获取当前游戏时间戳（秒）
         */


        getCurTimestamp() {
          // return Math.floor(GameData.timestamp);
          return Date.now() / 1000;
        }
        /**
         * 检查日期 t 是否在日期 o 之后
         */


        isAfterDay(t, o) {
          var date1 = new Date(t);
          var year1 = date1.getFullYear();
          var month1 = date1.getMonth();
          var day1 = date1.getDate();
          var date2 = new Date(o);
          var year2 = date2.getFullYear();
          var month2 = date2.getMonth();
          var day2 = date2.getDate();
          return year1 < year2 || year1 === year2 && (month1 < month2 || month1 === month2 && day1 < day2);
        }
        /**
         * 检查是否是更晚的一天
         */


        isLaterDay(timestamp) {
          var currentDate = new Date(this.getCurTimestamp());
          var targetDate = new Date(timestamp);
          return targetDate.getFullYear() < currentDate.getFullYear() || targetDate.getFullYear() === currentDate.getFullYear() && (targetDate.getMonth() < currentDate.getMonth() || targetDate.getMonth() === currentDate.getMonth() && targetDate.getDate() < currentDate.getDate());
        }
        /**
         * 检查是否需要刷新到第二天
         */


        isLaterDayRefresh(timestamp, refreshHour) {
          var currentTimestamp = this.getCurTimestamp();
          var diff = currentTimestamp - timestamp;

          if (diff > 0) {
            var hoursPassed = Math.floor(diff / 3600000); // 转换为小时

            if (hoursPassed >= 24) {
              return true;
            }

            var lastDate = new Date(timestamp);
            var currentDate = new Date(currentTimestamp);

            if (lastDate.getDate() < currentDate.getDate()) {
              if (refreshHour <= currentDate.getHours()) {
                return true;
              }
            }
          }

          return false;
        }
        /**
         * 获取两个时间戳之间的天数差
         */


        getDiffDays(startTimestamp, endTimestamp) {
          if (endTimestamp < startTimestamp) return -1;
          var diff = endTimestamp - startTimestamp;
          return Math.floor(diff / (1000 * 60 * 60 * 24));
        }
        /**
         * 检查是否是第二天
         */


        isNextDay(timestamp) {
          var currentTimestamp = this.getCurTimestamp();
          var nextDayStart = timestamp + 86400 * 1000; // 加一天的时间

          return currentTimestamp >= nextDayStart;
        }
        /**
         * 打印日志
         */


        log(message) {
          for (var _len = arguments.length, args = new Array(_len > 1 ? _len - 1 : 0), _key = 1; _key < _len; _key++) {
            args[_key - 1] = arguments[_key];
          }

          console.log(message, ...args);
        }
        /**
         * 打印错误日志
         */


        error(message) {
          for (var _len2 = arguments.length, args = new Array(_len2 > 1 ? _len2 - 1 : 0), _key2 = 1; _key2 < _len2; _key2++) {
            args[_key2 - 1] = arguments[_key2];
          }

          console.error(message, ...args);
        }
        /**
         * 打印警告日志
         */


        warn(message) {
          for (var _len3 = arguments.length, args = new Array(_len3 > 1 ? _len3 - 1 : 0), _key3 = 1; _key3 < _len3; _key3++) {
            args[_key3 - 1] = arguments[_key3];
          }

          console.warn(message, ...args);
        }
        /**
         * 生成随机整数
         */


        random_int(min, max) {
          var random = Math.floor(Math.random() * (max - min + 1)) + min;
          return Math.max(min, Math.min(max, random));
        }
        /**
         * 从数组中随机获取一个元素
         */


        getRandomInArray(array, remove) {
          if (remove === void 0) {
            remove = false;
          }

          var length = array.length;
          if (length === 0) return null;
          var index = this.random_int(0, length - 1);
          var element = array[index];

          if (remove) {
            array.splice(index, 1);
          }

          return element;
        }
        /**
         * 从数组中随机获取多个元素
         */


        getRandomCountInArray(array, count, remove) {
          if (remove === void 0) {
            remove = false;
          }

          var result = [];
          var tempArray = [...array];

          for (var i = 0; i < count; i++) {
            var element = this.getRandomInArray(tempArray, true);

            if (element !== null) {
              result.push(element);
            }
          }

          if (remove) {
            result.forEach(item => {
              var index = array.indexOf(item);

              if (index >= 0) {
                array.splice(index, 1);
              }
            });
          }

          return result;
        }
        /**
         * 在数组的指定位置插入元素
         * @param array 原数组
         * @param index 插入位置
         * @param element 插入的元素
         * @returns 插入后的数组
         */


        arrayInsert(array, index, element) {
          if (index < array.length) {
            var tail = array.splice(index, array.length);
            array.push(element);
            return array.concat(tail);
          } else {
            array.push(element);
            return array;
          }
        }
        /**
         * 随机插入元素到数组
         * @param array 原数组
         * @param element 插入的元素
         * @returns 插入后的数组
         */


        arrayInsertRandom(array, element) {
          var index = this.random_int(0, array.length);
          var tail = array.splice(index, array.length);
          array.push(element);
          return array.concat(tail);
        }
        /**
         * 获取字典的长度
         * @param dict 字典对象
         * @returns 字典的长度
         */


        getDictLength(dict) {
          var count = 0;

          for (var key in dict) {
            if (dict[key] !== null) {
              count++;
            }
          }

          return count;
        }
        /**
         * 根据分隔符获取字符串的指定部分
         * @param str 原字符串
         * @param delimiter 分隔符
         * @param index 索引
         * @returns 指定部分的字符串
         */


        getStringDivideForIndex(str, delimiter, index) {
          var parts = str.split(delimiter);
          return index >= 0 && parts.length > index ? parts[index] : null;
        }
        /**
         * 将字符串转换为 Vec2
         * @param str 原字符串
         * @param delimiter 分隔符
         * @returns Vec2 对象
         */


        stringToPoint(str, delimiter) {
          var parts = str.split(delimiter);

          if (parts.length > 1) {
            return v2(Number(parts[0]), Number(parts[1]));
          }

          return Vec2.ZERO;
        }
        /**
         * 将字符串转换为 Size
         * @param str 原字符串
         * @param delimiter 分隔符
         * @returns Size 对象
         */


        stringToSize(str, delimiter) {
          var parts = str.split(delimiter);

          if (parts.length > 1) {
            return size(Number(parts[0]), Number(parts[1]));
          }

          return Size.ZERO;
        }
        /**
         * 将字符串转换为数字数组
         * @param str 原字符串
         * @param delimiter 分隔符
         * @returns 数字数组
         */


        stringToNumber(str, delimiter) {
          var parts = str.split(delimiter);
          return parts.map(part => Number(part)).filter(num => !isNaN(num));
        }
        /**
         * 获取两点之间的距离
         * @param point1 点1
         * @param point2 点2
         * @returns 距离
         */


        getPosDis(point1, point2) {
          var dx = point2.x - point1.x;
          var dy = point2.y - point1.y;
          return Math.sqrt(dx * dx + dy * dy);
        }
        /**
         * 获取两个 Vec2 的距离
         * @param vec1 Vec2 对象1
         * @param vec2 Vec2 对象2
         * @returns 距离
         */


        getDisForVec2(vec1, vec2) {
          return vec1.subtract(vec2).length();
        }
        /**
         * 获取点到直线的距离
         * @param point 点
         * @param lineStart 直线起点
         * @param lineEnd 直线终点
         * @returns 距离
         */


        getDisFromPointToLine(point, lineStart, lineEnd) {
          var lineVec = lineEnd.subtract(lineStart);
          var pointVec = point.subtract(lineStart);
          var angle = pointVec.signAngle(lineVec);
          return pointVec.length() * Math.sin(angle);
        }
        /**
         * 获取点到直线的 Y 偏移
         * @param point 点
         * @param lineStart 直线起点
         * @param lineEnd 直线终点
         * @returns Y 偏移
         */


        getPointToLineY(point, lineStart, lineEnd) {
          var slope = (lineEnd.y - lineStart.y) / (lineEnd.x - lineStart.x);
          var intercept = lineStart.y - slope * lineStart.x;
          return slope * point.x + intercept - point.y;
        } // /**
        //  * 设置 URL 的 SpriteFrame
        //  * @param url 图片 URL
        //  * @param sprite 目标 Sprite
        //  * @param callback 回调函数
        //  */
        // setUrlFrame(url: string, sprite: Sprite, callback?: () => void) {
        //     if (!url || !sprite) return;
        //     resources.load({ url, type: 'png' }, (err, texture) => {
        //         if (err) {
        //             this.error(err.message || err);
        //             return;
        //         }
        //         const frame = new SpriteFrame(texture);
        //         sprite.spriteFrame = frame;
        //         if (callback) callback();
        //     });
        // }
        // /**
        //  * 加载网络上的 Spine 动画
        //  * @param url 动画的 URL
        //  * @param callback 加载完成后的回调函数
        //  */
        // loadNetSpine(url: string, callback: (data: any) => void) {
        //     const xhr = loader.getXMLHttpRequest();
        //     xhr.open("GET", url, true);
        //     xhr.timeout = 5000;
        //     xhr.ontimeout = () => {
        //         console.error("Request timeout");
        //     };
        //     xhr.onreadystatechange = () => {
        //         if (xhr.readyState === 4 && xhr.status === 200) {
        //             callback(xhr.responseText);
        //         }
        //     };
        //     xhr.send();
        // }

        /**
         * 加载本地资源的 SpriteFrame
         * @param path 资源路径
         * @param sprite 目标 Sprite
         * @param callback 加载完成后的回调函数
         */


        loadResSprite(path, sprite, callback) {
          resources.load(path + "/spriteFrame", SpriteFrame, (err, spriteFrame) => {
            if (err) {
              console.error(err);
            } else {
              if (sprite) {
                sprite.spriteFrame = spriteFrame;
              }

              if (callback) {
                callback(spriteFrame);
              }
            }
          });
        }
        /**
         * 加载本地资源的 Spine 动画
         * @param path 资源路径
         * @param skeleton 目标 Skeleton
         * @param onSuccess 成功回调
         * @param onError 错误回调
         */


        loadSkel(path, skeleton, onSuccess, onError) {
          resources.load(path, sp.SkeletonData, (err, skeletonData) => {
            if (err) {
              console.error(err);

              if (onError) {
                onError();
              }
            } else {
              if (skeleton) {
                skeleton.skeletonData = skeletonData;
                skeleton.premultipliedAlpha = false;
              }

              if (onSuccess) {
                onSuccess(skeletonData);
              }
            }
          });
        }
        /**
         * 获取限制长度的字符串
         * @param str 原字符串
         * @param maxLength 最大长度
         * @returns 限制后的字符串
         */


        getLimitStr(str, maxLength) {
          if (this.getStringCharacterLength(str) > maxLength) {
            return this.subStrByCharacter(str, maxLength) + "...";
          }

          return str;
        }
        /**
         * 获取字符串的字符长度（区分中英文）
         * @param str 原字符串
         * @returns 字符长度
         */


        getStringCharacterLength(str) {
          var length = 0;

          for (var i = 0; i < str.length; i++) {
            var charCode = str.charCodeAt(i);
            length += charCode >= 0 && charCode <= 128 ? 1 : 2;
          }

          return length;
        }
        /**
         * 按字符长度截取字符串
         * @param str 原字符串
         * @param maxLength 最大长度
         * @returns 截取后的字符串
         */


        subStrByCharacter(str, maxLength) {
          var length = 0;
          var result = "";

          for (var i = 0; i < str.length; i++) {
            var charCode = str.charCodeAt(i);
            length += charCode >= 0 && charCode <= 128 ? 1 : 2;

            if (length > maxLength) {
              break;
            }

            result += str[i];
          }

          return result;
        }
        /**
         * 计算两个数字的绝对差值
         * @param num1 数字1
         * @param num2 数字2
         * @returns 绝对差值
         */


        numberDiffAbs(num1, num2) {
          return Math.abs(num1 - num2);
        }
        /**
         * 计算两个数字的差值
         * @param num1 数字1
         * @param num2 数字2
         * @returns 差值
         */


        numberDiff(num1, num2) {
          return num1 - num2;
        }
        /**
         * 判断两个数字的差值是否在范围内
         * @param num1 数字1
         * @param num2 数字2
         * @param range 范围
         * @returns 是否在范围内
         */


        isNumberDiffRange(num1, num2, range) {
          return this.numberDiffAbs(num1, num2) <= range;
        }
        /**
         * 判断两个 Vec2 的差值是否在范围内
         * @param vec1 Vec2 对象1
         * @param vec2 Vec2 对象2
         * @param range 范围
         * @returns 是否在范围内
         */


        isVec2DiffRange(vec1, vec2, range) {
          var diff = vec1.subtract(vec2);
          return Math.abs(diff.x) <= range && Math.abs(diff.y) <= range;
        }
        /**
         * 复制数组
         */


        copyArray(array) {
          return [...array];
        } // /**
        //  * 加密字符串
        //  */
        // encode(data: string): string {
        //     const key = CryptoJS.enc.Latin1.parse("mdy8155zs2619lfm");
        //     const iv = CryptoJS.enc.Latin1.parse("s9mnbdk1giu2e3wn");
        //     return CryptoJS.AES.encrypt(data, key, {
        //         iv: iv,
        //         mode: CryptoJS.mode.CBC,
        //         padding: CryptoJS.pad.ZeroPadding
        //     }).toString();
        // }
        // /**
        //  * 解密字符串
        //  */
        // decode(data: string): string {
        //     const key = CryptoJS.enc.Latin1.parse("mdy8155zs2619lfm");
        //     const iv = CryptoJS.enc.Latin1.parse("s9mnbdk1giu2e3wn");
        //     const decrypted = CryptoJS.AES.decrypt(data, key, {
        //         iv: iv,
        //         mode: CryptoJS.mode.CBC,
        //         padding: CryptoJS.pad.ZeroPadding
        //     });
        //     return CryptoJS.enc.Utf8.stringify(decrypted);
        // }

        /**
         * 将数字转换为字符串（简化为千位或百万单位）
         * @param num 数字
         * @returns 转换后的字符串
         */


        numberToString3(num) {
          var result = "";
          var suffix = "";

          if (num < 1000) {
            result = num.toString();
          } else if (num >= 1000000) {
            result = Math.floor(num / 1000000).toString();
            suffix = "m";
          } else if (num >= 1000) {
            result = Math.floor(num / 1000).toString();
            suffix = "k";
          }

          return result + suffix;
        }
        /**
         * 将数字转换为字符串（保留一位小数，简化为千位或百万单位）
         * @param num 数字
         * @returns 转换后的字符串
         */


        numberToString2(num) {
          var result = "";
          var suffix = "";

          if (num < 1000) {
            result = num.toString();
          } else if (num >= 1000000) {
            var millions = Math.floor(num / 1000000);
            var remainder = Math.floor(num % 1000000 / 100000);
            result = millions + "." + remainder;
            suffix = "m";
          } else if (num >= 1000) {
            var thousands = Math.floor(num / 1000);

            var _remainder = Math.floor(num % 1000 / 100);

            result = thousands + "." + _remainder;
            suffix = "k";
          }

          return result + suffix;
        }
        /**
         * 将数字转换为字符串（保留两位小数，简化为千位或百万单位）
         * @param num 数字
         * @returns 转换后的字符串
         */


        numberToString(num) {
          var result = "";
          var suffix = "";

          if (num < 1000) {
            result = num.toString();
          } else if (num >= 1000000) {
            var millions = Math.floor(num / 1000000);
            var remainder1 = Math.floor(num % 1000000 / 100000);
            var remainder2 = Math.floor(num % 100000 / 10000);
            result = millions + "." + remainder1 + remainder2;
            suffix = "m";
          } else if (num >= 1000) {
            var thousands = Math.floor(num / 1000);

            var _remainder2 = Math.floor(num % 1000 / 100);

            var _remainder3 = Math.floor(num % 100 / 10);

            result = thousands + "." + _remainder2 + _remainder3;
            suffix = "k";
          }

          return result + suffix;
        }
        /**
         * 将秒数转换为 [小时, 分钟, 秒] 的数组
         * @param seconds 秒数
         * @returns [小时, 分钟, 秒]
         */


        getTime(seconds) {
          var hours = Math.floor(seconds / 3600);
          var minutes = Math.floor(seconds % 3600 / 60);
          var secs = Math.floor(seconds % 60);
          return [hours, minutes, secs];
        }
        /**
         * 将秒数转换为时间字符串（格式：HH:MM:SS 或 MM:SS）
         * @param seconds 秒数
         * @returns 时间字符串
         */


        getTimeSecondStr(seconds) {
          if (seconds < 60) {
            return "00:" + this.getTimestrDouble(seconds);
          } else if (seconds < 3600) {
            var minutes = Math.floor(seconds / 60);
            var secs = Math.floor(seconds % 60);
            return this.getTimestrDouble(minutes) + ":" + this.getTimestrDouble(secs);
          } else {
            var hours = Math.floor(seconds / 3600);
            var remainder = seconds % 3600;

            var _minutes = Math.floor(remainder / 60);

            var _secs = Math.floor(remainder % 60);

            return this.getTimestrDouble(hours) + ":" + this.getTimestrDouble(_minutes) + ":" + this.getTimestrDouble(_secs);
          }
        }
        /**
         * 将秒数转换为时间字符串（格式：HH:MM:SS，始终包含小时）
         * @param seconds 秒数
         * @returns 时间字符串
         */


        getTimeSecondStr2(seconds) {
          var hours = Math.floor(seconds / 3600);
          var remainder = seconds % 3600;
          var minutes = Math.floor(remainder / 60);
          var secs = Math.floor(remainder % 60);
          return this.getTimestrDouble(hours) + ":" + this.getTimestrDouble(minutes) + ":" + this.getTimestrDouble(secs);
        }
        /**
         * 将数字转换为两位字符串（不足两位补 0）
         * @param num 数字
         * @returns 两位字符串
         */


        getTimestrDouble(num) {
          return num < 10 ? "0" + num : "" + num;
        }
        /**
         * 将秒数转换为简短的时间字符串（如 "1h30m" 或 "30m"）
         * @param seconds 秒数
         * @returns 时间字符串
         */


        getTimeWord(seconds) {
          var result = "";
          seconds = Math.ceil(seconds);

          if (seconds < 60) {
            result = seconds + "s";
          } else if (seconds < 3600) {
            var minutes = Math.floor(seconds / 60);
            var remainingSeconds = Math.floor(seconds % 60);
            result = minutes + "m";

            if (remainingSeconds > 0) {
              result += remainingSeconds + "s";
            }
          } else if (seconds < 86400) {
            var hours = Math.floor(seconds / 3600);

            var _remainingSeconds = seconds % 3600;

            var _minutes2 = Math.floor(_remainingSeconds / 60);

            result = hours + "h";

            if (_minutes2 > 0) {
              result += _minutes2 + "m";
            }
          } else {
            var days = Math.floor(seconds / 86400);

            var _remainingSeconds2 = seconds % 86400;

            var _hours = Math.floor(_remainingSeconds2 / 3600);

            result = days + "d";

            if (_hours > 0) {
              result += _hours + "h";
            }
          }

          return result;
        }
        /**
         * 将秒数转换为简短的时间字符串（如 "1小时30分" 或 "30秒"）
         * @param seconds 秒数
         * @returns 时间字符串
         */


        getTimeWord1(seconds) {
          var result = "";
          seconds = Math.ceil(seconds);

          if (seconds < 60) {
            result = seconds + "\u79D2";
          } else if (seconds < 3600) {
            var minutes = Math.floor(seconds / 60);
            var remainingSeconds = Math.floor(seconds % 60);
            result = minutes + "\u5206";

            if (remainingSeconds > 0) {
              result += remainingSeconds + "\u79D2";
            }
          } else if (seconds < 86400) {
            var hours = Math.floor(seconds / 3600);

            var _remainingSeconds3 = seconds % 3600;

            var _minutes3 = Math.floor(_remainingSeconds3 / 60);

            result = hours + "\u5C0F\u65F6";

            if (_minutes3 > 0) {
              result += _minutes3 + "\u5206";
            }
          } else {
            var days = Math.floor(seconds / 86400);

            var _remainingSeconds4 = seconds % 86400;

            var _hours2 = Math.floor(_remainingSeconds4 / 3600);

            result = days + "\u5929";

            if (_hours2 > 0) {
              result += _hours2 + "\u5C0F\u65F6";
            }
          }

          return result;
        }
        /**
         * 将秒数转换为简短的时间字符串（如 "1h30m" 或 "30m"）
         * @param seconds 秒数
         * @returns 时间字符串
         */


        getTimeWord2(seconds) {
          var result = "";
          seconds = Math.ceil(seconds);

          if (seconds < 60) {
            result = seconds + "s";
          } else if (seconds < 3600) {
            var minutes = Math.floor(seconds / 60);
            result = minutes + "m";
          } else if (seconds < 86400) {
            var hours = Math.floor(seconds / 3600);
            var remainingSeconds = seconds % 3600;

            var _minutes4 = Math.floor(remainingSeconds / 60);

            result = hours + "h";

            if (_minutes4 > 0) {
              result += _minutes4 + "m";
            }
          } else {
            var days = Math.floor(seconds / 86400);

            var _remainingSeconds5 = seconds % 86400;

            var _hours3 = Math.floor(_remainingSeconds5 / 3600);

            result = days + "d";

            if (_hours3 > 0) {
              result += _hours3 + "h";
            }
          }

          return result;
        }
        /**
         * 将秒数转换为简短的时间字符串（如 "1天2小时30分" 或 "30秒"）
         * @param seconds 秒数
         * @returns 时间字符串
         */


        getTimeWord3(seconds) {
          var result = "";
          seconds = Math.ceil(seconds);

          if (seconds < 3600) {
            var minutes = Math.floor(seconds / 60);
            var remainingSeconds = Math.floor(seconds % 60);
            result = minutes + "m" + remainingSeconds + "s";
          } else if (seconds < 86400) {
            var hours = Math.floor(seconds / 3600);

            var _remainingSeconds6 = seconds % 3600;

            var _minutes5 = Math.floor(_remainingSeconds6 / 60);

            result = hours + "h" + _minutes5 + "m";
          } else {
            var days = Math.floor(seconds / 86400);

            var _remainingSeconds7 = seconds % 86400;

            var _hours4 = Math.ceil(_remainingSeconds7 / 3600);

            result = days + "d" + _hours4 + "h";
          }

          return result;
        }
        /**
        * 将秒数转换为简短的时间字符串（如 "1小时30分" 或 "30秒"）
        * @param seconds 秒数
        * @returns 时间字符串
        */


        getTimeWord4(seconds) {
          var result = "";
          seconds = Math.ceil(seconds);

          if (seconds < 3600) {
            var minutes = Math.floor(seconds / 60);
            var remainingSeconds = Math.floor(seconds % 60);
            result = minutes + "\u5206" + remainingSeconds + "\u79D2";
          } else if (seconds < 86400) {
            var hours = Math.floor(seconds / 3600);

            var _remainingSeconds8 = seconds % 3600;

            var _minutes6 = Math.floor(_remainingSeconds8 / 60);

            result = hours + "\u5C0F\u65F6" + _minutes6 + "\u5206";
          } else {
            var days = Math.floor(seconds / 86400);

            var _remainingSeconds9 = seconds % 86400;

            var _hours5 = Math.floor(_remainingSeconds9 / 3600);

            result = days + "\u5929" + _hours5 + "\u5C0F\u65F6";
          }

          return result;
        }
        /**
        * 将秒数转换为时间字符串（如 "1天2小时30分" 或 "30秒"）
        * @param seconds 秒数
        * @returns 时间字符串
        */


        getTimeStrForHMS1(seconds) {
          if (seconds < 60) {
            return seconds + "\u79D2";
          } else if (seconds < 3600) {
            var minutes = Math.floor(seconds / 60);
            var remainingSeconds = Math.floor(seconds % 60);
            return minutes + "\u5206" + remainingSeconds + "\u79D2";
          } else if (seconds < 86400) {
            var hours = Math.floor(seconds / 3600);

            var _remainingSeconds10 = seconds % 3600;

            var _minutes7 = Math.floor(_remainingSeconds10 / 60);

            return hours + "\u5C0F\u65F6" + _minutes7 + "\u5206" + _remainingSeconds10 + "\u79D2";
          } else {
            var days = Math.floor(seconds / 86400);

            var _remainingSeconds11 = seconds % 86400;

            var _hours6 = Math.floor(_remainingSeconds11 / 3600);

            var _minutes8 = Math.floor(_remainingSeconds11 % 3600 / 60);

            return days + "\u5929" + _hours6 + "\u5C0F\u65F6" + _minutes8 + "\u5206";
          }
        }
        /**
         * 获取随机 Vec2 坐标
         * @param xRange x 坐标范围
         * @param yRange y 坐标范围
         * @returns 随机 Vec2 坐标
         */


        getRandomVec2(xRange, yRange) {
          var x = this.random_int(xRange.x, xRange.y);
          var y = this.random_int(yRange.x, yRange.y);
          return v2(x, y);
        }
        /**
         * 检查数组是否包含某个元素
         * @param array 数组
         * @param element 元素
         * @returns 是否包含
         */


        arrContains(array, element) {
          return array.indexOf(element) != -1;
        }
        /**
         * 从数组中移除某个元素
         * @param array 数组
         * @param element 元素
         * @returns 是否成功移除
         */


        arrRemove(array, element) {
          var index = array.indexOf(element);

          if (index >= 0) {
            array.splice(index, 1);
            return true;
          }

          return false;
        }
        /**
         * 检查数组是否包含某个元素（与 arrContains 类似）
         * @param array 数组
         * @param element 元素
         * @returns 是否包含
         */


        arrContain(array, element) {
          return array.indexOf(element) != -1;
          ;
        }
        /**
         * 添加脚本组件到节点
         * @param node 节点
         * @param script 脚本类
         * @returns 添加的脚本组件
         */


        addScript(node, script) {
          if (!node || !script) return null;
          return node.getComponent(script) || node.addComponent(script);
        }
        /**
         * 根据名称移除子节点
         * @param parent 父节点
         * @param name 子节点名称
         */


        removeChildByName(parent, name) {
          if (!parent) return;
          var child = parent.getChildByName(name);

          if (child) {
            child.destroy();
          }
        }
        /**
         * 检查飞机是否超出屏幕范围
         * @param position 飞机位置
         * @returns 是否超出屏幕
         */


        isPlaneOutScreen(position) {
          var viewCenterX = (_crd && GameConst === void 0 ? (_reportPossibleCrUseOfGameConst({
            error: Error()
          }), GameConst) : GameConst).ViewCenter.x + 50;
          var viewHeight = (_crd && GameConst === void 0 ? (_reportPossibleCrUseOfGameConst({
            error: Error()
          }), GameConst) : GameConst).ViewHeight + 50;
          return position.x < -viewCenterX || position.x > viewCenterX || position.y < -viewHeight || position.y > 50;
        }
        /**
         * 获取贝塞尔曲线上的点
         * @param p0 起点
         * @param p1 控制点1
         * @param p2 控制点2
         * @param p3 终点
         * @param t 参数 t（0 到 1）
         * @returns 贝塞尔曲线上的点
         */


        getBezier(p0, p1, p2, p3, t) {
          return p0 * Math.pow(1 - t, 3) + 3 * p1 * t * Math.pow(1 - t, 2) + 3 * p2 * Math.pow(t, 2) * (1 - t) + p3 * Math.pow(t, 3);
        }
        /**
         * 获取直线上的点
         * @param start 起点
         * @param direction 方向向量
         * @param distance 距离
         * @param t 参数 t（比例）
         * @returns 直线上的点
         */


        getStraight(start, direction, distance, t) {
          var normalizedDir = direction.subtract(start).normalize();
          return start.add(normalizedDir.multiplyScalar(distance * t));
        }
        /**
         * 获取方向向量上的点
         * @param start 起点
         * @param direction 方向向量
         * @param distance 距离
         * @param t 参数 t（比例）
         * @returns 方向向量上的点
         */


        getStraightForDir(start, direction, distance, t) {
          return start.add(direction.multiplyScalar(distance * t));
        }
        /**
         * 获取两点之间的角度
         * @param start 起点
         * @param end 终点
         * @returns 角度
         */


        getAngle(start, end) {
          var dx = end.x - start.x;
          var dy = end.y - start.y;
          var distance = Math.sqrt(dx * dx + dy * dy);
          var angle = Math.asin(dx / distance);
          return dy < 0 ? 180 - misc.radiansToDegrees(angle) : misc.radiansToDegrees(angle);
        }
        /**
         * 根据角度获取点的位置
         * @param point 点
         * @param angle 角度
         * @returns 新位置
         */


        getPositionByAngle(point, angle) {
          var radius = Math.sqrt(point.x * point.x + point.y * point.y);
          var radian = Math.atan2(point.y, point.x) + misc.degreesToRadians(angle);
          return v2(Math.cos(radian) * radius, Math.sin(radian) * radius);
        }
        /**
         * 清空数组中的节点
         * @param array 节点数组
         */


        clearArrayForNode(array) {
          if (!array) return;
          array.forEach(node => {
            if (node) node.destroy();
          });
          array.length = 0;
        }
        /**
         * 清空 Map 中的节点
         * @param map 节点 Map
         */


        clearMapForNode(map) {
          if (!map) return;
          map.forEach(node => {
            if (node) node.destroy();
          });
          map.clear();
        }
        /**
         * 获取随机用户 ID
         * @returns 随机用户 ID
         */


        getRandomUserId() {
          var charArr = "0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ";
          var userId = "";

          for (var i = 0; i < 32; i++) {
            var randomIndex = this.random_int(0, charArr.length - 1);
            userId += charArr[randomIndex];
          }

          return userId;
        }
        /**
         * 获取随机广告 ID
         * @returns 随机广告 ID
         */


        getRandomAdId() {
          var charArr = "0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ";
          var adId = "";

          for (var i = 0; i < 64; i++) {
            var randomIndex = this.random_int(0, charArr.length - 1);
            adId += charArr[randomIndex];
          }

          return adId;
        }
        /**
         * 比较两个版本号
         * @param version1 版本号1
         * @param version2 版本号2
         * @returns 比较结果（-1：version1 < version2，0：version1 == version2，1：version1 > version2）
         */


        version_compare(version1, version2) {
          if (version1 === version2) return 0;
          var v1Parts = version1.split(".");
          var v2Parts = version2.split(".");
          var minLength = Math.min(v1Parts.length, v2Parts.length);

          for (var i = 0; i < minLength; i++) {
            var v1 = parseInt(v1Parts[i], 10);

            var _v = parseInt(v2Parts[i], 10);

            if (v1 > _v) return 1;
            if (v1 < _v) return -1;
          }

          return v1Parts.length > v2Parts.length ? 1 : -1;
        }
        /**
         * 获取两点之间的角度（以度为单位）
         * @param x1 起点 x 坐标
         * @param y1 起点 y 坐标
         * @param x2 终点 x 坐标
         * @param y2 终点 y 坐标
         * @returns 角度
         */


        getLineDegrees(x1, y1, x2, y2) {
          var vector = v2(x2, y2).subtract(v2(x1, y1));
          var reference = v2(0, -1); // 参考向量

          var angle = vector.signAngle(reference);
          return misc.radiansToDegrees(angle);
        }
        /**
         * 获取方向向量
         * @param x1 起点 x 坐标
         * @param y1 起点 y 坐标
         * @param x2 终点 x 坐标
         * @param y2 终点 y 坐标
         * @returns 方向向量
         */


        getDir(x1, y1, x2, y2) {
          return v2(x2, y2).subtract(v2(x1, y1)).normalize();
        }
        /**
         * 获取方向向量的角度（以度为单位）
         * @param dir 方向向量
         * @returns 角度
         */


        getDegreeForDir(dir) {
          var reference = v2(0, -1); // 参考向量

          if (dir.equals(reference) || dir.equals(Vec2.ZERO)) {
            return 0;
          }

          var angle = dir.signAngle(reference);
          return misc.radiansToDegrees(angle);
        }
        /**
         * 将弧度转换为角度
         * @param radians 弧度
         * @returns 角度
         */


        toDegrees(radians) {
          return radians * 180 / Math.PI;
        }
        /**
         * 将角度重置到 [0, 360) 范围内
         * @param angle 角度
         * @returns 重置后的角度
         */


        getResetAngle(angle) {
          if (angle > 0) {
            angle -= 360 * Math.floor(angle / 360);
          } else if (angle < 0) {
            angle += 360 * Math.ceil(Math.abs(angle / 360));
          }

          return angle;
        }
        /**
         * 将角度重置到 [-180, 180) 范围内
         * @param angle 角度
         * @returns 重置后的角度
         */


        getSingedResetAngle(angle) {
          if (angle > 180) {
            angle -= 360 * Math.ceil(angle / 360);
          } else if (angle < -180) {
            angle += 360 * Math.ceil(Math.abs(angle / 360));
          }

          return angle;
        }
        /**
         * 将角度限制在 [-180, 180) 范围内
         * @param angle 角度
         * @returns 限制后的角度
         */


        getSingedAngle(angle) {
          if (angle > 180) {
            angle -= 360 * Math.ceil(angle / 360);

            if (angle < -180) {
              angle += 360;
            }
          } else if (angle < -180) {
            angle += 360 * Math.ceil(Math.abs(angle / 360));

            if (angle > 180) {
              angle -= 360;
            }
          }

          return angle;
        }
        /**
         * 将数组中的元素移动指定位置
         * @param array 原数组
         * @param offset 偏移量
         * @param copy 是否复制数组
         * @returns 移动后的数组
         */


        arrMove(array, offset, copy) {
          if (copy === void 0) {
            copy = false;
          }

          var length = array.length;

          if (copy) {
            array = this.copyArray(array);
          }

          offset %= length;

          if (offset < 0) {
            offset = length - Math.abs(offset);
          }

          array = this.reverse(array, 0, length - offset - 1);
          array = this.reverse(array, length - offset, length - 1);
          return this.reverse(array, 0, length - 1);
        }
        /**
         * 反转数组中指定范围的元素
         * @param array 原数组
         * @param start 起始索引
         * @param end 结束索引
         * @returns 反转后的数组
         */


        reverse(array, start, end) {
          while (start < end) {
            var temp = array[start];
            array[start] = array[end];
            array[end] = temp;
            start++;
            end--;
          }

          return array;
        }
        /**
         * 清空数组中的组件
         * @param array 组件数组
         */


        clearArrayForComp(array) {
          if (!array) return;
          array.forEach(comp => {
            if (comp && comp.node) {
              comp.node.destroy();
            }
          });
          array.splice(0);
        }
        /**
         * 清空 Map 中的节点数组
         * @param map 节点数组的 Map
         */


        clearMapForNodeArr(map) {
          if (!map) return;
          map.forEach(nodeArray => {
            nodeArray.forEach(node => {
              if (node) node.destroy();
            });
          });
          map.clear();
        }
        /**
         * 清空 Map 中的组件
         * @param map 组件的 Map
         */


        clearMapForComp(map) {
          if (!map) return;
          map.forEach(comp => {
            if (comp && comp.node) {
              comp.node.destroy();
            }
          });
          map.clear();
        }
        /**
         * 清空 Map 中的组件数组
         * @param map 组件数组的 Map
         */


        clearMapForCompArr(map) {
          if (!map) return;
          map.forEach(compArray => {
            compArray.forEach(comp => {
              if (comp && comp.node) {
                comp.node.destroy();
              }
            });
          });
          map.clear();
        }
        /**
         * 清空节点池
         * @param pool 节点池
         */


        clearNodePool(pool) {
          if (!pool) return;

          while (pool.size() > 0) {
            var node = pool.get();
            if (node) node.destroy();
          }

          pool.clear();
        }

      };

      _export("Tools", Tools = new DYTools());

      _cclegacy._RF.pop();

      _crd = false;
    }
  };
});
//# sourceMappingURL=5f40ea786cecb9774c3a2f41d853e7d51ba064dc.js.map