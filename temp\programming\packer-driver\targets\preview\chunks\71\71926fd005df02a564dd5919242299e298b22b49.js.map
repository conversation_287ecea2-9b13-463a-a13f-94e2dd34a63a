{"version": 3, "sources": ["file:///E:/M2Game/Client/assets/scripts/GameOld/EnemyBullet.ts"], "names": ["_decorator", "Component", "Sprite", "find", "Collider2D", "Contact2DType", "ProgressBar", "director", "Global", "GamePersistNode", "Player", "audioManager", "ccclass", "property", "EnemyBullet", "enemyBulletType", "curPos", "enemyBullet1MoveSpeed", "enemyBullet2MoveSpeed", "enemyBullet1ReduceBlood", "enemyBullet2ReduceBlood", "enemyBulletFactory", "persistNode", "isLeft", "onLoad", "getComponent", "collider", "node", "on", "BEGIN_CONTACT", "onBeginContact", "selfCollider", "otherCollider", "contact", "ENEMY_BULLET_1", "planeBlood", "getChildByName", "progress", "planeTotalBlood", "recycleProduct", "loadScene", "instance", "playSound", "gameOverAudioClip", "init", "spriteFrame", "update", "deltaTime", "enemyBullet1Move", "ENEMY_BULLET_2", "enemyBullet2Move", "getPosition", "y", "x", "WIDTH", "setPosition", "HEIGHT"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAASA,MAAAA,U,OAAAA,U;AAAYC,MAAAA,S,OAAAA,S;AAA8BC,MAAAA,M,OAAAA,M;AAAcC,MAAAA,I,OAAAA,I;AAAMC,MAAAA,U,OAAAA,U;AAAYC,MAAAA,a,OAAAA,a;AAAuCC,MAAAA,W,OAAAA,W;AAAaC,MAAAA,Q,OAAAA,Q;;AAE9HC,MAAAA,M,iBAAAA,M;;AACAC,MAAAA,e,iBAAAA,e;;AACAC,MAAAA,M,iBAAAA,M;;AACAC,MAAAA,Y,iBAAAA,Y;;;;;;;;;OACH;AAAEC,QAAAA,OAAF;AAAWC,QAAAA;AAAX,O,GAAwBb,U;;6BAGjBc,W,WADZF,OAAO,CAAC,aAAD,C,gBAAR,MACaE,WADb,SACiCb,SADjC,CAC2C;AAAA;AAAA;AAAA,eAEvCc,eAFuC,GAEb,IAFa;AAAA,eAIvCC,MAJuC,GAIxB,IAJwB;AAAA,eAMvCC,qBANuC,GAMP,CANO;AAMD;AANC,eAQvCC,qBARuC,GAQP,CARO;AAQD;AARC,eAUvCC,uBAVuC,GAUL,CAVK;AAUK;AAVL,eAYvCC,uBAZuC,GAYL,CAZK;AAYK;AAZL,eAcvCC,kBAduC,GAcL,IAdK;AAAA,eAgBvCC,WAhBuC,GAgBnB,IAhBmB;AAAA,eAkBvCC,MAlBuC,GAkBrB,IAlBqB;AAAA;;AAqBvCC,QAAAA,MAAM,GAAG;AACL,eAAKF,WAAL,GAAmBnB,IAAI,CAAC,iBAAD,CAAvB;AACA,eAAKkB,kBAAL,GAA0B,KAAKC,WAAL,CAAiBG,YAAjB;AAAA;AAAA,kDAA+CJ,kBAAzE;AAEA,eAAKJ,qBAAL,GAA6B,KAAKK,WAAL,CAAiBG,YAAjB;AAAA;AAAA,kDAA+CR,qBAA5E;AACA,eAAKC,qBAAL,GAA6B,KAAKI,WAAL,CAAiBG,YAAjB;AAAA;AAAA,kDAA+CP,qBAA5E;AAEA,eAAKC,uBAAL,GAA+B,KAAKG,WAAL,CAAiBG,YAAjB;AAAA;AAAA,kDAA+CN,uBAA9E;AACA,eAAKC,uBAAL,GAA+B,KAAKE,WAAL,CAAiBG,YAAjB;AAAA;AAAA,kDAA+CL,uBAA9E;AAGA,cAAIM,QAAQ,GAAG,KAAKC,IAAL,CAAUF,YAAV,CAAuBrB,UAAvB,CAAf;;AAEA,cAAIsB,QAAJ,EAAc;AACVA,YAAAA,QAAQ,CAACE,EAAT,CAAYvB,aAAa,CAACwB,aAA1B,EAAyC,KAAKC,cAA9C,EAA8D,IAA9D;AACH;AACJ;;AAEDA,QAAAA,cAAc,CAACC,YAAD,EAA2BC,aAA3B,EAAsDC,OAAtD,EAAyF;AACnG;AACA,cAAI,KAAKlB,eAAL,IAAwB;AAAA;AAAA,gCAAOmB,cAAnC,EAAmD;AAC/CF,YAAAA,aAAa,CAACP,YAAd;AAAA;AAAA,kCAAmCU,UAAnC,IAAiD,KAAKhB,uBAAtD;AACH,WAFD,MAEO;AACHa,YAAAA,aAAa,CAACP,YAAd;AAAA;AAAA,kCAAmCU,UAAnC,IAAiD,KAAKf,uBAAtD;AACH,WANkG,CAQnG;;;AACAY,UAAAA,aAAa,CAACL,IAAd,CAAmBS,cAAnB,CAAkC,OAAlC,EAA2CX,YAA3C,CAAwDnB,WAAxD,EAAqE+B,QAArE,GAAgFL,aAAa,CAACP,YAAd;AAAA;AAAA,gCAAmCU,UAAnC,GAAgDH,aAAa,CAACP,YAAd;AAAA;AAAA,gCAAmCa,eAAnK;AACA,eAAKjB,kBAAL,CAAwBkB,cAAxB,CAAuC,KAAKZ,IAA5C;;AAEA,cAAIK,aAAa,CAACP,YAAd;AAAA;AAAA,gCAAmCU,UAAnC,IAAiD,CAArD,EAAwD;AACpD5B,YAAAA,QAAQ,CAACiC,SAAT,CAAmB,MAAnB;AACA;AAAA;AAAA,8CAAaC,QAAb,CAAsBC,SAAtB,CAAgC,KAAKpB,WAAL,CAAiBG,YAAjB;AAAA;AAAA,oDAA+CkB,iBAA/E;AACH;AACJ;AAED;AACJ;AACA;;;AACKC,QAAAA,IAAI,CAAC7B,eAAD,EAA0B8B,WAA1B,EAAoD;AACrD,eAAK9B,eAAL,GAAuBA,eAAvB;AACA,eAAKY,IAAL,CAAUF,YAAV,CAAuBvB,MAAvB,EAA+B2C,WAA/B,GAA6CA,WAA7C;AACH;;AAEDC,QAAAA,MAAM,CAACC,SAAD,EAAoB;AACtB,cAAI,KAAKhC,eAAL,IAAwB;AAAA;AAAA,gCAAOmB,cAAnC,EAAmD;AAC/C,iBAAKc,gBAAL,CAAsBD,SAAtB;AACH,WAFD,MAEO,IAAG,KAAKhC,eAAL,IAAwB;AAAA;AAAA,gCAAOkC,cAAlC,EAAkD;AACrD,iBAAKC,gBAAL,CAAsBH,SAAtB;AACH;AACJ;AAED;AACJ;AACA;;;AACIC,QAAAA,gBAAgB,CAACD,SAAD,EAAmB;AAC/B,eAAK/B,MAAL,GAAc,KAAKW,IAAL,CAAUwB,WAAV,EAAd;AACA,eAAKnC,MAAL,CAAYoC,CAAZ,IAAiB,KAAKnC,qBAAL,GAA6B8B,SAA9C,CAF+B,CAI/B;;AACA,cAAI,KAAKxB,MAAT,EAAiB;AACb,iBAAKP,MAAL,CAAYqC,CAAZ,IAAiB,KAAKpC,qBAAL,GAA6B8B,SAA9C;;AACA,gBAAI,KAAK/B,MAAL,CAAYqC,CAAZ,GAAgB,CAAC;AAAA;AAAA,kCAAOC,KAAR,GAAgB,CAApC,EAAuC;AACnC,mBAAK/B,MAAL,GAAc,KAAd;AACH;AACJ,WALD,MAKO;AACH,iBAAKP,MAAL,CAAYqC,CAAZ,IAAiB,KAAKpC,qBAAL,GAA6B8B,SAA9C;;AACA,gBAAI,KAAK/B,MAAL,CAAYqC,CAAZ,GAAgB;AAAA;AAAA,kCAAOC,KAAP,GAAe,CAAnC,EAAsC;AAClC,mBAAK/B,MAAL,GAAc,IAAd;AACH;AACJ;;AAED,eAAKI,IAAL,CAAU4B,WAAV,CAAsB,KAAKvC,MAA3B;;AAEA,cAAI,KAAKA,MAAL,CAAYoC,CAAZ,GAAgB,CAAC;AAAA;AAAA,gCAAOI,MAAR,GAAiB,CAArC,EAAwC;AACpC,iBAAKnC,kBAAL,CAAwBkB,cAAxB,CAAuC,KAAKZ,IAA5C;AACH;AACJ;AAED;AACJ;AACA;;;AACIuB,QAAAA,gBAAgB,CAACH,SAAD,EAAoB;AAChC,eAAK/B,MAAL,GAAc,KAAKW,IAAL,CAAUwB,WAAV,EAAd;AACA,eAAKnC,MAAL,CAAYoC,CAAZ,IAAiB,KAAKlC,qBAAL,GAA6B6B,SAA9C;AACA,eAAKpB,IAAL,CAAU4B,WAAV,CAAsB,KAAKvC,MAA3B;;AAEA,cAAI,KAAKA,MAAL,CAAYoC,CAAZ,GAAgB,CAAC;AAAA;AAAA,gCAAOI,MAAR,GAAiB,CAArC,EAAwC;AACpC,iBAAKnC,kBAAL,CAAwBkB,cAAxB,CAAuC,KAAKZ,IAA5C;AACH;AACJ;;AA/GsC,O", "sourcesContent": ["import { _decorator, Component, Node, Sprite<PERSON>rame, Sprite, Vec3, find, Collider2D, Contact2DType, IPhysics2DContact, log, ProgressBar, director } from 'cc';\nimport { GameFactory } from './factroy/GameFactory';\nimport { Global } from './Global';\nimport { GamePersistNode } from './GamePersistNode';\nimport { Player } from './Player';\nimport { audioManager } from '../ResUpdate/audioManager';\nconst { ccclass, property } = _decorator;\n\n@ccclass('EnemyBullet')\nexport class EnemyBullet extends Component {\n\n    enemyBulletType: string = null;\n\n    curPos: Vec3 = null;\n\n    enemyBullet1MoveSpeed: number = 0;    //敌机子弹1的移动速度\n\n    enemyBullet2MoveSpeed: number = 0;    //敌机子弹2的移动速度\n\n    enemyBullet1ReduceBlood: number = 0;        //当被敌机子弹一打中后掉多少血\n\n    enemyBullet2ReduceBlood: number = 0;        //当被敌机子弹二打中后掉多少血\n\n    enemyBulletFactory: GameFactory = null;\n\n    persistNode: Node = null;\n\n    isLeft: boolean = true;\n    \n\n    onLoad() {\n        this.persistNode = find(\"GamePersistNode\");\n        this.enemyBulletFactory = this.persistNode.getComponent(GamePersistNode).enemyBulletFactory;\n\n        this.enemyBullet1MoveSpeed = this.persistNode.getComponent(GamePersistNode).enemyBullet1MoveSpeed;\n        this.enemyBullet2MoveSpeed = this.persistNode.getComponent(GamePersistNode).enemyBullet2MoveSpeed;\n\n        this.enemyBullet1ReduceBlood = this.persistNode.getComponent(GamePersistNode).enemyBullet1ReduceBlood;\n        this.enemyBullet2ReduceBlood = this.persistNode.getComponent(GamePersistNode).enemyBullet2ReduceBlood;\n\n\n        let collider = this.node.getComponent(Collider2D);\n\n        if (collider) {\n            collider.on(Contact2DType.BEGIN_CONTACT, this.onBeginContact, this);\n        }\n    }\n\n    onBeginContact(selfCollider: Collider2D, otherCollider: Collider2D, contact: IPhysics2DContact | null) {\n        //判断被哪种敌机子弹击中\n        if (this.enemyBulletType == Global.ENEMY_BULLET_1) {\n            otherCollider.getComponent(Player).planeBlood -= this.enemyBullet1ReduceBlood;\n        } else {\n            otherCollider.getComponent(Player).planeBlood -= this.enemyBullet2ReduceBlood;\n        }\n\n        //更新血条\n        otherCollider.node.getChildByName(\"Blood\").getComponent(ProgressBar).progress = otherCollider.getComponent(Player).planeBlood / otherCollider.getComponent(Player).planeTotalBlood;\n        this.enemyBulletFactory.recycleProduct(this.node);\n\n        if (otherCollider.getComponent(Player).planeBlood <= 0) {\n            director.loadScene(\"Main\");\n            audioManager.instance.playSound(this.persistNode.getComponent(GamePersistNode).gameOverAudioClip);\n        }\n    }\n\n    /**\n     * 初始化enemyBullet\n     */\n     init(enemyBulletType: string, spriteFrame: SpriteFrame) {\n        this.enemyBulletType = enemyBulletType;\n        this.node.getComponent(Sprite).spriteFrame = spriteFrame;\n    }\n\n    update(deltaTime: number) {\n        if (this.enemyBulletType == Global.ENEMY_BULLET_1) {\n            this.enemyBullet1Move(deltaTime);\n        } else if(this.enemyBulletType == Global.ENEMY_BULLET_2) {\n            this.enemyBullet2Move(deltaTime);\n        }\n    }\n\n    /**\n     * 敌机子弹1移动\n     */\n    enemyBullet1Move(deltaTime: number){\n        this.curPos = this.node.getPosition();\n        this.curPos.y -= this.enemyBullet1MoveSpeed * deltaTime;\n\n        //子弹一水平方向上移动\n        if (this.isLeft) {\n            this.curPos.x -= this.enemyBullet1MoveSpeed * deltaTime;\n            if (this.curPos.x < -Global.WIDTH / 2) {\n                this.isLeft = false;\n            }\n        } else {\n            this.curPos.x += this.enemyBullet1MoveSpeed * deltaTime;\n            if (this.curPos.x > Global.WIDTH / 2) {\n                this.isLeft = true;\n            }\n        }\n\n        this.node.setPosition(this.curPos);\n\n        if (this.curPos.y < -Global.HEIGHT / 2) {\n            this.enemyBulletFactory.recycleProduct(this.node);\n        }\n    }\n\n    /**\n     * 敌机子弹2移动\n     */\n    enemyBullet2Move(deltaTime: number) {\n        this.curPos = this.node.getPosition();\n        this.curPos.y -= this.enemyBullet2MoveSpeed * deltaTime;\n        this.node.setPosition(this.curPos);\n\n        if (this.curPos.y < -Global.HEIGHT / 2) {\n            this.enemyBulletFactory.recycleProduct(this.node);\n        }\n    }\n}\n\n"]}