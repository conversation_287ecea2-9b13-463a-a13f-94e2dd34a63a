{"version": 3, "sources": ["file:///E:/M2Game/Client/assets/scripts/Game/ui/plane/enemy/EnemyBase.ts"], "names": ["_decorator", "UIOpacity", "UITransform", "v2", "Vec2", "Vec3", "EnemyEntity", "GameEnum", "ColliderComp", "EnemyEffectComp", "EnemyAttrComponent", "Tools", "BattleLayer", "GameConst", "Bullet", "GameIns", "MainPlane", "ccclass", "property", "EnemyBase", "uiData", "scaleType", "propertyRate", "_curHp", "exp", "maxHp", "defence", "resist", "hurtBuffMap", "Map", "_hurtBuffTime", "_fireDemage", "_fireHurtCd", "_fireHurtTime", "_isFireCirt", "_buffCount<PERSON>rr", "collide<PERSON>omp", "_collideLevel", "EnemyCollideLevel", "Main", "bCollideDead", "damaged", "_isTracked", "_countTime", "_bStandBy", "_standByTime", "_standByEnd", "_lootArr", "_lootItemArr", "_curLoot", "_lootHp", "_lootNeedHp", "_lootHpUnit", "_itemParent", "_isItem", "effectComp", "attrCom", "dieBullet", "bullets", "itemParent", "value", "isItem", "collideLevel", "collideAble", "enabled", "preLoad", "addComp", "init", "node", "getComponent", "addScript", "reset", "collideAtk", "clear", "removeAllBuffEffect", "setUIData", "data", "setCollideData", "collider", "initComps", "setScaleType", "type", "getScaleType", "initAttr", "attr", "hasAttribution", "setExp", "m_comps", "for<PERSON>ach", "comp", "startBattle", "addLoot", "loot", "push", "updateGameLogic", "deltaTime", "isDead", "checkStandby", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Enemy<PERSON>uff", "Fire", "hurtEffectManager", "createFollowHurtNum", "x", "y", "hurt", "updateSkillResist", "update", "active", "opacity", "key", "time", "get", "set", "removeBuff", "initPropertyRate", "rates", "length", "curHp", "attack", "getUIData", "setStandByTime", "isStandBy", "setPos", "isTracked", "setPosition", "getDir", "ZERO", "getAngle", "isFullBlood", "getMaxHp", "hp", "getHpPercent", "changeHp", "delta", "checkHp", "die", "EnemyDestroyType", "Die", "setCurHp", "getCurHp", "isDamageable", "sceneLayer", "position", "convertToWorldSpaceAR", "me", "enemyPlane<PERSON><PERSON>er", "convertToNodeSpaceAR", "ViewCenter", "setDamaged", "getSkillResist", "skillType", "addBuff", "buffType", "params", "isCritical", "Ice", "<PERSON><PERSON><PERSON>", "count", "onAddBuff", "delete", "<PERSON><PERSON><PERSON><PERSON>", "onRemoveBuff", "buffD<PERSON>", "skillResistUIDict", "fireDamage", "hpParam", "entity", "offset", "setData", "setCollideEntity", "mainEntity", "setCollideOffset", "entityOffset", "setCollideScale", "widthScale", "heightScale", "setSize", "onCollide", "getAttack", "createHurtNumByType", "finalDamage", "Math", "max", "warAttackManager", "playerWARatio", "damage", "_checkHurtLoot", "onHurt", "checkLoot", "destroyType", "m_colliderPreTime", "new_uuid", "onDie", "mainPlaneManager", "checkKillHp", "checkKillAtk", "bullet", "dieRemove", "enemyManager", "subAnnihilate", "mainAnnihilate", "EnemyType", "Ligature", "LigatureLine", "<PERSON><PERSON><PERSON>", "GoldBox", "LigatureUnit", "worldPosition", "checkEnemyDieBomb", "will<PERSON><PERSON><PERSON>", "showAttrShield", "playDieAnim", "blastSound", "blastParams", "blastParam", "i", "blastCount", "index", "random_int", "param", "splice", "effectData", "Number", "scale", "battleManager", "getRatio", "angle", "callback", "onDieAnimEnd", "scheduleOnce", "blastDurations", "addBullet", "removeBullet", "indexOf"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAASA,MAAAA,U,OAAAA,U;AAAYC,MAAAA,S,OAAAA,S;AAAWC,MAAAA,W,OAAAA,W;AAAaC,MAAAA,E,OAAAA,E;AAAIC,MAAAA,I,OAAAA,I;AAAMC,MAAAA,I,OAAAA,I;;AAChDC,MAAAA,W;;AACAC,MAAAA,Q;;AACEC,MAAAA,Y,iBAAAA,Y;;AACFC,MAAAA,e;;AACAC,MAAAA,kB;;AACEC,MAAAA,K,iBAAAA,K;;AACFC,MAAAA,W;;AACEC,MAAAA,S,iBAAAA,S;;AACFC,MAAAA,M;;AACEC,MAAAA,O,kBAAAA,O;;AACAC,MAAAA,S,kBAAAA,S;;;;;;;;;OAIH;AAAEC,QAAAA,OAAF;AAAWC,QAAAA;AAAX,O,GAAwBlB,U;;yBAGTmB,S,GADpBF,O,UAAD,MACqBE,SADrB;AAAA;AAAA,sCACmD;AAAA;AAAA;AAAA,eAE/CC,MAF+C,GAEtC,IAFsC;AAAA,eAG/CC,SAH+C,GAGnC,CAAC,CAHkC;AAAA,eAI/CC,YAJ+C,GAIhC,EAJgC;AAAA,eAK/CC,MAL+C,GAKtC,CALsC;AAAA,eAM/CC,GAN+C,GAMzC,CANyC;AAAA,eAO/CC,KAP+C,GAOvC,CAPuC;AAAA,eAQ/CC,OAR+C,GAQrC,CARqC;AAAA,eAS/CC,MAT+C,GAStC,EATsC;AAAA,eAU/CC,WAV+C,GAUjC,IAAIC,GAAJ,EAViC;AAAA,eAW/CC,aAX+C,GAW/B,IAAID,GAAJ,EAX+B;AAAA,eAY/CE,WAZ+C,GAYjC,CAZiC;AAAA,eAa/CC,WAb+C,GAajC,CAbiC;AAAA,eAc/CC,aAd+C,GAc/B,CAd+B;AAAA,eAe/CC,WAf+C,GAejC,KAfiC;AAAA,eAgB/CC,aAhB+C,GAgB/B,IAAIN,GAAJ,EAhB+B;AAAA,eAiB/CO,WAjB+C,GAiBpB,IAjBoB;AAAA,eAkB/CC,aAlB+C,GAkB/B;AAAA;AAAA,oCAASC,iBAAT,CAA2BC,IAlBI;AAAA,eAmB/CC,YAnB+C,GAmBhC,KAnBgC;AAAA,eAoB/CC,OApB+C,GAoBrC,KApBqC;AAAA,eAqB/CC,UArB+C,GAqBlC,KArBkC;AAAA,eAsB/CC,UAtB+C,GAsBlC,CAtBkC;AAAA,eAuB/CC,SAvB+C,GAuBnC,KAvBmC;AAAA,eAwB/CC,YAxB+C,GAwBhC,CAxBgC;AAAA,eAyB/CC,WAzB+C,GAyBjC,KAzBiC;AAAA,eA0B/CC,QA1B+C,GA0BpC,EA1BoC;AAAA,eA2B/CC,YA3B+C,GA2BhC,EA3BgC;AAAA,eA4B/CC,QA5B+C,GA4BpC,IA5BoC;AAAA,eA6B/CC,OA7B+C,GA6BrC,CA7BqC;AAAA,eA8B/CC,WA9B+C,GA8BjC,CA9BiC;AAAA,eA+B/CC,WA/B+C,GA+BjC,CA/BiC;AAAA,eAgC/CC,WAhC+C,GAgCjC,IAhCiC;AAAA,eAiC/CC,OAjC+C,GAiCrC,KAjCqC;AAAA,eAkC/CC,UAlC+C,GAkClC,IAlCkC;AAAA,eAmC/CC,OAnC+C,GAmCrC,IAnCqC;AAAA,eAoC/CC,SApC+C,GAoCnC,KApCmC;AAAA,eAqC/CC,OArC+C,GAqCrC,EArCqC;AAAA;;AAuCjC,YAAVC,UAAU,GAAG;AACb,iBAAO,KAAKN,WAAZ;AACH;;AACa,YAAVM,UAAU,CAACC,KAAD,EAAQ;AAClB,eAAKP,WAAL,GAAmBO,KAAnB;AACH;;AACS,YAANC,MAAM,GAAG;AACT,iBAAO,KAAKP,OAAZ;AACH;;AACS,YAANO,MAAM,CAACD,KAAD,EAAQ;AACd,eAAKN,OAAL,GAAeM,KAAf;AACH;;AACe,YAAZE,YAAY,GAAG;AACf,iBAAO,KAAKzB,aAAZ;AACH;;AACe,YAAZyB,YAAY,CAACF,KAAD,EAAQ;AACpB,eAAKvB,aAAL,GAAqBuB,KAArB;AACH;AAED;AACJ;AACA;AACA;;;AACmB,YAAXG,WAAW,GAAG;AACd,iBAAO,KAAK3B,WAAL,IAAoB,KAAKA,WAAL,CAAiB4B,OAA5C;AACH;AAED;AACJ;AACA;AACA;;;AACmB,YAAXD,WAAW,CAACH,KAAD,EAAQ;AACnB,cAAI,KAAKxB,WAAT,EAAsB;AAClB,iBAAKA,WAAL,CAAiB4B,OAAjB,GAA2BJ,KAA3B;AACH;AACJ;AACD;AACJ;AACA;;;AACIK,QAAAA,OAAO,GAAG;AACN;AACA,eAAK7B,WAAL,GAAmB,KAAK8B,OAAL;AAAA;AAAA,4CAA2B;AAAA;AAAA,6CAA3B,CAAnB;AACA,eAAK9B,WAAL,CAAiB+B,IAAjB,CAAsB,IAAtB,EAHM,CAKN;;AACA,eAAKZ,UAAL,GAAkB,KAAKa,IAAL,CAAUC,YAAV;AAAA;AAAA,iDAAlB,CANM,CAQN;;AACA,eAAKb,OAAL,GAAe;AAAA;AAAA,8BAAMc,SAAN,CAAgB,KAAKF,IAArB;AAAA;AAAA,uDAAf;AACH;AAED;AACJ;AACA;;;AACIG,QAAAA,KAAK,GAAG;AACJ,gBAAMA,KAAN;AACA,eAAKnD,MAAL,GAAc,IAAd;AACA,eAAKC,SAAL,GAAiB,CAAC,CAAlB;AACA,eAAKE,MAAL,GAAc,CAAd;AACA,eAAKE,KAAL,GAAa,CAAb;AACA,eAAKD,GAAL,GAAW,CAAX;AACA,eAAKgD,UAAL,GAAkB,CAAlB;AACA,eAAK5C,WAAL,CAAiB6C,KAAjB;;AACA,eAAK3C,aAAL,CAAmB2C,KAAnB;;AACA,eAAK9C,MAAL,GAAc,EAAd;AACA,eAAKe,UAAL,GAAkB,KAAlB;AACA,eAAKC,UAAL,GAAkB,CAAlB;AACA,eAAKC,SAAL,GAAiB,KAAjB;AACA,eAAKC,YAAL,GAAoB,CAApB;AACA,eAAKR,aAAL,GAAqB;AAAA;AAAA,oCAASC,iBAAT,CAA2BC,IAAhD;AACA,eAAKE,OAAL,GAAe,KAAf;AACA,eAAKM,QAAL,GAAgB,EAAhB;AACA,eAAKC,YAAL,GAAoB,EAApB;AACA,eAAKC,QAAL,GAAgB,IAAhB;AACA,eAAKC,OAAL,GAAe,CAAf;AACA,eAAKC,WAAL,GAAmB,CAAnB;AACA,eAAKC,WAAL,GAAmB,CAAnB;AACA,eAAKC,WAAL,GAAmB,IAAnB;AACA,eAAKC,OAAL,GAAe,KAAf;AACA,eAAKoB,mBAAL;AACA,eAAKjB,SAAL,GAAiB,KAAjB;AACA,eAAKC,OAAL,GAAe,EAAf;AACH;AAED;AACJ;AACA;AACA;;;AACIiB,QAAAA,SAAS,CAACC,IAAD,EAAO;AACZ,eAAKxD,MAAL,GAAcwD,IAAd;;AACA,cAAI,KAAKxD,MAAT,EAAiB;AACb,iBAAKyD,cAAL,CAAoB,KAAKzD,MAAL,CAAY0D,QAAhC;AACA,iBAAKC,SAAL;AACH;AACJ;AAED;AACJ;AACA;AACA;;;AACIC,QAAAA,YAAY,CAACC,IAAD,EAAO;AACf,eAAK5D,SAAL,GAAiB4D,IAAjB;AACH;AAED;AACJ;AACA;AACA;;;AACIC,QAAAA,YAAY,GAAG;AACX,iBAAO,KAAK7D,SAAZ;AACH;AAED;AACJ;AACA;AACA;;;AACI8D,QAAAA,QAAQ,CAACC,IAAD,EAAO;AACX,eAAK5B,OAAL,CAAaW,IAAb,CAAkB,IAAlB,EAAwBiB,IAAI,IAAI,EAAhC;AACH;AAED;AACJ;AACA;AACA;AACA;;;AACIC,QAAAA,cAAc,CAACD,IAAD,EAAO;AACjB,iBAAO,KAAK5B,OAAL,IAAgB,KAAKA,OAAL,CAAa6B,cAAb,CAA4BD,IAA5B,CAAvB;AACH;AAED;AACJ;AACA;AACA;;;AACIE,QAAAA,MAAM,CAAC9D,GAAD,EAAM;AACR,eAAKA,GAAL,GAAWA,GAAX;AACH;AAED;AACJ;AACA;;;AACIuD,QAAAA,SAAS,GAAG;AACR,eAAKQ,OAAL,CAAaC,OAAb,CAAsBC,IAAD,IAAU;AAC3BA,YAAAA,IAAI,CAACtB,IAAL,CAAU,IAAV;AACH,WAFD;AAGH;AAED;AACJ;AACA;;;AACIuB,QAAAA,WAAW,GAAG;AACV,eAAK3B,WAAL,GAAmB,IAAnB;AACH;AAED;AACJ;AACA;AACA;;;AACI4B,QAAAA,OAAO,CAACC,IAAD,EAAO;AACV,eAAK7C,QAAL,CAAc8C,IAAd,CAAmBD,IAAnB;AACH;AAED;AACJ;AACA;AACA;;;AACIE,QAAAA,eAAe,CAACC,SAAD,EAAY;AACvB,cAAI,KAAKC,MAAL,IAAe,KAAKC,YAAL,CAAkBF,SAAlB,CAAnB,EAAiD;AAC7C;AACH,WAHsB,CAKvB;;;AACA,cAAI,KAAKG,WAAL,CAAiB;AAAA;AAAA,oCAASC,SAAT,CAAmBC,IAApC,CAAJ,EAA+C;AAC3C,iBAAKnE,aAAL,IAAsB8D,SAAtB;;AACA,gBAAI,KAAK9D,aAAL,IAAsB,KAAKD,WAA/B,EAA4C;AACxC,mBAAKC,aAAL,GAAqB,CAArB;AACA;AAAA;AAAA,sCAAQoE,iBAAR,CAA0BC,mBAA1B,CACI,KAAKlE,WADT,EAEI,KAAKF,WAFT,EAGI,KAAKH,WAHT,EAII;AAAEwE,gBAAAA,CAAC,EAAE,CAAL;AAAQC,gBAAAA,CAAC,EAAE;AAAX,eAJJ;AAMA,mBAAKC,IAAL,CAAU,KAAK1E,WAAf;AACH;AACJ,WAlBsB,CAoBvB;;;AACA,eAAK2E,iBAAL,CAAuBX,SAAvB,EArBuB,CAuBvB;;AACA,cAAI,KAAKvC,OAAT,EAAkB;AACd,iBAAKA,OAAL,CAAasC,eAAb,CAA6BC,SAA7B;AACH;;AAED,eAAKrD,UAAL,GAAkB,KAAlB,CA5BuB,CA8BvB;;AACA,eAAK6C,OAAL,CAAaC,OAAb,CAAsBC,IAAD,IAAU;AAC3BA,YAAAA,IAAI,CAACkB,MAAL,CAAYZ,SAAZ;AACH,WAFD;AAGH;AAED;AACJ;AACA;AACA;AACA;;;AACIE,QAAAA,YAAY,CAACF,SAAD,EAAY;AACpB,eAAKpD,UAAL,IAAmBoD,SAAnB;;AACA,cAAI,KAAKnD,SAAT,EAAoB;AAChB,gBAAI,KAAKD,UAAL,GAAkB,KAAKE,YAA3B,EAAyC;AACrC,mBAAK+D,MAAL,GAAc,IAAd;AACA,mBAAKhE,SAAL,GAAiB,KAAjB;AACA,mBAAKD,UAAL,GAAkB,CAAlB;AACA,mBAAKG,WAAL,GAAmB,IAAnB;AACA,mBAAKsB,IAAL,CAAUC,YAAV,CAAuBpE,SAAvB,EAAkC4G,OAAlC,GAA4C,GAA5C;AACA,mBAAKnB,WAAL;AACH;;AACD,mBAAO,IAAP;AACH;;AACD,iBAAO,KAAP;AACH;AAED;AACJ;AACA;AACA;;;AACIgB,QAAAA,iBAAiB,CAACX,SAAD,EAAY;AACzB,eAAKnE,WAAL,CAAiB4D,OAAjB,CAAyB,CAAC5B,KAAD,EAAQkD,GAAR,KAAgB;AACrC,gBAAMC,IAAI,GAAG,KAAKjF,aAAL,CAAmBkF,GAAnB,CAAuBF,GAAvB,CAAb;;AACA,gBAAIC,IAAI,KAAK,IAAT,IAAiBA,IAAI,GAAG,CAA5B,EAA+B;AAC3B,mBAAKjF,aAAL,CAAmBmF,GAAnB,CAAuBH,GAAvB,EAA4BC,IAAI,GAAGhB,SAAnC;AACH,aAFD,MAEO;AACH,mBAAKmB,UAAL,CAAgBJ,GAAhB;AACH;AACJ,WAPD;AAQH;AAED;AACJ;AACA;AACA;;;AACIK,QAAAA,gBAAgB,CAACC,KAAD,EAAQ;AACpB,eAAK9F,YAAL,GAAoB8F,KAApB;;AACA,cAAI,KAAK9F,YAAL,CAAkB+F,MAAlB,GAA2B,CAA/B,EAAkC;AAC9B,iBAAKC,KAAL,IAAc,KAAKhG,YAAL,CAAkB,CAAlB,CAAd;AACA,iBAAKG,KAAL,GAAa,KAAK6F,KAAlB;AACA,iBAAKC,MAAL,IAAe,KAAKjG,YAAL,CAAkB,CAAlB,CAAf;AACA,iBAAKkD,UAAL,IAAmB,KAAKlD,YAAL,CAAkB,CAAlB,CAAnB;AACH;AACJ;AAED;AACJ;AACA;AACA;;;AACIkG,QAAAA,SAAS,GAAG;AACR,iBAAO,KAAKpG,MAAZ;AACH;AACD;AACJ;AACA;AACA;;;AACIqG,QAAAA,cAAc,CAACV,IAAD,EAAO;AACjB,eAAKnE,SAAL,GAAiB,IAAjB;AACA,eAAKC,YAAL,GAAoBkE,IAApB;AACA,eAAK3C,IAAL,CAAUC,YAAV,CAAuBpE,SAAvB,EAAkC4G,OAAlC,GAA4C,CAA5C;AACH;AAED;AACJ;AACA;AACA;;;AACIa,QAAAA,SAAS,GAAG;AACR,iBAAO,KAAK9E,SAAZ;AACH;AAED;AACJ;AACA;AACA;AACA;AACA;;;AACI+E,QAAAA,MAAM,CAACpB,CAAD,EAAIC,CAAJ,EAAOoB,SAAP,EAA0B;AAAA,cAAnBA,SAAmB;AAAnBA,YAAAA,SAAmB,GAAP,KAAO;AAAA;;AAC5B,eAAKxD,IAAL,CAAUyD,WAAV,CAAsBtB,CAAtB,EAAyBC,CAAzB;AACA,eAAK9D,UAAL,GAAkBkF,SAAlB;AACH;AAED;AACJ;AACA;AACA;;;AACiB,YAATA,SAAS,GAAG;AACZ,iBAAO,KAAKlF,UAAZ;AACH;AAED;AACJ;AACA;AACA;;;AACIoF,QAAAA,MAAM,GAAG;AACL,iBAAO1H,IAAI,CAAC2H,IAAZ;AACH;AAED;AACJ;AACA;AACA;;;AACIC,QAAAA,QAAQ,GAAG;AACP,iBAAO,CAAP;AACH;AAED;AACJ;AACA;AACA;;;AACIC,QAAAA,WAAW,GAAG;AACV,iBAAO,KAAKX,KAAL,IAAc,KAAK7F,KAA1B;AACH;AAED;AACJ;AACA;AACA;;;AACIyG,QAAAA,QAAQ,GAAG;AACP,iBAAO,KAAKzG,KAAZ;AACH;AAED;AACJ;AACA;AACA;;;AACa,YAAL6F,KAAK,GAAG;AACR,iBAAO,KAAK/F,MAAZ;AACH;AAED;AACJ;AACA;AACA;;;AACa,YAAL+F,KAAK,CAACa,EAAD,EAAK;AACV,eAAK5G,MAAL,GAAc4G,EAAd;AACH;AAED;AACJ;AACA;AACA;;;AACIC,QAAAA,YAAY,GAAG;AACX,iBAAO,KAAKd,KAAL,GAAa,KAAK7F,KAAzB;AACH;AAED;AACJ;AACA;AACA;;;AACI4G,QAAAA,QAAQ,CAACC,KAAD,EAAQ;AACZ,eAAKhB,KAAL,IAAcgB,KAAd;;AACA,cAAI,KAAKhB,KAAL,GAAa,CAAjB,EAAoB;AAChB,iBAAKA,KAAL,GAAa,CAAb;AACH,WAFD,MAEO,IAAI,KAAKA,KAAL,GAAa,KAAK7F,KAAtB,EAA6B;AAChC,iBAAK6F,KAAL,GAAa,KAAK7F,KAAlB;AACH;;AACD,eAAK8G,OAAL;AACH;AAED;AACJ;AACA;AACA;;;AACIA,QAAAA,OAAO,GAAG;AACN,cAAI,KAAKjB,KAAL,IAAc,CAAlB,EAAqB;AACjB,iBAAKkB,GAAL,CAAS;AAAA;AAAA,sCAASC,gBAAT,CAA0BC,GAAnC;AACA,mBAAO,IAAP;AACH;;AACD,iBAAO,KAAP;AACH;AAED;AACJ;AACA;AACA;;;AACIC,QAAAA,QAAQ,CAACR,EAAD,EAAK;AACT,eAAKb,KAAL,GAAaa,EAAb;AACH;AAED;AACJ;AACA;AACA;;;AACIS,QAAAA,QAAQ,GAAG;AACP,iBAAO,KAAKtB,KAAZ;AACH;AAED;AACJ;AACA;AACA;;;AACIuB,QAAAA,YAAY,GAAG;AACX,cAAI,KAAKC,UAAL,GAAkB,CAAlB,IAAuB,CAAC,KAAKlG,SAAjC,EAA4C;AACxC,gBAAI,KAAKH,OAAT,EAAkB;AACd,qBAAO,IAAP;AACH;;AACD,gBAAIsG,QAAQ,GAAG,KAAK3E,IAAL,CAAU2E,QAAzB;;AACA,gBAAI,KAAKpF,UAAL,KAAoB,IAAxB,EAA8B;AAC1BoF,cAAAA,QAAQ,GAAG,KAAK3E,IAAL,CAAUC,YAAV,CAAuBnE,WAAvB,EAAoC8I,qBAApC,CAA0D3I,IAAI,CAAC0H,IAA/D,CAAX;AACAgB,cAAAA,QAAQ,GAAG;AAAA;AAAA,8CAAYE,EAAZ,CAAeC,eAAf,CAA+B7E,YAA/B,CAA4CnE,WAA5C,EAAyDiJ,oBAAzD,CAA8EJ,QAA9E,CAAX;AACH;;AACD,mBACIA,QAAQ,CAACvC,CAAT,GAAa,CAAb,IACAuC,QAAQ,CAACxC,CAAT,GAAa,CAAC;AAAA;AAAA,wCAAU6C,UAAV,CAAqB7C,CADnC,IAEAwC,QAAQ,CAACxC,CAAT,GAAa;AAAA;AAAA,wCAAU6C,UAAV,CAAqB7C,CAHtC;AAKH;;AACD,iBAAO,CAAC,KAAK3D,SAAb;AACH;AAED;AACJ;AACA;AACA;;;AACIyG,QAAAA,UAAU,CAACzF,KAAD,EAAQ;AACd,eAAKnB,OAAL,GAAemB,KAAf;AACH;AACD;AACJ;AACA;AACA;AACA;;;AACI0F,QAAAA,cAAc,CAACC,SAAD,EAAY;AACtB,iBAAO,KAAK5H,MAAL,CAAY4H,SAAZ,KAA0B,CAAjC;AACH;AAED;AACJ;AACA;AACA;AACA;AACA;;;AACIC,QAAAA,OAAO,CAACC,QAAD,EAAWC,MAAX,EAAmBC,UAAnB,EAA+B;AAClC,eAAK/H,WAAL,CAAiBqF,GAAjB,CAAqBwC,QAArB,EAA+B,IAA/B;;AACA,kBAAQA,QAAR;AACI,iBAAK;AAAA;AAAA,sCAAStD,SAAT,CAAmByD,GAAxB;AACI,mBAAK9H,aAAL,CAAmBmF,GAAnB,CAAuBwC,QAAvB,EAAiCC,MAAM,CAAC,CAAD,CAAvC;;AACA;;AACJ,iBAAK;AAAA;AAAA,sCAASvD,SAAT,CAAmBC,IAAxB;AACI,mBAAKlE,WAAL,GAAmByH,UAAnB;;AACA,mBAAK7H,aAAL,CAAmBmF,GAAnB,CAAuBwC,QAAvB,EAAiCC,MAAM,CAAC,CAAD,CAAvC;;AACA,mBAAK3H,WAAL,GAAmB2H,MAAM,CAAC,CAAD,CAAN,KAAc,CAAd,GAAkB,KAAK3H,WAAvB,GAAqC2H,MAAM,CAAC,CAAD,CAA9D;AACA,mBAAK1H,WAAL,GAAmB0H,MAAM,CAAC,CAAD,CAAzB;AACA;;AACJ,iBAAK;AAAA;AAAA,sCAASvD,SAAT,CAAmB0D,KAAxB;AACI,kBAAMC,KAAK,GAAG,KAAK3H,aAAL,CAAmB6E,GAAnB,CAAuByC,QAAvB,KAAoC,CAAlD;;AACA,mBAAKtH,aAAL,CAAmB8E,GAAnB,CAAuBwC,QAAvB,EAAiCK,KAAK,GAAG,CAAzC;;AACA;AAbR;;AAeA,eAAKC,SAAL,CAAeN,QAAf;AACH;AAED;AACJ;AACA;AACA;;;AACIvC,QAAAA,UAAU,CAACuC,QAAD,EAAW;AACjB,eAAK7H,WAAL,CAAiBoI,MAAjB,CAAwBP,QAAxB;;AACA,eAAK3H,aAAL,CAAmBkI,MAAnB,CAA0BP,QAA1B;;AACA,cAAIQ,YAAY,GAAG,IAAnB;;AACA,cAAIR,QAAQ,KAAK;AAAA;AAAA,oCAAStD,SAAT,CAAmB0D,KAApC,EAA2C;AACvC,gBAAMC,KAAK,GAAG,KAAK3H,aAAL,CAAmB6E,GAAnB,CAAuByC,QAAvB,CAAd;;AACA,gBAAIK,KAAK,GAAG,CAAZ,EAAe;AACX,mBAAK3H,aAAL,CAAmB8E,GAAnB,CAAuBwC,QAAvB,EAAiCK,KAAK,GAAG,CAAzC;;AACA,kBAAIA,KAAK,GAAG,CAAR,GAAY,CAAhB,EAAmB;AACfG,gBAAAA,YAAY,GAAG,KAAf;AACH;AACJ;AACJ;;AACD,cAAIA,YAAJ,EAAkB;AACd,iBAAKC,YAAL,CAAkBT,QAAlB;AACH;AACJ;AAED;AACJ;AACA;;;AACI/E,QAAAA,mBAAmB,GAAG;AAClB,eAAKvC,aAAL,CAAmBsC,KAAnB;;AACA,cAAI,KAAKlB,UAAT,EAAqB;AACjB,iBAAKA,UAAL,CAAgBmB,mBAAhB;AACH;AACJ;AAED;AACJ;AACA;AACA;;;AACIwF,QAAAA,YAAY,CAACT,QAAD,EAAW;AACnB,cAAI,KAAKlG,UAAT,EAAqB;AACjB,iBAAKA,UAAL,CAAgB2D,UAAhB,CAA2BuC,QAA3B;AACH;AACJ;AAED;AACJ;AACA;AACA;;;AACIM,QAAAA,SAAS,CAACN,QAAD,EAAW;AAChB,cAAI,KAAKlG,UAAL,IAAmB,KAAKnC,MAA5B,EAAoC;AAChC,gBAAM+I,QAAQ,GAAG,KAAK/I,MAAL,CAAYgJ,iBAAZ,CAA8BX,QAA9B,CAAjB;;AACA,oBAAQA,QAAR;AACI,mBAAK;AAAA;AAAA,wCAAStD,SAAT,CAAmByD,GAAxB;AACI,oBAAIO,QAAQ,IAAIA,QAAQ,CAAC9C,MAAT,GAAkB,CAAlC,EAAqC;AACjC,uBAAK9D,UAAL,CAAgBiG,OAAhB,CAAwBC,QAAxB,EAAkCU,QAAlC;AACH;;AACD;;AACJ,mBAAK;AAAA;AAAA,wCAAShE,SAAT,CAAmBC,IAAxB;AACI,oBAAMiE,UAAU,GAAG,MAAM,KAAKjJ,MAAL,CAAYkJ,OAAZ,CAAoB,CAApB,CAAzB;;AACA,oBAAI,CAACH,QAAD,IAAaE,UAAjB,EAA6B;AACzBF,kBAAAA,QAAQ,CAAC,CAAD,CAAR,CAAY,CAAZ,IAAiBE,UAAjB;AACH;;AACD,qBAAK9G,UAAL,CAAgBiG,OAAhB,CAAwBC,QAAxB,EAAkCU,QAAlC;AACA;;AACJ,mBAAK;AAAA;AAAA,wCAAShE,SAAT,CAAmB0D,KAAxB;AACI,qBAAKtG,UAAL,CAAgBiG,OAAhB,CAAwBC,QAAxB,EAAkCU,QAAlC;AACA;AAfR;AAiBH;AACJ;AAED;AACJ;AACA;AACA;AACA;;;AACIjE,QAAAA,WAAW,CAACuD,QAAD,EAAW;AAClB,iBAAO,KAAK7H,WAAL,CAAiBoF,GAAjB,CAAqByC,QAArB,CAAP;AACH;AAED;AACJ;AACA;AACA;AACA;AACA;;;AACI5E,QAAAA,cAAc,CAACC,QAAD,EAAWyF,MAAX,EAA0BC,MAA1B,EAA6C;AAAA,cAAlCD,MAAkC;AAAlCA,YAAAA,MAAkC,GAAzB,IAAyB;AAAA;;AAAA,cAAnBC,MAAmB;AAAnBA,YAAAA,MAAmB,GAAVrK,EAAE,CAAC,CAAD,EAAI,CAAJ,CAAQ;AAAA;;AACvD,cAAI,KAAKiC,WAAT,EAAsB;AAClB,iBAAKA,WAAL,CAAiBqI,OAAjB,CAAyB3F,QAAzB,EAAmCyF,MAAnC,EAA2CC,MAA3C;AACH;AACJ;AAED;AACJ;AACA;AACA;;;AACIE,QAAAA,gBAAgB,CAACH,MAAD,EAAS;AACrB,cAAI,KAAKnI,WAAT,EAAsB;AAClB,iBAAKA,WAAL,CAAiBuI,UAAjB,GAA8BJ,MAA9B;AACH;AACJ;AAED;AACJ;AACA;AACA;;;AACIK,QAAAA,gBAAgB,CAACJ,MAAD,EAAS;AACrB,cAAI,KAAKpI,WAAT,EAAsB;AAClB,iBAAKA,WAAL,CAAiByI,YAAjB,GAAgCL,MAAhC;AACH;AACJ;AAED;AACJ;AACA;AACA;AACA;;;AACIM,QAAAA,eAAe,CAACC,UAAD,EAAaC,WAAb,EAA0B;AACrC,cAAI,KAAK5I,WAAL,IAAoB,KAAKhB,MAA7B,EAAqC;AACjC,iBAAKgB,WAAL,CAAiB6I,OAAjB,CACI,KAAK7J,MAAL,CAAY0D,QAAZ,CAAqB,CAArB,IAA0BiG,UAD9B,EAEI,KAAK3J,MAAL,CAAY0D,QAAZ,CAAqB,CAArB,IAA0BkG,WAF9B;AAIH;AACJ;AAED;AACJ;AACA;AACA;;;AACIE,QAAAA,SAAS,CAACpG,QAAD,EAAW;AAChB,cAAI,KAAK8B,MAAL,IAAe,CAAC,KAAKZ,MAAzB,EAAiC;AAC7B,gBAAIlB,QAAQ,CAACyF,MAAT;AAAA;AAAA,iCAAJ,EAAuC;AACnC,kBAAMhD,MAAM,GAAGzC,QAAQ,CAACyF,MAAT,CAAgBY,SAAhB,CAA0B,IAA1B,CAAf;AACA;AAAA;AAAA,sCAAQ9E,iBAAR,CAA0B+E,mBAA1B,CACI,KAAKhJ,WADT,EAEI0C,QAAQ,CAACyF,MAFb,EAGIhD,MAHJ,EAII;AAAEhB,gBAAAA,CAAC,EAAE,CAAL;AAAQC,gBAAAA,CAAC,EAAE;AAAX,eAJJ;AAMA,kBAAM6E,WAAW,GAAGC,IAAI,CAACC,GAAL,CAAShE,MAAM,GAAG,EAAlB,EAAsBA,MAAM,GAAG,KAAK7F,OAApC,IAA+C;AAAA;AAAA,sCAAQ8J,gBAAR,CAAyBC,aAA5F;;AACA,kBAAI,KAAKhF,IAAL,CAAU4E,WAAV,CAAJ,EAA4B,CACxB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACH;AACJ,aA3BD,MA2BO,IAAIvG,QAAQ,CAACyF,MAAT;AAAA;AAAA,2CAAwC,KAAKzG,YAAL,KAAsB;AAAA;AAAA,sCAASxB,iBAAT,CAA2BC,IAAzF,IAAiG,KAAKC,YAA1G,EAAwH;AAC3H,mBAAKgG,GAAL,CAAS;AAAA;AAAA,wCAASC,gBAAT,CAA0BC,GAAnC;AACH;AACJ;AACJ;AACD;AACJ;AACA;AACA;AACA;;;AACIjC,QAAAA,IAAI,CAACiF,MAAD,EAAS;AACT,cAAI,CAAC,KAAK9E,MAAN,IAAgB,KAAKZ,MAArB,IAA+B,CAAC,KAAK6C,YAAL,EAApC,EAAyD;AACrD,mBAAO,KAAP;AACH;;AACD,eAAKR,QAAL,CAAc,CAACqD,MAAf;;AACA,eAAKC,cAAL,CAAoBD,MAApB;;AACA,eAAKE,MAAL;AACA,iBAAO,IAAP;AACH;AAED;AACJ;AACA;AACA;;;AACID,QAAAA,cAAc,CAACD,MAAD,EAAS;AACnB,cAAI,KAAKzI,QAAT,EAAmB;AACf,iBAAK4I,SAAL,CAAeH,MAAf;AACH;AACJ;AAED;AACJ;AACA;AACA;AACA;;;AACIG,QAAAA,SAAS,CAACH,MAAD,EAAa3C,QAAb,EAAmC,CACxC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AARwC,cAAlC2C,MAAkC;AAAlCA,YAAAA,MAAkC,GAAzB,CAAyB;AAAA;;AAAA,cAAtB3C,QAAsB;AAAtBA,YAAAA,QAAsB,GAAX3I,IAAI,CAAC2H,IAAM;AAAA;AAS3C;AAED;AACJ;AACA;;;AACI6D,QAAAA,MAAM,GAAG,CAAG;AAEZ;AACJ;AACA;AACA;;;AACIpD,QAAAA,GAAG,CAACsD,WAAD,EAAc;AACb,cAAI,KAAK9F,MAAT,EAAiB;AACb;AACH;;AACD,eAAKA,MAAL,GAAc,IAAd;AACA,eAAKjC,WAAL,GAAmB,KAAnB;;AAEA,cAAI,KAAK3B,WAAL,IAAoB,KAAKA,WAAL,CAAiBmI,MAAzC,EAAiD;AAC7C;AAAA;AAAA,oCAAQlE,iBAAR,CAA0B0F,iBAA1B,CAA4C/B,MAA5C,CAAmD,KAAK5H,WAAL,CAAiBmI,MAAjB,CAAwByB,QAA3E;AACH;;AAED,cAAI,KAAKxI,OAAT,EAAkB;AACd,iBAAKA,OAAL,CAAagF,GAAb;AACH;;AAED,eAAKyD,KAAL,CAAWH,WAAX;AACH;AAED;AACJ;AACA;AACA;;;AACIG,QAAAA,KAAK,CAACH,WAAD,EAAc;AACf,cAAIA,WAAW,KAAK;AAAA;AAAA,oCAASrD,gBAAT,CAA0BC,GAA9C,EAAmD;AAC/C;AACA;AAAA;AAAA,oCAAQwD,gBAAR,CAAyBC,WAAzB;AACA;AAAA;AAAA,oCAAQD,gBAAR,CAAyBE,YAAzB;;AAEA,gBAAI,KAAK3I,SAAT,EAAoB;AAChB,mBAAK,IAAM4I,MAAX,IAAqB,KAAK3I,OAA1B,EAAmC;AAC/B2I,gBAAAA,MAAM,CAACC,SAAP;AACH;AACJ;;AAED,gBAAI,CAAC,KAAKzI,MAAV,EAAkB,CACd;AACA;AACA;AACH;AACJ,WAhBD,MAgBO;AACH;AAAA;AAAA,oCAAQ0I,YAAR,CAAqBC,aAArB,GAAqC,KAArC;AACA;AAAA;AAAA,oCAAQD,YAAR,CAAqBE,cAArB,GAAsC,KAAtC;AACH;;AAED,eAAK/I,OAAL,GAAe,EAAf;;AAEA,kBAAQ,KAAKuB,IAAb;AACI,iBAAK;AAAA;AAAA,sCAASyH,SAAT,CAAmBC,QAAxB;AACA,iBAAK;AAAA;AAAA,sCAASD,SAAT,CAAmBE,YAAxB;AACI;;AACJ;AACI,kBAAI7D,QAAQ,GAAG,KAAK3E,IAAL,CAAU2E,QAAzB;;AACA,kBAAI,KAAKD,UAAL,GAAkB,CAAtB,EAAyB;AACrB,oBACI,KAAK7D,IAAL,KAAc;AAAA;AAAA,0CAASyH,SAAT,CAAmBG,MAAjC,IACA,KAAK5H,IAAL,KAAc;AAAA;AAAA,0CAASyH,SAAT,CAAmBI,OADjC,IAEA,KAAK7H,IAAL,KAAc;AAAA;AAAA,0CAASyH,SAAT,CAAmBK,YAHrC,EAIE;AACEhE,kBAAAA,QAAQ,GAAG,KAAK3E,IAAL,CAAUC,YAAV,CAAuBnE,WAAvB,EAAoC8I,qBAApC,CAA0D3I,IAAI,CAAC0H,IAA/D,CAAX;AACAgB,kBAAAA,QAAQ,GAAG;AAAA;AAAA,kDAAYE,EAAZ,CAAeC,eAAf,CAA+B7E,YAA/B,CAA4CnE,WAA5C,EAAyDiJ,oBAAzD,CAA8EJ,QAA9E,CAAX;AACH;AACJ,eATD,MASO;AACH,oBAAMiE,aAAa,GAAG,KAAK5I,IAAL,CAAUC,YAAV,CAAuBnE,WAAvB,EAAoC8I,qBAApC,CAA0D3I,IAAI,CAAC0H,IAA/D,CAAtB;AACAgB,gBAAAA,QAAQ,GAAG;AAAA;AAAA,gDAAYE,EAAZ,CAAeC,eAAf,CAA+B7E,YAA/B,CAA4CnE,WAA5C,EAAyDiJ,oBAAzD,CAA8E6D,aAA9E,CAAX;AACH;;AACD;AAAA;AAAA,sCAAQT,YAAR,CAAqBU,iBAArB,CAAuClE,QAAvC;AAnBR;AAqBH;AAED;AACJ;AACA;;;AACImE,QAAAA,UAAU,GAAG,CAAG;AAEhB;AACJ;AACA;;;AACIC,QAAAA,cAAc,GAAG;AACb,cAAI,KAAK3J,OAAT,EAAkB;AACd,iBAAKA,OAAL,CAAa2J,cAAb;AACH;AACJ;AAED;AACJ;AACA;;;AACIC,QAAAA,WAAW,GAAG;AACV,cAAI,CAAC,KAAKhM,MAAV,EAAkB;AACd;AACH;;AAED,cAAI,KAAKA,MAAL,CAAYiM,UAAZ,GAAyB,CAA7B,EAAgC,CAC5B;AACH;;AAED,cAAMC,WAAW,GAAG,CAAC,GAAG,KAAKlM,MAAL,CAAYmM,UAAhB,CAApB;;AACA,eAAK,IAAIC,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAG,KAAKpM,MAAL,CAAYqM,UAAhC,EAA4CD,CAAC,EAA7C,EAAiD;AAC7C,gBAAME,KAAK,GAAG;AAAA;AAAA,gCAAMC,UAAN,CAAiB,CAAjB,EAAoBL,WAAW,CAACjG,MAAZ,GAAqB,CAAzC,CAAd;AACA,gBAAMuG,KAAK,GAAGN,WAAW,CAACO,MAAZ,CAAmBH,KAAnB,EAA0B,CAA1B,EAA6B,CAA7B,CAAd;AAEA,gBAAMI,UAAU,GAAG;AACfvH,cAAAA,CAAC,EAAEwH,MAAM,CAACH,KAAK,CAAC,CAAD,CAAN,CAAN,GAAmB,KAAKxJ,IAAL,CAAU4J,KAAV,CAAgBzH,CADvB;AAEfC,cAAAA,CAAC,EAAEoH,KAAK,CAAC,CAAD,CAAL,GAAW,KAAKxJ,IAAL,CAAU4J,KAAV,CAAgBxH,CAFf;AAGfwH,cAAAA,KAAK,EAAE;AAAA;AAAA,sCAAQC,aAAR,CAAsBC,QAAtB,KAAmC;AAAA;AAAA,kCAAMP,UAAN,CAAiBC,KAAK,CAAC,CAAD,CAAtB,EAA2BA,KAAK,CAAC,CAAD,CAAhC,CAAnC,GAA0E,GAHlE;AAIfO,cAAAA,KAAK,EAAEP,KAAK,CAAC,CAAD,CAAL,GAAW,CAAX,GAAe;AAAA;AAAA,kCAAMD,UAAN,CAAiB,CAAjB,EAAoB,GAApB,CAAf,GAA0CC,KAAK,CAAC,CAAD;AAJvC,aAAnB;AAOA,gBAAMQ,QAAQ,GAAGZ,CAAC,KAAK,KAAKpM,MAAL,CAAYqM,UAAZ,GAAyB,CAA/B,GAAmC,MAAM,KAAKY,YAAL,EAAzC,GAA+D,IAAhF;AAEA,iBAAKC,YAAL,CAAkB,MAAM,CACpB;AACH,aAFD,EAEG,KAAKlN,MAAL,CAAYmN,cAAZ,CAA2Bf,CAA3B,CAFH;AAGH;;AAED,eAAKc,YAAL,CAAkB,MAAM;AACpB;AACA,iBAAK5J,mBAAL;AACH,WAHD,EAGG,GAHH;AAIH;AAED;AACJ;AACA;;;AACI2J,QAAAA,YAAY,GAAG,CAAG;AAElB;AACJ;AACA;AACA;;;AACIG,QAAAA,SAAS,CAACnC,MAAD,EAAS;AACd,cAAI,KAAK5I,SAAL,IAAkB,KAAKC,OAA3B,EAAoC;AAChC,iBAAKA,OAAL,CAAamC,IAAb,CAAkBwG,MAAlB;AACH;AACJ;AAED;AACJ;AACA;AACA;;;AACIoC,QAAAA,YAAY,CAACpC,MAAD,EAAS;AACjB,cAAI,KAAK5I,SAAL,IAAkB,KAAKC,OAA3B,EAAoC;AAChC,gBAAMgK,KAAK,GAAG,KAAKhK,OAAL,CAAagL,OAAb,CAAqBrC,MAArB,CAAd;;AACA,gBAAIqB,KAAK,IAAI,CAAb,EAAgB;AACZ,mBAAKhK,OAAL,CAAamK,MAAb,CAAoBH,KAApB,EAA2B,CAA3B;AACH;AACJ;AACJ;;AAv1B8C,O", "sourcesContent": ["import { _decorator, UIOpacity, UITransform, v2, Vec2, Vec3 } from \"cc\";\r\nimport EnemyEntity from \"./EnemyEntity\";\r\nimport GameEnum from \"../../../const/GameEnum\";\r\nimport { ColliderComp } from \"../../base/ColliderComp\";\r\nimport EnemyEffectComp from \"./EnemyEffectComp\";\r\nimport EnemyAttrComponent from \"./EnemyAttrComponent\";\r\nimport { Tools } from \"../../../utils/Tools\";\r\nimport BattleLayer from \"../../layer/BattleLayer\";\r\nimport { GameConst } from \"../../../const/GameConst\";\r\nimport Bullet from \"../../bullet/Bullet\";\r\nimport { GameIns } from \"../../../GameIns\";\r\nimport { MainPlane } from \"../mainPlane/MainPlane\";\r\nimport EnemyEffectLayer from \"./EnemyEffectLayer\";\r\n\r\n\r\nconst { ccclass, property } = _decorator;\r\n\r\n@ccclass\r\nexport default class EnemyBase extends EnemyEntity {\r\n\r\n    uiData = null;\r\n    scaleType = -1;\r\n    propertyRate = [];\r\n    _curHp = 0;\r\n    exp = 0;\r\n    maxHp = 0;\r\n    defence = 0;\r\n    resist = {};\r\n    hurtBuffMap = new Map();\r\n    _hurtBuffTime = new Map();\r\n    _fireDemage = 0;\r\n    _fireHurtCd = 0;\r\n    _fireHurtTime = 0;\r\n    _isFireCirt = false;\r\n    _buffCountArr = new Map();\r\n    collideComp:ColliderComp = null;\r\n    _collideLevel = GameEnum.EnemyCollideLevel.Main;\r\n    bCollideDead = false;\r\n    damaged = false;\r\n    _isTracked = false;\r\n    _countTime = 0;\r\n    _bStandBy = false;\r\n    _standByTime = 0;\r\n    _standByEnd = false;\r\n    _lootArr = [];\r\n    _lootItemArr = [];\r\n    _curLoot = null;\r\n    _lootHp = 0;\r\n    _lootNeedHp = 0;\r\n    _lootHpUnit = 0;\r\n    _itemParent = null;\r\n    _isItem = false;\r\n    effectComp = null;\r\n    attrCom = null;\r\n    dieBullet = false;\r\n    bullets = [];\r\n\r\n    get itemParent() {\r\n        return this._itemParent;\r\n    }\r\n    set itemParent(value) {\r\n        this._itemParent = value;\r\n    }\r\n    get isItem() {\r\n        return this._isItem;\r\n    }\r\n    set isItem(value) {\r\n        this._isItem = value;\r\n    }\r\n    get collideLevel() {\r\n        return this._collideLevel;\r\n    }\r\n    set collideLevel(value) {\r\n        this._collideLevel = value;\r\n    }\r\n\r\n    /**\r\n     * 获取碰撞是否可用\r\n     * @returns {boolean} 是否可用\r\n     */\r\n    get collideAble() {\r\n        return this.collideComp && this.collideComp.enabled;\r\n    }\r\n\r\n    /**\r\n     * 设置碰撞是否可用\r\n     * @param {boolean} value 是否可用\r\n     */\r\n    set collideAble(value) {\r\n        if (this.collideComp) {\r\n            this.collideComp.enabled = value;\r\n        }\r\n    }\r\n    /**\r\n  * 预加载敌人组件\r\n  */\r\n    preLoad() {\r\n        // 添加碰撞组件并初始化\r\n        this.collideComp = this.addComp(ColliderComp, new ColliderComp());\r\n        this.collideComp.init(this);\r\n\r\n        // 获取敌人效果组件\r\n        this.effectComp = this.node.getComponent(EnemyEffectComp);\r\n\r\n        // 添加属性组件\r\n        this.attrCom = Tools.addScript(this.node, EnemyAttrComponent);\r\n    }\r\n\r\n    /**\r\n     * 重置敌人状态\r\n     */\r\n    reset() {\r\n        super.reset();\r\n        this.uiData = null;\r\n        this.scaleType = -1;\r\n        this._curHp = 0;\r\n        this.maxHp = 0;\r\n        this.exp = 0;\r\n        this.collideAtk = 0;\r\n        this.hurtBuffMap.clear();\r\n        this._hurtBuffTime.clear();\r\n        this.resist = {};\r\n        this._isTracked = false;\r\n        this._countTime = 0;\r\n        this._bStandBy = false;\r\n        this._standByTime = 0;\r\n        this._collideLevel = GameEnum.EnemyCollideLevel.Main;\r\n        this.damaged = false;\r\n        this._lootArr = [];\r\n        this._lootItemArr = [];\r\n        this._curLoot = null;\r\n        this._lootHp = 0;\r\n        this._lootNeedHp = 0;\r\n        this._lootHpUnit = 0;\r\n        this._itemParent = this;\r\n        this._isItem = false;\r\n        this.removeAllBuffEffect();\r\n        this.dieBullet = false;\r\n        this.bullets = [];\r\n    }\r\n\r\n    /**\r\n     * 设置 UI 数据\r\n     * @param {Object} data UI 数据\r\n     */\r\n    setUIData(data) {\r\n        this.uiData = data;\r\n        if (this.uiData) {\r\n            this.setCollideData(this.uiData.collider);\r\n            this.initComps();\r\n        }\r\n    }\r\n\r\n    /**\r\n     * 设置缩放类型\r\n     * @param {number} type 缩放类型\r\n     */\r\n    setScaleType(type) {\r\n        this.scaleType = type;\r\n    }\r\n\r\n    /**\r\n     * 获取缩放类型\r\n     * @returns {number} 缩放类型\r\n     */\r\n    getScaleType() {\r\n        return this.scaleType;\r\n    }\r\n\r\n    /**\r\n     * 初始化属性\r\n     * @param {string} attr 属性字符串\r\n     */\r\n    initAttr(attr) {\r\n        this.attrCom.init(this, attr || \"\");\r\n    }\r\n\r\n    /**\r\n     * 检查是否具有指定属性\r\n     * @param {string} attr 属性名称\r\n     * @returns {boolean} 是否具有该属性\r\n     */\r\n    hasAttribution(attr) {\r\n        return this.attrCom && this.attrCom.hasAttribution(attr);\r\n    }\r\n\r\n    /**\r\n     * 设置经验值\r\n     * @param {number} exp 经验值\r\n     */\r\n    setExp(exp) {\r\n        this.exp = exp;\r\n    }\r\n\r\n    /**\r\n     * 初始化组件\r\n     */\r\n    initComps() {\r\n        this.m_comps.forEach((comp) => {\r\n            comp.init(this);\r\n        });\r\n    }\r\n\r\n    /**\r\n     * 开始战斗\r\n     */\r\n    startBattle() {\r\n        this.collideAble = true;\r\n    }\r\n\r\n    /**\r\n     * 添加掉落物\r\n     * @param {Object} loot 掉落物\r\n     */\r\n    addLoot(loot) {\r\n        this._lootArr.push(loot);\r\n    }\r\n\r\n    /**\r\n     * 更新游戏逻辑\r\n     * @param {number} deltaTime 帧间隔时间\r\n     */\r\n    updateGameLogic(deltaTime) {\r\n        if (this.isDead || this.checkStandby(deltaTime)) {\r\n            return;\r\n        }\r\n\r\n        // 处理火焰伤害 Buff\r\n        if (this.hasHurtBuff(GameEnum.EnemyBuff.Fire)) {\r\n            this._fireHurtTime += deltaTime;\r\n            if (this._fireHurtTime >= this._fireHurtCd) {\r\n                this._fireHurtTime = 0;\r\n                GameIns.hurtEffectManager.createFollowHurtNum(\r\n                    this.collideComp,\r\n                    this._isFireCirt,\r\n                    this._fireDemage,\r\n                    { x: 0, y: 0 }\r\n                );\r\n                this.hurt(this._fireDemage);\r\n            }\r\n        }\r\n\r\n        // 更新技能抗性\r\n        this.updateSkillResist(deltaTime);\r\n\r\n        // 更新属性组件\r\n        if (this.attrCom) {\r\n            this.attrCom.updateGameLogic(deltaTime);\r\n        }\r\n\r\n        this._isTracked = false;\r\n\r\n        // 更新所有组件\r\n        this.m_comps.forEach((comp) => {\r\n            comp.update(deltaTime);\r\n        });\r\n    }\r\n\r\n    /**\r\n     * 检查待机状态\r\n     * @param {number} deltaTime 帧间隔时间\r\n     * @returns {boolean} 是否处于待机状态\r\n     */\r\n    checkStandby(deltaTime) {\r\n        this._countTime += deltaTime;\r\n        if (this._bStandBy) {\r\n            if (this._countTime > this._standByTime) {\r\n                this.active = true;\r\n                this._bStandBy = false;\r\n                this._countTime = 0;\r\n                this._standByEnd = true;\r\n                this.node.getComponent(UIOpacity).opacity = 255;\r\n                this.startBattle();\r\n            }\r\n            return true;\r\n        }\r\n        return false;\r\n    }\r\n\r\n    /**\r\n     * 更新技能抗性\r\n     * @param {number} deltaTime 帧间隔时间\r\n     */\r\n    updateSkillResist(deltaTime) {\r\n        this.hurtBuffMap.forEach((value, key) => {\r\n            const time = this._hurtBuffTime.get(key);\r\n            if (time !== null && time > 0) {\r\n                this._hurtBuffTime.set(key, time - deltaTime);\r\n            } else {\r\n                this.removeBuff(key);\r\n            }\r\n        });\r\n    }\r\n\r\n    /**\r\n     * 初始化属性倍率\r\n     * @param {Array<number>} rates 属性倍率数组\r\n     */\r\n    initPropertyRate(rates) {\r\n        this.propertyRate = rates;\r\n        if (this.propertyRate.length > 2) {\r\n            this.curHp *= this.propertyRate[0];\r\n            this.maxHp = this.curHp;\r\n            this.attack *= this.propertyRate[1];\r\n            this.collideAtk *= this.propertyRate[2];\r\n        }\r\n    }\r\n\r\n    /**\r\n     * 获取 UI 数据\r\n     * @returns {Object} UI 数据\r\n     */\r\n    getUIData() {\r\n        return this.uiData;\r\n    }\r\n    /**\r\n * 设置待机时间\r\n * @param {number} time 待机时间\r\n */\r\n    setStandByTime(time) {\r\n        this._bStandBy = true;\r\n        this._standByTime = time;\r\n        this.node.getComponent(UIOpacity).opacity = 0;\r\n    }\r\n\r\n    /**\r\n     * 检查是否处于待机状态\r\n     * @returns {boolean} 是否待机\r\n     */\r\n    isStandBy() {\r\n        return this._bStandBy;\r\n    }\r\n\r\n    /**\r\n     * 设置敌人位置\r\n     * @param {number} x X 坐标\r\n     * @param {number} y Y 坐标\r\n     * @param {boolean} isTracked 是否被追踪\r\n     */\r\n    setPos(x, y, isTracked = false) {\r\n        this.node.setPosition(x, y);\r\n        this._isTracked = isTracked;\r\n    }\r\n\r\n    /**\r\n     * 获取是否被追踪\r\n     * @returns {boolean} 是否被追踪\r\n     */\r\n    get isTracked() {\r\n        return this._isTracked;\r\n    }\r\n\r\n    /**\r\n     * 获取方向\r\n     * @returns {Vec2} 方向向量\r\n     */\r\n    getDir() {\r\n        return Vec2.ZERO;\r\n    }\r\n\r\n    /**\r\n     * 获取角度\r\n     * @returns {number} 角度\r\n     */\r\n    getAngle() {\r\n        return 0;\r\n    }\r\n\r\n    /**\r\n     * 检查是否满血\r\n     * @returns {boolean} 是否满血\r\n     */\r\n    isFullBlood() {\r\n        return this.curHp >= this.maxHp;\r\n    }\r\n\r\n    /**\r\n     * 获取最大血量\r\n     * @returns {number} 最大血量\r\n     */\r\n    getMaxHp() {\r\n        return this.maxHp;\r\n    }\r\n\r\n    /**\r\n     * 获取当前血量\r\n     * @returns {number} 当前血量\r\n     */\r\n    get curHp() {\r\n        return this._curHp;\r\n    }\r\n\r\n    /**\r\n     * 设置当前血量\r\n     * @param {number} hp 当前血量\r\n     */\r\n    set curHp(hp) {\r\n        this._curHp = hp;\r\n    }\r\n\r\n    /**\r\n     * 获取血量百分比\r\n     * @returns {number} 血量百分比\r\n     */\r\n    getHpPercent() {\r\n        return this.curHp / this.maxHp;\r\n    }\r\n\r\n    /**\r\n     * 改变血量\r\n     * @param {number} delta 血量变化值\r\n     */\r\n    changeHp(delta) {\r\n        this.curHp += delta;\r\n        if (this.curHp < 0) {\r\n            this.curHp = 0;\r\n        } else if (this.curHp > this.maxHp) {\r\n            this.curHp = this.maxHp;\r\n        }\r\n        this.checkHp();\r\n    }\r\n\r\n    /**\r\n     * 检查血量是否为 0\r\n     * @returns {boolean} 是否死亡\r\n     */\r\n    checkHp() {\r\n        if (this.curHp <= 0) {\r\n            this.die(GameEnum.EnemyDestroyType.Die);\r\n            return true;\r\n        }\r\n        return false;\r\n    }\r\n\r\n    /**\r\n     * 设置当前血量\r\n     * @param {number} hp 当前血量\r\n     */\r\n    setCurHp(hp) {\r\n        this.curHp = hp;\r\n    }\r\n\r\n    /**\r\n     * 获取当前血量\r\n     * @returns {number} 当前血量\r\n     */\r\n    getCurHp() {\r\n        return this.curHp;\r\n    }\r\n\r\n    /**\r\n     * 检查敌人是否可以被伤害\r\n     * @returns {boolean} 是否可以被伤害\r\n     */\r\n    isDamageable() {\r\n        if (this.sceneLayer < 0 && !this._bStandBy) {\r\n            if (this.damaged) {\r\n                return true;\r\n            }\r\n            let position = this.node.position;\r\n            if (this.itemParent !== this) {\r\n                position = this.node.getComponent(UITransform).convertToWorldSpaceAR(Vec3.ZERO);\r\n                position = BattleLayer.me.enemyPlaneLayer.getComponent(UITransform).convertToNodeSpaceAR(position);\r\n            }\r\n            return (\r\n                position.y < 0 &&\r\n                position.x > -GameConst.ViewCenter.x &&\r\n                position.x < GameConst.ViewCenter.x\r\n            );\r\n        }\r\n        return !this._bStandBy;\r\n    }\r\n\r\n    /**\r\n     * 设置敌人是否已被伤害\r\n     * @param {boolean} value 是否已被伤害\r\n     */\r\n    setDamaged(value) {\r\n        this.damaged = value;\r\n    }\r\n    /**\r\n     * 获取技能抗性\r\n     * @param {number} skillType 技能类型\r\n     * @returns {number} 技能抗性\r\n     */\r\n    getSkillResist(skillType) {\r\n        return this.resist[skillType] || 1;\r\n    }\r\n\r\n    /**\r\n     * 添加 Buff\r\n     * @param {number} buffType Buff 类型\r\n     * @param {Array<number>} params Buff 参数\r\n     * @param {boolean} isCritical 是否暴击\r\n     */\r\n    addBuff(buffType, params, isCritical) {\r\n        this.hurtBuffMap.set(buffType, true);\r\n        switch (buffType) {\r\n            case GameEnum.EnemyBuff.Ice:\r\n                this._hurtBuffTime.set(buffType, params[0]);\r\n                break;\r\n            case GameEnum.EnemyBuff.Fire:\r\n                this._isFireCirt = isCritical;\r\n                this._hurtBuffTime.set(buffType, params[0]);\r\n                this._fireDemage = params[1] === 0 ? this._fireDemage : params[1];\r\n                this._fireHurtCd = params[2];\r\n                break;\r\n            case GameEnum.EnemyBuff.Treat:\r\n                const count = this._buffCountArr.get(buffType) || 0;\r\n                this._buffCountArr.set(buffType, count + 1);\r\n                break;\r\n        }\r\n        this.onAddBuff(buffType);\r\n    }\r\n\r\n    /**\r\n     * 移除 Buff\r\n     * @param {number} buffType Buff 类型\r\n     */\r\n    removeBuff(buffType) {\r\n        this.hurtBuffMap.delete(buffType);\r\n        this._hurtBuffTime.delete(buffType);\r\n        let shouldRemove = true;\r\n        if (buffType === GameEnum.EnemyBuff.Treat) {\r\n            const count = this._buffCountArr.get(buffType);\r\n            if (count > 0) {\r\n                this._buffCountArr.set(buffType, count - 1);\r\n                if (count - 1 > 0) {\r\n                    shouldRemove = false;\r\n                }\r\n            }\r\n        }\r\n        if (shouldRemove) {\r\n            this.onRemoveBuff(buffType);\r\n        }\r\n    }\r\n\r\n    /**\r\n     * 移除所有 Buff 效果\r\n     */\r\n    removeAllBuffEffect() {\r\n        this._buffCountArr.clear();\r\n        if (this.effectComp) {\r\n            this.effectComp.removeAllBuffEffect();\r\n        }\r\n    }\r\n\r\n    /**\r\n     * 当 Buff 被移除时的回调\r\n     * @param {number} buffType Buff 类型\r\n     */\r\n    onRemoveBuff(buffType) {\r\n        if (this.effectComp) {\r\n            this.effectComp.removeBuff(buffType);\r\n        }\r\n    }\r\n\r\n    /**\r\n     * 当 Buff 被添加时的回调\r\n     * @param {number} buffType Buff 类型\r\n     */\r\n    onAddBuff(buffType) {\r\n        if (this.effectComp && this.uiData) {\r\n            const buffData = this.uiData.skillResistUIDict[buffType];\r\n            switch (buffType) {\r\n                case GameEnum.EnemyBuff.Ice:\r\n                    if (buffData && buffData.length > 0) {\r\n                        this.effectComp.addBuff(buffType, buffData);\r\n                    }\r\n                    break;\r\n                case GameEnum.EnemyBuff.Fire:\r\n                    const fireDamage = 0.4 * this.uiData.hpParam[3];\r\n                    if (!buffData && fireDamage) {\r\n                        buffData[0][0] = fireDamage;\r\n                    }\r\n                    this.effectComp.addBuff(buffType, buffData);\r\n                    break;\r\n                case GameEnum.EnemyBuff.Treat:\r\n                    this.effectComp.addBuff(buffType, buffData);\r\n                    break;\r\n            }\r\n        }\r\n    }\r\n\r\n    /**\r\n     * 检查是否具有指定的伤害 Buff\r\n     * @param {number} buffType Buff 类型\r\n     * @returns {boolean} 是否具有该 Buff\r\n     */\r\n    hasHurtBuff(buffType) {\r\n        return this.hurtBuffMap.get(buffType);\r\n    }\r\n\r\n    /**\r\n     * 设置碰撞数据\r\n     * @param {Array<number>} collider 碰撞数据\r\n     * @param {number} scale 缩放比例\r\n     * @param {Vec2} offset 偏移量\r\n     */\r\n    setCollideData(collider, entity = null, offset = v2(0, 0)) {\r\n        if (this.collideComp) {\r\n            this.collideComp.setData(collider, entity, offset);\r\n        }\r\n    }\r\n\r\n    /**\r\n     * 设置碰撞实体\r\n     * @param {Entity} entity 碰撞实体\r\n     */\r\n    setCollideEntity(entity) {\r\n        if (this.collideComp) {\r\n            this.collideComp.mainEntity = entity;\r\n        }\r\n    }\r\n\r\n    /**\r\n     * 设置碰撞偏移\r\n     * @param {Vec2} offset 偏移量\r\n     */\r\n    setCollideOffset(offset) {\r\n        if (this.collideComp) {\r\n            this.collideComp.entityOffset = offset;\r\n        }\r\n    }\r\n\r\n    /**\r\n     * 设置碰撞缩放\r\n     * @param {number} widthScale 宽度缩放\r\n     * @param {number} heightScale 高度缩放\r\n     */\r\n    setCollideScale(widthScale, heightScale) {\r\n        if (this.collideComp && this.uiData) {\r\n            this.collideComp.setSize(\r\n                this.uiData.collider[3] * widthScale,\r\n                this.uiData.collider[4] * heightScale\r\n            );\r\n        }\r\n    }\r\n\r\n    /**\r\n     * 碰撞处理\r\n     * @param {ColliderComp} collider 碰撞组件\r\n     */\r\n    onCollide(collider) {\r\n        if (this.active && !this.isDead) {\r\n            if (collider.entity instanceof Bullet) {\r\n                const attack = collider.entity.getAttack(this);\r\n                GameIns.hurtEffectManager.createHurtNumByType(\r\n                    this.collideComp,\r\n                    collider.entity,\r\n                    attack,\r\n                    { x: 0, y: 0 }\r\n                );\r\n                const finalDamage = Math.max(attack / 10, attack - this.defence) * GameIns.warAttackManager.playerWARatio;\r\n                if (this.hurt(finalDamage)) {\r\n                    // switch (collider.entity.getType()) {\r\n                    //     case GameEnum.BulletType.Ice:\r\n                    //         const iceResist = this.getSkillResist(GameEnum.EnemyBuff.Ice);\r\n                    //         const iceParams = collider.entity.bulletState.extra;\r\n                    //         if (iceParams && iceParams.length > 0) {\r\n                    //             this.addBuff(GameEnum.EnemyBuff.Ice, [iceParams[0] * iceResist]);\r\n                    //         }\r\n                    //         break;\r\n                    //     case GameEnum.BulletType.Fire:\r\n                    //         const fireResist = this.getSkillResist(GameEnum.EnemyBuff.Fire);\r\n                    //         const fireParams = collider.entity.bulletState.extra;\r\n                    //         if (fireParams && fireParams.length > 0) {\r\n                    //             this.addBuff(GameEnum.EnemyBuff.Fire, [fireParams[0] * fireResist, fireParams[1] / 100 * finalDamage, fireParams[2]], collider.entity.isCirt);\r\n                    //         }\r\n                    //         break;\r\n                    // }\r\n                }\r\n            } else if (collider.entity instanceof MainPlane && this.collideLevel === GameEnum.EnemyCollideLevel.Main && this.bCollideDead) {\r\n                this.die(GameEnum.EnemyDestroyType.Die);\r\n            }\r\n        }\r\n    }\r\n    /**\r\n     * 处理敌人受到的伤害\r\n     * @param {number} damage 伤害值\r\n     * @returns {boolean} 是否成功处理伤害\r\n     */\r\n    hurt(damage) {\r\n        if (!this.active || this.isDead || !this.isDamageable()) {\r\n            return false;\r\n        }\r\n        this.changeHp(-damage);\r\n        this._checkHurtLoot(damage);\r\n        this.onHurt();\r\n        return true;\r\n    }\r\n\r\n    /**\r\n     * 检查是否需要生成掉落物\r\n     * @param {number} damage 伤害值\r\n     */\r\n    _checkHurtLoot(damage) {\r\n        if (this._curLoot) {\r\n            this.checkLoot(damage);\r\n        }\r\n    }\r\n\r\n    /**\r\n     * 检查并生成掉落物\r\n     * @param {number} damage 伤害值\r\n     * @param {Vec2} position 掉落物生成位置\r\n     */\r\n    checkLoot(damage = 0, position = Vec2.ZERO) {\r\n        // if (this.isDead && this.scaleType !== GameEnum.EnemyScale.None) {\r\n        //     const lootType = GameIns.lootManager.checkLoot(this.scaleType);\r\n        //     GameIns.lootManager.addProp(\r\n        //         this.node.convertToWorldSpaceAR(position),\r\n        //         this.uiData.lootParam1,\r\n        //         lootType\r\n        //     );\r\n        // }\r\n    }\r\n\r\n    /**\r\n     * 当敌人受到伤害时的回调\r\n     */\r\n    onHurt() { }\r\n\r\n    /**\r\n     * 处理敌人死亡逻辑\r\n     * @param {number} destroyType 敌人销毁类型\r\n     */\r\n    die(destroyType) {\r\n        if (this.isDead) {\r\n            return;\r\n        }\r\n        this.isDead = true;\r\n        this.collideAble = false;\r\n\r\n        if (this.collideComp && this.collideComp.entity) {\r\n            GameIns.hurtEffectManager.m_colliderPreTime.delete(this.collideComp.entity.new_uuid);\r\n        }\r\n\r\n        if (this.attrCom) {\r\n            this.attrCom.die();\r\n        }\r\n\r\n        this.onDie(destroyType);\r\n    }\r\n\r\n    /**\r\n     * 敌人死亡时的回调\r\n     * @param {number} destroyType 敌人销毁类型\r\n     */\r\n    onDie(destroyType) {\r\n        if (destroyType === GameEnum.EnemyDestroyType.Die) {\r\n            // GameIns.lootManager.addExp(this.exp);\r\n            GameIns.mainPlaneManager.checkKillHp();\r\n            GameIns.mainPlaneManager.checkKillAtk();\r\n\r\n            if (this.dieBullet) {\r\n                for (const bullet of this.bullets) {\r\n                    bullet.dieRemove();\r\n                }\r\n            }\r\n\r\n            if (!this.isItem) {\r\n                // TaskManager.TaskMgr.taskNumberChange(TaskManager.TaskType.KillPlane, 1);\r\n                // TaskManager.TaskMgr.achievementNumberChange(TaskManager.AchievementType.KillEnemy, 1);\r\n                // GameData.GData.killEnemyNumber += 1;\r\n            }\r\n        } else {\r\n            GameIns.enemyManager.subAnnihilate = false;\r\n            GameIns.enemyManager.mainAnnihilate = false;\r\n        }\r\n\r\n        this.bullets = [];\r\n\r\n        switch (this.type) {\r\n            case GameEnum.EnemyType.Ligature:\r\n            case GameEnum.EnemyType.LigatureLine:\r\n                break;\r\n            default:\r\n                let position = this.node.position;\r\n                if (this.sceneLayer < 0) {\r\n                    if (\r\n                        this.type === GameEnum.EnemyType.Turret ||\r\n                        this.type === GameEnum.EnemyType.GoldBox ||\r\n                        this.type === GameEnum.EnemyType.LigatureUnit\r\n                    ) {\r\n                        position = this.node.getComponent(UITransform).convertToWorldSpaceAR(Vec3.ZERO);\r\n                        position = BattleLayer.me.enemyPlaneLayer.getComponent(UITransform).convertToNodeSpaceAR(position);\r\n                    }\r\n                } else {\r\n                    const worldPosition = this.node.getComponent(UITransform).convertToWorldSpaceAR(Vec3.ZERO);\r\n                    position = BattleLayer.me.enemyPlaneLayer.getComponent(UITransform).convertToNodeSpaceAR(worldPosition);\r\n                }\r\n                GameIns.enemyManager.checkEnemyDieBomb(position);\r\n        }\r\n    }\r\n\r\n    /**\r\n     * 准备移除敌人时的回调\r\n     */\r\n    willRemove() { }\r\n\r\n    /**\r\n     * 显示属性护盾\r\n     */\r\n    showAttrShield() {\r\n        if (this.attrCom) {\r\n            this.attrCom.showAttrShield();\r\n        }\r\n    }\r\n\r\n    /**\r\n     * 播放敌人死亡动画\r\n     */\r\n    playDieAnim() {\r\n        if (!this.uiData) {\r\n            return;\r\n        }\r\n\r\n        if (this.uiData.blastSound > 0) {\r\n            // GameIns.audioManager.playEffect(`blast${this.uiData.blastSound}`);\r\n        }\r\n\r\n        const blastParams = [...this.uiData.blastParam];\r\n        for (let i = 0; i < this.uiData.blastCount; i++) {\r\n            const index = Tools.random_int(0, blastParams.length - 1);\r\n            const param = blastParams.splice(index, 1)[0];\r\n\r\n            const effectData = {\r\n                x: Number(param[1]) * this.node.scale.x,\r\n                y: param[2] * this.node.scale.y,\r\n                scale: GameIns.battleManager.getRatio() * Tools.random_int(param[3], param[4]) / 100,\r\n                angle: param[5] < 0 ? Tools.random_int(0, 359) : param[5],\r\n            };\r\n\r\n            const callback = i === this.uiData.blastCount - 1 ? () => this.onDieAnimEnd() : null;\r\n\r\n            this.scheduleOnce(() => {\r\n                // EnemyEffectLayer.me.addBlastEffect(this, param[0], effectData, callback);\r\n            }, this.uiData.blastDurations[i]);\r\n        }\r\n\r\n        this.scheduleOnce(() => {\r\n            // MainCamera.me.shake1(this.uiData.blastShake);\r\n            this.removeAllBuffEffect();\r\n        }, 0.1);\r\n    }\r\n\r\n    /**\r\n     * 敌人死亡动画结束时的回调\r\n     */\r\n    onDieAnimEnd() { }\r\n\r\n    /**\r\n     * 添加子弹到敌人\r\n     * @param {Bullet} bullet 子弹对象\r\n     */\r\n    addBullet(bullet) {\r\n        if (this.dieBullet && this.bullets) {\r\n            this.bullets.push(bullet);\r\n        }\r\n    }\r\n\r\n    /**\r\n     * 从敌人移除子弹\r\n     * @param {Bullet} bullet 子弹对象\r\n     */\r\n    removeBullet(bullet) {\r\n        if (this.dieBullet && this.bullets) {\r\n            const index = this.bullets.indexOf(bullet);\r\n            if (index >= 0) {\r\n                this.bullets.splice(index, 1);\r\n            }\r\n        }\r\n    }\r\n}"]}