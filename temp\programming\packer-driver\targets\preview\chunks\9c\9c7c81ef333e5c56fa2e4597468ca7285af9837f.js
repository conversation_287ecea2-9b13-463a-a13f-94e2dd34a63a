System.register(["__unresolved_0", "cc", "__unresolved_1", "__unresolved_2", "__unresolved_3", "__unresolved_4"], function (_export, _context) {
  "use strict";

  var _reporterNs, _cclegacy, __checkObsolete__, __checkObsoleteInNamespace__, _decorator, Component, Sprite, find, Collider2D, Contact2DType, v2, ProgressBar, Label, Animation, Enemy, Global, GamePersistNode, audioManager, _dec, _class, _crd, ccclass, property, PlayerBullet;

  function _reportPossibleCrUseOfEnemy(extras) {
    _reporterNs.report("Enemy", "./Enemy", _context.meta, extras);
  }

  function _reportPossibleCrUseOfGameFactory(extras) {
    _reporterNs.report("GameFactory", "./factroy/GameFactory", _context.meta, extras);
  }

  function _reportPossibleCrUseOfGlobal(extras) {
    _reporterNs.report("Global", "./Global", _context.meta, extras);
  }

  function _reportPossibleCrUseOfGamePersistNode(extras) {
    _reporterNs.report("GamePersistNode", "./GamePersistNode", _context.meta, extras);
  }

  function _reportPossibleCrUseOfaudioManager(extras) {
    _reporterNs.report("audioManager", "../ResUpdate/audioManager", _context.meta, extras);
  }

  return {
    setters: [function (_unresolved_) {
      _reporterNs = _unresolved_;
    }, function (_cc) {
      _cclegacy = _cc.cclegacy;
      __checkObsolete__ = _cc.__checkObsolete__;
      __checkObsoleteInNamespace__ = _cc.__checkObsoleteInNamespace__;
      _decorator = _cc._decorator;
      Component = _cc.Component;
      Sprite = _cc.Sprite;
      find = _cc.find;
      Collider2D = _cc.Collider2D;
      Contact2DType = _cc.Contact2DType;
      v2 = _cc.v2;
      ProgressBar = _cc.ProgressBar;
      Label = _cc.Label;
      Animation = _cc.Animation;
    }, function (_unresolved_2) {
      Enemy = _unresolved_2.Enemy;
    }, function (_unresolved_3) {
      Global = _unresolved_3.Global;
    }, function (_unresolved_4) {
      GamePersistNode = _unresolved_4.GamePersistNode;
    }, function (_unresolved_5) {
      audioManager = _unresolved_5.audioManager;
    }],
    execute: function () {
      _crd = true;

      _cclegacy._RF.push({}, "5ac34OOGUdF6LmxcXR+eh79", "PlayerBullet", undefined);

      __checkObsolete__(['_decorator', 'Component', 'Node', 'SpriteFrame', 'Sprite', 'Vec3', 'find', 'Collider2D', 'Contact2DType', 'IPhysics2DContact', 'v2', 'ProgressBar', 'Label', 'Animation']);

      ({
        ccclass,
        property
      } = _decorator);

      _export("PlayerBullet", PlayerBullet = (_dec = ccclass('PlayerBullet'), _dec(_class = class PlayerBullet extends Component {
        constructor() {
          super(...arguments);
          this.playerBulletType = null;
          //子弹类型
          this.curPos = null;
          //当前子弹位置
          this.normalBulletMoveSpeed = 0;
          //普通子弹移动速度
          this.lightBulletMoveSpeed = 0;
          //激光子弹移动速度
          this.missileBulletMoveSpeed = 0;
          //激光子弹移动速度
          this.playerNormalReduce = 0;
          //被普通子弹击中，敌机掉多少血
          this.playerLightReduce = 0;
          //被激光子弹击中，敌机掉多少血
          this.playerMissileReduce = 0;
          //导弹子弹击中，敌机掉多少血
          this.playerBulletFactory = null;
          this.enemyFactory = null;
          //敌机工厂
          this.persistNode = null;
        }

        onLoad() {
          this.persistNode = find("GamePersistNode");
          this.playerBulletFactory = this.persistNode.getComponent(_crd && GamePersistNode === void 0 ? (_reportPossibleCrUseOfGamePersistNode({
            error: Error()
          }), GamePersistNode) : GamePersistNode).playerBulletFactory;
          this.enemyFactory = this.persistNode.getComponent(_crd && GamePersistNode === void 0 ? (_reportPossibleCrUseOfGamePersistNode({
            error: Error()
          }), GamePersistNode) : GamePersistNode).enemyFactory; //关联面板子弹移动速度

          this.normalBulletMoveSpeed = this.persistNode.getComponent(_crd && GamePersistNode === void 0 ? (_reportPossibleCrUseOfGamePersistNode({
            error: Error()
          }), GamePersistNode) : GamePersistNode).normalBulletMoveSpeed;
          this.lightBulletMoveSpeed = this.persistNode.getComponent(_crd && GamePersistNode === void 0 ? (_reportPossibleCrUseOfGamePersistNode({
            error: Error()
          }), GamePersistNode) : GamePersistNode).lightBulletMoveSpeed;
          this.missileBulletMoveSpeed = this.persistNode.getComponent(_crd && GamePersistNode === void 0 ? (_reportPossibleCrUseOfGamePersistNode({
            error: Error()
          }), GamePersistNode) : GamePersistNode).missileBulletMoveSpeed; //关联面板敌机掉血

          this.playerNormalReduce = this.persistNode.getComponent(_crd && GamePersistNode === void 0 ? (_reportPossibleCrUseOfGamePersistNode({
            error: Error()
          }), GamePersistNode) : GamePersistNode).playerNormalReduce;
          this.playerLightReduce = this.persistNode.getComponent(_crd && GamePersistNode === void 0 ? (_reportPossibleCrUseOfGamePersistNode({
            error: Error()
          }), GamePersistNode) : GamePersistNode).playerLightReduce;
          this.playerMissileReduce = this.persistNode.getComponent(_crd && GamePersistNode === void 0 ? (_reportPossibleCrUseOfGamePersistNode({
            error: Error()
          }), GamePersistNode) : GamePersistNode).playerMissileReduce;
          var collider = this.node.getComponent(Collider2D);

          if (collider) {
            collider.on(Contact2DType.BEGIN_CONTACT, this.onBeginContact, this);
          }
        }
        /**
         * 开始碰撞后的回调函数
         */


        onBeginContact(selfCollider, otherCollider, contact) {
          if (this.playerBulletType == (_crd && Global === void 0 ? (_reportPossibleCrUseOfGlobal({
            error: Error()
          }), Global) : Global).NORMAL_BULLET) {
            otherCollider.getComponent(_crd && Enemy === void 0 ? (_reportPossibleCrUseOfEnemy({
              error: Error()
            }), Enemy) : Enemy).enemyBlood -= this.playerNormalReduce;
          } else if (this.playerBulletType == (_crd && Global === void 0 ? (_reportPossibleCrUseOfGlobal({
            error: Error()
          }), Global) : Global).LIGHT_BULLET) {
            otherCollider.getComponent(_crd && Enemy === void 0 ? (_reportPossibleCrUseOfEnemy({
              error: Error()
            }), Enemy) : Enemy).enemyBlood -= this.playerLightReduce;
          } else if (this.playerBulletType == (_crd && Global === void 0 ? (_reportPossibleCrUseOfGlobal({
            error: Error()
          }), Global) : Global).MISSILE_BULLET) {
            otherCollider.getComponent(_crd && Enemy === void 0 ? (_reportPossibleCrUseOfEnemy({
              error: Error()
            }), Enemy) : Enemy).enemyBlood -= this.playerMissileReduce;
          }

          otherCollider.node.getChildByName("EnemyBlood").getComponent(ProgressBar).progress = otherCollider.getComponent(_crd && Enemy === void 0 ? (_reportPossibleCrUseOfEnemy({
            error: Error()
          }), Enemy) : Enemy).enemyBlood / otherCollider.getComponent(_crd && Enemy === void 0 ? (_reportPossibleCrUseOfEnemy({
            error: Error()
          }), Enemy) : Enemy).enemyTotalBlood;

          if (otherCollider.getComponent(_crd && Enemy === void 0 ? (_reportPossibleCrUseOfEnemy({
            error: Error()
          }), Enemy) : Enemy).enemyBlood <= 0) {
            //敌机血量判断是否小于0
            //添加动画节点
            var anim = this.persistNode.getComponent(_crd && GamePersistNode === void 0 ? (_reportPossibleCrUseOfGamePersistNode({
              error: Error()
            }), GamePersistNode) : GamePersistNode).animFactory.createAnim();
            anim.setPosition(otherCollider.node.getPosition());
            find("Canvas").addChild(anim);
            anim.getComponent(Animation).play(); //播放动画

            this.enemyFactory.recycleProduct(otherCollider.node); //敌机消失

            (_crd && audioManager === void 0 ? (_reportPossibleCrUseOfaudioManager({
              error: Error()
            }), audioManager) : audioManager).instance.playSound(this.persistNode.getComponent(_crd && GamePersistNode === void 0 ? (_reportPossibleCrUseOfGamePersistNode({
              error: Error()
            }), GamePersistNode) : GamePersistNode).boomAudioClip);

            if (otherCollider.node.getComponent(_crd && Enemy === void 0 ? (_reportPossibleCrUseOfEnemy({
              error: Error()
            }), Enemy) : Enemy).enemyType == (_crd && Global === void 0 ? (_reportPossibleCrUseOfGlobal({
              error: Error()
            }), Global) : Global).ENEMY_1) {
              (_crd && Global === void 0 ? (_reportPossibleCrUseOfGlobal({
                error: Error()
              }), Global) : Global).SCORE += 20;
            } else {
              (_crd && Global === void 0 ? (_reportPossibleCrUseOfGlobal({
                error: Error()
              }), Global) : Global).SCORE += 40;
            }

            find("Canvas/Score").getComponent(Label).string = "Score:" + (_crd && Global === void 0 ? (_reportPossibleCrUseOfGlobal({
              error: Error()
            }), Global) : Global).SCORE.toString();
          }

          this.playerBulletFactory.recycleProduct(this.node); //子弹消失
        }
        /**
         * 初始化playerBullet
         */


        init(playerBulletType, spriteFrame) {
          this.playerBulletType = playerBulletType;
          this.node.getComponent(Sprite).spriteFrame = spriteFrame;
          this.node.angle = v2(0, 1).signAngle(v2(0, 1)) * 180 / Math.PI;
        }

        update(deltaTime) {
          if (this.playerBulletType == (_crd && Global === void 0 ? (_reportPossibleCrUseOfGlobal({
            error: Error()
          }), Global) : Global).NORMAL_BULLET) {
            this.normalBulletMove(deltaTime);
          } else if (this.playerBulletType == (_crd && Global === void 0 ? (_reportPossibleCrUseOfGlobal({
            error: Error()
          }), Global) : Global).LIGHT_BULLET) {
            this.lightBulletMove(deltaTime);
          } else if (this.playerBulletType == (_crd && Global === void 0 ? (_reportPossibleCrUseOfGlobal({
            error: Error()
          }), Global) : Global).MISSILE_BULLET) {
            this.missileBulletMove(deltaTime);
          }
        }
        /**
         * 普通子弹行为
         * @param deltaTime 
         */


        normalBulletMove(deltaTime) {
          this.curPos = this.node.getPosition();
          this.curPos.y += this.normalBulletMoveSpeed * deltaTime;
          this.node.setPosition(this.curPos);

          if (this.curPos.y > (_crd && Global === void 0 ? (_reportPossibleCrUseOfGlobal({
            error: Error()
          }), Global) : Global).HEIGHT / 2) {
            this.playerBulletFactory.recycleProduct(this.node);
          }
        }
        /**
         * 激光子弹行为
         * @param deltaTime 
         */


        lightBulletMove(deltaTime) {
          this.curPos = this.node.getPosition();
          this.curPos.y += this.lightBulletMoveSpeed * deltaTime;
          this.node.setPosition(this.curPos);

          if (this.curPos.y > (_crd && Global === void 0 ? (_reportPossibleCrUseOfGlobal({
            error: Error()
          }), Global) : Global).HEIGHT / 2) {
            this.playerBulletFactory.recycleProduct(this.node);
          }
        }
        /**
         * 导弹子弹行为
         * @param deltaTime 
         */


        missileBulletMove(deltaTime) {
          var enemy = this.node.parent.getChildByName("Enemy"); //得到敌机节点

          this.curPos = this.node.getPosition();

          if (enemy != null) {
            var enemyPos = enemy.getPosition(); ////得到敌机位置

            var normalizeVec = enemyPos.subtract(this.curPos).normalize(); //得到子弹指向敌机的单位向量

            this.curPos.x += normalizeVec.x * this.missileBulletMoveSpeed * deltaTime; //子弹沿着单位向量移动

            this.curPos.y += normalizeVec.y * this.missileBulletMoveSpeed * deltaTime;
            this.node.setPosition(this.curPos);
            this.node.angle = v2(0, 1).signAngle(v2(normalizeVec.x, normalizeVec.y)) * 180 / Math.PI; //让导弹有夹角
          } else {
            //没有敌机时，跟普通子弹一样向上移动
            this.curPos.y += this.missileBulletMoveSpeed * deltaTime;
            this.node.setPosition(this.curPos);
            this.node.angle = v2(0, 1).signAngle(v2(0, 1)) * 180 / Math.PI;
          }

          if (this.curPos.y > (_crd && Global === void 0 ? (_reportPossibleCrUseOfGlobal({
            error: Error()
          }), Global) : Global).HEIGHT / 2) {
            this.playerBulletFactory.recycleProduct(this.node);
          }
        }

      }) || _class));

      _cclegacy._RF.pop();

      _crd = false;
    }
  };
});
//# sourceMappingURL=9c7c81ef333e5c56fa2e4597468ca7285af9837f.js.map