{"version": 3, "sources": ["file:///E:/M2Game/Client/assets/scripts/GameOld/world/move/DefaultMovable.ts"], "names": ["_decorator", "Component", "Vec3", "ccclass", "property", "executeInEditMode", "DefaultMovable", "angle", "vx", "vy", "activeModifiers", "Map", "addSolver", "solver", "set", "targetType", "update", "dt", "for<PERSON>ach", "key", "tick", "node", "lastPos", "getPosition", "newPos", "x", "y", "z", "setPosition", "isFinished", "delete"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;AAASA,MAAAA,U,OAAAA,U;AAAYC,MAAAA,S,OAAAA,S;AAAiBC,MAAAA,I,OAAAA,I;;;;;;;;;OAChC;AAAEC,QAAAA,OAAF;AAAWC,QAAAA,QAAX;AAAqBC,QAAAA;AAArB,O,GAA2CL,U;;gCAKpCM,c,WAFZH,OAAO,CAAC,gBAAD,C,gBACPE,iB,UADD,MAEaC,cAFb,SAEoCL,SAFpC,CAEkE;AAAA;AAAA;AAAA,eAC9DM,KAD8D;AAE9D;AAF8D,eAG9DC,EAH8D;AAI9D;AAJ8D,eAK9DC,EAL8D;AAM9D;AAEA;AAR8D,eAS9DC,eAT8D,GAST,IAAIC,GAAJ,EATS;AAAA;;AAWvDC,QAAAA,SAAS,CAACC,MAAD,EAAwB;AACpC,eAAKH,eAAL,CAAqBI,GAArB,CAAyBD,MAAM,CAACE,UAAhC,EAA4CF,MAA5C;AACH;;AAESG,QAAAA,MAAM,CAACC,EAAD,EAAmB;AAC/B,eAAKP,eAAL,CAAqBQ,OAArB,CAA6B,CAACL,MAAD,EAASM,GAAT,KACzBN,MAAM,CAACO,IAAP,CAAY,IAAZ,EAAkBH,EAAlB,CADJ,EAD+B,CAK/B;;AACA,eAAKI,IAAL,CAAUd,KAAV,GAAkB,KAAKA,KAAvB;AACA,cAAMe,OAAO,GAAG,KAAKD,IAAL,CAAUE,WAAV,EAAhB;AACA,cAAMC,MAAM,GAAG,IAAItB,IAAJ,CACXoB,OAAO,CAACG,CAAR,GAAY,KAAKjB,EAAL,GAAUS,EADX,EAEXK,OAAO,CAACI,CAAR,GAAY,KAAKjB,EAAL,GAAUQ,EAFX,EAGXK,OAAO,CAACK,CAHG,CAAf;AAKA,eAAKN,IAAL,CAAUO,WAAV,CAAsBJ,MAAtB,EAb+B,CAe/B;;AACA,eAAKd,eAAL,CAAqBQ,OAArB,CAA6B,CAACL,MAAD,EAASM,GAAT,KAAiB;AAC1C,gBAAIN,MAAM,CAACgB,UAAP,EAAJ,EAAyB;AACrB,mBAAKnB,eAAL,CAAqBoB,MAArB,CAA4BX,GAA5B;AACH;AACJ,WAJD;AAKH;;AApC6D,O", "sourcesContent": ["import { _decorator, Component, Node, Vec3 } from 'cc';\r\nconst { ccclass, property, executeInEditMode } = _decorator;\r\nimport { IMovable, IMoveModifier, eSolverTarget } from './IMovable';\r\n\r\n@ccclass('DefaultMovable')\r\n@executeInEditMode\r\nexport class DefaultMovable extends Component implements IMovable {\r\n    angle: number;\r\n    //wa: number;\r\n    vx: number;\r\n    //vax: number;\r\n    vy: number;\r\n    //vay: number;\r\n\r\n    // make a map by eSolverTarget\r\n    activeModifiers: Map<eSolverTarget, IMoveModifier> = new Map();\r\n\r\n    public addSolver(solver: IMoveModifier) {\r\n        this.activeModifiers.set(solver.targetType, solver);\r\n    }\r\n\r\n    protected update(dt: number): void {\r\n        this.activeModifiers.forEach((solver, key) => \r\n            solver.tick(this, dt)\r\n        );\r\n\r\n        // 根据angle, vx&vy更新位置和朝向\r\n        this.node.angle = this.angle;\r\n        const lastPos = this.node.getPosition();\r\n        const newPos = new Vec3(\r\n            lastPos.x + this.vx * dt,\r\n            lastPos.y + this.vy * dt,\r\n            lastPos.z\r\n        );\r\n        this.node.setPosition(newPos);\r\n\r\n        // remove finished solvers\r\n        this.activeModifiers.forEach((solver, key) => {\r\n            if (solver.isFinished()) {\r\n                this.activeModifiers.delete(key);\r\n            }\r\n        });\r\n    }\r\n}"]}