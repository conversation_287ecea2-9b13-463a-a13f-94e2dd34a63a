{"version": 3, "sources": ["file:///E:/M2Game/Client/assets/scripts/Game/ui/layer/EffectLayer.ts"], "names": ["_decorator", "Component", "Node", "Color", "tween", "UITransform", "Sprite", "UIOpacity", "GameConst", "GameConfig", "ccclass", "property", "EffectLayer", "onLoad", "me", "whiteNode", "getComponent", "width", "ViewWidth", "height", "ViewHeight", "showWhiteScreen", "delay", "opacity", "active", "color", "WHITE", "ActionFrameTime", "to", "call", "start", "lightingShow", "BLACK", "showRedScreen", "redNode", "frameTime"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;AAASA,MAAAA,U,OAAAA,U;AAAYC,MAAAA,S,OAAAA,S;AAAWC,MAAAA,I,OAAAA,I;AAAMC,MAAAA,K,OAAAA,K;AAAOC,MAAAA,K,OAAAA,K;AAAOC,MAAAA,W,OAAAA,W;AAAaC,MAAAA,M,OAAAA,M;AAAQC,MAAAA,S,OAAAA,S;;AAChEC,MAAAA,S,iBAAAA,S;;AACFC,MAAAA,U;;;;;;;;;OAED;AAAEC,QAAAA,OAAF;AAAWC,QAAAA;AAAX,O,GAAwBX,U;;yBAGTY,W,WADpBF,OAAO,CAAC,aAAD,C,UAEHC,QAAQ,CAACT,IAAD,C,UAGRS,QAAQ,CAACT,IAAD,C,sCALb,MACqBU,WADrB,SACyCX,SADzC,CACmD;AAAA;AAAA;;AAAA;;AAAA;AAAA;;AAS/CY,QAAAA,MAAM,GAAG;AACLD,UAAAA,WAAW,CAACE,EAAZ,GAAiB,IAAjB;AACA,eAAKC,SAAL,CAAeC,YAAf,CAA4BX,WAA5B,EAAyCY,KAAzC,GAAiD;AAAA;AAAA,sCAAUC,SAA3D;AACA,eAAKH,SAAL,CAAeC,YAAf,CAA4BX,WAA5B,EAAyCc,MAAzC,GAAkD;AAAA;AAAA,sCAAUC,UAA5D;AACH;;AAEDC,QAAAA,eAAe,CAACC,KAAD,EAAgBC,OAAhB,EAAuC;AAAA,cAAvBA,OAAuB;AAAvBA,YAAAA,OAAuB,GAAL,GAAK;AAAA;;AAClD,eAAKR,SAAL,CAAeS,MAAf,GAAwB,IAAxB;AACA,eAAKT,SAAL,CAAeC,YAAf,CAA4BT,SAA5B,EAAuCgB,OAAvC,GAAiDA,OAAjD;AACA,eAAKR,SAAL,CAAeC,YAAf,CAA4BV,MAA5B,EAAoCmB,KAApC,GAA4CtB,KAAK,CAACuB,KAAlD;AAEAtB,UAAAA,KAAK,CAAC,KAAKW,SAAL,CAAeC,YAAf,CAA4BT,SAA5B,CAAD,CAAL,CACKe,KADL,CACW,IAAI;AAAA;AAAA,wCAAWK,eAD1B,EAEKC,EAFL,CAEQ,IAFR,EAEc;AAAEL,YAAAA,OAAO,EAAE;AAAX,WAFd,EAGKM,IAHL,CAGU,MAAM;AACR,iBAAKd,SAAL,CAAeS,MAAf,GAAwB,KAAxB;AACH,WALL,EAMKM,KANL;AAOH;;AAEDC,QAAAA,YAAY,GAAG;AACX,eAAKhB,SAAL,CAAeS,MAAf,GAAwB,IAAxB;AACA,eAAKT,SAAL,CAAeC,YAAf,CAA4BT,SAA5B,EAAuCgB,OAAvC,GAAiD,MAAjD;AACA,eAAKR,SAAL,CAAeC,YAAf,CAA4BV,MAA5B,EAAoCmB,KAApC,GAA4CtB,KAAK,CAACuB,KAAlD;AAEAtB,UAAAA,KAAK,CAAC,KAAKW,SAAN,CAAL,CACKO,KADL,CACW,IAAI,EADf,EAEKO,IAFL,CAEU,MAAM;AACR,iBAAKd,SAAL,CAAeC,YAAf,CAA4BT,SAA5B,EAAuCgB,OAAvC,GAAiD,KAAjD;AACA,iBAAKR,SAAL,CAAeC,YAAf,CAA4BV,MAA5B,EAAoCmB,KAApC,GAA4CtB,KAAK,CAAC6B,KAAlD;AACH,WALL,EAMKV,KANL,CAMW,IAAI,EANf,EAOKO,IAPL,CAOU,MAAM;AACR,iBAAKd,SAAL,CAAeS,MAAf,GAAwB,KAAxB;AACH,WATL,EAUKF,KAVL,CAUW,IAAI,EAVf,EAWKO,IAXL,CAWU,MAAM;AACR,iBAAKd,SAAL,CAAeC,YAAf,CAA4BT,SAA5B,EAAuCgB,OAAvC,GAAiD,KAAjD;AACA,iBAAKR,SAAL,CAAeC,YAAf,CAA4BV,MAA5B,EAAoCmB,KAApC,GAA4CtB,KAAK,CAAC6B,KAAlD;AACH,WAdL,EAeKV,KAfL,CAeW,IAAI,EAff,EAgBKO,IAhBL,CAgBU,MAAM;AACR,iBAAKd,SAAL,CAAeS,MAAf,GAAwB,KAAxB;AACH,WAlBL,EAmBKM,KAnBL;AAoBH;;AAEDG,QAAAA,aAAa,GAAG;AACZ,eAAKC,OAAL,CAAalB,YAAb,CAA0BT,SAA1B,EAAqCgB,OAArC,GAA+C,CAA/C;AACA,eAAKW,OAAL,CAAalB,YAAb,CAA0BX,WAA1B,EAAuCY,KAAvC,GAA+C;AAAA;AAAA,sCAAUC,SAAzD;AACA,eAAKgB,OAAL,CAAalB,YAAb,CAA0BX,WAA1B,EAAuCc,MAAvC,GAAgD;AAAA;AAAA,sCAAUC,UAA1D;AAEA,cAAMe,SAAS,GAAG;AAAA;AAAA,wCAAWR,eAA7B;AAEAvB,UAAAA,KAAK,CAAC,KAAK8B,OAAL,CAAalB,YAAb,CAA0BT,SAA1B,CAAD,CAAL,CACKqB,EADL,CACQ,CADR,EACW;AAAEL,YAAAA,OAAO,EAAE;AAAX,WADX,EAEKK,EAFL,CAEQ,IAAIO,SAFZ,EAEuB;AAAEZ,YAAAA,OAAO,EAAE;AAAX,WAFvB,EAGKK,EAHL,CAGQ,IAAIO,SAHZ,EAGuB;AAAEZ,YAAAA,OAAO,EAAE;AAAX,WAHvB,EAIKK,EAJL,CAIQ,KAAKO,SAJb,EAIwB;AAAEZ,YAAAA,OAAO,EAAE;AAAX,WAJxB,EAKKO,KALL;AAMH;;AArE8C,O,UAOxChB,E;;;;;iBALW,I;;;;;;;iBAGF,I", "sourcesContent": ["import { _decorator, Component, Node, Color, tween, UITransform, Sprite, UIOpacity } from 'cc';\r\nimport { GameConst } from '../../const/GameConst';\r\nimport GameConfig from '../../const/GameConfig';\r\n\r\nconst { ccclass, property } = _decorator;\r\n\r\n@ccclass('EffectLayer')\r\nexport default class EffectLayer extends Component {\r\n    @property(Node)\r\n    whiteNode: Node = null;\r\n\r\n    @property(Node)\r\n    redNode: Node = null;\r\n\r\n    static me: EffectLayer;\r\n\r\n    onLoad() {\r\n        EffectLayer.me = this;\r\n        this.whiteNode.getComponent(UITransform).width = GameConst.ViewWidth;\r\n        this.whiteNode.getComponent(UITransform).height = GameConst.ViewHeight;\r\n    }\r\n\r\n    showWhiteScreen(delay: number, opacity: number = 255) {\r\n        this.whiteNode.active = true;\r\n        this.whiteNode.getComponent(UIOpacity).opacity = opacity;\r\n        this.whiteNode.getComponent(Sprite).color = Color.WHITE;\r\n\r\n        tween(this.whiteNode.getComponent(UIOpacity))\r\n            .delay(4 * GameConfig.ActionFrameTime)\r\n            .to(0.33, { opacity: 0 })\r\n            .call(() => {\r\n                this.whiteNode.active = false;\r\n            })\r\n            .start();\r\n    }\r\n\r\n    lightingShow() {\r\n        this.whiteNode.active = true;\r\n        this.whiteNode.getComponent(UIOpacity).opacity = 140.25;\r\n        this.whiteNode.getComponent(Sprite).color = Color.WHITE;\r\n\r\n        tween(this.whiteNode)\r\n            .delay(2 / 30)\r\n            .call(() => {\r\n                this.whiteNode.getComponent(UIOpacity).opacity = 178.5;\r\n                this.whiteNode.getComponent(Sprite).color = Color.BLACK;\r\n            })\r\n            .delay(2 / 30)\r\n            .call(() => {\r\n                this.whiteNode.active = false;\r\n            })\r\n            .delay(1 / 30)\r\n            .call(() => {\r\n                this.whiteNode.getComponent(UIOpacity).opacity = 127.5;\r\n                this.whiteNode.getComponent(Sprite).color = Color.BLACK;\r\n            })\r\n            .delay(1 / 30)\r\n            .call(() => {\r\n                this.whiteNode.active = false;\r\n            })\r\n            .start();\r\n    }\r\n\r\n    showRedScreen() {\r\n        this.redNode.getComponent(UIOpacity).opacity = 0;\r\n        this.redNode.getComponent(UITransform).width = GameConst.ViewWidth;\r\n        this.redNode.getComponent(UITransform).height = GameConst.ViewHeight;\r\n\r\n        const frameTime = GameConfig.ActionFrameTime;\r\n\r\n        tween(this.redNode.getComponent(UIOpacity))\r\n            .to(0, { opacity: 204 })\r\n            .to(4 * frameTime, { opacity: 255 })\r\n            .to(2 * frameTime, { opacity: 224 })\r\n            .to(15 * frameTime, { opacity: 0 })\r\n            .start();\r\n    }\r\n}"]}