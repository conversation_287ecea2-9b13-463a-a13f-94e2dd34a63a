{"version": 3, "sources": ["file:///E:/M2Game/Client/assets/scripts/Game/ui/bullet/FireBullet.ts"], "names": [], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AAEA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AAEA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AAEA;AACA;AAEA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA", "sourcesContent": ["// import { _decorator, Sprite } from 'cc';\r\n// import Bullet from './Bullet';\r\n// import frameWork from './frameWork';\r\n// import EnemyManager from './EnemyManager';\r\n// import GameEnum from './GameEnum';\r\n// import ColliderComp from './ColliderComp';\r\n// import Tools from './Tools';\r\n// import ParticleComponent from './ParticleCompenent';\r\n// import HurtEffectManager from './HurtEffectManager';\r\n// import GameConst from './GameConst';\r\n\r\n// const { ccclass, property } = _decorator;\r\n\r\n// @ccclass('FireBullet')\r\n// export default class FireBullet extends Bullet {\r\n//     @property(Sprite)\r\n//     animIcon = null;\r\n\r\n//     constructor() {\r\n//         super();\r\n//         this._animFrames = [];\r\n//         this._animFrameNum = 0;\r\n//         this._animTime = 0;\r\n//         this._animFrameIndex = 0;\r\n//         this._isBlast = false;\r\n//         this._curRadius = 0;\r\n//         this._maxRadius = 0;\r\n//         this._diffuseSpeed = 100;\r\n//         this._enemyArr = [];\r\n//         this._particleCom = null;\r\n//     }\r\n\r\n//     create(config) {\r\n//         this._animFrames.splice(0);\r\n//         this._animFrameNum = 0;\r\n//         this.reset();\r\n//         super.create(config);\r\n\r\n//         if (this.getType() === 27) {\r\n//             this._particleCom = this.addComp(\r\n//                 ParticleComponent,\r\n//                 new ParticleComponent('p1', 0.2, this.getType(), this.m_config.id)\r\n//             );\r\n//         } else if (this.getType() === 28) {\r\n//             this._particleCom = this.addComp(\r\n//                 ParticleComponent,\r\n//                 new ParticleComponent('p2', 0.2, this.getType(), this.m_config.id)\r\n//             );\r\n//         }\r\n//     }\r\n\r\n//     reset() {\r\n//         this._animTime = 0;\r\n//         this._animFrameIndex = 0;\r\n//         this._isBlast = false;\r\n//         this._curRadius = 0;\r\n//         this._maxRadius = 0;\r\n//         this._diffuseSpeed = 100;\r\n//         this._enemyArr.splice(0);\r\n//     }\r\n\r\n//     async setSkin() {\r\n//         if (this.m_config.am !== '') {\r\n//             const animData = this.m_config.am.split('*');\r\n//             if (animData.length > 1) {\r\n//                 const baseName = animData[0];\r\n//                 this._animFrameNum = parseInt(animData[1]);\r\n//                 for (let i = 0; i < this._animFrameNum; i++) {\r\n//                     this._animFrames.push(\r\n//                         GameIns.loadManager.getImage(`${baseName}${i}`, 'mainBullet')\r\n//                     );\r\n//                 }\r\n//                 this.animIcon.spriteFrame = this._animFrames[0];\r\n//             }\r\n//         } else {\r\n//             await super.setSkin();\r\n//         }\r\n//     }\r\n\r\n//     onCollide(target) {\r\n//         if (this.bulletState.through) return;\r\n\r\n//         this.playHurtAudio();\r\n\r\n//         if (this.m_config.exstyle1 && this.m_config.exstyle1 !== '') {\r\n//             HurtEffectManager.me.createHurtEffect(\r\n//                 target,\r\n//                 this,\r\n//                 this.m_config.exstyle1,\r\n//                 this.m_config.exstyle2\r\n//             );\r\n//         }\r\n\r\n//         if (this.getType() === 28) {\r\n//             this._maxRadius = this.bulletState.extra[1];\r\n//             this._isBlast = true;\r\n\r\n//             const screenPos = this.m_collideComp.getScreenPos();\r\n//             EnemyManager.EnemyMgr.planes.forEach((enemy) => {\r\n//                 if (enemy.active && !Tools.arrContain(this._enemyArr, enemy)) {\r\n//                     let enemyPos;\r\n//                     let distance = Infinity;\r\n\r\n//                     switch (enemy.type) {\r\n//                         case GameEnum.EnemyType.Normal:\r\n//                             if (enemy.collideLevel > GameEnum.EnemyCollideLevel.None) {\r\n//                                 enemyPos = enemy.getComp(ColliderComp).getScreenPos();\r\n//                                 distance = Tools.getPosDis(screenPos, enemyPos);\r\n//                             }\r\n//                             break;\r\n\r\n//                         case GameEnum.EnemyType.Turret:\r\n//                             enemyPos = enemy.getComp(ColliderComp).getScreenPos();\r\n//                             distance = Tools.getPosDis(screenPos, enemyPos);\r\n//                             break;\r\n\r\n//                         case GameEnum.EnemyType.Ligature:\r\n//                             enemy.getPlanes().forEach((plane) => {\r\n//                                 const planePos = plane.getComp(ColliderComp).getScreenPos();\r\n//                                 const planeDistance = Tools.getPosDis(screenPos, planePos);\r\n//                                 if (planeDistance < this._curRadius) {\r\n//                                     plane.hurt(this.getAttack(plane));\r\n//                                     this._enemyArr.push(plane);\r\n//                                 }\r\n//                             });\r\n//                             distance = Infinity;\r\n//                             break;\r\n\r\n//                         case GameEnum.EnemyType.Build:\r\n//                             if (enemy.collideLevel > GameEnum.EnemyCollideLevel.None) {\r\n//                                 enemyPos = enemy.getComp(ColliderComp).getScreenPos();\r\n//                                 distance = Tools.getPosDis(screenPos, enemyPos);\r\n//                             }\r\n//                             break;\r\n\r\n//                         case GameEnum.EnemyType.Ship:\r\n//                             // Handle Ship type if needed\r\n//                             break;\r\n//                     }\r\n\r\n//                     if (distance < this._curRadius) {\r\n//                         enemy.hurt(this.getAttack(enemy) * this.bulletState.extra[0]);\r\n//                     }\r\n//                 }\r\n//             });\r\n//         }\r\n\r\n//         this.remove();\r\n//     }\r\n\r\n//     update(deltaTime) {\r\n//         if (!GameConst.GConst.GameAble) return;\r\n\r\n//         deltaTime = deltaTime > 0.2 ? 0.016666666666667 : deltaTime;\r\n//         super.update(deltaTime);\r\n\r\n//         if (this._animFrameNum > 0) {\r\n//             this._animTime += deltaTime;\r\n//             if (this._animTime >= 0.06) {\r\n//                 this._animFrameIndex++;\r\n//                 if (this._animFrameIndex >= this._animFrameNum) {\r\n//                     this._animFrameIndex = 0;\r\n//                 }\r\n//                 this._animTime = 0;\r\n//                 this.animIcon.spriteFrame = this._animFrames[this._animFrameIndex];\r\n//             }\r\n//         }\r\n//     }\r\n\r\n//     remove() {\r\n//         this.reset();\r\n//         super.remove();\r\n//     }\r\n\r\n//     refresh() {\r\n//         super.refresh();\r\n//         if (this.getType() === 27 && this._particleCom) {\r\n//             this._particleCom.setDelay(0.2);\r\n//         }\r\n//     }\r\n// }"]}