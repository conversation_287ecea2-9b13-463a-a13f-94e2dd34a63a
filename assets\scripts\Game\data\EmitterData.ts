
import { _decorator, error, v2, Vec2 } from "cc";
import { Tools } from "../utils/Tools";
const { ccclass, property } = _decorator;

/**
 * 发射器数据
 * 所有时间相关的，单位都是秒(s)
 */
@ccclass("EmitterData")
export class EmitterData {
    isOnlyInScreen : boolean = true;   // 仅在屏幕内才发射
    isPreWarm : boolean = false;       // 是否预热
    isLoop : boolean = true;           // 是否循环

    initialDelay : number = 0.0;       // 初始延迟

    preWarmDuration : number = 0.0;    // 预热持续时长
    preWarmEffect : string;            // 预热特效:(这个是否用prefab，直接包含音效、音量等信息)
    preWarmSound : string;             // 预热音效

    emitDuration : number = 1.0;       // 发射器持续时间
    emitInterval : number = 1.0;       // 发射间隔
    emitPower : number = 1.0;          // 用来修改子弹初始速度的乘数(备用)
    loopInterval : number = 0.0;       // 循环间隔
    
    perEmitCount : number = 1;         // 单次发射数量
    perEmitInterval : number = 0.0;    // 单次发射多个子弹时的间隔
    perEmitOffsetX : number = 0.0;     // 单次发射多个子弹时的x偏移

    angle : number = -90;              // 发射角度: -90朝下
    count : number = 1;                // 发射条数(弹道数量)
    arc   : number = 60;               // 发射范围(弧度范围)
    radius : number = 1.0;             // 发射半径

    emitEffect : string;               // 发射特效(多个的话建议做到prefab上?) 包含音效?
}