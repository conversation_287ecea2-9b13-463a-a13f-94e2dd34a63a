System.register(["__unresolved_0", "cc", "__unresolved_1", "__unresolved_2"], function (_export, _context) {
  "use strict";

  var _reporterNs, _cclegacy, __checkObsolete__, __checkObsoleteInNamespace__, _decorator, Sprite, Plane, GameFunc, _dec, _dec2, _class, _class2, _descriptor, _crd, ccclass, property, ShadowPlane;

  function _initializerDefineProperty(target, property, descriptor, context) { if (!descriptor) return; Object.defineProperty(target, property, { enumerable: descriptor.enumerable, configurable: descriptor.configurable, writable: descriptor.writable, value: descriptor.initializer ? descriptor.initializer.call(context) : void 0 }); }

  function _applyDecoratedDescriptor(target, property, decorators, descriptor, context) { var desc = {}; Object.keys(descriptor).forEach(function (key) { desc[key] = descriptor[key]; }); desc.enumerable = !!desc.enumerable; desc.configurable = !!desc.configurable; if ('value' in desc || desc.initializer) { desc.writable = true; } desc = decorators.slice().reverse().reduce(function (desc, decorator) { return decorator(target, property, desc) || desc; }, desc); if (context && desc.initializer !== void 0) { desc.value = desc.initializer ? desc.initializer.call(context) : void 0; desc.initializer = undefined; } if (desc.initializer === void 0) { Object.defineProperty(target, property, desc); desc = null; } return desc; }

  function _initializerWarningHelper(descriptor, context) { throw new Error('Decorating class property failed. Please ensure that ' + 'transform-class-properties is enabled and runs after the decorators transform.'); }

  function _reportPossibleCrUseOfPlane(extras) {
    _reporterNs.report("Plane", "./Plane", _context.meta, extras);
  }

  function _reportPossibleCrUseOfGameFunc(extras) {
    _reporterNs.report("GameFunc", "../../GameFunc", _context.meta, extras);
  }

  return {
    setters: [function (_unresolved_) {
      _reporterNs = _unresolved_;
    }, function (_cc) {
      _cclegacy = _cc.cclegacy;
      __checkObsolete__ = _cc.__checkObsolete__;
      __checkObsoleteInNamespace__ = _cc.__checkObsoleteInNamespace__;
      _decorator = _cc._decorator;
      Sprite = _cc.Sprite;
    }, function (_unresolved_2) {
      Plane = _unresolved_2.default;
    }, function (_unresolved_3) {
      GameFunc = _unresolved_3.GameFunc;
    }],
    execute: function () {
      _crd = true;

      _cclegacy._RF.push({}, "b4975LRYDpEcZ9VB+6iHhpO", "ShadowPlane", undefined);

      __checkObsolete__(['_decorator', 'Sprite']);

      ({
        ccclass,
        property
      } = _decorator);

      _export("default", ShadowPlane = (_dec = ccclass("ShadowPlane"), _dec2 = property(Sprite), _dec(_class = (_class2 = class ShadowPlane extends (_crd && Plane === void 0 ? (_reportPossibleCrUseOfPlane({
        error: Error()
      }), Plane) : Plane) {
        constructor() {
          super(...arguments);

          _initializerDefineProperty(this, "skinImg", _descriptor, this);

          // m_fires: FireShells[] = [];
          this.m_screenDatas = [];
          this.m_config = null;
          this.m_fireState = null;
          this.m_changeOver = false;
          this.attackRatio = 1;
          this.isFrist = 0;
          this.bulletOpacity = 255;
          this.m_opacity = 255;
          // m_skillFires: Map<number, FireShells[]> = new Map();
          this.m_data = void 0;
          this.enemy = void 0;
        }

        onLoad() {// this.m_config = GameIns.mainPlaneManager.mainRecord;
          // this.m_data = GameIns.mainPlaneManager.data;
        }

        start() {
          this.enemy = false; // this.initPlane(true);

          this.new_uuid = (_crd && GameFunc === void 0 ? (_reportPossibleCrUseOfGameFunc({
            error: Error()
          }), GameFunc) : GameFunc).uuid;
        }

        init(isActive) {
          if (isActive === void 0) {
            isActive = false;
          }

          // if (SkillManager.default.me.getMySkillByType(23)) {
          //     this.node.active = true;
          //     if (BattleManager.default.me.isContinue) {
          //         this.hideSelf();
          //     }
          //     this.initPlane(isActive);
          // } else {
          this.node.active = false; // }
          // this.m_skillFires.forEach((fires) => {
          //     fires.forEach((fire) => {
          //         fire.clearData();
          //     });
          // });
        } // async initPlane(isActive: boolean = false) {
        //     this.m_config = MainPlaneManager.default.me.mainRecord;
        //     await GameIns.default.loadManager.setImage(
        //         this.skinImg,
        //         `${this.m_config.zjimage}_yz`,
        //         `package_mainPlane_trans_${this.m_config.type}`
        //     );
        //     if (!GameConfig.default.isHD) {
        //         const spriteFrame = this.skinImg.getComponent(Sprite).spriteFrame;
        //         if (spriteFrame) {
        //             this.skinImg.node.width = 0.66667 * spriteFrame.getOriginalSize().width;
        //             this.skinImg.node.height = 0.66667 * spriteFrame.getOriginalSize().height;
        //         }
        //     }
        //     this.getFireState();
        //     this.changeScreenLv(this.m_data.screenLv);
        //     this.mechaShoadow(isActive);
        // }
        // mechaShoadow(isActive: boolean = false) {
        //     const isGoldType = GameIns.battleManager.gameType !== GameEnum.GameType.Gold || this.m_config.type !== 710;
        //     const conditionMet = this.m_config.type === 710 && GameIns.gameDataManager.isConditionOver;
        //     if (isGoldType && !conditionMet && isActive) {
        //         this.skinImg.enabled = true;
        //         this.skinImg.node.destroyAllChildren();
        //     } else {
        //         this.skinImg.enabled = false;
        //         MainPlaneManager.MainPlaneMgr.plane.setUnit(this.skinImg.node, this.m_config.type === 709);
        //         const grayMaterial = Material.getBuiltinMaterial("2d-gray-sprite");
        //         this.skinImg.node.children.forEach((child) => {
        //             switch (this.m_config.type) {
        //                 case 708:
        //                 case 709:
        //                     child.scale = 0.64;
        //                     break;
        //                 case 710:
        //                     child.scale = 0.66667;
        //                     child.y = -60;
        //                     break;
        //                 case 711:
        //                     child.scale = 0.64;
        //                     break;
        //                 case 712:
        //                     child.scale = 0.64;
        //                     break;
        //                 case 713:
        //                     child.scale = 0.53333;
        //                     child.y = 9;
        //                     break;
        //                 case 714:
        //                 case 715:
        //                     child.scale = 0.64;
        //                     child.y = 9;
        //                     break;
        //                 default:
        //                     child.scale = 1;
        //             }
        //             child.active = this.m_config.type === 710 || GameIns.gameDataManager.isConditionOver;
        //             child.opacity = 178.5;
        //             child.children.forEach((grandChild) => {
        //                 const sprite = grandChild.getComponent(Sprite);
        //                 if (sprite) {
        //                     sprite.setMaterial(0, grayMaterial);
        //                 }
        //             });
        //         });
        //         this.skinImg.node.scale = 1;
        //     }
        // }
        // // ...前面的代码...
        // /**
        //  * 显示或隐藏机甲
        //  * @param isVisible 是否显示
        //  */
        // mechaShow(isVisible: boolean) {
        //     if (this.m_config.type === 710 || GameIns.gameDataManager.isConditionOver) {
        //         this.skinImg.enabled = !isVisible;
        //         this.skinImg.node.children.forEach((child) => {
        //             child.active = isVisible;
        //         });
        //     }
        // }
        // /**
        //  * 根据屏幕等级切换攻击点
        //  * @param level 屏幕等级
        //  */
        // changeScreenLv(level: number) {
        //     if (level !== 0) {
        //         this.m_screenDatas = [];
        //         const key = GameIns.gameDataManager.isConditionOver ? "transatk" : "shiftingatk";
        //         const attackData = this.m_config[`${key}${level}`];
        //         for (let i = 0; i < attackData.length; i += 8) {
        //             const segment = attackData.slice(i, i + 8);
        //             this.m_screenDatas.push(segment);
        //         }
        //         this.m_screenDatas.forEach((data, index) => {
        //             if (this.m_fires[index] == null) {
        //                 this.createAttackPoint(data);
        //             } else {
        //                 this.changeScreen(index, data);
        //             }
        //         });
        //         for (let i = this.m_screenDatas.length; i < this.m_fires.length; i++) {
        //             this.changeScreen(i, null);
        //         }
        //     }
        // }
        // /**
        //  * 切换屏幕攻击点
        //  * @param index 索引
        //  * @param data 攻击点数据
        //  */
        // changeScreen(index: number, data: any) {
        //     if (data == null) {
        //         if (index < this.m_fires.length) {
        //             const fireData = this.m_screenDatas[index];
        //             this.m_fires[index].setData(fireData, this.m_fireState, false, this);
        //         }
        //     } else if (index < this.m_fires.length) {
        //         this.m_fires[index].setData(data, this.m_fireState, false, this);
        //     } else {
        //         this.createAttackPoint(data);
        //     }
        // }
        // /**
        //  * 创建攻击点
        //  * @param data 攻击点数据
        //  */
        // createAttackPoint(data: any) {
        //     const node = new Node();
        //     node.parent = this.node;
        //     const fireShell = node.addComponent(FireShells);
        //     fireShell.setData(data, this.m_fireState, false, this);
        //     this.m_fires.push(fireShell);
        // }
        // /**
        //  * 获取火力状态
        //  */
        // getFireState() {
        //     const attackLv = SkillManager.default.me.getLv(SkillManager.SkillType.mainAttack);
        //     const hpAttackLv = SkillManager.default.me.getLv(SkillManager.SkillType.mainHpAttack);
        //     const speedLv = SkillManager.default.me.getLv(SkillManager.SkillType.mainSpeed);
        //     let attack = this.m_data.initAtk1 > 0 ? this.m_data.attack1 : this.m_data.attack2;
        //     if (
        //         MainPlaneManager.MainPlaneMgr.idToType(this.m_data.id) === 711 &&
        //         (GameIns.gameDataManager.isMechaOver || MainPlaneManager.MainPlaneMgr.checkPlayMechaAnim())
        //     ) {
        //         attack = this.m_data.attack2;
        //     }
        //     const cirtLv = SkillManager.default.me.getLv(SkillManager.SkillType.addCirt);
        //     this.m_fireState = {
        //         attack: attack * this.attackRatio,
        //         attackLv,
        //         hpAttackLv,
        //         speedLv,
        //         cirtLv,
        //     };
        // }
        // /**
        //  * 刷新火力状态
        //  */
        // refreshFireState() {
        //     this.getFireState();
        //     this.m_fires.forEach((fire) => {
        //         fire.setState(this.m_fireState, this);
        //     });
        //     const skillFires = this.m_skillFires.get(28);
        //     if (skillFires) {
        //         skillFires.forEach((fire) => {
        //             fire.setState(this.m_fireState, this, true);
        //         });
        //     }
        // }
        // /**
        //  * 播放影子瞄准动画
        //  */
        // playShadowAim() {
        //     this.init(true);
        //     this.isFrist = 1;
        //     this.node.y = 0;
        //     this.node.parent = GameIns.planeManager.mainPlane.node;
        //     this.node.zIndex = -1;
        //     this.skinImg.node.scale = 1.332;
        //     this.m_changeOver = false;
        //     tween(this.skinImg.node)
        //         .to(UIAnimMethods.default.fromTo(0, 1), { opacity: 255 })
        //         .to(UIAnimMethods.default.fromTo(1, 7), { opacity: 255 * this.m_opacity })
        //         .start();
        //     tween(this.node)
        //         .to(UIAnimMethods.default.fromTo(0, 1), { y: 0 })
        //         .to(UIAnimMethods.default.fromTo(1, 7), { y: -25 })
        //         .to(UIAnimMethods.default.fromTo(7, 10), { y: -20, x: -5 })
        //         .to(UIAnimMethods.default.fromTo(10, 12), { y: -20, x: 0 })
        //         .call(BattleManager.default.me.rogueui.bootDown.bind(BattleManager.default.me.rogueui))
        //         .call(this.playShadowAim2.bind(this))
        //         .start();
        // }
        // /**
        //  * 播放影子瞄准动画的第二阶段
        //  */
        // playShadowAim2() {
        //     tween(this.node)
        //         .delay(3)
        //         .to(UIAnimMethods.default.fromTo(0, 10), { y: 0 })
        //         .call(this.changeFather.bind(this))
        //         .start();
        // }
        // /**
        //  * 更改父节点
        //  */
        // changeFather() {
        //     this.node.parent = BattleLayer.default.me.selfPlaneLayer;
        //     this.node.zIndex = -1;
        //     this.node.y = GameIns.planeManager.mainPlane.node.y;
        //     this.m_changeOver = true;
        // }
        // /**
        //  * 隐藏自身
        //  */
        // hideSelf() {
        //     this.node.y = 0;
        //     this.node.parent = GameIns.planeManager.mainPlane.node;
        //     this.node.opacity = 0;
        //     this.skinImg.node.scale = 0;
        //     this.m_changeOver = false;
        // }
        // /**
        //  * 显示自身
        //  */
        // showSelf() {
        //     this.isFrist = 1;
        //     if (SkillManager.default.me.getSkillLevelById(23)) {
        //         this.node.active = true;
        //     }
        //     this.node.opacity = this.m_opacity;
        //     this.changeFather();
        // }
        // /**
        //  * 根据技能添加攻击点
        //  * @param skillId 技能 ID
        //  * @param data 攻击点数据
        //  * @param extra 额外数据
        //  */
        // addAttackPointBySkill(skillId: number, data: any[], extra: any[]) {
        //     const skillFires = this.m_skillFires.get(skillId);
        //     this.getFireState();
        //     this.m_fireState.extra = extra || [];
        //     if (!skillFires || skillFires.length === 0) {
        //         data.forEach((point) => {
        //             const node = new Node();
        //             node.parent = this.node;
        //             const fireShell = node.addComponent(FireShells);
        //             fireShell.setData(point, this.m_fireState, false, this);
        //             this.m_skillFires.set(skillId, [fireShell]);
        //         });
        //     } else {
        //         const maxLength = Math.max(skillFires.length, data.length);
        //         const updatedFires: FireShells[] = [];
        //         for (let i = 0; i < maxLength; i++) {
        //             if (data.length > i && skillFires.length > i) {
        //                 skillFires[i].setData(data[i], this.m_fireState, false, this);
        //                 updatedFires.push(skillFires[i]);
        //             } else if (data.length > i) {
        //                 const node = new Node();
        //                 node.parent = this.node;
        //                 const fireShell = node.addComponent(FireShells);
        //                 fireShell.setData(data[i], this.m_fireState, false, this);
        //                 updatedFires.push(fireShell);
        //             } else {
        //                 skillFires[i].node.destroy();
        //             }
        //         }
        //         this.m_skillFires.set(skillId, updatedFires);
        //     }
        // }
        // /**
        //  * 退出战斗
        //  */
        // battleQuit() {
        //     this.m_data.die = false;
        //     this.m_skillFires.forEach((fires) => {
        //         fires.forEach((fire) => {
        //             fire.node.parent = null;
        //             fire.node.destroy();
        //         });
        //     });
        //     this.m_skillFires.clear();
        //     this.node.active = false;
        // }
        // /**
        //  * 更新方法
        //  * @param deltaTime 每帧时间
        //  */
        // update(deltaTime: number) {
        //     if (this.node.active) {
        //         this.node.x = -GameIns.planeManager.mainPlane.node.x;
        //         if (this.m_changeOver) {
        //             this.node.y = GameIns.planeManager.mainPlane.node.y;
        //             this.skinImg.node.scale = GameIns.planeManager.mainPlane.skin.scale;
        //         } else {
        //             this.skinImg.node.scale = GameIns.planeManager.mainPlane.skin.scale * this.isFrist;
        //         }
        //     }
        // }


      }, (_descriptor = _applyDecoratedDescriptor(_class2.prototype, "skinImg", [_dec2], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function initializer() {
          return null;
        }
      })), _class2)) || _class));

      _cclegacy._RF.pop();

      _crd = false;
    }
  };
});
//# sourceMappingURL=89d3bd81b1fc700fc936f392a2c83ac6b3d97fee.js.map