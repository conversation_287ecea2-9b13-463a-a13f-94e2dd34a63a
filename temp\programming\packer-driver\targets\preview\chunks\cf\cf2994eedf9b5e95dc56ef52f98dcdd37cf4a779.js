System.register(["cc"], function (_export, _context) {
  "use strict";

  var _cclegacy, SingletonBase, _crd;

  _export("SingletonBase", void 0);

  return {
    setters: [function (_cc) {
      _cclegacy = _cc.cclegacy;
    }],
    execute: function () {
      _crd = true;

      _cclegacy._RF.push({}, "e2154G8xtlJBLeB3hiPg5+A", "SingletonBase", undefined);

      /**
       * 单件基类，所有单件对象（一般管理器类使用）
       */
      _export("SingletonBase", SingletonBase = class SingletonBase {
        constructor() {
          this._instance = null;
        }

        static getInstance(o) {
          if (this.prototype._instance == null) {
            this.prototype._instance = new o();
          }

          return this.prototype._instance;
        }

      });

      _cclegacy._RF.pop();

      _crd = false;
    }
  };
});
//# sourceMappingURL=cf2994eedf9b5e95dc56ef52f98dcdd37cf4a779.js.map