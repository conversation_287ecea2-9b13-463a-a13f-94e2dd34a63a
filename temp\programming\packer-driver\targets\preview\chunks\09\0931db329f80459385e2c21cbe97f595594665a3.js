System.register(["__unresolved_0", "cc", "__unresolved_1"], function (_export, _context) {
  "use strict";

  var _reporterNs, _cclegacy, __checkObsolete__, __checkObsoleteInNamespace__, _decorator, BossBase, _dec, _class, _crd, ccclass, property, BossEntity;

  function _reportPossibleCrUseOfBossBase(extras) {
    _reporterNs.report("BossBase", "./BossBase", _context.meta, extras);
  }

  function _reportPossibleCrUseOfAttackPoint(extras) {
    _reporterNs.report("AttackPoint", "../../base/AttackPoint", _context.meta, extras);
  }

  function _reportPossibleCrUseOfBossUnit(extras) {
    _reporterNs.report("BossUnit", "./BossUnit", _context.meta, extras);
  }

  function _reportPossibleCrUseOfTrackComponent(extras) {
    _reporterNs.report("TrackComponent", "../../base/TrackComponent", _context.meta, extras);
  }

  function _reportPossibleCrUseOfPfFrameAnim(extras) {
    _reporterNs.report("PfFrameAnim", "../../base/PfFrameAnim", _context.meta, extras);
  }

  return {
    setters: [function (_unresolved_) {
      _reporterNs = _unresolved_;
    }, function (_cc) {
      _cclegacy = _cc.cclegacy;
      __checkObsolete__ = _cc.__checkObsolete__;
      __checkObsoleteInNamespace__ = _cc.__checkObsoleteInNamespace__;
      _decorator = _cc._decorator;
    }, function (_unresolved_2) {
      BossBase = _unresolved_2.default;
    }],
    execute: function () {
      _crd = true;

      _cclegacy._RF.push({}, "6bc73hm8i9KIYJkFQMVQ7+6", "BossEntity", undefined);

      __checkObsolete__(['_decorator', 'Component']);

      ({
        ccclass,
        property
      } = _decorator);

      _export("default", BossEntity = (_dec = ccclass("BossEntity"), _dec(_class = class BossEntity extends (_crd && BossBase === void 0 ? (_reportPossibleCrUseOfBossBase({
        error: Error()
      }), BossBase) : BossBase) {
        constructor() {
          super(...arguments);
          this._datas = [];
          this._data = null;
          this._atkPointsPool = [];
          this._attacks = [];
          this._uiNode = null;
          this._topAnimNode = null;
          this._idleName = "idle1";
          this._units = new Map();
          this._colUnitsIndex = 0;
          this._colUnits = [];
          this._deadUnitIds = [];
          this._atkUnits = [];
          this._atkUnitSounds = new Map();
          this._atkUnitSoundIds = new Map();
          this._formIndex = -1;
          this._formNum = 0;
          this._nextForm = false;
          this._prePosX = 0;
          this._prePosY = 0;
          this._posX = 0;
          this._posY = 0;
          this._trackCom = null;
          this._curTrackType = -1;
          this._curTrack = null;
          this._trackTime = 0;
          this._trackOffX = 0;
          this._trackOffY = 0;
          this._moveToX = 0;
          this._moveToY = 0;
          this._moveSpeed = 0;
          this._bArriveDes = false;
          this._transFormMove = false;
          this._nextWayPointTime = 0;
          this._nextWayPointX = 0;
          this._nextWayPointY = 0;
          this._nextWayPointInterval = 0;
          this._nextWaySpeed = 0;
          this._shootAble = true;
          this._atkActions = [];
          this._nextAttackInterval = 0;
          this._nextAttackTime = 0;
          this._bOrderAttack = false;
          this._orderIndex = 0;
          this._attackID = 0;
          this._atkPointDatas = [];
          this._attackPoints = [];
          this._orderAtkArr = [];
          this._action = -1;
          this._bDamageable = false;
          this._bAttackMove = false;
          this._bFirstWayPoint = false;
          this.transformBattle = true;
          this._bRemoveable = false;
          this._shadow = null;
          this.wingmanPlanes = [];
          this._cloakeAnim = null;
        }

        /**
         * 初始化 Boss 数据
         * @param datas Boss 数据数组
         */
        init(datas) {
          this._datas = datas;
          this._formNum = this._datas.length;
          this._bFirstWayPoint = true; // this._initUI();
          // this._initProperty();
          // this._initTrack();
          // this.setFormIndex(0);

          this.active = true;
        }
        /**
         * 设置影子
         * @param shadow 影子对象
         */


        setShadow(shadow) {
          this._shadow = shadow;
        } //     /**
        //      * 设置形态索引
        //      * @param index 形态索引
        //      */
        //     setFormIndex(index: number) {
        //         if (this._formIndex !== index) {
        //             this._formIndex = index;
        //             this._bOrderAttack = true;
        //             this._orderIndex = 0;
        //             this._data = this._datas[this._formIndex];
        //             this._idleName = `idle${this._formIndex + 1}`;
        //             this._collideAtk = this._data.collideAttack;
        //             if (index === 0) {
        //                 this._initUnits();
        //                 this.setAction(BossAction.Appear);
        //             } else {
        //                 this._units.forEach((unit) => {
        //                     unit.init(BossManager.getUnitData(unit.id), this);
        //                     unit.setCollideAtk(this._data.collideAttack);
        //                     unit.setPropertyRate(this.propertyRate);
        //                 });
        //                 this.setAction(BossAction.Transform);
        //             }
        //             this._orderAtkArr = [];
        //             for (let i = 0; i < this._data.attackActions.length; i++) {
        //                 this._orderAtkArr.push(i);
        //             }
        //             this._atkPointDatas = [];
        //             for (const point of this._data.attackPoints) {
        //                 const data = [point.bAvailable, point];
        //                 this._atkPointDatas.push(data);
        //             }
        //             this._atkActions = [...this._data.attackActions];
        //             this._colUnitsIndex = 0;
        //             this._colUnits = [];
        //             this._deadUnitIds = [];
        //             this.unitArr = [];
        //         }
        //     }
        //     /**
        //      * 进入下一形态
        //      */
        //     enterNextForm() {
        //         if (this._formIndex < this._datas.length - 1) {
        //             this._formIndex++;
        //             this.setFormIndex(this._formIndex);
        //         }
        //     }
        //     /**
        //      * 设置 Boss 的行为
        //      * @param action 行为类型
        //      */
        //     setAction(action: BossAction) {
        //         if (this._action !== action) {
        //             this._action = action;
        //             switch (this._action) {
        //                 case BossAction.Normal:
        //                     this._playSkel(this._idleName, true);
        //                     this.setDamangeable(true);
        //                     break;
        //                 case BossAction.Appear:
        //                     this._playSkel(`enter${this._formIndex + 1}`, true);
        //                     this.setDamangeable(false);
        //                     this._startAppearTrack();
        //                     break;
        //                 case BossAction.Transform:
        //                     this._playSkel(`ready${this._formIndex + 1}`, false, () => {
        //                         this.transformBattle && this.transformEnd();
        //                     });
        //                     this.setDamangeable(false);
        //                     break;
        //                 case BossAction.AttackPrepare:
        //                     this._checkAtkAnim() || this.scheduleOnce(() => {
        //                         this.setAction(BossAction.AttackIng);
        //                     });
        //                     this.setDamangeable(true);
        //                     break;
        //                 case BossAction.AttackIng:
        //                 case BossAction.AttackOver:
        //                     this.setDamangeable(true);
        //                     break;
        //                 case BossAction.Blast:
        //                     this.setDamangeable(false);
        //                     break;
        //                 default:
        //                     this.setDamangeable(true);
        //             }
        //         }
        //     }
        //     // ...前面的代码...
        //     /**
        //      * 检查并生成 Boss 的僚机
        //      */
        //     checkBossWingman() {
        //         if (this._data.enemyId > 0 && this._data.enemyPos.length > 0) {
        //             for (let i = 0; i < this._data.enemyPos.length; i++) {
        //                 const wingman = EnemyManager.EnemyMgr.addPlane(this._data.enemyId);
        //                 wingman.setExp(0);
        //                 wingman.setScaleType(EnemyScale.None);
        //                 wingman.attack = this._data.attack;
        //                 wingman.node.position = this._data.enemyPos[i];
        //                 const trackGroup = i % 2 === 0 ? this._data.enemyTrackGroup1 : this._data.enemyTrackGroup2;
        //                 wingman.initTrack(trackGroup, [-1, 0, 0], wingman.node.position.x, wingman.node.position.y);
        //                 wingman.startBattle();
        //                 wingman.collideAble = false;
        //                 this.wingmanPlanes.push(wingman);
        //             }
        //         }
        //     }
        //     /**
        //      * 开始战斗
        //      */
        //     startBattle() {
        //         this.active = true;
        //         this._startNormalTrack();
        //         this.setAction(BossAction.Normal);
        //         this._checkNextCollideUnits();
        //         this.checkBossWingman();
        //     }
        //     /**
        //      * 检查下一个碰撞单元
        //      */
        //     _checkNextCollideUnits(): boolean {
        //         const currentUnits = this._data.unitsOrder[this._colUnitsIndex];
        //         let hasAliveUnits = false;
        //         for (const unitId of currentUnits) {
        //             if (!Tools.arrContain(this._deadUnitIds, unitId)) {
        //                 hasAliveUnits = true;
        //                 break;
        //             }
        //         }
        //         if (hasAliveUnits) {
        //             if (this._colUnits.length === 0) {
        //                 for (const unitId of currentUnits) {
        //                     const unit = this._units.get(unitId);
        //                     unit.setCollideAble(true);
        //                     this._colUnits.push(unit);
        //                     this.unitArr.push(unit);
        //                 }
        //             }
        //         } else {
        //             this._colUnitsIndex++;
        //             if (this._colUnitsIndex >= this._data.unitsOrder.length) {
        //                 return false;
        //             }
        //             const nextUnits = this._data.unitsOrder[this._colUnitsIndex];
        //             this._colUnits = [];
        //             this.unitArr = [];
        //             for (const unitId of nextUnits) {
        //                 const unit = this._units.get(unitId);
        //                 unit.setCollideAble(true);
        //                 this._colUnits.push(unit);
        //                 this.unitArr.push(unit);
        //             }
        //         }
        //         return true;
        //     }
        //     /**
        //      * 更新游戏逻辑
        //      * @param deltaTime 每帧时间
        //      */
        //     updateGameLogic(deltaTime: number) {
        //         if (this.active && !this.isDead && !this._nextForm) {
        //             this.wingmanPlanes.forEach((wingman) => {
        //                 wingman.node.angle += this._data.enemyRotate * deltaTime;
        //             });
        //             switch (this._action) {
        //                 case BossAction.Normal:
        //                     this._processNextWayPoint(deltaTime);
        //                     this._updateMove(deltaTime);
        //                     this._processNextAttack(deltaTime);
        //                     break;
        //                 case BossAction.Appear:
        //                     this._updateMove(deltaTime);
        //                     if (this._bArriveDes) {
        //                         this.setAction(BossAction.Transform);
        //                     }
        //                     break;
        //                 case BossAction.Transform:
        //                     if (this._transFormMove) {
        //                         this._updateMove(deltaTime);
        //                     }
        //                     break;
        //                 case BossAction.AttackPrepare:
        //                     this._processNextWayPoint(deltaTime);
        //                     if (this._bAttackMove) {
        //                         this._updateMove(deltaTime);
        //                     }
        //                     break;
        //                 case BossAction.AttackIng:
        //                     this._processNextWayPoint(deltaTime);
        //                     if (this._bAttackMove) {
        //                         this._updateMove(deltaTime);
        //                     }
        //                     this._udpateShoot(deltaTime);
        //                     break;
        //                 case BossAction.AttackOver:
        //                     this._processNextWayPoint(deltaTime);
        //                     if (this._bAttackMove) {
        //                         this._updateMove(deltaTime);
        //                     }
        //                     this.setAction(BossAction.Normal);
        //                     break;
        //                 case BossAction.Blast:
        //                     break;
        //             }
        //             this._units.forEach((unit) => {
        //                 unit.updateGameLogic(deltaTime);
        //             });
        //         }
        //     }
        //     /**
        //      * Boss 死亡逻辑
        //      */
        //     toDie() {
        //         if (!this.isDead) {
        //             this.isDead = true;
        //             this.setAction(BossAction.Blast);
        //             this._playDieAnim();
        //             if (this._data.id >= 250 && this._data.id < 300) {
        //                 let allBossesDead = true;
        //                 for (const boss of BossManager.BossMgr.bosses) {
        //                     if (boss !== this && !boss.isDead) {
        //                         allBossesDead = false;
        //                         break;
        //                     }
        //                 }
        //                 if (allBossesDead) {
        //                     this.checkLoot();
        //                 }
        //             } else if (this._data.nextBoss.length > 0 && GameIns.battleManager.isGameType(GameType.Boss)) {
        //                 // Do nothing, next boss will be handled
        //             } else {
        //                 this.checkLoot();
        //             }
        //             this.onDie();
        //             GameIns.loadManager.addExp(this._data.exp);
        //         }
        //     }
        //     /**
        //      * 单元销毁逻辑
        //      * @param unit 被销毁的单元
        //      */
        //     unitDestroyed(unit: any) {
        //         this._deadUnitIds.push(unit.unitId);
        //         Tools.arrRemove(this._colUnits, unit);
        //         let allUnitsDead = true;
        //         this._units.forEach((unit) => {
        //             if (!unit.isDead) {
        //                 allUnitsDead = false;
        //             }
        //         });
        //         for (let i = 0; i < this._atkActions.length; i++) {
        //             let hasActivePoints = false;
        //             for (const pointId of this._atkActions[i].atkPointId) {
        //                 const pointData = this._atkPointDatas[pointId];
        //                 if (pointData[0]) {
        //                     if (unit.unitId === pointData[1].atkUnitId) {
        //                         pointData[0] = false;
        //                     } else {
        //                         hasActivePoints = true;
        //                     }
        //                 }
        //             }
        //             if (!hasActivePoints) {
        //                 this._atkActions.splice(i, 1);
        //                 i--;
        //             }
        //         }
        //         for (let i = 0; i < this._attackPoints.length; i++) {
        //             if (this._attackPoints[i].getAtkUnitId() === unit.unitId) {
        //                 this._attackPoints.splice(i, 1);
        //                 i--;
        //             }
        //         }
        //         const soundKey = this._atkUnitSounds.get(unit.unitId);
        //         if (soundKey) {
        //             this._atkUnitSounds.delete(unit.unitId);
        //         }
        //         const soundId = this._atkUnitSoundIds.get(unit.unitId);
        //         if (soundId !== null) {
        //             this._atkUnitSoundIds.delete(unit.unitId);
        //             GameIns.default.audioManager.stopEffect(soundId);
        //         }
        //         this.setBodySkin(this._deadUnitIds.length);
        //         if (allUnitsDead) {
        //             this._formIndex++;
        //             if (this._checkNextForm()) {
        //                 this._nextForm = true;
        //             } else {
        //                 this._formIndex--;
        //                 this.toDie();
        //             }
        //         } else {
        //             this._checkNextCollideUnits();
        //         }
        //     }
        //     /**
        //      * 单元销毁动画结束
        //      * @param unit 被销毁的单元
        //      */
        //     unitDestroyAnimEnd(unit: any) {
        //         if (this._nextForm) {
        //             this._nextForm = false;
        //             this.setFormIndex(this._formIndex);
        //         }
        //     }
        //     /**
        //      * 检查是否有下一形态
        //      */
        //     _checkNextForm(): boolean {
        //         return this._formIndex < this._datas.length;
        //     }
        //     // ...继续按照文件中的顺序还原剩余函数...
        //     /**
        //      * 初始化属性
        //      */
        //     _initProperty() {
        //         for (let i = 0; i < this._datas.length; i++) {
        //             const data = this._datas[i];
        //             this._attacks.push(data.attack);
        //         }
        //     }
        //     /**
        //      * 设置属性倍率
        //      * @param rates 属性倍率数组
        //      * @param updateHp 是否更新血量
        //      */
        //     setPropertyRate(rates: number[], updateHp: boolean = false) {
        //         super.setPropertyRate(rates);
        //         this.m_totalHp = 0;
        //         this.m_curHp = 0;
        //         if (rates.length > 1) {
        //             for (let i = 0; i < this._attacks.length; i++) {
        //                 this._attacks[i] *= rates[1];
        //             }
        //         }
        //         if (rates.length > 2) {
        //             this._collideAtk *= rates[2];
        //         }
        //         this._units.forEach((unit) => {
        //             unit.setPropertyRate(this.propertyRate);
        //             this.m_totalHp += unit.maxHp;
        //             this.m_curHp += unit.maxHp;
        //         });
        //         if (this._data.nextBoss.length > 0) {
        //             for (let i = 0; i < this._data.nextBoss.length; i++) {
        //                 const bossData = BossManager.getBossDatas(this._data.nextBoss[i]);
        //                 if (bossData.length > 0) {
        //                     for (const unitId of bossData[0].units) {
        //                         const unitData = BossManager.getUnitData(unitId);
        //                         if (unitData) {
        //                             this.m_totalHp += unitData.hp * (this.propertyRate[0] || 1);
        //                             this.m_curHp += unitData.hp * (this.propertyRate[0] || 1);
        //                         }
        //                     }
        //                 }
        //             }
        //         }
        //         if (!updateHp) {
        //             BossBattleManager.setCurHp(this.m_curHp);
        //             BossBattleManager.setTotalHp(this.m_totalHp);
        //         }
        //     }
        //     /**
        //      * 获取形态数量
        //      */
        //     get formNum(): number {
        //         return this._formNum;
        //     }
        //     /**
        //      * 获取当前形态索引
        //      */
        //     get formIndex(): number {
        //         return this._formIndex;
        //     }
        //     /**
        //      * 获取当前形态的攻击力
        //      */
        //     get attack(): number {
        //         return this._attacks[this._formIndex];
        //     }
        //     /**
        //      * 获取碰撞攻击力
        //      */
        //     getColliderAtk(): number {
        //         return this._collideAtk;
        //     }
        //     /**
        //      * 获取 X 坐标
        //      */
        //     get posX(): number {
        //         return this._posX;
        //     }
        //     /**
        //      * 获取 Y 坐标
        //      */
        //     get posY(): number {
        //         return this._posY;
        //     }

        /**
         * 是否可被攻击
         */


        isDamageable() {
          return this._bDamageable;
        } //     /**
        //      * 是否可移除
        //      */
        //     get removeAble(): boolean {
        //         return this._bRemoveable;
        //     }
        //     set removeAble(value: boolean) {
        //         this._bRemoveable = value;
        //     }
        //     /**
        //      * 获取所有碰撞单元
        //      */
        //     getAllColUnits(): any[] {
        //         return this._colUnits;
        //     }
        //     /**
        //      * 设置是否可被攻击
        //      * @param damageable 是否可被攻击
        //      */
        //     setDamangeable(damageable: boolean) {
        //         this._bDamageable = damageable;
        //         this._units.forEach((unit) => {
        //             if (!unit.isDead) {
        //                 unit.damageable = damageable;
        //             }
        //         });
        //     }
        //     /**
        //      * 初始化 UI
        //      */
        //     _initUI() {
        //         this._uiNode = new Node();
        //         this.node.addChild(this._uiNode);
        //         this._topAnimNode = new Node();
        //         this.node.addChild(this._topAnimNode);
        //     }
        //     /**
        //      * 初始化单元
        //      */
        //     _initUnits() {
        //         Tools.removeChildByName(this._uiNode, "units");
        //         const unitsNode = new Node();
        //         this._uiNode.addChild(unitsNode);
        //         unitsNode.name = "units";
        //         for (const unitId of this._data.units) {
        //             const unitData = BossManager.getUnitData(unitId);
        //             if (unitData) {
        //                 const unitNode = new Node();
        //                 unitsNode.addChild(unitNode);
        //                 const unit = unitNode.addComponent(BossUnit);
        //                 unit.init(unitData, this);
        //                 unit.setCollideAtk(this._data.collideAttack);
        //                 unit.setPropertyRate(this.propertyRate);
        //                 this._units.set(unitData.uId, unit);
        //             }
        //         }
        //         for (let i = this._atkPointsPool.length; i < this._atkPointDatas.length; i++) {
        //             const pointNode = new Node();
        //             this.node.addChild(pointNode);
        //             pointNode.angle = -180;
        //             const attackPoint = pointNode.addComponent(AttackPoint);
        //             this._atkPointsPool.push(attackPoint);
        //         }
        //     }
        //     /**
        //      * 设置身体皮肤
        //      * @param skinIndex 皮肤索引
        //      */
        //     setBodySkin(skinIndex: number) {
        //         this._units.forEach((unit) => {
        //             if (unit.isBody()) {
        //                 unit.setSkin(skinIndex);
        //             }
        //         });
        //     }
        //     // ...继续按照文件中的顺序还原剩余函数...
        //     /**
        //  * 初始化轨迹
        //  */
        //     _initTrack() {
        //         this._trackCom = Tools.addScript(this.node, TrackComponent);
        //         this._trackCom.setTrackGroupStartCall((trackGroup, trackIndex, trackType) => { });
        //         this._trackCom.setTrackGroupOverCall((trackGroup) => {
        //             if (this._action === BossAction.Appear) {
        //                 this._trackCom.setTrackAble(false);
        //                 this.setAction(BossAction.Transform);
        //             }
        //         });
        //         this._trackCom.setTrackOverCall(() => { });
        //         this._trackCom.setTrackLeaveCall(() => { });
        //         this._trackCom.setTrackStartCall((track) => {
        //             this.setTrackType(track);
        //         });
        //     }
        //     /**
        //      * 开始出现轨迹
        //      */
        //     _startAppearTrack() {
        //         const trackGroup = new EnemyWave.TrackGroup();
        //         trackGroup.loopNum = 1;
        //         trackGroup.trackIDs = [this._data.appearParam[2]];
        //         trackGroup.speeds = [this._data.appearParam[3]];
        //         trackGroup.trackIntervals = [0];
        //         this._trackCom.init(this, [trackGroup], [], this._data.appearParam[0], this._data.appearParam[1]);
        //         this._trackCom.setTrackAble(true);
        //         this._trackCom.startTrack();
        //     }
        //     /**
        //      * 开始正常轨迹
        //      */
        //     _startNormalTrack() {
        //         this._trackCom.init(this, this._data.trackGroups, [], this.node.x, this.node.y);
        //         this._trackCom.setTrackAble(true);
        //         this._trackCom.startTrack();
        //         this.setAction(BossAction.Normal);
        //     }
        //     /**
        //      * 设置轨迹类型
        //      * @param track 当前轨迹
        //      */
        //     setTrackType(track: any) {
        //         if (track) {
        //             switch (track.type) {
        //                 case 4:
        //                 case 5:
        //                     if (this._curTrackType !== 4 && this._curTrackType !== 5) {
        //                         this._trackCom.setTrackAble(false);
        //                         this._shootAble = false;
        //                         this._colUnits.forEach((unit) => unit.setCollideAble(false));
        //                         this._playCloakeHideAnim(() => {
        //                             this._trackCom.setTrackAble(true);
        //                         });
        //                     }
        //                     break;
        //                 default:
        //                     if (this._curTrackType === 4 || this._curTrackType === 5) {
        //                         this._shootAble = false;
        //                         this._trackCom.setTrackAble(false);
        //                         this._playCloakeShowAnim(() => {
        //                             this._shootAble = true;
        //                             this._trackCom.setTrackAble(true);
        //                             this._colUnits.forEach((unit) => unit.setCollideAble(true));
        //                         });
        //                         this._playSkel("cloake", true);
        //                     }
        //             }
        //             this._curTrackType = track.type;
        //         }
        //     }
        //     /**
        //      * 移动到指定位置
        //      * @param x X 坐标
        //      * @param y Y 坐标
        //      * @param speed 移动速度
        //      * @param transformMove 是否为变形移动
        //      */
        //     moveToPos(x: number, y: number, speed: number, transformMove: boolean = false) {
        //         this._moveToX = x;
        //         this._moveToY = y;
        //         this._moveSpeed = speed;
        //         this._bArriveDes = false;
        //         this._transFormMove = transformMove;
        //     }
        //     /**
        //      * 设置位置
        //      * @param pos 位置
        //      * @param arrived 是否到达目标
        //      */
        //     setPosition(pos: Vec2, arrived: boolean = false) {
        //         this._posX = pos.x;
        //         this._posY = pos.y;
        //         this.setPos(this._posX, this._posY);
        //         this._bArriveDes = arrived;
        //     }
        //     /**
        //      * 设置位置
        //      * @param x X 坐标
        //      * @param y Y 坐标
        //      * @param update 是否更新
        //      */
        //     setPos(x: number, y: number, update: boolean = true) {
        //         super.setPos(x, y, update);
        //         this._posX = x;
        //         this._posY = y;
        //     }
        //     /**
        //      * 处理下一个路径点
        //      * @param deltaTime 每帧时间
        //      */
        //     _processNextWayPoint(deltaTime: number) {
        //         if (this._bArriveDes && this._data.trackGroups.length === 0) {
        //             this._nextWayPointTime += deltaTime;
        //             if (this._nextWayPointTime > this._nextWayPointInterval) {
        //                 this._nextWayPointInterval = Tools.getRandomInArray(this._data.wayPointIntervals);
        //                 this._nextWayPointTime = 0;
        //                 if (this._bFirstWayPoint) {
        //                     this._bFirstWayPoint = false;
        //                 } else {
        //                     const index = Tools.random_int(0, this._data.wayPointXs.length - 1);
        //                     this._nextWayPointX = this._data.wayPointXs[index];
        //                     this._nextWayPointY = this._data.wayPointYs[index];
        //                     this._nextWaySpeed = Tools.getRandomInArray(this._data.speeds);
        //                     this.moveToPos(this._nextWayPointX, this._nextWayPointY, this._nextWaySpeed);
        //                 }
        //             }
        //         }
        //     }
        //     /**
        //      * 更新移动逻辑
        //      * @param deltaTime 每帧时间
        //      */
        //     _updateMove(deltaTime: number) {
        //         if (this._action === BossAction.Appear || this._data.trackGroups.length > 0) {
        //             this._trackCom.updateGameLogic(deltaTime);
        //         } else if (!this._bArriveDes) {
        //             this._prePosX = this._posX;
        //             this._prePosY = this._posY;
        //             const dx = this._moveToX - this._posX;
        //             const dy = this._moveToY - this._posY;
        //             const distance = Math.sqrt(dx * dx + dy * dy);
        //             const moveDistance = Math.min(this._moveSpeed * deltaTime, distance);
        //             const moveX = (dx / distance) * moveDistance;
        //             const moveY = (dy / distance) * moveDistance;
        //             this._posX += moveX;
        //             this._posY += moveY;
        //             this.setPos(this._posX, this._posY);
        //             this._bArriveDes = distance <= 0.5;
        //         }
        //     }
        //     // ...继续按照文件中的顺序还原剩余函数...
        //     /**
        //      * 处理下一次攻击
        //      * @param deltaTime 每帧时间
        //      */
        //     _processNextAttack(deltaTime: number) {
        //         if (this._shootAble && this._action === BossAction.Normal) {
        //             this._nextAttackTime += deltaTime;
        //             if (this._nextAttackTime > this._nextAttackInterval) {
        //                 this._nextAttackInterval = Tools.getRandomInArray(this._data.attackIntervals);
        //                 this._nextAttackTime = 0;
        //                 let attackAction = null;
        //                 if (this._bOrderAttack) {
        //                     const randomIndex = Tools.getRandomInArray(this._orderAtkArr);
        //                     Tools.arrRemove(this._orderAtkArr, randomIndex);
        //                     attackAction = this._atkActions[randomIndex];
        //                     this._orderIndex++;
        //                     if (this._orderIndex > this._atkActions.length - 1) {
        //                         this._bOrderAttack = false;
        //                     }
        //                 } else {
        //                     attackAction = Tools.getRandomInArray(this._atkActions);
        //                 }
        //                 if (attackAction) {
        //                     this._bAttackMove = attackAction.bAtkMove;
        //                     this._attackID = attackAction.atkActId;
        //                     this._attackPoints.splice(0);
        //                     for (const pointId of attackAction.atkPointId) {
        //                         const pointData = this._atkPointDatas[pointId];
        //                         if (pointData[0]) {
        //                             const attackPoint = this._atkPointsPool[pointId] || new AttackPoint();
        //                             attackPoint.initForBoss(pointData[1], this);
        //                             this._attackPoints.push(attackPoint);
        //                         }
        //                     }
        //                     if (this._attackPoints.length > 0) {
        //                         this.setAction(BossAction.AttackPrepare);
        //                     }
        //                 }
        //             }
        //         }
        //     }
        //     /**
        //      * 更新射击逻辑
        //      * @param deltaTime 每帧时间
        //      */
        //     _udpateShoot(deltaTime: number) {
        //         if (this._shootAble) {
        //             let allAttacksOver = true;
        //             for (const attackPoint of this._attackPoints) {
        //                 attackPoint.updateGameLogic(deltaTime);
        //                 if (!attackPoint.isAttackOver()) {
        //                     allAttacksOver = false;
        //                 }
        //             }
        //             if (allAttacksOver) {
        //                 this.setAction(BossAction.AttackOver);
        //             }
        //         }
        //     }
        //     /**
        //      * 检查攻击动画
        //      */
        //     _checkAtkAnim(): boolean {
        //         let hasAnimation = false;
        //         for (const attackPoint of this._attackPoints) {
        //             for (const anim of attackPoint.getAtkAnims()) {
        //                 const unit = this._units.get(anim[0]);
        //                 if (unit && !Tools.arrContain(this._deadUnitIds, unit.unitId)) {
        //                     hasAnimation = true;
        //                     unit.playSkel(anim[1], false, () => {
        //                         this.setAction(BossAction.AttackIng);
        //                     });
        //                 }
        //             }
        //         }
        //         return hasAnimation;
        //     }
        //     /**
        //      * 播放骨骼动画
        //      * @param animName 动画名称
        //      * @param loop 是否循环
        //      * @param callback 动画结束回调
        //      * @param unitId 单元 ID
        //      */
        //     _playSkel(animName: string, loop: boolean, callback: Function = null, unitId: number = -1) {
        //         this._units.forEach((unit, id) => {
        //             if (unitId === -1 || id === unitId) {
        //                 unit.playSkel(animName, loop, callback);
        //             }
        //         });
        //     }
        //     /**
        //      * 播放死亡动画
        //      */
        //     _playDieAnim() {
        //         const frameTime = GameConfig.ActionFrameTime;
        //         const playBlastEffects = () => {
        //             for (const blast of this._data.blastParam) {
        //                 const delay = blast[3] * frameTime;
        //                 this.scheduleOnce(() => {
        //                     EnemyEffectLayer.addBlastEffect(this, blast[2], {
        //                         x: blast[0],
        //                         y: blast[1],
        //                         scale: blast[4],
        //                         angle: blast[5],
        //                     });
        //                 }, delay);
        //             }
        //         };
        //         if (this._data.nextBoss.length > 0) {
        //             this._checkNextBoss();
        //         } else {
        //             this._bRemoveable = true;
        //         }
        //         playBlastEffects();
        //     }
        //     /**
        //      * 播放白屏死亡动画
        //      */
        //     playDieWhiteAnim() {
        //         this.scheduleOnce(() => {
        //             EffectLayer.showWhiteScreen(4 * GameConfig.ActionFrameTime, 255);
        //             this._uiNode.opacity = 0;
        //             this._units.forEach((unit) => {
        //                 unit.hideSmoke();
        //             });
        //         }, 41 * GameConfig.ActionFrameTime);
        //     }
        //     /**
        //      * 播放坠落动画
        //      */
        //     _playFallAnim() {
        //         const frameTime = GameConfig.ActionFrameTime;
        //         const fallSequence = sequence(
        //             moveTo(2 * frameTime, v2(-1, 6)),
        //             moveTo(frameTime, v2(3, -2))
        //         );
        //         this._uiNode.runAction(repeatForever(fallSequence));
        //         this._uiNode.runAction(scaleTo(60 * frameTime, 0.5));
        //     }
        //     /**
        //      * 播放震动动画
        //      */
        //     _playShakeAnim() {
        //         const frameTime = GameConfig.ActionFrameTime;
        //         tween(this._uiNode)
        //             .to(2 * frameTime, { position: v2(-3, -2) })
        //             .to(2 * frameTime, { position: v2(11, -14), angle: 1 })
        //             .to(2 * frameTime, { position: v2(7, 4) })
        //             .to(2 * frameTime, { position: v2(20, -9), angle: 0 })
        //             .to(2 * frameTime, { position: v2(29, 7) })
        //             .to(frameTime, { position: v2(13, -5) })
        //             .to(frameTime, { position: v2(17, 2) })
        //             .to(frameTime, { position: v2(4, -6) })
        //             .to(frameTime, { position: v2(14, 4) })
        //             .to(frameTime, { position: v2(-1, -4) })
        //             .to(frameTime, { position: v2(5, 6) })
        //             .to(frameTime, { position: v2(-3, -5) })
        //             .to(frameTime, { position: v2(1, 3) })
        //             .to(frameTime, { position: v2(-7, -6) })
        //             .to(frameTime, { position: v2(0, 2) })
        //             .to(frameTime, { position: v2(-3, -4) })
        //             .delay(frameTime)
        //             .to(frameTime, { position: v2(0, 0) })
        //             .start();
        //     }
        //     /**
        //      * 播放坠落震动动画
        //      */
        //     _playFallShake() {
        //         const frameTime = GameConfig.ActionFrameTime;
        //         const fallShakeSequence = sequence(
        //             moveTo(2 * frameTime, v2(-1, 6)),
        //             moveTo(frameTime, v2(3, -2))
        //         );
        //         this._uiNode.runAction(repeatForever(fallShakeSequence));
        //     }
        //     /**
        //      * 播放隐身动画
        //      */
        //     _playCloakeAnim() {
        //         if (!this._cloakeAnim) {
        //             const animNode = instantiate(GameConst.frameAnim);
        //             this.node.addChild(animNode, 11);
        //             this._cloakeAnim = animNode.getComponent(PfFrameAnim);
        //             this._cloakeAnim.init(
        //                 EnemyManager.enemyAtlas,
        //                 "a_",
        //                 12,
        //                 GameConfig.ActionFrameTime
        //             );
        //             animNode.active = false;
        //         }
        //         this._cloakeAnim.node.scale = 1.3;
        //         this._cloakeAnim.node.active = true;
        //         this._cloakeAnim.reset(1);
        //     }
        //     /**
        //      * 播放隐身消失动画
        //      * @param callback 动画结束回调
        //      */
        //     _playCloakeHideAnim(callback: Function = null) {
        //         const frameTime = GameConfig.ActionFrameTime;
        //         GameIns.audioManager.playEffect("cloake");
        //         this._playCloakeAnim();
        //         tween(this.node)
        //             .to(5 * frameTime, { opacity: 90 })
        //             .to(2 * frameTime, { opacity: 0 })
        //             .call(() => {
        //                 this._playSkel("cloake", true);
        //             })
        //             .to(6 * frameTime, { opacity: 255 })
        //             .call(() => {
        //                 if (callback) callback();
        //             })
        //             .start();
        //     }
        //     /**
        //      * 播放隐身显现动画
        //      * @param callback 动画结束回调
        //      */
        //     _playCloakeShowAnim(callback: Function = null) {
        //         const frameTime = GameConfig.ActionFrameTime;
        //         tween(this.node)
        //             .to(4 * frameTime, { opacity: 102 })
        //             .to(2 * frameTime, { opacity: 255 })
        //             .to(4 * frameTime, { opacity: 102 })
        //             .to(2 * frameTime, { opacity: 255 })
        //             .to(3 * frameTime, { opacity: 102 })
        //             .to(frameTime, { opacity: 0 })
        //             .call(() => {
        //                 this._playSkel(this._idleName, true);
        //                 this._playCloakeAnim();
        //             })
        //             .to(7 * frameTime, { opacity: 255 })
        //             .call(() => {
        //                 if (callback) callback();
        //             })
        //             .start();
        //     }
        //     /**
        //      * 检查并生成下一个 Boss
        //      */
        //     _checkNextBoss() {
        //         if (this._data.id === 200) {
        //             this._playSkel("next", false, () => {
        //                 for (const nextBossId of this._data.nextBoss) {
        //                     const bossData = BossManager.getBossDatas(nextBossId)[0];
        //                     const boss = BossManager.createBossById(nextBossId);
        //                     boss.setPosition(this.node.position, true);
        //                     boss.active = false;
        //                     boss.setPropertyRate(this.propertyRate, true);
        //                     tween(boss.node)
        //                         .to(1, { position: v2(bossData.appearParam[0], bossData.appearParam[1]) })
        //                         .call(() => {
        //                             boss.setPosition(boss.node.position, true);
        //                             boss.startBattle();
        //                         })
        //                         .start();
        //                 }
        //                 this.node.active = false;
        //                 this._bRemoveable = true;
        //             });
        //         }
        //     }
        //     /**
        //      * 改变血量
        //      * @param delta 血量变化值
        //      */
        //     hpChange(delta: number) {
        //         this.m_curHp += delta;
        //         if (this.m_curHp < 0) {
        //             this.m_curHp = 0;
        //         }
        //         BossBattleManager.hpChange(delta, this.node);
        //     }
        //     /**
        //      * 获取血量百分比
        //      */
        //     getHpPercent(): number {
        //         return this.m_curHp / this.m_totalHp;
        //     }


      }) || _class));

      _cclegacy._RF.pop();

      _crd = false;
    }
  };
});
//# sourceMappingURL=0931db329f80459385e2c21cbe97f595594665a3.js.map