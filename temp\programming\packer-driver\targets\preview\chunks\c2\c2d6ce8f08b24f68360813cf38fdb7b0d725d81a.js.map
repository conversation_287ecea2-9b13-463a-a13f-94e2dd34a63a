{"version": 3, "sources": ["file:///E:/M2Game/Client/assets/scripts/GameOld/Global.ts"], "names": ["_decorator", "Component", "view", "ccclass", "property", "Global", "WIDTH", "getDesignResolutionSize", "width", "HEIGHT", "height", "NORMAL_BULLET", "LIGHT_BULLET", "MISSILE_BULLET", "ENEMY_1", "ENEMY_2", "ENEMY_BULLET_1", "ENEMY_BULLET_2", "BLOOD_GOODS", "LIGHT_GOODS", "MISSILE_GOODS", "SCORE"], "mappings": ";;;;;;;;;;AAASA,MAAAA,U,OAAAA,U;AAAYC,MAAAA,S,OAAAA,S;AAAiBC,MAAAA,I,OAAAA,I;;;;;;;;;OAChC;AAAEC,QAAAA,OAAF;AAAWC,QAAAA;AAAX,O,GAAwBJ,U;;wBAGjBK,M,WADZF,OAAO,CAAC,QAAD,C,2BAAR,MACaE,MADb,SAC4BJ,SAD5B,CACsC,E,UACpBK,K,GAAgBJ,IAAI,CAACK,uBAAL,GAA+BC,K,UAC/CC,M,GAAiBP,IAAI,CAACK,uBAAL,GAA+BG,M,UAEhDC,a,GAAuB,e,UACvBC,Y,GAAsB,c,UACtBC,c,GAAwB,gB,UAExBC,O,GAAkB,Q,UAClBC,O,GAAkB,Q,UAElBC,c,GAAyB,c,UACzBC,c,GAAyB,c,UAEzBC,W,GAAsB,Y,UACtBC,W,GAAsB,Y,UACtBC,a,GAAwB,c,UAExBC,K,GAAgB,C", "sourcesContent": ["import { _decorator, Component, Node, view } from 'cc';\nconst { ccclass, property } = _decorator;\n\n@ccclass('Global')\nexport class Global extends Component {\n    public static WIDTH: number = view.getDesignResolutionSize().width;  //获得设计宽度\n    public static HEIGHT: number = view.getDesignResolutionSize().height;  //获得设计高度\n\n    public static NORMAL_BULLET:string = \"normal_bullet\";\n    public static LIGHT_BULLET:string = \"light_bullet\";\n    public static MISSILE_BULLET:string = \"missile_bullet\";\n\n    public static ENEMY_1: string = \"enemy1\";\n    public static ENEMY_2: string = \"enemy2\";\n\n    public static ENEMY_BULLET_1: string = \"enemybullet1\"\n    public static ENEMY_BULLET_2: string = \"enemybullet2\"\n\n    public static BLOOD_GOODS: string = \"bloodGoods\"\n    public static LIGHT_GOODS: string = \"lightGoods\"\n    public static MISSILE_GOODS: string = \"missileGoods\"\n\n    public static SCORE: number = 0;\n}\n\n"]}