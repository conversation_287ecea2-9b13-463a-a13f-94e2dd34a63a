System.register(["__unresolved_0", "cc", "__unresolved_1", "__unresolved_2", "__unresolved_3", "__unresolved_4", "__unresolved_5", "__unresolved_6", "__unresolved_7"], function (_export, _context) {
  "use strict";

  var _reporterNs, _cclegacy, __checkObsolete__, __checkObsoleteInNamespace__, _decorator, Vec2, tween, misc, UIOpacity, v2, BossHurt, ColliderComp, GameConfig, GameIns, GameConst, Bullet, EnemyEffectLayer, _dec, _class, _crd, ccclass, property, BossUnitBase;

  function _reportPossibleCrUseOfBossHurt(extras) {
    _reporterNs.report("BossHurt", "./BossHurt", _context.meta, extras);
  }

  function _reportPossibleCrUseOfColliderComp(extras) {
    _reporterNs.report("ColliderComp", "../../base/ColliderComp", _context.meta, extras);
  }

  function _reportPossibleCrUseOfGameConfig(extras) {
    _reporterNs.report("GameConfig", "../../../const/GameConfig", _context.meta, extras);
  }

  function _reportPossibleCrUseOfGameIns(extras) {
    _reporterNs.report("GameIns", "../../../GameIns", _context.meta, extras);
  }

  function _reportPossibleCrUseOfGameConst(extras) {
    _reporterNs.report("GameConst", "../../../const/GameConst", _context.meta, extras);
  }

  function _reportPossibleCrUseOfBullet(extras) {
    _reporterNs.report("Bullet", "../../bullet/Bullet", _context.meta, extras);
  }

  function _reportPossibleCrUseOfEnemyEffectLayer(extras) {
    _reporterNs.report("EnemyEffectLayer", "../enemy/EnemyEffectLayer", _context.meta, extras);
  }

  return {
    setters: [function (_unresolved_) {
      _reporterNs = _unresolved_;
    }, function (_cc) {
      _cclegacy = _cc.cclegacy;
      __checkObsolete__ = _cc.__checkObsolete__;
      __checkObsoleteInNamespace__ = _cc.__checkObsoleteInNamespace__;
      _decorator = _cc._decorator;
      Vec2 = _cc.Vec2;
      tween = _cc.tween;
      misc = _cc.misc;
      UIOpacity = _cc.UIOpacity;
      v2 = _cc.v2;
    }, function (_unresolved_2) {
      BossHurt = _unresolved_2.default;
    }, function (_unresolved_3) {
      ColliderComp = _unresolved_3.ColliderComp;
    }, function (_unresolved_4) {
      GameConfig = _unresolved_4.default;
    }, function (_unresolved_5) {
      GameIns = _unresolved_5.GameIns;
    }, function (_unresolved_6) {
      GameConst = _unresolved_6.GameConst;
    }, function (_unresolved_7) {
      Bullet = _unresolved_7.default;
    }, function (_unresolved_8) {
      EnemyEffectLayer = _unresolved_8.default;
    }],
    execute: function () {
      _crd = true;

      _cclegacy._RF.push({}, "5da76SvtXBCCovJQ2kfvHY6", "BossUnitBase", undefined);

      __checkObsolete__(['_decorator', 'Component', 'Node', 'Vec2', 'tween', 'misc', 'UIOpacity', 'v2']);

      ({
        ccclass,
        property
      } = _decorator);

      _export("default", BossUnitBase = (_dec = ccclass('BossUnitBase'), _dec(_class = class BossUnitBase extends (_crd && BossHurt === void 0 ? (_reportPossibleCrUseOfBossHurt({
        error: Error()
      }), BossHurt) : BossHurt) {
        constructor() {
          super(...arguments);
          this.owner = null;
          this.collideComp = null;
          this._curHp = 0;
          this._maxHp = 0;
          this.defence = 0;
          this.blastParam = [];
          this.blastShake = [];
          this._whiteNode = null;
          this._winkCount = 0;
          this._bWinkWhite = false;
          this._winkAct = null;
        }

        initWinkWhite(whiteNode) {
          this._whiteNode = whiteNode;
          this._winkAct = tween().to(0, {
            opacity: 255
          }).to(3 * (_crd && GameConfig === void 0 ? (_reportPossibleCrUseOfGameConfig({
            error: Error()
          }), GameConfig) : GameConfig).ActionFrameTime, {
            opacity: 0
          });

          if (this._whiteNode) {
            this._whiteNode.getComponent(UIOpacity).opacity = 0;
          }
        }

        initCollide(data) {
          if (!this.collideComp) {
            this.collideComp = this.addComp(_crd && ColliderComp === void 0 ? (_reportPossibleCrUseOfColliderComp({
              error: Error()
            }), ColliderComp) : ColliderComp, new (_crd && ColliderComp === void 0 ? (_reportPossibleCrUseOfColliderComp({
              error: Error()
            }), ColliderComp) : ColliderComp)());
          }

          this.collideComp.init(this);
          this.collideComp.setData(data, this.owner, v2(this.node.position.x, this.node.position.y));
          this.m_comps.forEach(comp => {
            comp.init(this);
          });
        }

        setPropertyRate(rates) {
          if (rates.length > 2) {
            this._curHp *= rates[0];
            this._maxHp = this._curHp;
            this.attack *= rates[1];
            this._collideAtk *= rates[2];
          }
        }

        get curHp() {
          return this._curHp;
        }

        set curHp(value) {
          this._curHp = value;
        }

        get maxHp() {
          return this._maxHp;
        }

        set maxHp(value) {
          this._maxHp = value;
        }

        getCollideAble() {
          return this.collideComp ? this.collideComp.enabled : false;
        }

        setCollideAble(enabled) {
          if (this.collideComp) {
            this.collideComp.enabled = enabled;
          }
        }

        setCollideOffset(x, y) {
          if (this.collideComp) {
            this.collideComp.setOffset(x, y);
          }
        }

        getAngleToOwner() {
          var angle = 0;
          var parent = this.node.parent;

          while (parent && parent !== this.owner.node) {
            angle += parent.angle;
            parent = parent.parent;
          }

          return angle;
        }

        getScenePos() {
          var pos = new Vec2(this.node.position.x, this.node.position.y).rotate(misc.degreesToRadians(this.getAngleToOwner()));
          var parent = this.node.parent;
          var scaleX = 1;
          var scaleY = 1;

          while (parent && parent.name !== 'enemyPlane') {
            scaleX *= parent.scale.x;
            scaleY *= parent.scale.y;
            pos.x += parent.position.x;
            pos.y += parent.position.y;
            parent = parent.parent;
          }

          pos.x *= scaleX;
          pos.y *= scaleY;
          return pos;
        }

        updateGameLogic(deltaTime) {
          if (!this.isDead) {
            this.m_comps.forEach(comp => {
              comp.update(deltaTime);
            });

            if (this._bWinkWhite) {
              this._winkCount++;

              if (this._winkCount > 10) {
                this._winkCount = 0;
                this._bWinkWhite = false;
              }
            }
          }
        }

        refreshCollideOffset() {
          var offsetX = 0;
          var offsetY = 0;
          var scaleX = 1;
          var scaleY = 1;
          var currentNode = this.node;

          while (currentNode && this.owner && this.owner.node !== currentNode) {
            offsetX += currentNode.position.x;
            offsetY += currentNode.position.y;
            scaleX *= currentNode.scale.x;
            scaleY *= currentNode.scale.y;
            currentNode = currentNode.parent;
          }

          this.setCollideOffset(offsetX * scaleX, offsetY * scaleY);
        }

        onCollide(collider) {
          if (!this.isDead && this.active && collider.entity instanceof (_crd && Bullet === void 0 ? (_reportPossibleCrUseOfBullet({
            error: Error()
          }), Bullet) : Bullet)) {
            var damage = collider.entity.getAttack(this);
            damage = Math.max(damage / 10, damage - this.defence) * (_crd && GameConst === void 0 ? (_reportPossibleCrUseOfGameConst({
              error: Error()
            }), GameConst) : GameConst).playerWARatio;
            (_crd && GameIns === void 0 ? (_reportPossibleCrUseOfGameIns({
              error: Error()
            }), GameIns) : GameIns).hurtEffectManager.createHurtNumByType(this.collideComp, collider.entity, damage, {
              x: 0,
              y: 0
            });
            this.hurt(damage);
          }
        }

        hurt(damage) {
          if (this.isDead || !this.active) {
            return false;
          }

          this.changeHp(-damage);
          this.onHurt();
          return true;
        }

        onHurt() {
          this.winkWhite();
        }

        changeHp(amount) {
          var change = amount;
          var newHp = this._curHp + amount;

          if (newHp < 0) {
            change = -this._curHp;
          }

          this._curHp = newHp;

          if (this._curHp < 0) {
            this._curHp = 0;
          }

          this.onHpChange(change);
          this.checkHp();
        }

        onHpChange(change) {
          if (this.owner && this.owner.hpChange) {
            this.owner.hpChange(change);
          }
        }

        checkHp() {
          if (this.curHp <= 0) {
            this.die();
            return true;
          }

          return false;
        }

        die() {
          if (!this.isDead) {
            var _this$collideComp;

            this.isDead = true;
            this.setCollideAble(false);
            (_crd && GameIns === void 0 ? (_reportPossibleCrUseOfGameIns({
              error: Error()
            }), GameIns) : GameIns).hurtEffectManager.m_colliderPreTime.delete((_this$collideComp = this.collideComp) == null ? void 0 : _this$collideComp.entity.new_uuid);
            this.onDie();
          }
        }

        onDie() {
          this.playDieAnim();
        }

        winkWhite() {
          if (!this._bWinkWhite) {
            this._bWinkWhite = true;

            if (this._whiteNode && this._winkAct) {
              this._winkAct.clone(this._whiteNode).start();
            }
          }
        }

        playDieAnim() {
          this.blastParam.forEach((param, index) => {
            var effectData = {
              x: param[0],
              y: param[1],
              scale: param[4],
              angle: param[5]
            };
            var callback = index === this.blastParam.length - 1 ? () => this.onDieAnimEnd() : null;
            this.scheduleOnce(() => {
              (_crd && EnemyEffectLayer === void 0 ? (_reportPossibleCrUseOfEnemyEffectLayer({
                error: Error()
              }), EnemyEffectLayer) : EnemyEffectLayer).me.addBlastEffect(this.node, param[2], effectData, callback); // if (param[6] > 0) {
              //     AudioManager.me.playEffect(`blast${param[6]}`);
              // }
            }, param[3] * (_crd && GameConfig === void 0 ? (_reportPossibleCrUseOfGameConfig({
              error: Error()
            }), GameConfig) : GameConfig).ActionFrameTime);
          }); // this.scheduleOnce(() => {
          //     MainCamera.me.shake1(this.blastShake);
          // }, 0.1);
        }

        onDieAnimEnd() {}

        getHpPercent() {
          return this.curHp / this.maxHp;
        }

      }) || _class));

      _cclegacy._RF.pop();

      _crd = false;
    }
  };
});
//# sourceMappingURL=c0c3f21978816781e1f232c8f86e1c08b24ccc87.js.map