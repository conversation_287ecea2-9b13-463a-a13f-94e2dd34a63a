{"version": 3, "sources": ["file:///E:/M2Game/Client/assets/scripts/Game/ui/plane/enemy/EnemyPlane.ts"], "names": ["_decorator", "Node", "Sprite", "Vec2", "tween", "v2", "misc", "instantiate", "UIOpacity", "ParticleSystem2D", "UITransform", "v3", "EnemyBase", "Tools", "GameIns", "GameEnum", "EnemyShootComponent", "EnemyShootData", "TrackComponent", "GameConfig", "GameConst", "EnemyPlaneRole", "PfFrameAnim", "ccclass", "property", "EnemyPlane", "_data", "_curAction", "_curAngle", "_destAngle", "_curDir", "ZERO", "_destDir", "_rotateSpeed", "_leaveAct", "_roleIndex", "_curFormIndex", "_curTrackType", "_damagedEffect", "_removeCount", "_removeTime", "_dieAnimEnd", "_particleArr", "_trackCom", "_shootCom", "_role", "_initAngle", "_hpWhiteTween", "shadow<PERSON>omp", "_bDieShoot", "m_outTime", "dir", "preLoad", "push", "firePartile1", "firePartile2", "addScript", "node", "roleNode", "enemyManager", "pfPlaneRole", "role", "<PERSON><PERSON><PERSON><PERSON>", "getComponent", "preLoadUI", "data", "init", "_reset", "dieBullet", "setUIData", "getEnemyUIData", "uiId", "_refreshProperty", "_refreshUI", "_refreshColliders", "setDir", "setAngle", "initAttr", "attr", "reset", "angle", "EnemyAction", "Track", "_hideFireParticle", "initComps", "isSpecialEnemy", "id", "setAtkStartCall", "setAction", "AttackPrepare", "setAtkOverCall", "AttackOver", "initTrack", "trackData", "trackParams", "offsetX", "offsetY", "rotateSpeed", "setTrackGroupStartCall", "track", "groupType", "groupIndex", "setTrackSpecialType", "setTrackGroupOverCall", "uiData", "isAm", "Transform", "setTrackOverCall", "die", "EnemyDestroyType", "TrackOver", "setTrackLeaveCall", "Leave", "setTrackStartCall", "trackType", "setTrackType", "setFirstShoot<PERSON>elay", "delay", "startBattle", "active", "_refreshHpBar", "setTrackAble", "startTrack", "updateGameLogic", "deltaTime", "isDead", "_checkRemoveAble", "isStandBy", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Enemy<PERSON>uff", "Ice", "_updateAction", "setNextAble", "<PERSON><PERSON><PERSON>", "_updateCurDir", "collideAble", "isMoving", "bMoveAttack", "bStayAttack", "AttackIng", "TimeOver", "removeAble", "bTurnDir", "getDegreeForDir", "setSneakDir", "getDir", "setDestDir", "setDestAngle", "delta", "Math", "abs", "checkLiveAble", "setFormIndex", "index", "bAttackAbles", "shootData", "attackInterval", "attackNum", "attackPointArr", "attackArrNum", "length", "setShootData", "frameTime", "ActionFrameTime", "type", "tail", "getChildByName", "setPlaneFrame", "addComponent", "anchorY", "setScale", "to", "scale", "call", "start", "scheduleOnce", "_playTailEffects", "position", "endX", "trackOffsetX", "endY", "trackOffsetY", "setPos", "trackFinish", "appearNode", "_createAppearEffect", "blueShow", "playCloakeHideAnim", "playCloakeShowAnim", "tailEffects", "tailFrames", "scales", "scaleX", "scaleY", "for<PERSON>ach", "name", "frameAnim", "enemyAtlas", "stop", "showParam", "setPosition", "_createTailNode", "frameName", "_createAppearNode", "currentType", "nextType", "action", "setIsShooting", "hpBg", "opacity", "stopShoot", "playAnim", "setNextShootAtOnce", "playAtkAnim", "playAtkWarnAnim", "startAttack", "startShoot", "attackOver", "direction", "angleChange", "newAngle", "rotatedDir", "x", "y", "rotate", "degreesToRadians", "curHp", "hp", "maxHp", "defence", "attack", "collideLevel", "collideAtk", "collide<PERSON><PERSON><PERSON>", "bCollideDead", "isRespawn", "param", "fireParticle1", "spriteFrame", "getPlaneFrame", "fireParticle2", "hpParam", "hpSpr", "hpWhite", "error", "colliderData", "collider", "setCollideData", "hpRatio", "isDecreasing", "fill<PERSON><PERSON><PERSON>", "duration", "onHurt", "checkHp", "_playFireParticle", "onRemoveBuff", "buffType", "onAddBuff", "onDie", "destroyType", "will<PERSON><PERSON><PERSON>", "Die", "playDieAnim", "_checkDieShoot", "i", "damageParam", "particle", "removeChildByName", "checkLoot", "onDieAnimEnd", "immediate", "value", "setRoleOpacity", "getFireBulletAngle", "checkInScreen", "itemParent", "isPlaneOutScreen", "PrefabName"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAASA,MAAAA,U,OAAAA,U;AAAuBC,MAAAA,I,OAAAA,I;AAAMC,MAAAA,M,OAAAA,M;AAAQC,MAAAA,I,OAAAA,I;AAAsBC,MAAAA,K,OAAAA,K;AAAOC,MAAAA,E,OAAAA,E;AAAIC,MAAAA,I,OAAAA,I;AAAMC,MAAAA,W,OAAAA,W;AAAaC,MAAAA,S,OAAAA,S;AAAWC,MAAAA,gB,OAAAA,gB;AAAkBC,MAAAA,W,OAAAA,W;AAAaC,MAAAA,E,OAAAA,E;;AACrIC,MAAAA,S;;AACEC,MAAAA,K,iBAAAA,K;;AACAC,MAAAA,O,iBAAAA,O;;AACFC,MAAAA,Q;;AACAC,MAAAA,mB;;AAC6BC,MAAAA,c,iBAAAA,c;;AAC7BC,MAAAA,c;;AACAC,MAAAA,U;;AACEC,MAAAA,S,kBAAAA,S;;AACFC,MAAAA,c;;AACAC,MAAAA,W;;;;;;;;;OAED;AAAEC,QAAAA,OAAF;AAAWC,QAAAA;AAAX,O,GAAwBxB,U;;yBAGTyB,U,WADpBF,OAAO,CAAC,YAAD,C,UAEHC,QAAQ,CAACvB,IAAD,C,UAGRuB,QAAQ,CAACtB,MAAD,C,UAGRsB,QAAQ,CAACtB,MAAD,C,UAGRsB,QAAQ,CAACtB,MAAD,C,UAGRsB,QAAQ,CAACtB,MAAD,C,UAGRsB,QAAQ,CAACvB,IAAD,C,UAGRuB,QAAQ,CAACvB,IAAD,C,sCApBb,MACqBwB,UADrB;AAAA;AAAA,kCACkD;AAAA;AAAA;;AAAA;;AAAA;;AAAA;;AAAA;;AAAA;;AAAA;;AAAA;;AAAA,eAsB9CC,KAtB8C,GAsBtB,IAtBsB;AAAA,eAuB9CC,UAvB8C,GAuBzB,CAvByB;AAAA,eAwB9CC,SAxB8C,GAwB1B,CAxB0B;AAAA,eAyB9CC,UAzB8C,GAyBzB,IAzByB;AAAA,eA0B9CC,OA1B8C,GA0B9B3B,IAAI,CAAC4B,IA1ByB;AAAA,eA2B9CC,QA3B8C,GA2B7B7B,IAAI,CAAC4B,IA3BwB;AAAA,eA4B9CE,YA5B8C,GA4BvB,CA5BuB;AAAA,eA6B9CC,SA7B8C,GA6B1B,CAAC,CA7ByB;AAAA,eA8B9CC,UA9B8C,GA8BzB,CA9ByB;AAAA,eA+B9CC,aA/B8C,GA+BtB,CA/BsB;AAAA,eAgC9CC,aAhC8C,GAgCtB,CAAC,CAhCqB;AAAA,eAiC9CC,cAjC8C,GAiCpB,KAjCoB;AAAA,eAkC9CC,YAlC8C,GAkCvB,CAlCuB;AAAA,eAmC9CC,WAnC8C,GAmCxB,CAnCwB;AAAA,eAoC9CC,WApC8C,GAoCvB,KApCuB;AAAA,eAqC9CC,YArC8C,GAqCvB,EArCuB;AAAA,eAsC9CC,SAtC8C,GAsClB,IAtCkB;AAAA,eAuC9CC,SAvC8C,GAuCb,IAvCa;AAAA,eAwC9CC,KAxC8C,GAwCtB,IAxCsB;AAAA,eAyC9CC,UAzC8C,GAyCxB,IAzCwB;AAAA,eA0C9CC,aA1C8C,GA0CzB,IA1CyB;AAAA,eA2C9CC,UA3C8C,GA2C5B,IA3C4B;AAAA,eA4C9CC,UA5C8C,GA4CxB,KA5CwB;AAAA,eA6C9CC,SA7C8C,GA6C1B,CA7C0B;AAAA,eAgD9CC,GAhD8C;AAAA;;AAkD9CC,QAAAA,OAAO,GAAG;AACN,gBAAMA,OAAN;;AACA,eAAKV,YAAL,CAAkBW,IAAlB,CAAuB,KAAKC,YAA5B;;AACA,eAAKZ,YAAL,CAAkBW,IAAlB,CAAuB,KAAKE,YAA5B;;AACA,eAAKZ,SAAL,GAAiB;AAAA;AAAA,8BAAMa,SAAN,CAAgB,KAAKC,IAArB;AAAA;AAAA,+CAAjB;AACA,eAAKb,SAAL,GAAiB;AAAA;AAAA,8BAAMY,SAAN,CAAgB,KAAKC,IAArB;AAAA;AAAA,yDAAjB;AAEA,cAAMC,QAAQ,GAAGnD,WAAW,CAAC;AAAA;AAAA,kCAAQoD,YAAR,CAAqBC,WAAtB,CAA5B;AACA,eAAKC,IAAL,CAAUC,QAAV,CAAmBJ,QAAnB;AACA,eAAKb,KAAL,GAAaa,QAAQ,CAACK,YAAT;AAAA;AAAA,+CAAb;AACH;;AAEDC,QAAAA,SAAS,CAACC,IAAD,EAAY;AACjB,eAAKpB,KAAL,CAAWmB,SAAX,CAAqBC,IAArB;AACH;;AAEDC,QAAAA,IAAI,CAACD,IAAD,EAAuB;AACvB,eAAKE,MAAL;;AACA,eAAKzC,KAAL,GAAauC,IAAb;AACA,eAAKG,SAAL,GAAiBH,IAAI,CAACG,SAAtB;AACA,eAAKC,SAAL,CAAe;AAAA;AAAA,kCAAQV,YAAR,CAAqBW,cAArB,CAAoC,KAAK5C,KAAL,CAAW6C,IAA/C,CAAf;;AACA,eAAKC,gBAAL;;AACA,eAAKC,UAAL;;AACA,eAAKC,iBAAL;;AACA,eAAKC,MAAL,CAAYtE,EAAE,CAAC,CAAD,EAAI,CAAC,CAAL,CAAd;AACA,eAAKuE,QAAL,CAAc,CAAd;AACA,eAAKC,QAAL,CAAcZ,IAAI,CAACa,IAAnB,EAVuB,CAYvB;AACA;AACA;AACA;AACA;AACA;AACA;AACH;;AAEDX,QAAAA,MAAM,GAAG;AACL,gBAAMY,KAAN;AACA,eAAKlB,IAAL,CAAUmB,KAAV,GAAkB,CAAlB;AACA,eAAKrD,UAAL,GAAkB;AAAA;AAAA,oCAASsD,WAAT,CAAqBC,KAAvC;AACA,eAAK1C,WAAL,GAAmB,CAAnB;AACA,eAAKD,YAAL,GAAoB,CAApB;AACA,eAAKE,WAAL,GAAmB,KAAnB;AACA,eAAKQ,UAAL,GAAkB,KAAlB;AACA,eAAKd,UAAL,GAAkB,CAAlB;AACA,eAAKC,aAAL,GAAqB,CAArB;AACA,eAAKC,aAAL,GAAqB,CAAC,CAAtB;AACA,eAAKT,SAAL,GAAiB,CAAjB;AACA,eAAKC,UAAL,GAAkB,IAAlB;AACA,eAAKC,OAAL,GAAe3B,IAAI,CAAC4B,IAApB;AACA,eAAKC,QAAL,GAAgB7B,IAAI,CAAC4B,IAArB;AACA,eAAKE,YAAL,GAAoB,CAApB;AACA,eAAKC,SAAL,GAAiB,CAAC,CAAlB;AACA,eAAKI,cAAL,GAAsB,KAAtB;AACA,eAAKQ,UAAL,GAAkB,IAAlB;;AACA,eAAKqC,iBAAL;;AACA,eAAKnC,UAAL,GAAkB,IAAlB;AACA,eAAKE,SAAL,GAAiB,CAAjB;AACH;AAGD;AACJ;AACA;;;AACIkC,QAAAA,SAAS,GAAS;AACd;AACA,gBAAMA,SAAN,GAFc,CAId;;AACA,cAAMC,cAAc,GAAG,KAAK3D,KAAL,CAAW4D,EAAX,IAAiB,KAAjB,IAA0B,KAAK5D,KAAL,CAAW4D,EAAX,GAAgB,KAAjE,CALc,CAOd;;AACA,eAAK1C,SAAL,CAAesB,IAAf,CAAoB,IAApB,EAA0BmB,cAAc,GAAG,KAAKxC,KAAL,CAAWgB,IAAX,CAAgBJ,IAAnB,GAA0B,KAAKA,IAAvE,EAA6E,IAA7E,EAAmF4B,cAAnF,EARc,CAUd;;;AACA,eAAKzC,SAAL,CAAe2C,eAAf,CAA+B,MAAM;AACjC,iBAAKC,SAAL,CAAe;AAAA;AAAA,sCAASP,WAAT,CAAqBQ,aAApC;AACH,WAFD,EAXc,CAed;;;AACA,eAAK7C,SAAL,CAAe8C,cAAf,CAA8B,MAAM;AAChC,iBAAKF,SAAL,CAAe;AAAA;AAAA,sCAASP,WAAT,CAAqBU,UAApC;AACH,WAFD;AAGH;AAED;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;;;AACIC,QAAAA,SAAS,CAACC,SAAD,EAAYC,WAAZ,EAAyBC,OAAzB,EAAkCC,OAAlC,EAA2CC,WAA3C,EAA4D;AAAA,cAAjBA,WAAiB;AAAjBA,YAAAA,WAAiB,GAAH,CAAG;AAAA;;AACjE,eAAKhE,YAAL,GAAoBgE,WAApB;;AACA,eAAKtD,SAAL,CAAeuB,IAAf,CAAoB,IAApB,EAA0B2B,SAA1B,EAAqCC,WAArC,EAAkDC,OAAlD,EAA2DC,OAA3D,EAFiE,CAIjE;;;AACA,eAAKrD,SAAL,CAAeuD,sBAAf,CAAsC,CAACC,KAAD,EAAQC,SAAR,EAAmBC,UAAnB,KAAkC;AACpE,iBAAKC,mBAAL,CAAyBF,SAAzB,EAAoCC,UAApC;AACH,WAFD;;AAIA,eAAK1D,SAAL,CAAe4D,qBAAf,CAAsCH,SAAD,IAAe;AAChD,gBAAI,KAAKI,MAAL,CAAYC,IAAZ,IAAoBL,SAAS,KAAK,CAAtC,EAAyC;AACrC,mBAAKZ,SAAL,CAAe;AAAA;AAAA,wCAASP,WAAT,CAAqByB,SAApC;AACH;AACJ,WAJD;;AAMA,eAAK/D,SAAL,CAAegE,gBAAf,CAAgC,MAAM;AAClC,iBAAKC,GAAL,CAAS;AAAA;AAAA,sCAASC,gBAAT,CAA0BC,SAAnC;AACH,WAFD;;AAIA,eAAKnE,SAAL,CAAeoE,iBAAf,CAAiC,MAAM;AACnC,iBAAKH,GAAL,CAAS;AAAA;AAAA,sCAASC,gBAAT,CAA0BG,KAAnC;AACH,WAFD;;AAIA,eAAKrE,SAAL,CAAesE,iBAAf,CAAkCC,SAAD,IAAe;AAC5C,iBAAKC,YAAL,CAAkBD,SAAlB;AACH,WAFD;AAGH;AAED;AACJ;AACA;AACA;;;AACIE,QAAAA,kBAAkB,CAACC,KAAD,EAAQ;AACtB,eAAKzE,SAAL,CAAewE,kBAAf,CAAkCC,KAAlC;AACH;AAED;AACJ;AACA;;;AACIC,QAAAA,WAAW,GAAG;AACV,gBAAMA,WAAN;AACA,eAAKC,MAAL,GAAc,IAAd;;AACA,eAAKC,aAAL;;AACA,eAAK7E,SAAL,CAAe8E,YAAf,CAA4B,IAA5B;;AACA,eAAK9E,SAAL,CAAe+E,UAAf;AACH;AAED;AACJ;AACA;AACA;;;AACIC,QAAAA,eAAe,CAACC,SAAD,EAAY;AACvB,gBAAMD,eAAN,CAAsBC,SAAtB;;AAEA,cAAI,KAAKC,MAAT,EAAiB;AACb,gBAAI,KAAK5E,UAAT,EAAqB;AACjB,mBAAKL,SAAL,CAAe+E,eAAf,CAA+BC,SAA/B;AACH,aAFD,MAEO;AACH,mBAAKE,gBAAL,CAAsBF,SAAtB;AACH;AACJ,WAND,MAMO,IAAI,CAAC,KAAKG,SAAL,EAAL,EAAuB;AAC1B,iBAAKlF,KAAL,CAAW8E,eAAX,CAA2BC,SAA3B;;AACA,gBAAI,CAAC,KAAKI,WAAL,CAAiB;AAAA;AAAA,sCAASC,SAAT,CAAmBC,GAApC,CAAL,EAA+C;AAC3C,mBAAKvF,SAAL,CAAegF,eAAf,CAA+BC,SAA/B;;AACA,mBAAKhF,SAAL,CAAe+E,eAAf,CAA+BC,SAA/B;;AACA,mBAAKO,aAAL,CAAmBP,SAAnB;AACH;AACJ;AACJ;AAED;AACJ;AACA;AACA;;;AACIO,QAAAA,aAAa,CAACP,SAAD,EAAY;AACrB,eAAKhF,SAAL,CAAewF,WAAf,CAA2B,KAA3B;;AAEA,kBAAQ,KAAKzG,UAAb;AACI,iBAAK;AAAA;AAAA,sCAASsD,WAAT,CAAqBoD,KAA1B;AACI,mBAAKC,aAAL,CAAmBV,SAAnB;;AACA,mBAAKW,WAAL,GAAmB,KAAnB;AACA;;AAEJ,iBAAK;AAAA;AAAA,sCAAStD,WAAT,CAAqBC,KAA1B;AACI,mBAAKoD,aAAL,CAAmBV,SAAnB;;AACA,mBAAKhF,SAAL,CAAewF,WAAf,CACK,KAAKzF,SAAL,CAAe6F,QAAf,IAA2B,KAAK9G,KAAL,CAAW+G,WAAvC,IACC,CAAC,KAAK9F,SAAL,CAAe6F,QAAhB,IAA4B,KAAK9G,KAAL,CAAWgH,WAF5C;;AAIA;;AAEJ,iBAAK;AAAA;AAAA,sCAASzD,WAAT,CAAqByB,SAA1B;AACI;;AAEJ,iBAAK;AAAA;AAAA,sCAASzB,WAAT,CAAqBQ,aAA1B;AACA,iBAAK;AAAA;AAAA,sCAASR,WAAT,CAAqB0D,SAA1B;AACI,mBAAKL,aAAL,CAAmBV,SAAnB;;AACA;;AAEJ,iBAAK;AAAA;AAAA,sCAAS3C,WAAT,CAAqB+B,KAA1B;AACI,kBAAI,KAAK9E,SAAL,KAAmB,CAAvB,EAA0B;AACtB,qBAAK0E,GAAL,CAAS;AAAA;AAAA,0CAASC,gBAAT,CAA0B+B,QAAnC;AACH,eAFD,MAEO,IAAI,KAAK1G,SAAL,GAAiB,CAArB,EAAwB;AAC3B,qBAAKoG,aAAL,CAAmBV,SAAnB;AACH;;AACD;AA5BR;AA8BH;AAED;AACJ;AACA;AACA;;;AACIE,QAAAA,gBAAgB,CAACF,SAAD,EAAY;AACxB,cAAI,KAAKnF,WAAT,EAAsB;AAClB,iBAAKF,YAAL,IAAqBqF,SAArB;;AACA,gBAAI,KAAKrF,YAAL,GAAoB,KAAKC,WAA7B,EAA0C;AACtC,mBAAKqG,UAAL,GAAkB,IAAlB;AACH;AACJ;AACJ;AAED;AACJ;AACA;AACA;;;AACIlE,QAAAA,MAAM,CAACxB,GAAD,EAAM;AACR,eAAKA,GAAL,GAAWA,GAAX;;AACA,cAAI,KAAKzB,KAAL,CAAWoH,QAAf,EAAyB;AACrB,iBAAKlE,QAAL,CAAc,CAAC;AAAA;AAAA,gCAAMmE,eAAN,CAAsB5F,GAAtB,CAAf;AACH;;AACD,cAAI,KAAKN,KAAT,EAAgB;AACZ,iBAAKA,KAAL,CAAWmG,WAAX,CAAuB,CAAC;AAAA;AAAA,gCAAMD,eAAN,CAAsB5F,GAAtB,CAAxB;AACH;AACJ;AAED;AACJ;AACA;AACA;;;AACI8F,QAAAA,MAAM,GAAG;AACL,iBAAO,KAAKnH,OAAZ;AACH;AAED;AACJ;AACA;AACA;;;AACIoH,QAAAA,UAAU,CAAC/F,GAAD,EAAM;AACZ,eAAKnB,QAAL,GAAgBmB,GAAhB;;AACA,cAAI,KAAKL,UAAT,EAAqB;AACjB,iBAAKA,UAAL,GAAkB,KAAlB;AACA,iBAAK6B,MAAL,CAAYxB,GAAZ;AACH,WAHD,MAGO,IAAI,KAAKlB,YAAL,IAAqB,CAAzB,EAA4B;AAC/B,iBAAK0C,MAAL,CAAYxB,GAAZ;AACH;AACJ;AAED;AACJ;AACA;AACA;;;AACIyB,QAAAA,QAAQ,CAACI,KAAD,EAAQ;AACZ,cAAIA,KAAK,GAAG,GAAZ,EAAiB;AACbA,YAAAA,KAAK,IAAI,GAAT;AACH,WAFD,MAEO,IAAIA,KAAK,GAAG,CAAC,GAAb,EAAkB;AACrBA,YAAAA,KAAK,IAAI,GAAT;AACH;;AACD,eAAKpD,SAAL,GAAiBoD,KAAjB;AACA,eAAKnB,IAAL,CAAUmB,KAAV,GAAkBA,KAAlB;AACH;AAED;AACJ;AACA;AACA;;;AACImE,QAAAA,YAAY,CAACnE,KAAD,EAAQ;AAChB,cAAI,KAAK/C,YAAL,GAAoB,CAAxB,EAA2B;AACvB,gBAAImH,KAAK,GAAGpE,KAAK,GAAG,KAAKpD,SAAzB;;AACA,mBAAOyH,IAAI,CAACC,GAAL,CAASF,KAAT,IAAkB,GAAzB,EAA8B;AAC1BpE,cAAAA,KAAK,IAAIA,KAAK,GAAG,KAAKpD,SAAb,GAAyB,CAAC,GAA1B,GAAgC,GAAzC;AACAwH,cAAAA,KAAK,GAAGpE,KAAK,GAAG,KAAKpD,SAArB;AACH;;AACD,gBAAIyH,IAAI,CAACC,GAAL,CAASF,KAAT,IAAkB,GAAtB,EAA2B;AACvBpE,cAAAA,KAAK,IAAIA,KAAK,GAAG,KAAKpD,SAAb,GAAyB,CAAC,GAA1B,GAAgC,GAAzC;AACH;;AACD,iBAAKC,UAAL,GAAkBmD,KAAlB;AACH;AACJ;AAED;AACJ;AACA;AACA;;;AACIuE,QAAAA,aAAa,GAAG;AACZ,cAAI,KAAK5H,UAAL,KAAoB;AAAA;AAAA,oCAASsD,WAAT,CAAqBC,KAA7C,EAAoD;AAChD,iBAAKM,SAAL,CAAe;AAAA;AAAA,sCAASP,WAAT,CAAqB+B,KAApC;AACA,mBAAO,IAAP;AACH;;AACD,iBAAO,KAAP;AACH;AAED;AACJ;AACA;AACA;;;AACIwC,QAAAA,YAAY,CAACC,KAAD,EAAQ;AAChB,eAAKrH,aAAL,GAAqBqH,KAArB;;AACA,cAAI,KAAKrH,aAAL,IAAsB,CAAtB,IAA2B,KAAKV,KAAL,CAAWgI,YAAX,CAAwB,KAAKtH,aAA7B,CAA/B,EAA4E;AACxE,gBAAMuH,SAAS,GAAG;AAAA;AAAA,mDAAlB;AACAA,YAAAA,SAAS,CAACC,cAAV,GAA2B,KAAKlI,KAAL,CAAWkI,cAAtC;AACAD,YAAAA,SAAS,CAACE,SAAV,GAAsB,KAAKnI,KAAL,CAAWmI,SAAjC;AACAF,YAAAA,SAAS,CAACG,cAAV,GAA2B,KAAKpI,KAAL,CAAWoI,cAAX,CAA0B,KAAK1H,aAA/B,CAA3B;AACAuH,YAAAA,SAAS,CAACI,YAAV,GAAyBJ,SAAS,CAACG,cAAV,CAAyBE,MAAlD;;AACA,iBAAKpH,SAAL,CAAeqH,YAAf,CAA4BN,SAA5B;AACH,WAPD,MAOO;AACH,iBAAK/G,SAAL,CAAeqH,YAAf,CAA4B,IAA5B;AACH;AACJ;AAED;AACJ;AACA;AACA;;;AACI9C,QAAAA,YAAY,CAACtB,SAAD,EAAuB;AAC/B,cAAI,CAACA,SAAL,EAAgB;AACZ;AACH;;AAED,cAAMqE,SAAS,GAAG;AAAA;AAAA,wCAAWC,eAA7B;;AAEA,kBAAQtE,SAAS,CAACuE,IAAlB;AACI,iBAAK,CAAL;AAAQ;AACJ,mBAAKpH,UAAL,KAAoB,KAAKA,UAAL,CAAgBS,IAAhB,CAAqB8D,MAArB,GAA8B,KAAlD,EADJ,CAEI;AACA;;AACA,mBAAK5E,SAAL,CAAe8E,YAAf,CAA4B,KAA5B;;AACA,mBAAKc,WAAL,GAAmB,KAAnB;AAEA,kBAAI8B,IAAI,GAAG,KAAK5G,IAAL,CAAU6G,cAAV,CAAyB,MAAzB,CAAX;;AACA,kBAAI,CAACD,IAAL,EAAW;AACPA,gBAAAA,IAAI,GAAG,IAAIpK,IAAJ,EAAP;AACA,qBAAKwD,IAAL,CAAUK,QAAV,CAAmBuG,IAAnB;AACA;AAAA;AAAA,wCAAQ1G,YAAR,CAAqB4G,aAArB,CAAmCF,IAAI,CAACG,YAAL,CAAkBtK,MAAlB,CAAnC,EAA8D,SAA9D;AACAmK,gBAAAA,IAAI,CAACtG,YAAL,CAAkBrD,WAAlB,EAA+B+J,OAA/B,GAAyC,CAAzC;AACH;;AACDJ,cAAAA,IAAI,CAAC9C,MAAL,GAAc,IAAd;AACA8C,cAAAA,IAAI,CAACK,QAAL,CAAc,CAAd,EAAiB,GAAjB;AAEAtK,cAAAA,KAAK,CAACiK,IAAD,CAAL,CACKhD,KADL,CACW,IADX,EAEKsD,EAFL,CAEQ,IAAIT,SAFZ,EAEuB;AAAEU,gBAAAA,KAAK,EAAEjK,EAAE,CAAC,CAAD,EAAI,GAAJ;AAAX,eAFvB,EAGKgK,EAHL,CAGQ,IAAIT,SAHZ,EAGuB;AAAEU,gBAAAA,KAAK,EAAEjK,EAAE,CAAC,CAAD,EAAI,GAAJ;AAAX,eAHvB,EAIKgK,EAJL,CAIQT,SAJR,EAImB;AAAEU,gBAAAA,KAAK,EAAEjK,EAAE,CAAC,CAAD,EAAI,CAAJ;AAAX,eAJnB,EAKKkK,IALL,CAKU,MAAM;AACRR,gBAAAA,IAAI,CAAC9C,MAAL,GAAc,KAAd;AACH,eAPL,EAQKuD,KARL;AAUA,mBAAKC,YAAL,CAAkB,MAAM;AACpB,qBAAKC,gBAAL,CAAsBnF,SAAtB,EAAiCqE,SAAjC;AACH,eAFD,EAEG,IAAIA,SAFP;AAIA9J,cAAAA,KAAK,CAAC,KAAKqD,IAAN,CAAL,CACKkH,EADL,CACQ,IAAIT,SADZ,EACuB;AACfe,gBAAAA,QAAQ,EAAEtK,EAAE,CACRkF,SAAS,CAACqF,IAAV,GAAiB,KAAKvI,SAAL,CAAewI,YADxB,EAERtF,SAAS,CAACuF,IAAV,GAAiB,KAAKzI,SAAL,CAAe0I,YAFxB;AADG,eADvB,EAOKR,IAPL,CAOU,MAAM;AACR,qBAAKS,MAAL,CACIzF,SAAS,CAACqF,IAAV,GAAiB,KAAKvI,SAAL,CAAewI,YADpC,EAEItF,SAAS,CAACuF,IAAV,GAAiB,KAAKzI,SAAL,CAAe0I,YAFpC;AAIA,qBAAK9C,WAAL,GAAmB,IAAnB,CALQ,CAMR;;AACA,qBAAKvF,UAAL,KAAoB,KAAKA,UAAL,CAAgBS,IAAhB,CAAqB8D,MAArB,GAA8B,IAAlD;AACH,eAfL,EAgBKF,KAhBL,CAgBW,GAhBX,EAiBKwD,IAjBL,CAiBU,MAAM;AACR,qBAAKlI,SAAL,CAAe4I,WAAf,GAA6B,IAA7B;;AACA,qBAAK5I,SAAL,CAAe8E,YAAf,CAA4B,IAA5B;AACH,eApBL,EAqBKqD,KArBL;AAsBA;;AAEJ,iBAAK,CAAL;AAAQ;AACJ,mBAAK9H,UAAL,KAAoB,KAAKA,UAAL,CAAgBS,IAAhB,CAAqB8D,MAArB,GAA8B,KAAlD,EADJ,CAEI;;AACA,mBAAK5E,SAAL,CAAe8E,YAAf,CAA4B,KAA5B;;AACA,mBAAKc,WAAL,GAAmB,KAAnB;AACA,mBAAK+C,MAAL,CACIzF,SAAS,CAACqF,IAAV,GAAiB,KAAKvI,SAAL,CAAewI,YADpC,EAEItF,SAAS,CAACuF,IAAV,GAAiB,KAAKzI,SAAL,CAAe0I,YAFpC;;AAKA,kBAAMG,UAAU,GAAG,KAAKC,mBAAL,CAAyBvB,SAAzB,CAAnB;;AACA,mBAAKa,YAAL,CAAkB,MAAM;AACpB;AACA,qBAAKlI,KAAL,CAAW6I,QAAX,CAAoB,MAAM;AACtB,uBAAK/I,SAAL,CAAe4I,WAAf,GAA6B,IAA7B;AACA,uBAAKhD,WAAL,GAAmB,IAAnB;;AACA,uBAAK5F,SAAL,CAAe8E,YAAf,CAA4B,IAA5B,EAHsB,CAItB;;;AACA,uBAAKzE,UAAL,KAAoB,KAAKA,UAAL,CAAgBS,IAAhB,CAAqB8D,MAArB,GAA8B,IAAlD;AACH,iBAND;AAOH,eATD,EASG,IAAI2C,SATP;AAUA;;AAEJ,iBAAK,CAAL;AACA,iBAAK,CAAL;AAAQ;AACJ,kBAAI,KAAK7H,aAAL,KAAuB,CAAvB,IAA4B,KAAKA,aAAL,KAAuB,CAAvD,EAA0D;AACtD,qBAAKM,SAAL,CAAe8E,YAAf,CAA4B,KAA5B;;AACA,qBAAK7E,SAAL,CAAe2E,MAAf,GAAwB,KAAxB;AACA,qBAAKgB,WAAL,GAAmB,KAAnB,CAHsD,CAItD;;AACA,qBAAK1F,KAAL,CAAW8I,kBAAX,CAA8B,MAAM;AAChC,uBAAKhJ,SAAL,CAAe8E,YAAf,CAA4B,IAA5B;;AACA,uBAAKzE,UAAL,KAAoB,KAAKA,UAAL,CAAgBS,IAAhB,CAAqB8D,MAArB,GAA8B,KAAlD;AACH,iBAHD;AAIH,eATD,MASO;AACH,qBAAK3E,SAAL,CAAe2E,MAAf,GAAwB,KAAxB;;AACA,qBAAK5E,SAAL,CAAe8E,YAAf,CAA4B,KAA5B;;AACA,qBAAK5E,KAAL,CAAW+I,kBAAX,CAA8B,MAAM;AAChC,uBAAK5I,UAAL,KAAoB,KAAKA,UAAL,CAAgBS,IAAhB,CAAqB8D,MAArB,GAA8B,IAAlD;AACA,uBAAK3E,SAAL,CAAe2E,MAAf,GAAwB,IAAxB;;AACA,uBAAK5E,SAAL,CAAe8E,YAAf,CAA4B,IAA5B;;AACA,uBAAKc,WAAL,GAAmB,IAAnB,CAJgC,CAKhC;AACH,iBAND;AAOH;;AACD;;AAEJ;AACI,kBAAI,KAAKlG,aAAL,KAAuB,CAAvB,IAA4B,KAAKA,aAAL,KAAuB,CAAvD,EAA0D;AACtD,qBAAKO,SAAL,CAAe2E,MAAf,GAAwB,KAAxB;;AACA,qBAAK5E,SAAL,CAAe8E,YAAf,CAA4B,KAA5B;;AACA,qBAAK5E,KAAL,CAAW+I,kBAAX,CAA8B,MAAM;AAChC,uBAAK5I,UAAL,KAAoB,KAAKA,UAAL,CAAgBS,IAAhB,CAAqB8D,MAArB,GAA8B,IAAlD;AACA,uBAAK3E,SAAL,CAAe2E,MAAf,GAAwB,IAAxB;;AACA,uBAAK5E,SAAL,CAAe8E,YAAf,CAA4B,IAA5B;;AACA,uBAAKc,WAAL,GAAmB,IAAnB,CAJgC,CAKhC;AACH,iBAND;AAOH;;AACD;AAnHR;;AAsHA,eAAKlG,aAAL,GAAqBwD,SAAS,CAACuE,IAA/B;AACH;AAED;AACJ;AACA;AACA;AACA;;;AACYY,QAAAA,gBAAgB,CAACnF,SAAD,EAAiBqE,SAAjB,EAA0C;AAC9D,cAAM2B,WAAW,GAAG,CAAC,OAAD,EAAU,OAAV,EAAmB,OAAnB,CAApB;AACA,cAAMC,UAAU,GAAG,CAAC,SAAD,EAAY,SAAZ,EAAuB,SAAvB,CAAnB;AACA,cAAMC,MAAM,GAAG,CAAC,GAAD,EAAM,IAAN,EAAY;AAAEC,YAAAA,MAAM,EAAE,GAAV;AAAeC,YAAAA,MAAM,EAAE;AAAvB,WAAZ,CAAf;AAEAJ,UAAAA,WAAW,CAACK,OAAZ,CAAoB,CAACC,IAAD,EAAO1C,KAAP,KAAiB;AACjC,gBAAIY,IAAI,GAAG,KAAK5G,IAAL,CAAU6G,cAAV,CAAyB6B,IAAzB,CAAX;;AACA,gBAAI,CAAC9B,IAAL,EAAW;AACPA,cAAAA,IAAI,GAAG,IAAIpK,IAAJ,EAAP;AACAoK,cAAAA,IAAI,CAAC8B,IAAL,GAAYA,IAAZ;AACA,mBAAK1I,IAAL,CAAUK,QAAV,CAAmBuG,IAAnB;AACA;AAAA;AAAA,sCAAQ1G,YAAR,CAAqB4G,aAArB,CAAmCF,IAAI,CAACG,YAAL,CAAkBtK,MAAlB,CAAnC,EAA8D4L,UAAU,CAACrC,KAAD,CAAxE;AACH;;AACDY,YAAAA,IAAI,CAAC9C,MAAL,GAAc,IAAd,CARiC,CASjC;;AACA8C,YAAAA,IAAI,CAACK,QAAL,CAAc,CAAd,EAAiB,CAAjB;AAEA,gBAAIE,KAAK,GAAG,OAAOmB,MAAM,CAACtC,KAAD,CAAb,KAAyB,QAAzB,GAAoC9I,EAAE,CAACoL,MAAM,CAACtC,KAAD,CAAN,CAAcuC,MAAf,EAAuBD,MAAM,CAACtC,KAAD,CAAN,CAAcwC,MAArC,CAAtC,GAAqFtL,EAAE,CAACoL,MAAM,CAACtC,KAAD,CAAP,EAAgBsC,MAAM,CAACtC,KAAD,CAAtB,CAAnG;AACA,mBAAOmB,KAAP;AACAxK,YAAAA,KAAK,CAACiK,IAAD,CAAL,CACKM,EADL,CACQ,IAAIT,SADZ,EACuB;AAAEU,cAAAA,KAAK,EAAEA;AAAT,aADvB,EAEI;AAFJ,aAGKC,IAHL,CAGU,MAAM;AACRR,cAAAA,IAAI,CAAC9C,MAAL,GAAc,KAAd;AACH,aALL,EAMKuD,KANL;AAOH,WArBD;AAsBH;AAED;AACJ;AACA;AACA;AACA;;;AACYW,QAAAA,mBAAmB,CAACvB,SAAD,EAA0B;AACjD,cAAIsB,UAAU,GAAG,KAAK/H,IAAL,CAAU6G,cAAV,CAAyB,QAAzB,CAAjB;;AACA,cAAI,CAACkB,UAAL,EAAiB;AACbA,YAAAA,UAAU,GAAGjL,WAAW,CAAC;AAAA;AAAA,wCAAU6L,SAAX,CAAxB;AACAZ,YAAAA,UAAU,CAACW,IAAX,GAAkB,QAAlB;AACA,iBAAK1I,IAAL,CAAUK,QAAV,CAAmB0H,UAAnB;AAEA,gBAAMY,SAAS,GAAGZ,UAAU,CAACzH,YAAX;AAAA;AAAA,2CAAlB;AACAqI,YAAAA,SAAS,CAAClI,IAAV,CAAe;AAAA;AAAA,oCAAQP,YAAR,CAAqB0I,UAApC,EAAgD,KAAhD,EAAuD,EAAvD,EAA2DnC,SAA3D,EAAsE,MAAM;AACxEkC,cAAAA,SAAS,CAACE,IAAV;AACAd,cAAAA,UAAU,CAACjE,MAAX,GAAoB,KAApB;AACH,aAHD;AAIH;;AAEDiE,UAAAA,UAAU,CAACjE,MAAX,GAAoB,IAApB;;AACA,cAAI,KAAKf,MAAL,CAAY+F,SAAZ,CAAsBvC,MAAtB,GAA+B,CAAnC,EAAsC;AAClCwB,YAAAA,UAAU,CAACd,QAAX,CAAoB,KAAKlE,MAAL,CAAY+F,SAAZ,CAAsB,CAAtB,CAApB,EAA8C,KAAK/F,MAAL,CAAY+F,SAAZ,CAAsB,CAAtB,CAA9C;AACAf,YAAAA,UAAU,CAACgB,WAAX,CAAuB,KAAKhG,MAAL,CAAY+F,SAAZ,CAAsB,CAAtB,CAAvB,EAAiD,KAAK/F,MAAL,CAAY+F,SAAZ,CAAsB,CAAtB,CAAjD;AACH,WAlBgD,CAoBjD;;;AACAf,UAAAA,UAAU,CAACzH,YAAX;AAAA;AAAA,0CAAqCgB,KAArC;AAEA,iBAAOyG,UAAP;AACH;AAED;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;;;AACIiB,QAAAA,eAAe,CAACN,IAAD,EAAOO,SAAP,EAAkB9B,KAAlB,EAAyBqB,MAAzB,EAAyC,CACpD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AAEA;;AApBoD,cAAhBA,MAAgB;AAAhBA,YAAAA,MAAgB,GAAPrB,KAAO;AAAA;AAqBvD;AAED;AACJ;AACA;AACA;;;AACI+B,QAAAA,iBAAiB,GAAG,CAChB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACH;AAGD;AACJ;AACA;AACA;AACA;;;AACIrG,QAAAA,mBAAmB,CAACsG,WAAD,EAAcC,QAAd,EAAwB,CACvC;AAEA;AACA;AACA;AACA;AAEA;AACA;AACA;AAEA;AACA;AACA;AACH;AAED;AACJ;AACA;AACA;;;AACIrH,QAAAA,SAAS,CAACsH,MAAD,EAAS;AACd,cAAI,KAAKnL,UAAL,KAAoBmL,MAAxB,EAAgC;AAC5B,iBAAKnL,UAAL,GAAkBmL,MAAlB,CAD4B,CAG5B;;AACA,iBAAKlK,SAAL,CAAemK,aAAf,CAA6B,KAA7B;;AACA,iBAAKpK,SAAL,CAAe8E,YAAf,CAA4B,IAA5B;;AAEA,oBAAQ,KAAK9F,UAAb;AACI,mBAAK;AAAA;AAAA,wCAASsD,WAAT,CAAqBoD,KAA1B;AACI;AACA,qBAAK2E,IAAL,CAAUvJ,IAAV,CAAeM,YAAf,CAA4BvD,SAA5B,EAAuCyM,OAAvC,GAAiD,CAAjD,CAFJ,CAGI;;AACA;;AAEJ,mBAAK;AAAA;AAAA,wCAAShI,WAAT,CAAqBC,KAA1B;AACI;AACA;;AAEJ,mBAAK;AAAA;AAAA,wCAASD,WAAT,CAAqByB,SAA1B;AACI;AACA,qBAAK/D,SAAL,CAAe8E,YAAf,CAA4B,KAA5B;;AACA,qBAAK7E,SAAL,CAAesK,SAAf;;AACA,qBAAK/K,UAAL;AACA,qBAAKU,KAAL,CAAWsK,QAAX,CAAoB,WAApB,EAAiC,MAAM;AACnC,uBAAKtK,KAAL,CAAWsK,QAAX,CAAoB,SAAS,KAAKhL,UAAlC,EAA8C,IAA9C;;AACA,uBAAKqD,SAAL,CAAe;AAAA;AAAA,4CAASP,WAAT,CAAqBC,KAApC;;AACA,uBAAKtC,SAAL,CAAewK,kBAAf;AACH,iBAJD,MAIO,KAAK5H,SAAL,CAAe;AAAA;AAAA,0CAASP,WAAT,CAAqBC,KAApC,GAA4C,KAAKtC,SAAL,CAAewK,kBAAf,EAJnD;AAKA;;AAEJ,mBAAK;AAAA;AAAA,wCAASnI,WAAT,CAAqBQ,aAA1B;AACI;AACA,qBAAK4H,WAAL;AACA,qBAAK7H,SAAL,CAAe;AAAA;AAAA,0CAASP,WAAT,CAAqB0D,SAApC;;AACA,qBAAK9F,KAAL,CAAWyK,eAAX;;AACA,qBAAKzK,KAAL,CAAW0K,WAAX;;AACA;;AAEJ,mBAAK;AAAA;AAAA,wCAAStI,WAAT,CAAqB0D,SAA1B;AACI;AACA,qBAAK/F,SAAL,CAAe4K,UAAf;;AACA;;AAEJ,mBAAK;AAAA;AAAA,wCAASvI,WAAT,CAAqBU,UAA1B;AACI;AACA,qBAAK9C,KAAL,CAAW4K,UAAX;;AACA,qBAAKjI,SAAL,CAAe;AAAA;AAAA,0CAASP,WAAT,CAAqBC,KAApC;AACA;;AAEJ;AACI;AA3CR;AA6CH;AACJ;AAED;AACJ;AACA;AACA;;;AACIoD,QAAAA,aAAa,CAACV,SAAD,EAAY;AACrB,cAAI,KAAKlG,KAAL,CAAWoH,QAAX,IAAuB,KAAKjH,UAAL,KAAoB,IAA3C,IAAmD,KAAKA,UAAL,KAAoB,KAAKD,SAAhF,EAA2F;AACvF,gBAAM8L,SAAS,GAAG,KAAK7L,UAAL,GAAkB,KAAKD,SAAvB,GAAmC,CAAnC,GAAuC,CAAC,CAA1D;AACA,gBAAM+L,WAAW,GAAG/F,SAAS,GAAG,KAAK3F,YAAjB,GAAgCyL,SAApD;AACA,gBAAIE,QAAQ,GAAG,KAAKhM,SAAL,GAAiB+L,WAAhC;;AAEA,gBAAID,SAAS,GAAG,CAAhB,EAAmB;AACf,kBAAIE,QAAQ,GAAG,KAAK/L,UAApB,EAAgC+L,QAAQ,GAAG,KAAK/L,UAAhB;AACnC,aAFD,MAEO;AACH,kBAAI+L,QAAQ,GAAG,KAAK/L,UAApB,EAAgC+L,QAAQ,GAAG,KAAK/L,UAAhB;AACnC;;AAED,gBAAMgM,UAAU,GAAGxN,EAAE,CAAC,KAAKyB,OAAL,CAAagM,CAAd,EAAiB,KAAKhM,OAAL,CAAaiM,CAA9B,CAAF,CAAmCC,MAAnC,CAA0C1N,IAAI,CAAC2N,gBAAL,CAAsBN,WAAtB,CAA1C,CAAnB;AACA,iBAAKxK,GAAL,GAAW0K,UAAX,CAZuF,CAYhE;;AACvB,iBAAKjJ,QAAL,CAAcgJ,QAAd;AACH;AACJ;AAED;AACJ;AACA;;;AACIpJ,QAAAA,gBAAgB,GAAG;AACf,eAAK0J,KAAL,GAAa,KAAKxM,KAAL,CAAWyM,EAAxB;AACA,eAAKC,KAAL,GAAa,KAAK1M,KAAL,CAAWyM,EAAxB;AACA,eAAKE,OAAL,GAAe,KAAK3M,KAAL,CAAW2M,OAA1B;AACA,eAAKC,MAAL,GAAc,KAAK5M,KAAL,CAAW4M,MAAzB;AACA,eAAKC,YAAL,GAAoB,KAAK7M,KAAL,CAAW6M,YAA/B;AACA,eAAKC,UAAL,GAAkB,KAAK9M,KAAL,CAAW+M,aAA7B;AACA,eAAKC,YAAL,GAAoB,KAAKhN,KAAL,CAAWgN,YAA/B;AACH;AAED;AACJ;AACA;AACA;;;AACIjK,QAAAA,UAAU,CAACkK,SAAD,EAAoB;AAAA,cAAnBA,SAAmB;AAAnBA,YAAAA,SAAmB,GAAP,KAAO;AAAA;;AAC1B;AACA;AACA;AACA;AAEA;AACA;AACA;AAEA,eAAK9L,KAAL,CAAWqB,IAAX,CAAgB,KAAKsC,MAArB,EAA6B,IAA7B,EAAmC,KAAK9E,KAAL,CAAWkN,KAA9C;;AAEA,cAAMC,aAAa,GAAG,KAAKvL,YAAL,CAAkBS,YAAlB,CAA+BtD,gBAA/B,CAAtB;;AACA,cAAIoO,aAAJ,EAAmB;AACfA,YAAAA,aAAa,CAACC,WAAd,GAA4B;AAAA;AAAA,oCAAQnL,YAAR,CAAqBoL,aAArB,CAAmC,MAAnC,CAA5B;AACH;;AAED,cAAMC,aAAa,GAAG,KAAKzL,YAAL,CAAkBQ,YAAlB,CAA+BtD,gBAA/B,CAAtB;;AACA,cAAIuO,aAAJ,EAAmB;AACfA,YAAAA,aAAa,CAACF,WAAd,GAA4B;AAAA;AAAA,oCAAQnL,YAAR,CAAqBoL,aAArB,CAAmC,MAAnC,CAA5B;AACH;;AAED,cAAI,CAACJ,SAAL,EAAgB;AACZ,gBAAI;AACA,mBAAK3B,IAAL,CAAUvJ,IAAV,CAAeM,YAAf,CAA4BvD,SAA5B,EAAuCyM,OAAvC,GAAiD,KAAKvL,KAAL,CAAWuN,OAAX,KAAuB,CAAvB,GAA2B,GAA3B,GAAiC,CAAlF;AACA,mBAAKjC,IAAL,CAAUvJ,IAAV,CAAe+I,WAAf,CAA2B,KAAKhG,MAAL,CAAYyI,OAAZ,CAAoB,CAApB,CAA3B,EAAmD,KAAKzI,MAAL,CAAYyI,OAAZ,CAAoB,CAApB,CAAnD;AACA;AAAA;AAAA,sCAAQtL,YAAR,CAAqB4G,aAArB,CAAmC,KAAKyC,IAAxC,SAAmD,KAAKxG,MAAL,CAAYyI,OAAZ,CAAoB,CAApB,CAAnD;AACA;AAAA;AAAA,sCAAQtL,YAAR,CAAqB4G,aAArB,CAAmC,KAAK2E,KAAxC,SAAoD,KAAK1I,MAAL,CAAYyI,OAAZ,CAAoB,CAApB,CAApD;AACA;AAAA;AAAA,sCAAQtL,YAAR,CAAqB4G,aAArB,CAAmC,KAAK4E,OAAxC,SAAsD,KAAK3I,MAAL,CAAYyI,OAAZ,CAAoB,CAApB,CAAtD;AACH,aAND,CAME,OAAOG,KAAP,EAAc,CACZ;AACH;AACJ;AACJ;AAED;AACJ;AACA;;;AACI1K,QAAAA,iBAAiB,GAAG;AAChB,cAAIkG,KAAK,GAAG,KAAK/G,IAAL,CAAU+G,KAAV,CAAgBkD,CAA5B;AACA,cAAMuB,YAAY,GAAG,KAAK7I,MAAL,CAAY8I,QAAjC;AACA,eAAKC,cAAL,CAAoB,CAChBF,YAAY,CAAC,CAAD,CADI,EAEhBA,YAAY,CAAC,CAAD,CAAZ,GAAiBzE,KAFD,EAGhByE,YAAY,CAAC,CAAD,CAAZ,GAAiBzE,KAHD,EAIhByE,YAAY,CAAC,CAAD,CAAZ,GAAiBzE,KAJD,EAKhByE,YAAY,CAAC,CAAD,CAAZ,GAAiBzE,KALD,CAApB;AAOH;AACD;AACJ;AACA;;;AACIpD,QAAAA,aAAa,GAAG;AACZ,cAAMgI,OAAO,GAAG,KAAKtB,KAAL,GAAa,KAAKE,KAAlC;AACA,cAAMqB,YAAY,GAAGD,OAAO,GAAG,KAAKN,KAAL,CAAWQ,SAA1C,CAFY,CAIZ;;AACA,eAAKR,KAAL,CAAWQ,SAAX,GAAuBF,OAAvB,CALY,CAOZ;;AACA,cAAI,KAAKzM,aAAT,EAAwB;AACpB,iBAAKA,aAAL,CAAmBuJ,IAAnB;;AACA,iBAAKvJ,aAAL,GAAqB,IAArB;AACH,WAXW,CAaZ;;;AACA,cAAI0M,YAAJ,EAAkB;AACd,gBAAME,QAAQ,GAAGtG,IAAI,CAACC,GAAL,CAAS,KAAK6F,OAAL,CAAaO,SAAb,GAAyB,KAAKR,KAAL,CAAWQ,SAA7C,CAAjB;AACA,iBAAK3M,aAAL,GAAqB3C,KAAK,CAAC,KAAK+O,OAAN,CAAL,CAChBxE,EADgB,CACbgF,QADa,EACH;AAAED,cAAAA,SAAS,EAAE,KAAKR,KAAL,CAAWQ;AAAxB,aADG,EAEhB7E,IAFgB,CAEX,MAAM;AACR,mBAAK9H,aAAL,GAAqB,IAArB;AACH,aAJgB,EAKhB+H,KALgB,EAArB;AAMH,WARD,MAQO;AACH,iBAAKqE,OAAL,CAAaO,SAAb,GAAyBF,OAAzB;AACH;AACJ;AAED;AACJ;AACA;;;AACII,QAAAA,MAAM,GAAG;AACL,eAAKpI,aAAL;;AACA,eAAKqI,OAAL;;AACA,cAAI,CAAC,KAAKhI,MAAV,EAAkB,CACd;AACH;AACJ;AAED;AACJ;AACA;AACA;;;AACIgI,QAAAA,OAAO,GAAG;AACN,cAAI,MAAMA,OAAN,EAAJ,EAAqB;AACjB,mBAAO,IAAP;AACH;;AAED,cAAI,CAAC,KAAKvN,cAAN,IAAwB,KAAK4L,KAAL,GAAa,OAAO,KAAKE,KAArD,EAA4D;AACxD,iBAAK9L,cAAL,GAAsB,IAAtB;;AACA,iBAAKwN,iBAAL;AACH;;AAED,iBAAO,KAAP;AACH;AAED;AACJ;AACA;AACA;;;AACIC,QAAAA,YAAY,CAACC,QAAD,EAAW;AACnB,gBAAMD,YAAN,CAAmBC,QAAnB,EADmB,CAEnB;AACA;AACA;AACH;AAED;AACJ;AACA;AACA;;;AACIC,QAAAA,SAAS,CAACD,QAAD,EAAW;AAChB,gBAAMC,SAAN,CAAgBD,QAAhB,EADgB,CAEhB;AACA;AACA;AACH;AAED;AACJ;AACA;AACA;;;AACIE,QAAAA,KAAK,CAACC,WAAD,EAAc;AACf,gBAAMD,KAAN,CAAYC,WAAZ;AACA,eAAKnD,IAAL,CAAUvJ,IAAV,CAAeM,YAAf,CAA4BvD,SAA5B,EAAuCyM,OAAvC,GAAiD,CAAjD;AACA,eAAKmD,UAAL;;AAEA,kBAAQD,WAAR;AACI,iBAAK;AAAA;AAAA,sCAAStJ,gBAAT,CAA0BwJ,GAA/B;AACI,mBAAKC,WAAL;;AACA,mBAAKC,cAAL;;AACA;;AAEJ,iBAAK;AAAA;AAAA,sCAAS1J,gBAAT,CAA0BG,KAA/B;AACA,iBAAK;AAAA;AAAA,sCAASH,gBAAT,CAA0BC,SAA/B;AACA,iBAAK;AAAA;AAAA,sCAASD,gBAAT,CAA0B+B,QAA/B;AACI,mBAAKnG,WAAL,GAAmB,IAAnB;AACA;AAVR;AAYH;AAED;AACJ;AACA;;;AACI2N,QAAAA,UAAU,GAAG;AACT;AACA;AACA;AAEA,cAAI,KAAKrN,aAAT,EAAwB;AACpB,iBAAKA,aAAL,CAAmBuJ,IAAnB;;AACA,iBAAKvJ,aAAL,GAAqB,IAArB;AACH;;AAED,eAAKoM,OAAL,CAAaO,SAAb,GAAyB,CAAzB;AACH;AAED;AACJ;AACA;;;AACIa,QAAAA,cAAc,GAAG,CACb;AACA;AAEA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACH;AAED;AACJ;AACA;;;AACIlD,QAAAA,WAAW,GAAG;AACV,eAAKxK,KAAL,CAAWsK,QAAX,SAA0B,KAAKhL,UAA/B,EAA6C,MAAM;AAC/C,iBAAKU,KAAL,CAAWsK,QAAX,UAA2B,KAAKhL,UAAhC,EAA8C,IAA9C;AACH,WAFD;AAGH;AAED;AACJ;AACA;;;AACI2N,QAAAA,iBAAiB,GAAG;AAChB,eAAK,IAAIU,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAG,KAAKhK,MAAL,CAAYiK,WAAZ,CAAwBzG,MAA5C,EAAoDwG,CAAC,EAArD,EAAyD;AACrD,gBAAMC,WAAW,GAAG,KAAKjK,MAAL,CAAYiK,WAAZ,CAAwBD,CAAxB,CAApB;AACA,gBAAIE,QAAQ,GAAG,KAAKhO,YAAL,CAAkB8N,CAAlB,CAAf;;AAEA,gBAAI,CAACE,QAAL,EAAe;AACXA,cAAAA,QAAQ,GAAGnQ,WAAW,CAAC,KAAK+C,YAAN,CAAtB;AACA,mBAAKO,IAAL,CAAUC,QAAV,CAAmB4M,QAAnB;;AACA,mBAAKhO,YAAL,CAAkBW,IAAlB,CAAuBqN,QAAvB;AACH;;AAEDA,YAAAA,QAAQ,CAACnJ,MAAT,GAAkB,IAAlB;AACAmJ,YAAAA,QAAQ,CAAC3M,YAAT,CAAsBvD,SAAtB,EAAiCyM,OAAjC,GAA2C,GAA3C;AACAyD,YAAAA,QAAQ,CAAClE,WAAT,CAAqBiE,WAAW,CAAC,CAAD,CAAhC,EAAqCA,WAAW,CAAC,CAAD,CAAhD;AACAC,YAAAA,QAAQ,CAAChG,QAAT,CAAkB,GAAlB,EAAuB,GAAvB;AAEAtK,YAAAA,KAAK,CAACsQ,QAAD,CAAL,CACK/F,EADL,CACQ,IAAI;AAAA;AAAA,0CAAWR,eADvB,EACwC;AAAES,cAAAA,KAAK,EAAE6F,WAAW,CAAC,CAAD;AAApB,aADxC,EAEK3F,KAFL;AAGH;AACJ;AAED;AACJ;AACA;;;AACIwF,QAAAA,WAAW,GAAG;AACV,gBAAMA,WAAN;AAEA,eAAKvF,YAAL,CAAkB,MAAM;AACpB;AAAA;AAAA,gCAAM4F,iBAAN,CAAwB,KAAKlN,IAA7B,EAAmC,MAAnC;AACA,iBAAKmN,SAAL;AACA,iBAAK/M,IAAL,CAAUE,YAAV,CAAuBvD,SAAvB,EAAkCyM,OAAlC,GAA4C,CAA5C;;AACA,gBAAI,KAAKjK,UAAT,EAAqB;AACjB,mBAAKA,UAAL,CAAgB6F,UAAhB,GAA6B,IAA7B;AACH;AACJ,WAPD,EAOG,GAPH;;AASA,eAAK1D,iBAAL;AACH;AAED;AACJ;AACA;;;AACI0L,QAAAA,YAAY,GAAG;AACX,eAAKpO,WAAL,GAAmB,IAAnB;AACH;AAED;AACJ;AACA;AACA;;;AACI0C,QAAAA,iBAAiB,CAAC2L,SAAD,EAAoB;AAAA,cAAnBA,SAAmB;AAAnBA,YAAAA,SAAmB,GAAP,KAAO;AAAA;;AAAA,+CACS;AACtC,gBAAIJ,QAAQ,CAACnJ,MAAb,EAAqB;AACjB,kBAAIuJ,SAAJ,EAAe;AACXJ,gBAAAA,QAAQ,CAACnJ,MAAT,GAAkB,KAAlB;AACH,eAFD,MAEO;AACHnH,gBAAAA,KAAK,CAACsQ,QAAQ,CAAC3M,YAAT,CAAsBvD,SAAtB,CAAD,CAAL,CACKmK,EADL,CACQ,GADR,EACa;AAAEsC,kBAAAA,OAAO,EAAE;AAAX,iBADb,EAEKpC,IAFL,CAEU,MAAM;AACR6F,kBAAAA,QAAQ,CAACnJ,MAAT,GAAkB,KAAlB;AACH,iBAJL,EAKKuD,KALL;AAMH;AACJ;AACJ,WAdgC;;AACjC,eAAK,IAAM4F,QAAX,IAAuB,KAAKhO,YAA5B;AAAA;AAAA;AAcH;AACD;AACJ;AACA;AACA;;;AACa,YAALsC,KAAK,GAAG;AACR,iBAAO,KAAKnB,IAAL,GAAY,KAAKA,IAAL,CAAUmB,KAAtB,GAA8B,CAArC;AACH;AAED;AACJ;AACA;AACA;;;AACa,YAALA,KAAK,CAAC+L,KAAD,EAAQ;AACb,cAAI,KAAKlN,IAAT,EAAe;AACX,iBAAKA,IAAL,CAAUmB,KAAV,GAAkB+L,KAAlB;AACH;AACJ;AAED;AACJ;AACA;AACA;;;AACIC,QAAAA,cAAc,CAAC/D,OAAD,EAAU,CACpB;AACA;AACA;AACH;AAED;AACJ;AACA;AACA;;;AACIgE,QAAAA,kBAAkB,GAAG;AACjB;AACA;AACA;AACA,iBAAO,CAAP;AACH;AAED;AACJ;AACA;AACA;;;AACIC,QAAAA,aAAa,CAACtJ,SAAD,EAAY;AACrB,cAAI,KAAKuJ,UAAL,KAAoB,IAAxB,EAA8B;AAC1B,gBAAI;AAAA;AAAA,gCAAMC,gBAAN,CAAuB/Q,EAAE,CAAC,KAAKoD,IAAL,CAAUwH,QAAV,CAAmB6C,CAApB,EAAuB,KAAKrK,IAAL,CAAUwH,QAAV,CAAmB8C,CAA1C,CAAzB,CAAJ,EAA4E;AACxE,mBAAK7K,SAAL,IAAkB0E,SAAlB;;AACA,kBAAI,KAAK1E,SAAL,GAAiB,EAArB,EAAyB;AACrB,qBAAK2E,MAAL,GAAc,IAAd;AACA,qBAAKgB,UAAL,GAAkB,IAAlB;AACH;AACJ,aAND,MAMO;AACH,mBAAK3F,SAAL,GAAiB,CAAjB;AACH;AACJ;AACJ;;AA1iC6C,O,UA+CvCmO,U,GAAa,Y;;;;;iBA7CP,I;;;;;;;iBAGE,I;;;;;;;iBAGA,I;;;;;;;iBAGC,I;;;;;;;iBAGE,I;;;;;;;iBAGG,I;;;;;;;iBAGA,I", "sourcesContent": ["import { _decorator, Component, Node, Sprite, Vec2, ParticleSystem, tween, v2, misc, instantiate, UIOpacity, ParticleSystem2D, UITransform, v3 } from 'cc';\r\nimport EnemyBase from './EnemyBase';\r\nimport { Tools } from '../../../utils/Tools';\r\nimport { GameIns } from '../../../GameIns';\r\nimport GameEnum from '../../../const/GameEnum';\r\nimport EnemyShootComponent from './EnemyShootComponent';\r\nimport { EnemyData, EnemyPlaneData, EnemyShootData } from '../../../data/EnemyData';\r\nimport TrackComponent from '../../base/TrackComponent';\r\nimport GameConfig from '../../../const/GameConfig';\r\nimport { GameConst } from '../../../const/GameConst';\r\nimport EnemyPlaneRole from './EnemyPlaneRole';\r\nimport PfFrameAnim from '../../base/PfFrameAnim';\r\n\r\nconst { ccclass, property } = _decorator;\r\n\r\n@ccclass('EnemyPlane')\r\nexport default class EnemyPlane extends EnemyBase {\r\n    @property(Node)\r\n    role: Node = null;\r\n\r\n    @property(Sprite)\r\n    icon: Sprite = null;\r\n\r\n    @property(Sprite)\r\n    hpBg: Sprite = null;\r\n\r\n    @property(Sprite)\r\n    hpSpr: Sprite = null;\r\n\r\n    @property(Sprite)\r\n    hpWhite: Sprite = null;\r\n\r\n    @property(Node)\r\n    firePartile1: Node = null;\r\n\r\n    @property(Node)\r\n    firePartile2: Node = null;\r\n\r\n    _data: EnemyPlaneData = null;\r\n    _curAction: number = 0;\r\n    _curAngle: number = 0;\r\n    _destAngle: number = null;\r\n    _curDir: Vec2 = Vec2.ZERO;\r\n    _destDir: Vec2 = Vec2.ZERO;\r\n    _rotateSpeed: number = 0;\r\n    _leaveAct: number = -1;\r\n    _roleIndex: number = 1;\r\n    _curFormIndex: number = 0;\r\n    _curTrackType: number = -1;\r\n    _damagedEffect: boolean = false;\r\n    _removeCount: number = 0;\r\n    _removeTime: number = 0;\r\n    _dieAnimEnd: boolean = false;\r\n    _particleArr: Node[] = [];\r\n    _trackCom: TrackComponent = null;\r\n    _shootCom: EnemyShootComponent = null;\r\n    _role: EnemyPlaneRole = null;\r\n    _initAngle: boolean = true;\r\n    _hpWhiteTween: any = null;\r\n    shadowComp: any = null;\r\n    _bDieShoot: boolean = false;\r\n    m_outTime: number = 0;\r\n\r\n    static PrefabName = 'EnemyPlane';\r\n    dir: any;\r\n\r\n    preLoad() {\r\n        super.preLoad();\r\n        this._particleArr.push(this.firePartile1);\r\n        this._particleArr.push(this.firePartile2);\r\n        this._trackCom = Tools.addScript(this.node, TrackComponent);\r\n        this._shootCom = Tools.addScript(this.node, EnemyShootComponent);\r\n\r\n        const roleNode = instantiate(GameIns.enemyManager.pfPlaneRole);\r\n        this.role.addChild(roleNode);\r\n        this._role = roleNode.getComponent(EnemyPlaneRole);\r\n    }\r\n\r\n    preLoadUI(data: any) {\r\n        this._role.preLoadUI(data);\r\n    }\r\n\r\n    init(data: EnemyPlaneData) {\r\n        this._reset();\r\n        this._data = data;\r\n        this.dieBullet = data.dieBullet;\r\n        this.setUIData(GameIns.enemyManager.getEnemyUIData(this._data.uiId));\r\n        this._refreshProperty();\r\n        this._refreshUI();\r\n        this._refreshColliders();\r\n        this.setDir(v2(0, -1));\r\n        this.setAngle(0);\r\n        this.initAttr(data.attr);\r\n\r\n        // if (this.hasAttribution(GameEnum.EnemyAttr.Shield)) {\r\n        //     this._role.setEventCallback('shield', () => {\r\n        //         this.showAttrShield();\r\n        //     });\r\n        // } else {\r\n        //     this._role.setEventCallback('shield', null);\r\n        // }\r\n    }\r\n\r\n    _reset() {\r\n        super.reset();\r\n        this.role.angle = 0;\r\n        this._curAction = GameEnum.EnemyAction.Track;\r\n        this._removeTime = 0;\r\n        this._removeCount = 0;\r\n        this._dieAnimEnd = false;\r\n        this._bDieShoot = false;\r\n        this._roleIndex = 1;\r\n        this._curFormIndex = 0;\r\n        this._curTrackType = -1;\r\n        this._curAngle = 0;\r\n        this._destAngle = null;\r\n        this._curDir = Vec2.ZERO;\r\n        this._destDir = Vec2.ZERO;\r\n        this._rotateSpeed = 0;\r\n        this._leaveAct = -1;\r\n        this._damagedEffect = false;\r\n        this._initAngle = true;\r\n        this._hideFireParticle();\r\n        this.shadowComp = null;\r\n        this.m_outTime = 0;\r\n    }\r\n\r\n\r\n    /**\r\n     * 初始化组件\r\n     */\r\n    initComps(): void {\r\n        // 调用父类的组件初始化方法\r\n        super.initComps();\r\n\r\n        // 判断是否为特殊敌机（ID范围在50000到60000之间）\r\n        const isSpecialEnemy = this._data.id >= 50000 && this._data.id < 60000;\r\n\r\n        // 初始化射击组件\r\n        this._shootCom.init(this, isSpecialEnemy ? this._role.role.node : this.node, null, isSpecialEnemy);\r\n\r\n        // 设置攻击开始的回调\r\n        this._shootCom.setAtkStartCall(() => {\r\n            this.setAction(GameEnum.EnemyAction.AttackPrepare);\r\n        });\r\n\r\n        // 设置攻击结束的回调\r\n        this._shootCom.setAtkOverCall(() => {\r\n            this.setAction(GameEnum.EnemyAction.AttackOver);\r\n        });\r\n    }\r\n\r\n    /**\r\n     * 初始化轨迹\r\n     * @param {TrackData} trackData 轨迹数据\r\n     * @param {number} offsetX X 轴偏移\r\n     * @param {number} offsetY Y 轴偏移\r\n     * @param {boolean} isLoop 是否循环\r\n     * @param {number} rotateSpeed 旋转速度\r\n     */\r\n    initTrack(trackData, trackParams, offsetX, offsetY, rotateSpeed = 0) {\r\n        this._rotateSpeed = rotateSpeed;\r\n        this._trackCom.init(this, trackData, trackParams, offsetX, offsetY);\r\n\r\n        // 设置轨迹的各种回调\r\n        this._trackCom.setTrackGroupStartCall((track, groupType, groupIndex) => {\r\n            this.setTrackSpecialType(groupType, groupIndex);\r\n        });\r\n\r\n        this._trackCom.setTrackGroupOverCall((groupType) => {\r\n            if (this.uiData.isAm && groupType === 0) {\r\n                this.setAction(GameEnum.EnemyAction.Transform);\r\n            }\r\n        });\r\n\r\n        this._trackCom.setTrackOverCall(() => {\r\n            this.die(GameEnum.EnemyDestroyType.TrackOver);\r\n        });\r\n\r\n        this._trackCom.setTrackLeaveCall(() => {\r\n            this.die(GameEnum.EnemyDestroyType.Leave);\r\n        });\r\n\r\n        this._trackCom.setTrackStartCall((trackType) => {\r\n            this.setTrackType(trackType);\r\n        });\r\n    }\r\n\r\n    /**\r\n * 设置首次射击的延迟时间\r\n * @param {number} delay 延迟时间\r\n */\r\n    setFirstShootDelay(delay) {\r\n        this._shootCom.setFirstShootDelay(delay);\r\n    }\r\n\r\n    /**\r\n     * 开始战斗\r\n     */\r\n    startBattle() {\r\n        super.startBattle();\r\n        this.active = true;\r\n        this._refreshHpBar();\r\n        this._trackCom.setTrackAble(true);\r\n        this._trackCom.startTrack();\r\n    }\r\n\r\n    /**\r\n     * 更新游戏逻辑\r\n     * @param {number} deltaTime 帧间隔时间\r\n     */\r\n    updateGameLogic(deltaTime) {\r\n        super.updateGameLogic(deltaTime);\r\n\r\n        if (this.isDead) {\r\n            if (this._bDieShoot) {\r\n                this._shootCom.updateGameLogic(deltaTime);\r\n            } else {\r\n                this._checkRemoveAble(deltaTime);\r\n            }\r\n        } else if (!this.isStandBy()) {\r\n            this._role.updateGameLogic(deltaTime);\r\n            if (!this.hasHurtBuff(GameEnum.EnemyBuff.Ice)) {\r\n                this._trackCom.updateGameLogic(deltaTime);\r\n                this._shootCom.updateGameLogic(deltaTime);\r\n                this._updateAction(deltaTime);\r\n            }\r\n        }\r\n    }\r\n\r\n    /**\r\n     * 更新当前行为\r\n     * @param {number} deltaTime 帧间隔时间\r\n     */\r\n    _updateAction(deltaTime) {\r\n        this._shootCom.setNextAble(false);\r\n\r\n        switch (this._curAction) {\r\n            case GameEnum.EnemyAction.Sneak:\r\n                this._updateCurDir(deltaTime);\r\n                this.collideAble = false;\r\n                break;\r\n\r\n            case GameEnum.EnemyAction.Track:\r\n                this._updateCurDir(deltaTime);\r\n                this._shootCom.setNextAble(\r\n                    (this._trackCom.isMoving && this._data.bMoveAttack) ||\r\n                    (!this._trackCom.isMoving && this._data.bStayAttack)\r\n                );\r\n                break;\r\n\r\n            case GameEnum.EnemyAction.Transform:\r\n                break;\r\n\r\n            case GameEnum.EnemyAction.AttackPrepare:\r\n            case GameEnum.EnemyAction.AttackIng:\r\n                this._updateCurDir(deltaTime);\r\n                break;\r\n\r\n            case GameEnum.EnemyAction.Leave:\r\n                if (this._leaveAct === 0) {\r\n                    this.die(GameEnum.EnemyDestroyType.TimeOver);\r\n                } else if (this._leaveAct > 0) {\r\n                    this._updateCurDir(deltaTime);\r\n                }\r\n                break;\r\n        }\r\n    }\r\n\r\n    /**\r\n     * 检查是否可以移除\r\n     * @param {number} deltaTime 帧间隔时间\r\n     */\r\n    _checkRemoveAble(deltaTime) {\r\n        if (this._dieAnimEnd) {\r\n            this._removeCount += deltaTime;\r\n            if (this._removeCount > this._removeTime) {\r\n                this.removeAble = true;\r\n            }\r\n        }\r\n    }\r\n\r\n    /**\r\n     * 设置方向\r\n     * @param {Vec2} dir 方向向量\r\n     */\r\n    setDir(dir) {\r\n        this.dir = dir;\r\n        if (this._data.bTurnDir) {\r\n            this.setAngle(-Tools.getDegreeForDir(dir));\r\n        }\r\n        if (this._role) {\r\n            this._role.setSneakDir(-Tools.getDegreeForDir(dir));\r\n        }\r\n    }\r\n\r\n    /**\r\n     * 获取当前方向\r\n     * @returns {Vec2} 当前方向向量\r\n     */\r\n    getDir() {\r\n        return this._curDir;\r\n    }\r\n\r\n    /**\r\n     * 设置目标方向\r\n     * @param {Vec2} dir 目标方向向量\r\n     */\r\n    setDestDir(dir) {\r\n        this._destDir = dir;\r\n        if (this._initAngle) {\r\n            this._initAngle = false;\r\n            this.setDir(dir);\r\n        } else if (this._rotateSpeed <= 0) {\r\n            this.setDir(dir);\r\n        }\r\n    }\r\n\r\n    /**\r\n     * 设置当前角度\r\n     * @param {number} angle 当前角度\r\n     */\r\n    setAngle(angle) {\r\n        if (angle > 360) {\r\n            angle -= 360;\r\n        } else if (angle < -360) {\r\n            angle += 360;\r\n        }\r\n        this._curAngle = angle;\r\n        this.role.angle = angle;\r\n    }\r\n\r\n    /**\r\n     * 设置目标角度\r\n     * @param {number} angle 目标角度\r\n     */\r\n    setDestAngle(angle) {\r\n        if (this._rotateSpeed > 0) {\r\n            let delta = angle - this._curAngle;\r\n            while (Math.abs(delta) > 360) {\r\n                angle += angle > this._curAngle ? -360 : 360;\r\n                delta = angle - this._curAngle;\r\n            }\r\n            if (Math.abs(delta) > 180) {\r\n                angle += angle > this._curAngle ? -360 : 360;\r\n            }\r\n            this._destAngle = angle;\r\n        }\r\n    }\r\n\r\n    /**\r\n     * 检查是否可以存活\r\n     * @returns {boolean} 是否可以存活\r\n     */\r\n    checkLiveAble() {\r\n        if (this._curAction === GameEnum.EnemyAction.Track) {\r\n            this.setAction(GameEnum.EnemyAction.Leave);\r\n            return true;\r\n        }\r\n        return false;\r\n    }\r\n\r\n    /**\r\n     * 设置形态索引\r\n     * @param {number} index 形态索引\r\n     */\r\n    setFormIndex(index) {\r\n        this._curFormIndex = index;\r\n        if (this._curFormIndex >= 0 && this._data.bAttackAbles[this._curFormIndex]) {\r\n            const shootData = new EnemyShootData();\r\n            shootData.attackInterval = this._data.attackInterval;\r\n            shootData.attackNum = this._data.attackNum;\r\n            shootData.attackPointArr = this._data.attackPointArr[this._curFormIndex];\r\n            shootData.attackArrNum = shootData.attackPointArr.length;\r\n            this._shootCom.setShootData(shootData);\r\n        } else {\r\n            this._shootCom.setShootData(null);\r\n        }\r\n    }\r\n\r\n    /**\r\n     * 设置敌机的轨迹类型\r\n     * @param trackData 轨迹数据\r\n     */\r\n    setTrackType(trackData: any): void {\r\n        if (!trackData) {\r\n            return;\r\n        }\r\n\r\n        const frameTime = GameConfig.ActionFrameTime;\r\n\r\n        switch (trackData.type) {\r\n            case 2: // 渐变出现\r\n                this.shadowComp && (this.shadowComp.node.active = false);\r\n                // BattleManager.audioManager.playEffect(\"e_appear_2\");\r\n                // this.hpBg.node.opacity = 0;\r\n                this._trackCom.setTrackAble(false);\r\n                this.collideAble = false;\r\n\r\n                let tail = this.node.getChildByName(\"tail\");\r\n                if (!tail) {\r\n                    tail = new Node();\r\n                    this.node.addChild(tail);\r\n                    GameIns.enemyManager.setPlaneFrame(tail.addComponent(Sprite), \"ashow_0\");\r\n                    tail.getComponent(UITransform).anchorY = 0;\r\n                }\r\n                tail.active = true;\r\n                tail.setScale(1, 2.2);\r\n\r\n                tween(tail)\r\n                    .delay(0.02)\r\n                    .to(5 * frameTime, { scale: v3(1, 2.8) })\r\n                    .to(2 * frameTime, { scale: v3(1, 0.5) })\r\n                    .to(frameTime, { scale: v3(1, 0) })\r\n                    .call(() => {\r\n                        tail.active = false;\r\n                    })\r\n                    .start();\r\n\r\n                this.scheduleOnce(() => {\r\n                    this._playTailEffects(trackData, frameTime);\r\n                }, 7 * frameTime);\r\n\r\n                tween(this.node)\r\n                    .to(7 * frameTime, {\r\n                        position: v3(\r\n                            trackData.endX + this._trackCom.trackOffsetX,\r\n                            trackData.endY + this._trackCom.trackOffsetY\r\n                        ),\r\n                    })\r\n                    .call(() => {\r\n                        this.setPos(\r\n                            trackData.endX + this._trackCom.trackOffsetX,\r\n                            trackData.endY + this._trackCom.trackOffsetY\r\n                        );\r\n                        this.collideAble = true;\r\n                        // this.hpBg.node.opacity = this._data.hpParam === 1 ? 255 : 0;\r\n                        this.shadowComp && (this.shadowComp.node.active = true);\r\n                    })\r\n                    .delay(0.2)\r\n                    .call(() => {\r\n                        this._trackCom.trackFinish = true;\r\n                        this._trackCom.setTrackAble(true);\r\n                    })\r\n                    .start();\r\n                break;\r\n\r\n            case 3: // 瞬间出现\r\n                this.shadowComp && (this.shadowComp.node.active = false);\r\n                // this.hpBg.node.opacity = 0;\r\n                this._trackCom.setTrackAble(false);\r\n                this.collideAble = false;\r\n                this.setPos(\r\n                    trackData.endX + this._trackCom.trackOffsetX,\r\n                    trackData.endY + this._trackCom.trackOffsetY\r\n                );\r\n\r\n                const appearNode = this._createAppearEffect(frameTime);\r\n                this.scheduleOnce(() => {\r\n                    // this.role.opacity = 255;\r\n                    this._role.blueShow(() => {\r\n                        this._trackCom.trackFinish = true;\r\n                        this.collideAble = true;\r\n                        this._trackCom.setTrackAble(true);\r\n                        // this.hpBg.node.opacity = this._data.hpParam === 1 ? 255 : 0;\r\n                        this.shadowComp && (this.shadowComp.node.active = true);\r\n                    });\r\n                }, 2 * frameTime);\r\n                break;\r\n\r\n            case 4:\r\n            case 5: // 隐身或显现\r\n                if (this._curTrackType !== 4 && this._curTrackType !== 5) {\r\n                    this._trackCom.setTrackAble(false);\r\n                    this._shootCom.active = false;\r\n                    this.collideAble = false;\r\n                    // this.hpBg.node.opacity = 0;\r\n                    this._role.playCloakeHideAnim(() => {\r\n                        this._trackCom.setTrackAble(true);\r\n                        this.shadowComp && (this.shadowComp.node.active = false);\r\n                    });\r\n                } else {\r\n                    this._shootCom.active = false;\r\n                    this._trackCom.setTrackAble(false);\r\n                    this._role.playCloakeShowAnim(() => {\r\n                        this.shadowComp && (this.shadowComp.node.active = true);\r\n                        this._shootCom.active = true;\r\n                        this._trackCom.setTrackAble(true);\r\n                        this.collideAble = true;\r\n                        // this.hpBg.node.opacity = 255;\r\n                    });\r\n                }\r\n                break;\r\n\r\n            default:\r\n                if (this._curTrackType === 4 || this._curTrackType === 5) {\r\n                    this._shootCom.active = false;\r\n                    this._trackCom.setTrackAble(false);\r\n                    this._role.playCloakeShowAnim(() => {\r\n                        this.shadowComp && (this.shadowComp.node.active = true);\r\n                        this._shootCom.active = true;\r\n                        this._trackCom.setTrackAble(true);\r\n                        this.collideAble = true;\r\n                        // this.hpBg.node.opacity = 255;\r\n                    });\r\n                }\r\n                break;\r\n        }\r\n\r\n        this._curTrackType = trackData.type;\r\n    }\r\n\r\n    /**\r\n     * 播放尾部特效\r\n     * @param trackData 轨迹数据\r\n     * @param frameTime 每帧时间\r\n     */\r\n    private _playTailEffects(trackData: any, frameTime: number): void {\r\n        const tailEffects = [\"tail2\", \"tail1\", \"tail0\"];\r\n        const tailFrames = [\"ashow_1\", \"ashow_2\", \"ashow_3\"];\r\n        const scales = [1.8, 1.55, { scaleX: 2.2, scaleY: 0.5 }];\r\n\r\n        tailEffects.forEach((name, index) => {\r\n            let tail = this.node.getChildByName(name);\r\n            if (!tail) {\r\n                tail = new Node();\r\n                tail.name = name;\r\n                this.node.addChild(tail);\r\n                GameIns.enemyManager.setPlaneFrame(tail.addComponent(Sprite), tailFrames[index]);\r\n            }\r\n            tail.active = true;\r\n            // tail.opacity = 255;\r\n            tail.setScale(1, 1);\r\n\r\n            let scale = typeof scales[index] === \"object\" ? v3(scales[index].scaleX, scales[index].scaleY) : v3(scales[index], scales[index]);\r\n            typeof scale\r\n            tween(tail)\r\n                .to(5 * frameTime, { scale: scale })\r\n                // .to(5 * frameTime, { opacity: 0 })\r\n                .call(() => {\r\n                    tail.active = false;\r\n                })\r\n                .start();\r\n        });\r\n    }\r\n\r\n    /**\r\n     * 创建瞬间出现的特效\r\n     * @param frameTime 每帧时间\r\n     * @returns 创建的特效节点\r\n     */\r\n    private _createAppearEffect(frameTime: number): Node {\r\n        let appearNode = this.node.getChildByName(\"appear\");\r\n        if (!appearNode) {\r\n            appearNode = instantiate(GameConst.frameAnim);\r\n            appearNode.name = \"appear\";\r\n            this.node.addChild(appearNode);\r\n\r\n            const frameAnim = appearNode.getComponent(PfFrameAnim);\r\n            frameAnim.init(GameIns.enemyManager.enemyAtlas, \"as_\", 13, frameTime, () => {\r\n                frameAnim.stop();\r\n                appearNode.active = false;\r\n            });\r\n        }\r\n\r\n        appearNode.active = true;\r\n        if (this.uiData.showParam.length > 3) {\r\n            appearNode.setScale(this.uiData.showParam[0], this.uiData.showParam[1]);\r\n            appearNode.setPosition(this.uiData.showParam[2], this.uiData.showParam[3]);\r\n        }\r\n\r\n        // BattleManager.audioManager.playEffect(\"e_appear_3\");\r\n        appearNode.getComponent(PfFrameAnim).reset();\r\n\r\n        return appearNode;\r\n    }\r\n\r\n    /**\r\n     * 创建尾部节点\r\n     * @param {string} name 节点名称\r\n     * @param {string} frameName 精灵帧名称\r\n     * @param {number} scale 缩放比例\r\n     * @param {number} [scaleY] Y 轴缩放比例\r\n     * @returns {Node} 创建的尾部节点\r\n     */\r\n    _createTailNode(name, frameName, scale, scaleY = scale) {\r\n        // let tail = this.node.getChildByName(name);\r\n        // if (!tail) {\r\n        //     tail = new Node();\r\n        //     tail.name = name;\r\n        //     this.node.addChild(tail);\r\n        //     l.EnemyMgr.setPlaneFrame(tail.addComponent(Sprite), frameName);\r\n        // }\r\n        // tail.active = true;\r\n        // tail.opacity = 255;\r\n        // tail.scale = scale;\r\n        // tail.scaleY = scaleY;\r\n\r\n        // tween(tail)\r\n        //     .to(5 * c.default.ActionFrameTime, { scale, opacity: 0 })\r\n        //     .call(() => {\r\n        //         tail.active = false;\r\n        //     })\r\n        //     .start();\r\n\r\n        // return tail;\r\n    }\r\n\r\n    /**\r\n     * 创建出现动画节点\r\n     * @returns {Node} 出现动画节点\r\n     */\r\n    _createAppearNode() {\r\n        // let appear = this.node.getChildByName(\"appear\");\r\n        // if (!appear) {\r\n        //     appear = instantiate(GameConst.frameAnim);\r\n        //     appear.name = \"appear\";\r\n        //     this.node.addChild(appear);\r\n        //     const anim = appear.getComponent(f.default);\r\n        //     anim.init(l.EnemyMgr.enemyAtlas, \"as_\", 13, c.default.ActionFrameTime, () => {\r\n        //         anim.stop();\r\n        //         appear.active = false;\r\n        //     });\r\n        // }\r\n        // appear.active = true;\r\n        // if (this.uiData.showParam.length > 3) {\r\n        //     appear.scaleX = this.uiData.showParam[0];\r\n        //     appear.scaleY = this.uiData.showParam[1];\r\n        //     appear.position = v2(this.uiData.showParam[2], this.uiData.showParam[3]);\r\n        // }\r\n        // m.default.audioManager.playEffect(\"e_appear_3\");\r\n        // appear.getComponent(f.default).reset();\r\n\r\n        // return appear;\r\n    }\r\n\r\n\r\n    /**\r\n     * 设置轨迹的特殊类型\r\n     * @param {number} currentType 当前类型\r\n     * @param {number} nextType 下一个类型\r\n     */\r\n    setTrackSpecialType(currentType, nextType) {\r\n        // if (currentType === nextType) return;\r\n\r\n        // switch (nextType) {\r\n        //     case 0: // 正常状态\r\n        //         this._handleNormalState(currentType);\r\n        //         break;\r\n\r\n        //     case 1: // 潜行状态\r\n        //         this._handleSneakState();\r\n        //         break;\r\n\r\n        //     default:\r\n        //         this.collideAble = true;\r\n        // }\r\n    }\r\n\r\n    /**\r\n     * 设置敌机的当前行为\r\n     * @param {number} action 行为类型（枚举值）\r\n     */\r\n    setAction(action) {\r\n        if (this._curAction !== action) {\r\n            this._curAction = action;\r\n\r\n            // 停止射击并启用轨迹\r\n            this._shootCom.setIsShooting(false);\r\n            this._trackCom.setTrackAble(true);\r\n\r\n            switch (this._curAction) {\r\n                case GameEnum.EnemyAction.Sneak:\r\n                    // 潜行行为\r\n                    this.hpBg.node.getComponent(UIOpacity).opacity = 0;\r\n                    // this._role.playSneakAnim();\r\n                    break;\r\n\r\n                case GameEnum.EnemyAction.Track:\r\n                    // 跟踪行为\r\n                    break;\r\n\r\n                case GameEnum.EnemyAction.Transform:\r\n                    // 变形行为\r\n                    this._trackCom.setTrackAble(false);\r\n                    this._shootCom.stopShoot();\r\n                    this._roleIndex++;\r\n                    this._role.playAnim(\"transform\", () => {\r\n                        this._role.playAnim(\"idle\" + this._roleIndex, null);\r\n                        this.setAction(GameEnum.EnemyAction.Track);\r\n                        this._shootCom.setNextShootAtOnce();\r\n                    }) || (this.setAction(GameEnum.EnemyAction.Track), this._shootCom.setNextShootAtOnce());\r\n                    break;\r\n\r\n                case GameEnum.EnemyAction.AttackPrepare:\r\n                    // 准备攻击行为\r\n                    this.playAtkAnim();\r\n                    this.setAction(GameEnum.EnemyAction.AttackIng);\r\n                    this._role.playAtkWarnAnim();\r\n                    this._role.startAttack();\r\n                    break;\r\n\r\n                case GameEnum.EnemyAction.AttackIng:\r\n                    // 攻击中行为\r\n                    this._shootCom.startShoot();\r\n                    break;\r\n\r\n                case GameEnum.EnemyAction.AttackOver:\r\n                    // 攻击结束行为\r\n                    this._role.attackOver();\r\n                    this.setAction(GameEnum.EnemyAction.Track);\r\n                    break;\r\n\r\n                default:\r\n                    break;\r\n            }\r\n        }\r\n    }\r\n\r\n    /**\r\n     * 更新当前方向\r\n     * @param {number} deltaTime 帧间隔时间\r\n     */\r\n    _updateCurDir(deltaTime) {\r\n        if (this._data.bTurnDir && this._destAngle !== null && this._destAngle !== this._curAngle) {\r\n            const direction = this._destAngle > this._curAngle ? 1 : -1;\r\n            const angleChange = deltaTime * this._rotateSpeed * direction;\r\n            let newAngle = this._curAngle + angleChange;\r\n\r\n            if (direction > 0) {\r\n                if (newAngle > this._destAngle) newAngle = this._destAngle;\r\n            } else {\r\n                if (newAngle < this._destAngle) newAngle = this._destAngle;\r\n            }\r\n\r\n            const rotatedDir = v2(this._curDir.x, this._curDir.y).rotate(misc.degreesToRadians(angleChange));\r\n            this.dir = rotatedDir; // 使用旋转后的新向量\r\n            this.setAngle(newAngle);\r\n        }\r\n    }\r\n\r\n    /**\r\n     * 刷新敌机的属性\r\n     */\r\n    _refreshProperty() {\r\n        this.curHp = this._data.hp;\r\n        this.maxHp = this._data.hp;\r\n        this.defence = this._data.defence;\r\n        this.attack = this._data.attack;\r\n        this.collideLevel = this._data.collideLevel;\r\n        this.collideAtk = this._data.collideAttack;\r\n        this.bCollideDead = this._data.bCollideDead;\r\n    }\r\n\r\n    /**\r\n     * 刷新敌机的 UI\r\n     * @param {boolean} isRespawn 是否为重生\r\n     */\r\n    _refreshUI(isRespawn = false) {\r\n        // this.role.getComponent(UIOpacity).opacity = 255;\r\n        // this.scheduleOnce(() => {\r\n        //     this.role.getComponent(UIOpacity).opacity = 255;\r\n        // }, 0.1);\r\n\r\n        // if (!isRespawn) {\r\n        //     this.role.scale = GameIns.battleManager.getRatio();\r\n        // }\r\n\r\n        this._role.init(this.uiData, this, this._data.param);\r\n\r\n        const fireParticle1 = this.firePartile1.getComponent(ParticleSystem2D) as ParticleSystem2D;\r\n        if (fireParticle1) {\r\n            fireParticle1.spriteFrame = GameIns.enemyManager.getPlaneFrame('fire');\r\n        }\r\n\r\n        const fireParticle2 = this.firePartile2.getComponent(ParticleSystem2D);\r\n        if (fireParticle2) {\r\n            fireParticle2.spriteFrame = GameIns.enemyManager.getPlaneFrame('fire');\r\n        }\r\n\r\n        if (!isRespawn) {\r\n            try {\r\n                this.hpBg.node.getComponent(UIOpacity).opacity = this._data.hpParam === 1 ? 255 : 0;\r\n                this.hpBg.node.setPosition(this.uiData.hpParam[0], this.uiData.hpParam[1]);\r\n                GameIns.enemyManager.setPlaneFrame(this.hpBg, `hp${this.uiData.hpParam[2]}_0`);\r\n                GameIns.enemyManager.setPlaneFrame(this.hpSpr, `hp${this.uiData.hpParam[2]}_1`);\r\n                GameIns.enemyManager.setPlaneFrame(this.hpWhite, `hp${this.uiData.hpParam[2]}_2`);\r\n            } catch (error) {\r\n                // this.hpBg.node.y = this.icon.node.height >> 1;\r\n            }\r\n        }\r\n    }\r\n\r\n    /**\r\n     * 刷新敌机的碰撞器\r\n     */\r\n    _refreshColliders() {\r\n        let scale = this.role.scale.x;\r\n        const colliderData = this.uiData.collider;\r\n        this.setCollideData([\r\n            colliderData[0],\r\n            colliderData[1] *scale,\r\n            colliderData[2] *scale,\r\n            colliderData[3] *scale,\r\n            colliderData[4] *scale,\r\n        ]);\r\n    }\r\n    /**\r\n * 刷新血条\r\n */\r\n    _refreshHpBar() {\r\n        const hpRatio = this.curHp / this.maxHp;\r\n        const isDecreasing = hpRatio < this.hpSpr.fillRange;\r\n\r\n        // 更新血条显示\r\n        this.hpSpr.fillRange = hpRatio;\r\n\r\n        // 停止之前的血条动画\r\n        if (this._hpWhiteTween) {\r\n            this._hpWhiteTween.stop();\r\n            this._hpWhiteTween = null;\r\n        }\r\n\r\n        // 如果血量减少，播放白色血条的动画\r\n        if (isDecreasing) {\r\n            const duration = Math.abs(this.hpWhite.fillRange - this.hpSpr.fillRange);\r\n            this._hpWhiteTween = tween(this.hpWhite)\r\n                .to(duration, { fillRange: this.hpSpr.fillRange })\r\n                .call(() => {\r\n                    this._hpWhiteTween = null;\r\n                })\r\n                .start();\r\n        } else {\r\n            this.hpWhite.fillRange = hpRatio;\r\n        }\r\n    }\r\n\r\n    /**\r\n     * 受到伤害时的处理\r\n     */\r\n    onHurt() {\r\n        this._refreshHpBar();\r\n        this.checkHp();\r\n        if (!this.isDead) {\r\n            // this._role.winkWhite();\r\n        }\r\n    }\r\n\r\n    /**\r\n     * 检查血量\r\n     * @returns {boolean} 是否死亡\r\n     */\r\n    checkHp() {\r\n        if (super.checkHp()) {\r\n            return true;\r\n        }\r\n\r\n        if (!this._damagedEffect && this.curHp < 0.34 * this.maxHp) {\r\n            this._damagedEffect = true;\r\n            this._playFireParticle();\r\n        }\r\n\r\n        return false;\r\n    }\r\n\r\n    /**\r\n     * 移除 Buff 时的处理\r\n     * @param {number} buffType Buff 类型\r\n     */\r\n    onRemoveBuff(buffType) {\r\n        super.onRemoveBuff(buffType);\r\n        // if (buffType === GameEnum.EnemyBuff.Ice) {\r\n        //     this._role.resumeAnim();\r\n        // }\r\n    }\r\n\r\n    /**\r\n     * 添加 Buff 时的处理\r\n     * @param {number} buffType Buff 类型\r\n     */\r\n    onAddBuff(buffType) {\r\n        super.onAddBuff(buffType);\r\n        // if (buffType === GameEnum.EnemyBuff.Ice) {\r\n        //     this._role.pauseAnim();\r\n        // }\r\n    }\r\n\r\n    /**\r\n     * 敌机死亡时的处理\r\n     * @param {number} destroyType 销毁类型\r\n     */\r\n    onDie(destroyType) {\r\n        super.onDie(destroyType);\r\n        this.hpBg.node.getComponent(UIOpacity).opacity = 0;\r\n        this.willRemove();\r\n\r\n        switch (destroyType) {\r\n            case GameEnum.EnemyDestroyType.Die:\r\n                this.playDieAnim();\r\n                this._checkDieShoot();\r\n                break;\r\n\r\n            case GameEnum.EnemyDestroyType.Leave:\r\n            case GameEnum.EnemyDestroyType.TrackOver:\r\n            case GameEnum.EnemyDestroyType.TimeOver:\r\n                this._dieAnimEnd = true;\r\n                break;\r\n        }\r\n    }\r\n\r\n    /**\r\n     * 准备移除敌机\r\n     */\r\n    willRemove() {\r\n        // if (this._role) {\r\n        //     this._role.stopAnim();\r\n        // }\r\n\r\n        if (this._hpWhiteTween) {\r\n            this._hpWhiteTween.stop();\r\n            this._hpWhiteTween = null;\r\n        }\r\n\r\n        this.hpWhite.fillRange = 0;\r\n    }\r\n\r\n    /**\r\n     * 检查死亡时是否需要射击\r\n     */\r\n    _checkDieShoot() {\r\n        // if (this._data.dieShoot.length > 0 && !Tools.isPlaneOutScreen(v2(this.node.position.x, this.node.position.y))) {\r\n        //     this._bDieShoot = true;\r\n\r\n        //     const shootData = new EnemyShootData();\r\n        //     shootData.attackInterval = this._data.attackInterval;\r\n        //     shootData.attackNum = 1;\r\n        //     shootData.attackArrNum = 1;\r\n        //     shootData.attackPointArr = [this._data.dieShoot];\r\n\r\n        //     this._shootCom.setShootData(shootData);\r\n        //     this._shootCom.setAtkOverCall(() => {\r\n        //         this._bDieShoot = false;\r\n        //         this.role.getComponent(UIOpacity).opacity = 0;\r\n        //     });\r\n        //     this._shootCom.setNextShoot();\r\n        // }\r\n    }\r\n\r\n    /**\r\n     * 播放攻击动画\r\n     */\r\n    playAtkAnim() {\r\n        this._role.playAnim(`atk${this._roleIndex}`, () => {\r\n            this._role.playAnim(`idle${this._roleIndex}`, null);\r\n        });\r\n    }\r\n\r\n    /**\r\n     * 播放火焰粒子效果\r\n     */\r\n    _playFireParticle() {\r\n        for (let i = 0; i < this.uiData.damageParam.length; i++) {\r\n            const damageParam = this.uiData.damageParam[i];\r\n            let particle = this._particleArr[i];\r\n\r\n            if (!particle) {\r\n                particle = instantiate(this.firePartile1);\r\n                this.role.addChild(particle);\r\n                this._particleArr.push(particle);\r\n            }\r\n\r\n            particle.active = true;\r\n            particle.getComponent(UIOpacity).opacity = 255;\r\n            particle.setPosition(damageParam[1], damageParam[2]);\r\n            particle.setScale(0.5, 0.5);\r\n\r\n            tween(particle)\r\n                .to(4 * GameConfig.ActionFrameTime, { scale: damageParam[0] })\r\n                .start();\r\n        }\r\n    }\r\n\r\n    /**\r\n     * 播放死亡动画\r\n     */\r\n    playDieAnim() {\r\n        super.playDieAnim();\r\n\r\n        this.scheduleOnce(() => {\r\n            Tools.removeChildByName(this.node, \"fire\");\r\n            this.checkLoot();\r\n            this.role.getComponent(UIOpacity).opacity = 0;\r\n            if (this.shadowComp) {\r\n                this.shadowComp.removeAble = true;\r\n            }\r\n        }, 0.1);\r\n\r\n        this._hideFireParticle();\r\n    }\r\n\r\n    /**\r\n     * 死亡动画结束时的处理\r\n     */\r\n    onDieAnimEnd() {\r\n        this._dieAnimEnd = true;\r\n    }\r\n\r\n    /**\r\n     * 隐藏火焰粒子效果\r\n     * @param {boolean} immediate 是否立即隐藏\r\n     */\r\n    _hideFireParticle(immediate = false) {\r\n        for (const particle of this._particleArr) {\r\n            if (particle.active) {\r\n                if (immediate) {\r\n                    particle.active = false;\r\n                } else {\r\n                    tween(particle.getComponent(UIOpacity))\r\n                        .to(0.2, { opacity: 0 })\r\n                        .call(() => {\r\n                            particle.active = false;\r\n                        })\r\n                        .start();\r\n                }\r\n            }\r\n        }\r\n    }\r\n    /**\r\n     * 获取当前角度\r\n     * @returns {number} 当前角度\r\n     */\r\n    get angle() {\r\n        return this.role ? this.role.angle : 0;\r\n    }\r\n\r\n    /**\r\n     * 设置当前角度\r\n     * @param {number} value 角度值\r\n     */\r\n    set angle(value) {\r\n        if (this.role) {\r\n            this.role.angle = value;\r\n        }\r\n    }\r\n\r\n    /**\r\n     * 设置角色透明度\r\n     * @param {number} opacity 透明度值\r\n     */\r\n    setRoleOpacity(opacity) {\r\n        // if (this.role) {\r\n        //     this.role.opacity = opacity;\r\n        // }\r\n    }\r\n\r\n    /**\r\n     * 获取子弹发射的角度\r\n     * @returns {number} 子弹发射角度\r\n     */\r\n    getFireBulletAngle() {\r\n        // if (this._data.id >= 50000 && this._data.id < 60000) {\r\n        //     return this._role.role.node.angle;\r\n        // }\r\n        return 0;\r\n    }\r\n\r\n    /**\r\n     * 检查敌机是否在屏幕内\r\n     * @param {number} deltaTime 帧间隔时间\r\n     */\r\n    checkInScreen(deltaTime) {\r\n        if (this.itemParent === this) {\r\n            if (Tools.isPlaneOutScreen(v2(this.node.position.x, this.node.position.y))) {\r\n                this.m_outTime += deltaTime;\r\n                if (this.m_outTime > 10) {\r\n                    this.isDead = true;\r\n                    this.removeAble = true;\r\n                }\r\n            } else {\r\n                this.m_outTime = 0;\r\n            }\r\n        }\r\n    }\r\n}"]}