System.register(["__unresolved_0", "cc", "__unresolved_1", "__unresolved_2", "__unresolved_3"], function (_export, _context) {
  "use strict";

  var _reporterNs, _cclegacy, __checkObsolete__, __checkObsoleteInNamespace__, _decorator, BaseComp, GameIns, GameConst, _dec, _class, _crd, ccclass, property, MainSkillBase;

  function asyncGeneratorStep(gen, resolve, reject, _next, _throw, key, arg) { try { var info = gen[key](arg); var value = info.value; } catch (error) { reject(error); return; } if (info.done) { resolve(value); } else { Promise.resolve(value).then(_next, _throw); } }

  function _asyncToGenerator(fn) { return function () { var self = this, args = arguments; return new Promise(function (resolve, reject) { var gen = fn.apply(self, args); function _next(value) { asyncGeneratorStep(gen, resolve, reject, _next, _throw, "next", value); } function _throw(err) { asyncGeneratorStep(gen, resolve, reject, _next, _throw, "throw", err); } _next(undefined); }); }; }

  function _reportPossibleCrUseOfBaseComp(extras) {
    _reporterNs.report("BaseComp", "../../base/BaseComp", _context.meta, extras);
  }

  function _reportPossibleCrUseOfGameIns(extras) {
    _reporterNs.report("GameIns", "../../../GameIns", _context.meta, extras);
  }

  function _reportPossibleCrUseOfGameConst(extras) {
    _reporterNs.report("GameConst", "../../../const/GameConst", _context.meta, extras);
  }

  return {
    setters: [function (_unresolved_) {
      _reporterNs = _unresolved_;
    }, function (_cc) {
      _cclegacy = _cc.cclegacy;
      __checkObsolete__ = _cc.__checkObsolete__;
      __checkObsoleteInNamespace__ = _cc.__checkObsoleteInNamespace__;
      _decorator = _cc._decorator;
    }, function (_unresolved_2) {
      BaseComp = _unresolved_2.default;
    }, function (_unresolved_3) {
      GameIns = _unresolved_3.GameIns;
    }, function (_unresolved_4) {
      GameConst = _unresolved_4.GameConst;
    }],
    execute: function () {
      _crd = true;

      _cclegacy._RF.push({}, "735eeEwVQJOupTd1svj3giY", "MainSkillBase", undefined);

      __checkObsolete__(['_decorator', 'Component']);

      ({
        ccclass,
        property
      } = _decorator);

      _export("default", MainSkillBase = (_dec = ccclass('MainSkillBase'), _dec(_class = class MainSkillBase extends (_crd && BaseComp === void 0 ? (_reportPossibleCrUseOfBaseComp({
        error: Error()
      }), BaseComp) : BaseComp) {
        constructor() {
          super(...arguments);
          this.m_plane = null;
          this.m_record = null;
          this.m_isFire = false;
          this.m_CDTime = 0;
          this.m_isEnd = false;
          this.m_keepTime = 0;
          this.m_inGame = false;
        }

        onInit() {}

        getProgress() {
          return this.m_CDTime / this.m_record.cd;
        }

        get cdTime() {
          return this.m_CDTime;
        }

        set cdTime(value) {
          this.m_CDTime = value;
        }

        setData(record) {
          this.m_plane = (_crd && GameIns === void 0 ? (_reportPossibleCrUseOfGameIns({
            error: Error()
          }), GameIns) : GameIns).mainPlaneManager.plane;
          this.m_record = record;
          this.cdTime = (_crd && GameIns === void 0 ? (_reportPossibleCrUseOfGameIns({
            error: Error()
          }), GameIns) : GameIns).gameDataManager.skillCD || 0;
          this.m_keepTime = this.m_record.kd;
        }

        createUI() {
          return _asyncToGenerator(function* () {
            if ((_crd && GameIns === void 0 ? (_reportPossibleCrUseOfGameIns({
              error: Error()
            }), GameIns) : GameIns).mainPlaneManager.isPlane(710)) {
              return;
            } // let dialog = uiManager.getDialog(MechaUI);
            // if (!dialog) {
            //     try {
            //         await loadManager.loadPrefab('MechaUI');
            //         dialog = await uiManager.createDialog(MechaUI);
            //     } catch (error) {
            //         GFunc.wxLoadErr();
            //     }
            // }
            // if (dialog) {
            //     dialog.onActive();
            // }

          })();
        }

        setCanFire() {
          this.m_isFire = false;
          this.cdTime = 0;
        }

        canFire() {
          var canFire = !this.m_isFire;
          var isCooldown = this.cdTime <= 0;
          var isSkillUnlocked = (_crd && GameIns === void 0 ? (_reportPossibleCrUseOfGameIns({
            error: Error()
          }), GameIns) : GameIns).mainPlaneManager.checkLockSkill();
          var isInGame = this.m_inGame;
          var isNotLJStage = true; //!StageMgr.isLJStage(GameIns.battleManager.subStage);

          var isFireEnabled = (_crd && GameIns === void 0 ? (_reportPossibleCrUseOfGameIns({
            error: Error()
          }), GameIns) : GameIns).mainPlaneManager.fireEnable;
          return canFire && isCooldown && isSkillUnlocked && isInGame && isNotLJStage && isFireEnabled;
        }

        fire() {
          if (this.canFire()) {
            this.m_isFire = true;
            this.m_keepTime = this.m_record.kd;
            this.cdTime = this.m_record.cd;
            this.onFire();
          }
        }

        fireFinish() {
          if (!this.canFire()) {
            this.m_isFire = false;
            this.onFireFinish();
          }
        }

        onFire() {}

        onFireFinish() {}

        onUpdate(deltaTime) {}

        removeSkill() {}

        update(deltaTime) {
          if ((_crd && GameConst === void 0 ? (_reportPossibleCrUseOfGameConst({
            error: Error()
          }), GameConst) : GameConst).GameAble && this.m_inGame) {
            this.onUpdate(deltaTime);

            if (!this.canFire()) {
              this.cdTime -= deltaTime;
            }

            if (this.m_isFire) {
              this.m_keepTime -= deltaTime;

              if (this.m_keepTime <= 0) {
                this.fireFinish();
              }
            }
          }
        }

        quiteBattle() {
          this.m_inGame = false;

          if (this.m_isFire) {
            this.fireFinish();
          }
        }

        startBattle(resetCD) {
          if (resetCD === void 0) {
            resetCD = false;
          }

          this.m_inGame = true;

          if (!resetCD) {
            (_crd && GameIns === void 0 ? (_reportPossibleCrUseOfGameIns({
              error: Error()
            }), GameIns) : GameIns).gameDataManager.skillCD = this.cdTime;
          }
        }

        beginBattle() {
          this.createUI();
        }

        endBattle() {
          this.removeSkill(); // uiManager.removeDialog(MechaUI);
        }

        crazyAnim(isCrazy) {
          if (isCrazy) {
            this.setSkinActive(true);
            this.m_plane.skinAnim.setAnimation(0, 'tocrazyingame', false);
            this.m_plane.skinAnim.setCompleteListener(() => {
              this.m_plane.skinAnim.setCompleteListener(null);
              this.m_plane.skinAnim.setAnimation(0, 'crazyidle', true);
            });
          } else {
            this.m_plane.skinAnim.setAnimation(0, 'ingametoidle', false);
            this.m_plane.skinAnim.setCompleteListener(() => {
              this.m_plane.skinAnim.setCompleteListener(null);
              this.setSkinActive(false);
            });
          }
        }

        setSkinActive(isActive) {
          this.m_plane.mechaAnimNode.active = !isActive;
          this.m_plane.skinAnim.node.opacity = isActive ? 255 : 0;
        }

      }) || _class));

      _cclegacy._RF.pop();

      _crd = false;
    }
  };
});
//# sourceMappingURL=9c872508a68d7deb5db504b392eda89d27dd2ff4.js.map