{"version": 3, "sources": ["file:///E:/M2Game/Client/assets/scripts/leveldata/leveldata.ts"], "names": ["LevelData", "LevelDataTerrain", "LevelDataLayer", "name", "totalTime", "layers", "uuid", "position", "scale", "rotation", "speed", "backgrounds", "terrains"], "mappings": ";;;kEAGaA,S,EAKAC,gB,EAMAC,c;;;;;;;;;;;;;;;;;;;;;2BAXAF,S,GAAN,MAAMA,SAAN,CAAgB;AAAA;AAAA,eACZG,IADY,GACG,EADH;AAAA,eAEZC,SAFY,GAEQ,EAFR;AAAA,eAGZC,MAHY,GAGe,EAHf;AAAA;;AAAA,O;;kCAKVJ,gB,GAAN,MAAMA,gBAAN,CAAsB;AAAA;AAAA,eAClBK,IADkB,GACH,EADG;AAAA,eAElBC,QAFkB;AAAA,eAGlBC,KAHkB;AAAA,eAIlBC,QAJkB;AAAA;;AAAA,O;;gCAMhBP,c,GAAN,MAAMA,cAAN,CAAoB;AAAA;AAAA,eAChBQ,KADgB,GACA,GADA;AAAA,eAEhBC,WAFgB,GAEQ,EAFR;AAAA,eAGhBC,QAHgB,GAGe,EAHf;AAAA;;AAAA,O", "sourcesContent": ["import { Vec2 } from \"cc\";\r\n\r\n\r\nexport class LevelData {\r\n    public name: string = \"\";\r\n    public totalTime: number = 60;\r\n    public layers: LevelDataLayer[] = [];\r\n}\r\nexport class LevelDataTerrain{\r\n    public uuid: string = \"\";\r\n    public position: Vec2;\r\n    public scale: Vec2;\r\n    public rotation:number; \r\n}\r\nexport class LevelDataLayer{\r\n    public speed: number = 200;\r\n    public backgrounds: string[] = [];\r\n    public terrains: LevelDataTerrain[] = [];\r\n}"]}