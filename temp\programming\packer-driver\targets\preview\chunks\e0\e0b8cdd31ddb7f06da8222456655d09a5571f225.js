System.register(["__unresolved_0", "cc", "__unresolved_1"], function (_export, _context) {
  "use strict";

  var _reporterNs, _cclegacy, __checkObsolete__, __checkObsoleteInNamespace__, _decorator, Component, director, find, Label, Global, _dec, _class, _crd, ccclass, property, GameOver;

  function _reportPossibleCrUseOfGlobal(extras) {
    _reporterNs.report("Global", "./Global", _context.meta, extras);
  }

  return {
    setters: [function (_unresolved_) {
      _reporterNs = _unresolved_;
    }, function (_cc) {
      _cclegacy = _cc.cclegacy;
      __checkObsolete__ = _cc.__checkObsolete__;
      __checkObsoleteInNamespace__ = _cc.__checkObsoleteInNamespace__;
      _decorator = _cc._decorator;
      Component = _cc.Component;
      director = _cc.director;
      find = _cc.find;
      Label = _cc.Label;
    }, function (_unresolved_2) {
      Global = _unresolved_2.Global;
    }],
    execute: function () {
      _crd = true;

      _cclegacy._RF.push({}, "382d6cidSZLIoKJvgVrkeGq", "GameOver", undefined);

      __checkObsolete__(['_decorator', 'Component', 'Node', 'director', 'find', 'Label']);

      ({
        ccclass,
        property
      } = _decorator);

      _export("GameOver", GameOver = (_dec = ccclass('GameOver'), _dec(_class = class GameOver extends Component {
        onLoad() {
          find("Canvas/Score").getComponent(Label).string = "Score:" + (_crd && Global === void 0 ? (_reportPossibleCrUseOfGlobal({
            error: Error()
          }), Global) : Global).SCORE.toString();
        }

        onClicked(event, cutom) {
          director.loadScene("menu");
          (_crd && Global === void 0 ? (_reportPossibleCrUseOfGlobal({
            error: Error()
          }), Global) : Global).SCORE = 0;
        }

      }) || _class));

      _cclegacy._RF.pop();

      _crd = false;
    }
  };
});
//# sourceMappingURL=e0b8cdd31ddb7f06da8222456655d09a5571f225.js.map