import { _decorator, instantiate } from 'cc';
import { Emitter } from './Emitter';
import { Bullet } from './Bullet';
import { DefaultMovable } from '../move/DefaultMovable';
import { DefaultMoveModifier } from '../move/DefaultMoveModifier';
import { eSolverTarget, eEasing } from '../move/IMovable';
const { ccclass, property } = _decorator;

@ccclass('EmitterArc')
export class EmitterArc extends Emitter {

    canEmit(): boolean {
        if (!this.emitterData)
            return false;

        if (!this.bulletPrefab || this.emitterData.count <= 0) {
            return false;
        }

        return true;
    }

    /**
     * Calculate the direction for a bullet at the given index
     * @param index The index of the bullet (0 to count-1)
     * @returns Direction vector {x, y}
     */
    getSpawnDirection(index: number): { x: number, y: number } {
        // 计算发射方向
        const angleOffset = this.emitterData.count > 1 ? (this.emitterData.arc / (this.emitterData.count - 1)) * index - this.emitterData.arc / 2 : 0;
        const radian = (this.emitterData.angle + angleOffset) * (Math.PI / 180);
        return {
            x: Math.cos(radian),
            y: Math.sin(radian)
        };
    }

    /**
     * Get the spawn position for a bullet at the given index
     * @param index The index of the bullet (0 to count-1)
     * @returns Position offset from emitter center
     */
    getSpawnPosition(index: number): { x: number, y: number } {
        if (this.emitterData.radius <= 0) {
            return { x: 0, y: 0 };
        }

        const direction = this.getSpawnDirection(index);
        return {
            x: direction.x * this.emitterData.radius,
            y: direction.y * this.emitterData.radius
        };
    }

    createBullet(direction: { x: number, y: number }, position: { x: number, y: number }) {
        if (!this.bulletPrefab) {
            console.warn("EmitterArc: No bullet prefab assigned");
            return null;
        }

        // Instantiate the bullet from prefab
        const bulletNode = instantiate(this.bulletPrefab);
        if (!bulletNode) {
            console.error("EmitterArc: Failed to instantiate bullet prefab");
            return null;
        }

        // Get the bullet component
        // const bullet = bulletNode.getComponent(Bullet);
        // if (!bullet) {
        //     console.error("EmitterArc: Bullet prefab does not have Bullet component");
        //     bulletNode.destroy();
        //     return null;
        // }

        // Set bullet position relative to emitter
        const emitterPos = this.node.getWorldPosition();
        bulletNode.setWorldPosition(
            emitterPos.x + position.x,
            emitterPos.y + position.y,
            emitterPos.z
        );

        // Add or get the DefaultMovable component for movement
        let movable = bulletNode.getComponent(DefaultMovable);
        if (!movable) {
            movable = bulletNode.addComponent(DefaultMovable);
        }

        // Set initial velocity based on direction and speed multiplier
        movable.vx = direction.x * this.emitterData.emitPower;
        movable.vy = direction.y * this.emitterData.emitPower;
        movable.angle = Math.atan2(direction.y, direction.x) * 180 / Math.PI;
        
        movable.addSolver(new DefaultMoveModifier(eSolverTarget.Angle, 0, 180, eEasing.Linear));
        movable.addSolver(new DefaultMoveModifier(eSolverTarget.Vx, 0, 2, eEasing.Linear));
        movable.addSolver(new DefaultMoveModifier(eSolverTarget.Vy, -500, 2, eEasing.Linear));

        this.node.parent?.addChild(bulletNode);

        return bulletNode;
    }
}