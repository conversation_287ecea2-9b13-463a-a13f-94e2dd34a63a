System.register(["__unresolved_0", "cc", "__unresolved_1", "__unresolved_2", "__unresolved_3", "__unresolved_4", "__unresolved_5", "__unresolved_6", "__unresolved_7", "__unresolved_8", "__unresolved_9", "__unresolved_10", "__unresolved_11"], function (_export, _context) {
  "use strict";

  var _reporterNs, _cclegacy, __checkObsolete__, __checkObsoleteInNamespace__, _decorator, UIOpacity, UITransform, v2, Vec2, Vec3, EnemyEntity, GameEnum, ColliderComp, EnemyEffectComp, EnemyAttrComponent, Tools, BattleLayer, GameConst, Bullet, GameIns, MainPlane, _class, _crd, ccclass, property, EnemyBase;

  function _reportPossibleCrUseOfEnemyEntity(extras) {
    _reporterNs.report("EnemyEntity", "./EnemyEntity", _context.meta, extras);
  }

  function _reportPossibleCrUseOfGameEnum(extras) {
    _reporterNs.report("GameEnum", "../../../const/GameEnum", _context.meta, extras);
  }

  function _reportPossibleCrUseOfColliderComp(extras) {
    _reporterNs.report("ColliderComp", "../../base/ColliderComp", _context.meta, extras);
  }

  function _reportPossibleCrUseOfEnemyEffectComp(extras) {
    _reporterNs.report("EnemyEffectComp", "./EnemyEffectComp", _context.meta, extras);
  }

  function _reportPossibleCrUseOfEnemyAttrComponent(extras) {
    _reporterNs.report("EnemyAttrComponent", "./EnemyAttrComponent", _context.meta, extras);
  }

  function _reportPossibleCrUseOfTools(extras) {
    _reporterNs.report("Tools", "../../../utils/Tools", _context.meta, extras);
  }

  function _reportPossibleCrUseOfBattleLayer(extras) {
    _reporterNs.report("BattleLayer", "../../layer/BattleLayer", _context.meta, extras);
  }

  function _reportPossibleCrUseOfGameConst(extras) {
    _reporterNs.report("GameConst", "../../../const/GameConst", _context.meta, extras);
  }

  function _reportPossibleCrUseOfBullet(extras) {
    _reporterNs.report("Bullet", "../../bullet/Bullet", _context.meta, extras);
  }

  function _reportPossibleCrUseOfGameIns(extras) {
    _reporterNs.report("GameIns", "../../../GameIns", _context.meta, extras);
  }

  function _reportPossibleCrUseOfMainPlane(extras) {
    _reporterNs.report("MainPlane", "../mainPlane/MainPlane", _context.meta, extras);
  }

  return {
    setters: [function (_unresolved_) {
      _reporterNs = _unresolved_;
    }, function (_cc) {
      _cclegacy = _cc.cclegacy;
      __checkObsolete__ = _cc.__checkObsolete__;
      __checkObsoleteInNamespace__ = _cc.__checkObsoleteInNamespace__;
      _decorator = _cc._decorator;
      UIOpacity = _cc.UIOpacity;
      UITransform = _cc.UITransform;
      v2 = _cc.v2;
      Vec2 = _cc.Vec2;
      Vec3 = _cc.Vec3;
    }, function (_unresolved_2) {
      EnemyEntity = _unresolved_2.default;
    }, function (_unresolved_3) {
      GameEnum = _unresolved_3.default;
    }, function (_unresolved_4) {
      ColliderComp = _unresolved_4.ColliderComp;
    }, function (_unresolved_5) {
      EnemyEffectComp = _unresolved_5.default;
    }, function (_unresolved_6) {
      EnemyAttrComponent = _unresolved_6.default;
    }, function (_unresolved_7) {
      Tools = _unresolved_7.Tools;
    }, function (_unresolved_8) {
      BattleLayer = _unresolved_8.default;
    }, function (_unresolved_9) {
      GameConst = _unresolved_9.GameConst;
    }, function (_unresolved_10) {
      Bullet = _unresolved_10.default;
    }, function (_unresolved_11) {
      GameIns = _unresolved_11.GameIns;
    }, function (_unresolved_12) {
      MainPlane = _unresolved_12.MainPlane;
    }],
    execute: function () {
      _crd = true;

      _cclegacy._RF.push({}, "925b6gydGVFxpYLitdnHUBN", "EnemyBase", undefined);

      __checkObsolete__(['_decorator', 'UIOpacity', 'UITransform', 'v2', 'Vec2', 'Vec3']);

      ({
        ccclass,
        property
      } = _decorator);

      _export("default", EnemyBase = ccclass(_class = class EnemyBase extends (_crd && EnemyEntity === void 0 ? (_reportPossibleCrUseOfEnemyEntity({
        error: Error()
      }), EnemyEntity) : EnemyEntity) {
        constructor() {
          super(...arguments);
          this.uiData = null;
          this.scaleType = -1;
          this.propertyRate = [];
          this._curHp = 0;
          this.exp = 0;
          this.maxHp = 0;
          this.defence = 0;
          this.resist = {};
          this.hurtBuffMap = new Map();
          this._hurtBuffTime = new Map();
          this._fireDemage = 0;
          this._fireHurtCd = 0;
          this._fireHurtTime = 0;
          this._isFireCirt = false;
          this._buffCountArr = new Map();
          this.collideComp = null;
          this._collideLevel = (_crd && GameEnum === void 0 ? (_reportPossibleCrUseOfGameEnum({
            error: Error()
          }), GameEnum) : GameEnum).EnemyCollideLevel.Main;
          this.bCollideDead = false;
          this.damaged = false;
          this._isTracked = false;
          this._countTime = 0;
          this._bStandBy = false;
          this._standByTime = 0;
          this._standByEnd = false;
          this._lootArr = [];
          this._lootItemArr = [];
          this._curLoot = null;
          this._lootHp = 0;
          this._lootNeedHp = 0;
          this._lootHpUnit = 0;
          this._itemParent = null;
          this._isItem = false;
          this.effectComp = null;
          this.attrCom = null;
          this.dieBullet = false;
          this.bullets = [];
        }

        get itemParent() {
          return this._itemParent;
        }

        set itemParent(value) {
          this._itemParent = value;
        }

        get isItem() {
          return this._isItem;
        }

        set isItem(value) {
          this._isItem = value;
        }

        get collideLevel() {
          return this._collideLevel;
        }

        set collideLevel(value) {
          this._collideLevel = value;
        }
        /**
         * 获取碰撞是否可用
         * @returns {boolean} 是否可用
         */


        get collideAble() {
          return this.collideComp && this.collideComp.enabled;
        }
        /**
         * 设置碰撞是否可用
         * @param {boolean} value 是否可用
         */


        set collideAble(value) {
          if (this.collideComp) {
            this.collideComp.enabled = value;
          }
        }
        /**
        * 预加载敌人组件
        */


        preLoad() {
          // 添加碰撞组件并初始化
          this.collideComp = this.addComp(_crd && ColliderComp === void 0 ? (_reportPossibleCrUseOfColliderComp({
            error: Error()
          }), ColliderComp) : ColliderComp, new (_crd && ColliderComp === void 0 ? (_reportPossibleCrUseOfColliderComp({
            error: Error()
          }), ColliderComp) : ColliderComp)());
          this.collideComp.init(this); // 获取敌人效果组件

          this.effectComp = this.node.getComponent(_crd && EnemyEffectComp === void 0 ? (_reportPossibleCrUseOfEnemyEffectComp({
            error: Error()
          }), EnemyEffectComp) : EnemyEffectComp); // 添加属性组件

          this.attrCom = (_crd && Tools === void 0 ? (_reportPossibleCrUseOfTools({
            error: Error()
          }), Tools) : Tools).addScript(this.node, _crd && EnemyAttrComponent === void 0 ? (_reportPossibleCrUseOfEnemyAttrComponent({
            error: Error()
          }), EnemyAttrComponent) : EnemyAttrComponent);
        }
        /**
         * 重置敌人状态
         */


        reset() {
          super.reset();
          this.uiData = null;
          this.scaleType = -1;
          this._curHp = 0;
          this.maxHp = 0;
          this.exp = 0;
          this.collideAtk = 0;
          this.hurtBuffMap.clear();

          this._hurtBuffTime.clear();

          this.resist = {};
          this._isTracked = false;
          this._countTime = 0;
          this._bStandBy = false;
          this._standByTime = 0;
          this._collideLevel = (_crd && GameEnum === void 0 ? (_reportPossibleCrUseOfGameEnum({
            error: Error()
          }), GameEnum) : GameEnum).EnemyCollideLevel.Main;
          this.damaged = false;
          this._lootArr = [];
          this._lootItemArr = [];
          this._curLoot = null;
          this._lootHp = 0;
          this._lootNeedHp = 0;
          this._lootHpUnit = 0;
          this._itemParent = this;
          this._isItem = false;
          this.removeAllBuffEffect();
          this.dieBullet = false;
          this.bullets = [];
        }
        /**
         * 设置 UI 数据
         * @param {Object} data UI 数据
         */


        setUIData(data) {
          this.uiData = data;

          if (this.uiData) {
            this.setCollideData(this.uiData.collider);
            this.initComps();
          }
        }
        /**
         * 设置缩放类型
         * @param {number} type 缩放类型
         */


        setScaleType(type) {
          this.scaleType = type;
        }
        /**
         * 获取缩放类型
         * @returns {number} 缩放类型
         */


        getScaleType() {
          return this.scaleType;
        }
        /**
         * 初始化属性
         * @param {string} attr 属性字符串
         */


        initAttr(attr) {
          this.attrCom.init(this, attr || "");
        }
        /**
         * 检查是否具有指定属性
         * @param {string} attr 属性名称
         * @returns {boolean} 是否具有该属性
         */


        hasAttribution(attr) {
          return this.attrCom && this.attrCom.hasAttribution(attr);
        }
        /**
         * 设置经验值
         * @param {number} exp 经验值
         */


        setExp(exp) {
          this.exp = exp;
        }
        /**
         * 初始化组件
         */


        initComps() {
          this.m_comps.forEach(comp => {
            comp.init(this);
          });
        }
        /**
         * 开始战斗
         */


        startBattle() {
          this.collideAble = true;
        }
        /**
         * 添加掉落物
         * @param {Object} loot 掉落物
         */


        addLoot(loot) {
          this._lootArr.push(loot);
        }
        /**
         * 更新游戏逻辑
         * @param {number} deltaTime 帧间隔时间
         */


        updateGameLogic(deltaTime) {
          if (this.isDead || this.checkStandby(deltaTime)) {
            return;
          } // 处理火焰伤害 Buff


          if (this.hasHurtBuff((_crd && GameEnum === void 0 ? (_reportPossibleCrUseOfGameEnum({
            error: Error()
          }), GameEnum) : GameEnum).EnemyBuff.Fire)) {
            this._fireHurtTime += deltaTime;

            if (this._fireHurtTime >= this._fireHurtCd) {
              this._fireHurtTime = 0;
              (_crd && GameIns === void 0 ? (_reportPossibleCrUseOfGameIns({
                error: Error()
              }), GameIns) : GameIns).hurtEffectManager.createFollowHurtNum(this.collideComp, this._isFireCirt, this._fireDemage, {
                x: 0,
                y: 0
              });
              this.hurt(this._fireDemage);
            }
          } // 更新技能抗性


          this.updateSkillResist(deltaTime); // 更新属性组件

          if (this.attrCom) {
            this.attrCom.updateGameLogic(deltaTime);
          }

          this._isTracked = false; // 更新所有组件

          this.m_comps.forEach(comp => {
            comp.update(deltaTime);
          });
        }
        /**
         * 检查待机状态
         * @param {number} deltaTime 帧间隔时间
         * @returns {boolean} 是否处于待机状态
         */


        checkStandby(deltaTime) {
          this._countTime += deltaTime;

          if (this._bStandBy) {
            if (this._countTime > this._standByTime) {
              this.active = true;
              this._bStandBy = false;
              this._countTime = 0;
              this._standByEnd = true;
              this.node.getComponent(UIOpacity).opacity = 255;
              this.startBattle();
            }

            return true;
          }

          return false;
        }
        /**
         * 更新技能抗性
         * @param {number} deltaTime 帧间隔时间
         */


        updateSkillResist(deltaTime) {
          this.hurtBuffMap.forEach((value, key) => {
            var time = this._hurtBuffTime.get(key);

            if (time !== null && time > 0) {
              this._hurtBuffTime.set(key, time - deltaTime);
            } else {
              this.removeBuff(key);
            }
          });
        }
        /**
         * 初始化属性倍率
         * @param {Array<number>} rates 属性倍率数组
         */


        initPropertyRate(rates) {
          this.propertyRate = rates;

          if (this.propertyRate.length > 2) {
            this.curHp *= this.propertyRate[0];
            this.maxHp = this.curHp;
            this.attack *= this.propertyRate[1];
            this.collideAtk *= this.propertyRate[2];
          }
        }
        /**
         * 获取 UI 数据
         * @returns {Object} UI 数据
         */


        getUIData() {
          return this.uiData;
        }
        /**
        * 设置待机时间
        * @param {number} time 待机时间
        */


        setStandByTime(time) {
          this._bStandBy = true;
          this._standByTime = time;
          this.node.getComponent(UIOpacity).opacity = 0;
        }
        /**
         * 检查是否处于待机状态
         * @returns {boolean} 是否待机
         */


        isStandBy() {
          return this._bStandBy;
        }
        /**
         * 设置敌人位置
         * @param {number} x X 坐标
         * @param {number} y Y 坐标
         * @param {boolean} isTracked 是否被追踪
         */


        setPos(x, y, isTracked) {
          if (isTracked === void 0) {
            isTracked = false;
          }

          this.node.setPosition(x, y);
          this._isTracked = isTracked;
        }
        /**
         * 获取是否被追踪
         * @returns {boolean} 是否被追踪
         */


        get isTracked() {
          return this._isTracked;
        }
        /**
         * 获取方向
         * @returns {Vec2} 方向向量
         */


        getDir() {
          return Vec2.ZERO;
        }
        /**
         * 获取角度
         * @returns {number} 角度
         */


        getAngle() {
          return 0;
        }
        /**
         * 检查是否满血
         * @returns {boolean} 是否满血
         */


        isFullBlood() {
          return this.curHp >= this.maxHp;
        }
        /**
         * 获取最大血量
         * @returns {number} 最大血量
         */


        getMaxHp() {
          return this.maxHp;
        }
        /**
         * 获取当前血量
         * @returns {number} 当前血量
         */


        get curHp() {
          return this._curHp;
        }
        /**
         * 设置当前血量
         * @param {number} hp 当前血量
         */


        set curHp(hp) {
          this._curHp = hp;
        }
        /**
         * 获取血量百分比
         * @returns {number} 血量百分比
         */


        getHpPercent() {
          return this.curHp / this.maxHp;
        }
        /**
         * 改变血量
         * @param {number} delta 血量变化值
         */


        changeHp(delta) {
          this.curHp += delta;

          if (this.curHp < 0) {
            this.curHp = 0;
          } else if (this.curHp > this.maxHp) {
            this.curHp = this.maxHp;
          }

          this.checkHp();
        }
        /**
         * 检查血量是否为 0
         * @returns {boolean} 是否死亡
         */


        checkHp() {
          if (this.curHp <= 0) {
            this.die((_crd && GameEnum === void 0 ? (_reportPossibleCrUseOfGameEnum({
              error: Error()
            }), GameEnum) : GameEnum).EnemyDestroyType.Die);
            return true;
          }

          return false;
        }
        /**
         * 设置当前血量
         * @param {number} hp 当前血量
         */


        setCurHp(hp) {
          this.curHp = hp;
        }
        /**
         * 获取当前血量
         * @returns {number} 当前血量
         */


        getCurHp() {
          return this.curHp;
        }
        /**
         * 检查敌人是否可以被伤害
         * @returns {boolean} 是否可以被伤害
         */


        isDamageable() {
          if (this.sceneLayer < 0 && !this._bStandBy) {
            if (this.damaged) {
              return true;
            }

            var position = this.node.position;

            if (this.itemParent !== this) {
              position = this.node.getComponent(UITransform).convertToWorldSpaceAR(Vec3.ZERO);
              position = (_crd && BattleLayer === void 0 ? (_reportPossibleCrUseOfBattleLayer({
                error: Error()
              }), BattleLayer) : BattleLayer).me.enemyPlaneLayer.getComponent(UITransform).convertToNodeSpaceAR(position);
            }

            return position.y < 0 && position.x > -(_crd && GameConst === void 0 ? (_reportPossibleCrUseOfGameConst({
              error: Error()
            }), GameConst) : GameConst).ViewCenter.x && position.x < (_crd && GameConst === void 0 ? (_reportPossibleCrUseOfGameConst({
              error: Error()
            }), GameConst) : GameConst).ViewCenter.x;
          }

          return !this._bStandBy;
        }
        /**
         * 设置敌人是否已被伤害
         * @param {boolean} value 是否已被伤害
         */


        setDamaged(value) {
          this.damaged = value;
        }
        /**
         * 获取技能抗性
         * @param {number} skillType 技能类型
         * @returns {number} 技能抗性
         */


        getSkillResist(skillType) {
          return this.resist[skillType] || 1;
        }
        /**
         * 添加 Buff
         * @param {number} buffType Buff 类型
         * @param {Array<number>} params Buff 参数
         * @param {boolean} isCritical 是否暴击
         */


        addBuff(buffType, params, isCritical) {
          this.hurtBuffMap.set(buffType, true);

          switch (buffType) {
            case (_crd && GameEnum === void 0 ? (_reportPossibleCrUseOfGameEnum({
              error: Error()
            }), GameEnum) : GameEnum).EnemyBuff.Ice:
              this._hurtBuffTime.set(buffType, params[0]);

              break;

            case (_crd && GameEnum === void 0 ? (_reportPossibleCrUseOfGameEnum({
              error: Error()
            }), GameEnum) : GameEnum).EnemyBuff.Fire:
              this._isFireCirt = isCritical;

              this._hurtBuffTime.set(buffType, params[0]);

              this._fireDemage = params[1] === 0 ? this._fireDemage : params[1];
              this._fireHurtCd = params[2];
              break;

            case (_crd && GameEnum === void 0 ? (_reportPossibleCrUseOfGameEnum({
              error: Error()
            }), GameEnum) : GameEnum).EnemyBuff.Treat:
              var count = this._buffCountArr.get(buffType) || 0;

              this._buffCountArr.set(buffType, count + 1);

              break;
          }

          this.onAddBuff(buffType);
        }
        /**
         * 移除 Buff
         * @param {number} buffType Buff 类型
         */


        removeBuff(buffType) {
          this.hurtBuffMap.delete(buffType);

          this._hurtBuffTime.delete(buffType);

          var shouldRemove = true;

          if (buffType === (_crd && GameEnum === void 0 ? (_reportPossibleCrUseOfGameEnum({
            error: Error()
          }), GameEnum) : GameEnum).EnemyBuff.Treat) {
            var count = this._buffCountArr.get(buffType);

            if (count > 0) {
              this._buffCountArr.set(buffType, count - 1);

              if (count - 1 > 0) {
                shouldRemove = false;
              }
            }
          }

          if (shouldRemove) {
            this.onRemoveBuff(buffType);
          }
        }
        /**
         * 移除所有 Buff 效果
         */


        removeAllBuffEffect() {
          this._buffCountArr.clear();

          if (this.effectComp) {
            this.effectComp.removeAllBuffEffect();
          }
        }
        /**
         * 当 Buff 被移除时的回调
         * @param {number} buffType Buff 类型
         */


        onRemoveBuff(buffType) {
          if (this.effectComp) {
            this.effectComp.removeBuff(buffType);
          }
        }
        /**
         * 当 Buff 被添加时的回调
         * @param {number} buffType Buff 类型
         */


        onAddBuff(buffType) {
          if (this.effectComp && this.uiData) {
            var buffData = this.uiData.skillResistUIDict[buffType];

            switch (buffType) {
              case (_crd && GameEnum === void 0 ? (_reportPossibleCrUseOfGameEnum({
                error: Error()
              }), GameEnum) : GameEnum).EnemyBuff.Ice:
                if (buffData && buffData.length > 0) {
                  this.effectComp.addBuff(buffType, buffData);
                }

                break;

              case (_crd && GameEnum === void 0 ? (_reportPossibleCrUseOfGameEnum({
                error: Error()
              }), GameEnum) : GameEnum).EnemyBuff.Fire:
                var fireDamage = 0.4 * this.uiData.hpParam[3];

                if (!buffData && fireDamage) {
                  buffData[0][0] = fireDamage;
                }

                this.effectComp.addBuff(buffType, buffData);
                break;

              case (_crd && GameEnum === void 0 ? (_reportPossibleCrUseOfGameEnum({
                error: Error()
              }), GameEnum) : GameEnum).EnemyBuff.Treat:
                this.effectComp.addBuff(buffType, buffData);
                break;
            }
          }
        }
        /**
         * 检查是否具有指定的伤害 Buff
         * @param {number} buffType Buff 类型
         * @returns {boolean} 是否具有该 Buff
         */


        hasHurtBuff(buffType) {
          return this.hurtBuffMap.get(buffType);
        }
        /**
         * 设置碰撞数据
         * @param {Array<number>} collider 碰撞数据
         * @param {number} scale 缩放比例
         * @param {Vec2} offset 偏移量
         */


        setCollideData(collider, entity, offset) {
          if (entity === void 0) {
            entity = null;
          }

          if (offset === void 0) {
            offset = v2(0, 0);
          }

          if (this.collideComp) {
            this.collideComp.setData(collider, entity, offset);
          }
        }
        /**
         * 设置碰撞实体
         * @param {Entity} entity 碰撞实体
         */


        setCollideEntity(entity) {
          if (this.collideComp) {
            this.collideComp.mainEntity = entity;
          }
        }
        /**
         * 设置碰撞偏移
         * @param {Vec2} offset 偏移量
         */


        setCollideOffset(offset) {
          if (this.collideComp) {
            this.collideComp.entityOffset = offset;
          }
        }
        /**
         * 设置碰撞缩放
         * @param {number} widthScale 宽度缩放
         * @param {number} heightScale 高度缩放
         */


        setCollideScale(widthScale, heightScale) {
          if (this.collideComp && this.uiData) {
            this.collideComp.setSize(this.uiData.collider[3] * widthScale, this.uiData.collider[4] * heightScale);
          }
        }
        /**
         * 碰撞处理
         * @param {ColliderComp} collider 碰撞组件
         */


        onCollide(collider) {
          if (this.active && !this.isDead) {
            if (collider.entity instanceof (_crd && Bullet === void 0 ? (_reportPossibleCrUseOfBullet({
              error: Error()
            }), Bullet) : Bullet)) {
              var attack = collider.entity.getAttack(this);
              (_crd && GameIns === void 0 ? (_reportPossibleCrUseOfGameIns({
                error: Error()
              }), GameIns) : GameIns).hurtEffectManager.createHurtNumByType(this.collideComp, collider.entity, attack, {
                x: 0,
                y: 0
              });
              var finalDamage = Math.max(attack / 10, attack - this.defence) * (_crd && GameIns === void 0 ? (_reportPossibleCrUseOfGameIns({
                error: Error()
              }), GameIns) : GameIns).warAttackManager.playerWARatio;

              if (this.hurt(finalDamage)) {// switch (collider.entity.getType()) {
                //     case GameEnum.BulletType.Ice:
                //         const iceResist = this.getSkillResist(GameEnum.EnemyBuff.Ice);
                //         const iceParams = collider.entity.bulletState.extra;
                //         if (iceParams && iceParams.length > 0) {
                //             this.addBuff(GameEnum.EnemyBuff.Ice, [iceParams[0] * iceResist]);
                //         }
                //         break;
                //     case GameEnum.BulletType.Fire:
                //         const fireResist = this.getSkillResist(GameEnum.EnemyBuff.Fire);
                //         const fireParams = collider.entity.bulletState.extra;
                //         if (fireParams && fireParams.length > 0) {
                //             this.addBuff(GameEnum.EnemyBuff.Fire, [fireParams[0] * fireResist, fireParams[1] / 100 * finalDamage, fireParams[2]], collider.entity.isCirt);
                //         }
                //         break;
                // }
              }
            } else if (collider.entity instanceof (_crd && MainPlane === void 0 ? (_reportPossibleCrUseOfMainPlane({
              error: Error()
            }), MainPlane) : MainPlane) && this.collideLevel === (_crd && GameEnum === void 0 ? (_reportPossibleCrUseOfGameEnum({
              error: Error()
            }), GameEnum) : GameEnum).EnemyCollideLevel.Main && this.bCollideDead) {
              this.die((_crd && GameEnum === void 0 ? (_reportPossibleCrUseOfGameEnum({
                error: Error()
              }), GameEnum) : GameEnum).EnemyDestroyType.Die);
            }
          }
        }
        /**
         * 处理敌人受到的伤害
         * @param {number} damage 伤害值
         * @returns {boolean} 是否成功处理伤害
         */


        hurt(damage) {
          if (!this.active || this.isDead || !this.isDamageable()) {
            return false;
          }

          this.changeHp(-damage);

          this._checkHurtLoot(damage);

          this.onHurt();
          return true;
        }
        /**
         * 检查是否需要生成掉落物
         * @param {number} damage 伤害值
         */


        _checkHurtLoot(damage) {
          if (this._curLoot) {
            this.checkLoot(damage);
          }
        }
        /**
         * 检查并生成掉落物
         * @param {number} damage 伤害值
         * @param {Vec2} position 掉落物生成位置
         */


        checkLoot(damage, position) {// if (this.isDead && this.scaleType !== GameEnum.EnemyScale.None) {
          //     const lootType = GameIns.lootManager.checkLoot(this.scaleType);
          //     GameIns.lootManager.addProp(
          //         this.node.convertToWorldSpaceAR(position),
          //         this.uiData.lootParam1,
          //         lootType
          //     );
          // }

          if (damage === void 0) {
            damage = 0;
          }

          if (position === void 0) {
            position = Vec2.ZERO;
          }
        }
        /**
         * 当敌人受到伤害时的回调
         */


        onHurt() {}
        /**
         * 处理敌人死亡逻辑
         * @param {number} destroyType 敌人销毁类型
         */


        die(destroyType) {
          if (this.isDead) {
            return;
          }

          this.isDead = true;
          this.collideAble = false;

          if (this.collideComp && this.collideComp.entity) {
            (_crd && GameIns === void 0 ? (_reportPossibleCrUseOfGameIns({
              error: Error()
            }), GameIns) : GameIns).hurtEffectManager.m_colliderPreTime.delete(this.collideComp.entity.new_uuid);
          }

          if (this.attrCom) {
            this.attrCom.die();
          }

          this.onDie(destroyType);
        }
        /**
         * 敌人死亡时的回调
         * @param {number} destroyType 敌人销毁类型
         */


        onDie(destroyType) {
          if (destroyType === (_crd && GameEnum === void 0 ? (_reportPossibleCrUseOfGameEnum({
            error: Error()
          }), GameEnum) : GameEnum).EnemyDestroyType.Die) {
            // GameIns.lootManager.addExp(this.exp);
            (_crd && GameIns === void 0 ? (_reportPossibleCrUseOfGameIns({
              error: Error()
            }), GameIns) : GameIns).mainPlaneManager.checkKillHp();
            (_crd && GameIns === void 0 ? (_reportPossibleCrUseOfGameIns({
              error: Error()
            }), GameIns) : GameIns).mainPlaneManager.checkKillAtk();

            if (this.dieBullet) {
              for (var bullet of this.bullets) {
                bullet.dieRemove();
              }
            }

            if (!this.isItem) {// TaskManager.TaskMgr.taskNumberChange(TaskManager.TaskType.KillPlane, 1);
              // TaskManager.TaskMgr.achievementNumberChange(TaskManager.AchievementType.KillEnemy, 1);
              // GameData.GData.killEnemyNumber += 1;
            }
          } else {
            (_crd && GameIns === void 0 ? (_reportPossibleCrUseOfGameIns({
              error: Error()
            }), GameIns) : GameIns).enemyManager.subAnnihilate = false;
            (_crd && GameIns === void 0 ? (_reportPossibleCrUseOfGameIns({
              error: Error()
            }), GameIns) : GameIns).enemyManager.mainAnnihilate = false;
          }

          this.bullets = [];

          switch (this.type) {
            case (_crd && GameEnum === void 0 ? (_reportPossibleCrUseOfGameEnum({
              error: Error()
            }), GameEnum) : GameEnum).EnemyType.Ligature:
            case (_crd && GameEnum === void 0 ? (_reportPossibleCrUseOfGameEnum({
              error: Error()
            }), GameEnum) : GameEnum).EnemyType.LigatureLine:
              break;

            default:
              var position = this.node.position;

              if (this.sceneLayer < 0) {
                if (this.type === (_crd && GameEnum === void 0 ? (_reportPossibleCrUseOfGameEnum({
                  error: Error()
                }), GameEnum) : GameEnum).EnemyType.Turret || this.type === (_crd && GameEnum === void 0 ? (_reportPossibleCrUseOfGameEnum({
                  error: Error()
                }), GameEnum) : GameEnum).EnemyType.GoldBox || this.type === (_crd && GameEnum === void 0 ? (_reportPossibleCrUseOfGameEnum({
                  error: Error()
                }), GameEnum) : GameEnum).EnemyType.LigatureUnit) {
                  position = this.node.getComponent(UITransform).convertToWorldSpaceAR(Vec3.ZERO);
                  position = (_crd && BattleLayer === void 0 ? (_reportPossibleCrUseOfBattleLayer({
                    error: Error()
                  }), BattleLayer) : BattleLayer).me.enemyPlaneLayer.getComponent(UITransform).convertToNodeSpaceAR(position);
                }
              } else {
                var worldPosition = this.node.getComponent(UITransform).convertToWorldSpaceAR(Vec3.ZERO);
                position = (_crd && BattleLayer === void 0 ? (_reportPossibleCrUseOfBattleLayer({
                  error: Error()
                }), BattleLayer) : BattleLayer).me.enemyPlaneLayer.getComponent(UITransform).convertToNodeSpaceAR(worldPosition);
              }

              (_crd && GameIns === void 0 ? (_reportPossibleCrUseOfGameIns({
                error: Error()
              }), GameIns) : GameIns).enemyManager.checkEnemyDieBomb(position);
          }
        }
        /**
         * 准备移除敌人时的回调
         */


        willRemove() {}
        /**
         * 显示属性护盾
         */


        showAttrShield() {
          if (this.attrCom) {
            this.attrCom.showAttrShield();
          }
        }
        /**
         * 播放敌人死亡动画
         */


        playDieAnim() {
          if (!this.uiData) {
            return;
          }

          if (this.uiData.blastSound > 0) {// GameIns.audioManager.playEffect(`blast${this.uiData.blastSound}`);
          }

          var blastParams = [...this.uiData.blastParam];

          for (var i = 0; i < this.uiData.blastCount; i++) {
            var index = (_crd && Tools === void 0 ? (_reportPossibleCrUseOfTools({
              error: Error()
            }), Tools) : Tools).random_int(0, blastParams.length - 1);
            var param = blastParams.splice(index, 1)[0];
            var effectData = {
              x: Number(param[1]) * this.node.scale.x,
              y: param[2] * this.node.scale.y,
              scale: (_crd && GameIns === void 0 ? (_reportPossibleCrUseOfGameIns({
                error: Error()
              }), GameIns) : GameIns).battleManager.getRatio() * (_crd && Tools === void 0 ? (_reportPossibleCrUseOfTools({
                error: Error()
              }), Tools) : Tools).random_int(param[3], param[4]) / 100,
              angle: param[5] < 0 ? (_crd && Tools === void 0 ? (_reportPossibleCrUseOfTools({
                error: Error()
              }), Tools) : Tools).random_int(0, 359) : param[5]
            };
            var callback = i === this.uiData.blastCount - 1 ? () => this.onDieAnimEnd() : null;
            this.scheduleOnce(() => {// EnemyEffectLayer.me.addBlastEffect(this, param[0], effectData, callback);
            }, this.uiData.blastDurations[i]);
          }

          this.scheduleOnce(() => {
            // MainCamera.me.shake1(this.uiData.blastShake);
            this.removeAllBuffEffect();
          }, 0.1);
        }
        /**
         * 敌人死亡动画结束时的回调
         */


        onDieAnimEnd() {}
        /**
         * 添加子弹到敌人
         * @param {Bullet} bullet 子弹对象
         */


        addBullet(bullet) {
          if (this.dieBullet && this.bullets) {
            this.bullets.push(bullet);
          }
        }
        /**
         * 从敌人移除子弹
         * @param {Bullet} bullet 子弹对象
         */


        removeBullet(bullet) {
          if (this.dieBullet && this.bullets) {
            var index = this.bullets.indexOf(bullet);

            if (index >= 0) {
              this.bullets.splice(index, 1);
            }
          }
        }

      }) || _class);

      _cclegacy._RF.pop();

      _crd = false;
    }
  };
});
//# sourceMappingURL=7fc8594643f7577d11008b83e4dac78a34c5c0f0.js.map