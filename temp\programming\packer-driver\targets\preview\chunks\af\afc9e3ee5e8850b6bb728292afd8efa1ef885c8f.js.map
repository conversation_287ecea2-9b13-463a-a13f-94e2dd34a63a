{"version": 3, "sources": ["file:///E:/M2Game/Client/assets/scripts/ui/components/common/button/DragButton.ts"], "names": ["_decorator", "Component", "Input", "UITransform", "v2", "v3", "Vec3", "view", "ccclass", "property", "Drag<PERSON><PERSON><PERSON>", "tooltip", "isTouching", "startPos", "startTouchPos", "longPressTimer", "onLoad", "node", "on", "EventType", "TOUCH_START", "onTouchStart", "TOUCH_MOVE", "onTouchMove", "TOUCH_END", "onTouchEnd", "TOUCH_CANCEL", "onTouchCancel", "addClick", "callback", "target", "off", "isDragging", "Date", "getTime", "longPressDuration", "event", "touch", "getUILocation", "copy", "position", "newPos", "localPos", "parent", "inverseTransformPoint", "toVec3", "trans", "getComponent", "width", "getVisibleSize", "height", "validX", "Math", "max", "min", "x", "validY", "y", "z", "emit", "resetState"], "mappings": ";;;;;;;;;;;;;;;;AAASA,MAAAA,U,OAAAA,U;AAAYC,MAAAA,S,OAAAA,S;AAAuBC,MAAAA,K,OAAAA,K;AAAOC,MAAAA,W,OAAAA,W;AAAaC,MAAAA,E,OAAAA,E;AAAIC,MAAAA,E,OAAAA,E;AAAUC,MAAAA,I,OAAAA,I;AAAMC,MAAAA,I,OAAAA,I;;;;;;;;;OAC9E;AAAEC,QAAAA,OAAF;AAAWC,QAAAA;AAAX,O,GAAwBT,U;;4BAGjBU,U,WADZF,OAAO,CAAC,YAAD,C,UAEHC,QAAQ,CAAC;AACNE,QAAAA,OAAO,EAAE;AADH,OAAD,C,2BAFb,MACaD,UADb,SACgCT,SADhC,CAC0C;AAAA;AAAA;;AAAA;;AAAA,eAM9BW,UAN8B,GAMR,KANQ;AAAA,eAO9BC,QAP8B,GAObR,EAAE,EAPW;AAAA,eAQ9BS,aAR8B,GAQRV,EAAE,EARM;AAAA,eAS9BW,cAT8B,GASL,CATK;AAAA;;AAWtCC,QAAAA,MAAM,GAAG;AACL,eAAKC,IAAL,CAAUC,EAAV,CAAahB,KAAK,CAACiB,SAAN,CAAgBC,WAA7B,EAA0C,KAAKC,YAA/C,EAA6D,IAA7D;AACA,eAAKJ,IAAL,CAAUC,EAAV,CAAahB,KAAK,CAACiB,SAAN,CAAgBG,UAA7B,EAAyC,KAAKC,WAA9C,EAA2D,IAA3D;AACA,eAAKN,IAAL,CAAUC,EAAV,CAAahB,KAAK,CAACiB,SAAN,CAAgBK,SAA7B,EAAwC,KAAKC,UAA7C,EAAyD,IAAzD;AACA,eAAKR,IAAL,CAAUC,EAAV,CAAahB,KAAK,CAACiB,SAAN,CAAgBO,YAA7B,EAA2C,KAAKC,aAAhD,EAA+D,IAA/D;AACH;;AAEDC,QAAAA,QAAQ,CAACC,QAAD,EAAqBC,MAArB,EAAqC;AACzC,eAAKb,IAAL,CAAUc,GAAV,CAAc,OAAd;AACA,eAAKd,IAAL,CAAUC,EAAV,CAAa,OAAb,EAAsBW,QAAtB,EAAgCC,MAAhC;AACH;;AAEa,YAAVE,UAAU,GAAG;AACb,iBAAO,IAAIC,IAAJ,GAAWC,OAAX,KAAuB,KAAKnB,cAA5B,IAA8C,KAAKoB,iBAAL,GAAyB,IAA9E;AACH;;AAEOd,QAAAA,YAAY,CAACe,KAAD,EAAoB;AAAA;;AACpC,eAAKxB,UAAL,GAAkB,IAAlB;AACA,eAAKG,cAAL,GAAsB,IAAIkB,IAAJ,GAAWC,OAAX,EAAtB;AACA,0BAAAE,KAAK,CAACC,KAAN,0BAAaC,aAAb,CAA2B,KAAKxB,aAAhC;AACAR,UAAAA,IAAI,CAACiC,IAAL,CAAU,KAAK1B,QAAf,EAAyB,KAAKI,IAAL,CAAUuB,QAAnC;AACH;;AAEOjB,QAAAA,WAAW,CAACa,KAAD,EAAoB;AAAA;;AACnC,cAAI,CAAC,KAAKxB,UAAN,IAAoB,CAAC,KAAKoB,UAA9B,EAA0C;AAC1C,cAAMS,MAAM,GAAGrC,EAAE,EAAjB;AACA,2BAAAgC,KAAK,CAACC,KAAN,2BAAaC,aAAb,CAA2BG,MAA3B;AACA,cAAMC,QAAQ,wBAAG,KAAKzB,IAAL,CAAU0B,MAAb,qBAAG,kBAAkBC,qBAAlB,CAAwCH,MAAM,CAACI,MAAP,EAAxC,EAAyDJ,MAAM,CAACI,MAAP,EAAzD,CAAjB;AACA,eAAK5B,IAAL,CAAUuB,QAAV,GAAqBE,QAArB,CALmC,CAOnC;;AACA,cAAMI,KAAK,GAAG,KAAK7B,IAAL,CAAU8B,YAAV,CAAuB5C,WAAvB,CAAd;AACA,cAAM6C,KAAK,GAAGzC,IAAI,CAAC0C,cAAL,GAAsBD,KAAtB,GAA8B,CAA5C;AACA,cAAME,MAAM,GAAG3C,IAAI,CAAC0C,cAAL,GAAsBC,MAAtB,GAA+B,CAA9C;AACA,cAAMC,MAAM,GAAGC,IAAI,CAACC,GAAL,CAAS,CAACL,KAAD,GAASF,KAAK,CAACE,KAAN,GAAc,CAAhC,EAAmCI,IAAI,CAACE,GAAL,CAASN,KAAK,GAAGF,KAAK,CAACE,KAAN,GAAc,CAA/B,EAAkCN,QAAQ,CAACa,CAA3C,CAAnC,CAAf;AACA,cAAMC,MAAM,GAAGJ,IAAI,CAACC,GAAL,CAAS,CAACH,MAAD,GAAUJ,KAAK,CAACI,MAAN,GAAe,CAAlC,EAAqCE,IAAI,CAACE,GAAL,CAASJ,MAAM,GAAGJ,KAAK,CAACI,MAAN,GAAe,CAAjC,EAAoCR,QAAQ,CAACe,CAA7C,CAArC,CAAf;AACA,eAAKxC,IAAL,CAAUuB,QAAV,GAAqBnC,EAAE,CAAC8C,MAAD,EAASK,MAAT,EAAiBd,QAAQ,CAACgB,CAA1B,CAAvB;AACH;;AAEOjC,QAAAA,UAAU,CAACW,KAAD,EAAoB;AAClC,cAAI,CAAC,KAAKJ,UAAV,EAAsB;AAClB,iBAAKT,WAAL,CAAiBa,KAAjB;AACA,iBAAKnB,IAAL,CAAU0C,IAAV,CAAe,OAAf,EAAwBvB,KAAxB;AACH;;AACD,eAAKwB,UAAL;AACH;;AAEOjC,QAAAA,aAAa,GAAG;AACpB,eAAKiC,UAAL;AACH;;AAEOA,QAAAA,UAAU,GAAG;AACjB,eAAKhD,UAAL,GAAkB,KAAlB;AACA,eAAKG,cAAL,GAAsB,CAAtB;AACH;;AAjEqC,O;;;;;iBAIV,G", "sourcesContent": ["import { _decorator, Component, EventTouch, Input, UITransform, v2, v3, Vec2, Vec3, view } from 'cc';\nconst { ccclass, property } = _decorator;\n\n@ccclass('DragButton')\nexport class DragButton extends Component {\n    @property({\n        tooltip: '长按触发时间（秒）'\n    })\n    longPressDuration: number = 0.5;\n\n    private isTouching: boolean = false;\n    private startPos: Vec3 = v3();\n    private startTouchPos: Vec2 = v2();\n    private longPressTimer: number = 0;\n\n    onLoad() {\n        this.node.on(Input.EventType.TOUCH_START, this.onTouchStart, this);\n        this.node.on(Input.EventType.TOUCH_MOVE, this.onTouchMove, this);\n        this.node.on(Input.EventType.TOUCH_END, this.onTouchEnd, this);\n        this.node.on(Input.EventType.TOUCH_CANCEL, this.onTouchCancel, this);\n    }\n\n    addClick(callback: Function, target: Object) {\n        this.node.off('click');\n        this.node.on('click', callback, target);\n    }\n\n    get isDragging() {\n        return new Date().getTime() - this.longPressTimer >= this.longPressDuration * 1000\n    }\n\n    private onTouchStart(event: EventTouch) {\n        this.isTouching = true;\n        this.longPressTimer = new Date().getTime()\n        event.touch?.getUILocation(this.startTouchPos);\n        Vec3.copy(this.startPos, this.node.position);\n    }\n\n    private onTouchMove(event: EventTouch) {\n        if (!this.isTouching || !this.isDragging) return;\n        const newPos = v2();\n        event.touch?.getUILocation(newPos);\n        const localPos = this.node.parent?.inverseTransformPoint(newPos.toVec3(), newPos.toVec3());\n        this.node.position = localPos;\n\n        // 在updatePosition中添加限制\n        const trans = this.node.getComponent(UITransform)\n        const width = view.getVisibleSize().width / 2;\n        const height = view.getVisibleSize().height / 2;\n        const validX = Math.max(-width + trans.width / 2, Math.min(width - trans.width / 2, localPos.x));\n        const validY = Math.max(-height + trans.height / 2, Math.min(height - trans.height / 2, localPos.y));\n        this.node.position = v3(validX, validY, localPos.z);\n    }\n\n    private onTouchEnd(event: EventTouch) {\n        if (!this.isDragging) {\n            this.onTouchMove(event)\n            this.node.emit('click', event);\n        }\n        this.resetState();\n    }\n\n    private onTouchCancel() {\n        this.resetState();\n    }\n\n    private resetState() {\n        this.isTouching = false;\n        this.longPressTimer = 0;\n    }\n}"]}