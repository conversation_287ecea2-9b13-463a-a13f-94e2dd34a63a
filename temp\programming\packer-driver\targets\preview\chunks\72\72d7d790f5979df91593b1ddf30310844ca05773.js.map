{"version": 3, "sources": ["file:///E:/M2Game/Client/assets/scripts/Game/ui/plane/enemy/EnemyEffectLayer.ts"], "names": ["_decorator", "Component", "Node", "Prefab", "NodePool", "sp", "instantiate", "UIOpacity", "UITransform", "Tools", "GameIns", "BlastComp", "ccclass", "property", "EnemyEffectLayer", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "_blastPool", "Map", "_blastPF", "_blastArr", "m_blastPool", "m_blastData", "m_dieBomb", "m_dieBombData", "m_blastArr", "onLoad", "me", "start", "addComponent", "parent", "node", "setPosition", "reset", "i", "children", "length", "child", "destroy", "destroyAllChildren", "for<PERSON>ach", "pool", "clearNodePool", "clear", "clearArrayForNode", "clearMapForNodeArr", "init", "blastTypes", "blastNames", "name", "prefab", "loadManager", "getRes", "set", "has", "get", "type", "parseInt", "split", "arrC<PERSON>ain", "put", "count", "skeletonNames", "skeletonData", "SkeletonData", "skeleton", "Skeleton", "premultipliedAlpha", "enableBatch", "setAnimationCacheMode", "AnimationCacheMode", "SHARED_CACHE", "push", "bombCount", "_loadRes", "loadPrefab", "loadSpine", "getBlastNode", "error", "pop", "recycleBlastNode", "getComponent", "opacity", "arr<PERSON><PERSON><PERSON>", "addBlastEffectByName", "target", "effectName", "effectData", "callback", "playBlastEffect", "addBlastEffect", "playBlastAnimation", "setSiblingIndex", "setScale", "scale", "scene<PERSON>os", "x", "y", "sceneManager", "getScenePos", "angle", "setAnimation", "setCompleteListener", "getAnimationNode", "play", "recycleBlastAnimation", "playDieBombAnim", "position"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;AAASA,MAAAA,U,OAAAA,U;AAAYC,MAAAA,S,OAAAA,S;AAAWC,MAAAA,I,OAAAA,I;AAAMC,MAAAA,M,OAAAA,M;AAAQC,MAAAA,Q,OAAAA,Q;AAAgBC,MAAAA,E,OAAAA,E;AAAiBC,MAAAA,W,OAAAA,W;AAAaC,MAAAA,S,OAAAA,S;AAAWC,MAAAA,W,OAAAA,W;;AAC9FC,MAAAA,K,iBAAAA,K;;AACAC,MAAAA,O,iBAAAA,O;;AACFC,MAAAA,S;;;;;;;;;OAED;AAAEC,QAAAA,OAAF;AAAWC,QAAAA;AAAX,O,GAAwBb,U;;yBAGTc,gB,WADpBF,OAAO,CAAC,kBAAD,C,2BAAR,MACqBE,gBADrB,SAC8Cb,SAD9C,CACwD;AAAA;AAAA;AAAA,eAGpDc,eAHoD,GAG5B,IAH4B;AAAA,eAIpDC,YAJoD,GAI/B,IAJ+B;AAAA,eAMpDC,UANoD,GAMhB,IAAIC,GAAJ,EANgB;AAAA,eAOpDC,QAPoD,GAOpB,IAAID,GAAJ,EAPoB;AAAA,eAQpDE,SARoD,GAQhC,EARgC;AAAA,eASpDC,WAToD,GASjB,IAAIH,GAAJ,EATiB;AAAA,eAUpDI,WAVoD,GAUR,IAAIJ,GAAJ,EAVQ;AAAA,eAWpDK,SAXoD,GAW9B,IAAInB,QAAJ,EAX8B;AAAA,eAYpDoB,aAZoD,GAYnB,IAZmB;AAAA,eAcpDC,UAdoD,GAcvC,EAduC;AAAA;;AAgBpDC,QAAAA,MAAM,GAAG;AACLZ,UAAAA,gBAAgB,CAACa,EAAjB,GAAsB,IAAtB;AACH;;AAEDC,QAAAA,KAAK,GAAG;AACJ,eAAKb,eAAL,GAAuB,IAAIb,IAAJ,EAAvB;AACA,eAAKa,eAAL,CAAqBc,YAArB,CAAkCrB,WAAlC;AACA,eAAKO,eAAL,CAAqBe,MAArB,GAA8B,KAAKC,IAAnC;AACA,eAAKhB,eAAL,CAAqBiB,WAArB,CAAiC,CAAjC,EAAoC,CAApC;AAEA,eAAKhB,YAAL,GAAoB,IAAId,IAAJ,EAApB;AACA,eAAKc,YAAL,CAAkBa,YAAlB,CAA+BrB,WAA/B;AACA,eAAKQ,YAAL,CAAkBc,MAAlB,GAA2B,KAAKC,IAAhC;AACA,eAAKf,YAAL,CAAkBgB,WAAlB,CAA8B,CAA9B,EAAiC,CAAjC;AACH;;AAEDC,QAAAA,KAAK,GAAG;AACJ,eAAK,IAAIC,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAG,KAAKH,IAAL,CAAUI,QAAV,CAAmBC,MAAvC,EAA+CF,CAAC,EAAhD,EAAoD;AAChD,gBAAMG,KAAK,GAAG,KAAKN,IAAL,CAAUI,QAAV,CAAmBD,CAAnB,CAAd;;AACA,gBAAIG,KAAK,KAAK,KAAKtB,eAAf,IAAkCsB,KAAK,KAAK,KAAKrB,YAArD,EAAmE;AAC/DqB,cAAAA,KAAK,CAACP,MAAN,GAAe,IAAf;AACAO,cAAAA,KAAK,CAACC,OAAN;AACAJ,cAAAA,CAAC;AACJ,aAJD,MAIO;AACHG,cAAAA,KAAK,CAACE,kBAAN;AACH;AACJ;;AAED,eAAKtB,UAAL,CAAgBuB,OAAhB,CAAyBC,IAAD,IAAU;AAC9B;AAAA;AAAA,gCAAMC,aAAN,CAAoBD,IAApB;AACH,WAFD;;AAGA,eAAKxB,UAAL,CAAgB0B,KAAhB;;AACA,eAAKxB,QAAL,CAAcwB,KAAd;;AACA;AAAA;AAAA,8BAAMC,iBAAN,CAAwB,KAAKxB,SAA7B;AACA;AAAA;AAAA,8BAAMyB,kBAAN,CAAyB,KAAKxB,WAA9B;AACA,eAAKC,WAAL,CAAiBqB,KAAjB;AACA;AAAA;AAAA,8BAAMC,iBAAN,CAAwB,KAAKxB,SAA7B;AACA;AAAA;AAAA,8BAAMsB,aAAN,CAAoB,KAAKnB,SAAzB;AACA,eAAKC,aAAL,GAAqB,IAArB;AACH;;AAEDsB,QAAAA,IAAI,CAACC,UAAD,EAA8B;AAAA,cAA7BA,UAA6B;AAA7BA,YAAAA,UAA6B,GAAN,IAAM;AAAA;;AAC9B,cAAMC,UAAU,GAAG,CAAC,QAAD,EAAW,QAAX,EAAqB,QAArB,EAA+B,QAA/B,EAAyC,QAAzC,EAAmD,QAAnD,EAA6D,QAA7D,EAAuE,QAAvE,CAAnB;;AACA,eAAK,IAAMC,IAAX,IAAmBD,UAAnB,EAA+B;AAC3B,gBAAME,MAAM,GAAG;AAAA;AAAA,oCAAQC,WAAR,CAAoBC,MAApB,CAA2BH,IAA3B,EAAiC9C,MAAjC,CAAf;;AACA,gBAAI+C,MAAJ,EAAY;AACR,mBAAK/B,QAAL,CAAckC,GAAd,CAAkBJ,IAAlB,EAAwBC,MAAxB;;AACA,kBAAI,CAAC,KAAKjC,UAAL,CAAgBqC,GAAhB,CAAoBL,IAApB,CAAL,EAAgC;AAC5B,qBAAKhC,UAAL,CAAgBoC,GAAhB,CAAoBJ,IAApB,EAA0B,IAAI7C,QAAJ,EAA1B;AACH;;AAED,kBAAMqC,KAAI,GAAG,KAAKxB,UAAL,CAAgBsC,GAAhB,CAAoBN,IAApB,CAAb;;AACA,kBAAIF,UAAU,IAAIA,UAAU,CAACX,MAAX,GAAoB,CAAtC,EAAyC;AACrC,oBAAMoB,IAAI,GAAGC,QAAQ,CAACR,IAAI,CAACS,KAAL,CAAW,OAAX,EAAoB,CAApB,CAAD,CAArB;;AACA,oBAAI;AAAA;AAAA,oCAAMC,UAAN,CAAiBZ,UAAjB,EAA6BS,IAA7B,CAAJ,EAAwC;AACpC,sBAAIA,IAAI,KAAK,CAAb,EAAgB;AACZ,wBAAMzB,IAAI,GAAGzB,WAAW,CAAC4C,MAAD,CAAxB;;AACAT,oBAAAA,KAAI,CAACmB,GAAL,CAAS7B,IAAT;AACH,mBAHD,MAGO;AACH,wBAAM8B,KAAK,GAAGL,IAAI,KAAK,CAAT,GAAa,EAAb,GAAkB,CAAhC;;AACA,yBAAK,IAAItB,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAG2B,KAApB,EAA2B3B,CAAC,EAA5B,EAAgC;AAC5B,0BAAMH,KAAI,GAAGzB,WAAW,CAAC4C,MAAD,CAAxB;;AACAT,sBAAAA,KAAI,CAACmB,GAAL,CAAS7B,KAAT;AACH;AACJ;AACJ;AACJ,eAdD,MAcO;AACH,qBAAK,IAAIG,EAAC,GAAG,CAAb,EAAgBA,EAAC,GAAG,CAApB,EAAuBA,EAAC,EAAxB,EAA4B;AACxB,sBAAMH,MAAI,GAAGzB,WAAW,CAAC4C,MAAD,CAAxB;;AACAT,kBAAAA,KAAI,CAACmB,GAAL,CAAS7B,MAAT;AACH;AACJ;AACJ;AACJ;;AAED,cAAM+B,aAAa,GAAG,CAAC,aAAD,EAAgB,kBAAhB,EAAoC,iBAApC,CAAtB;;AACA,eAAK,IAAMb,KAAX,IAAmBa,aAAnB,EAAkC;AAC9B,gBAAMC,aAAY,GAAG;AAAA;AAAA,oCAAQZ,WAAR,CAAoBC,MAApB,CAA2BH,KAA3B,EAAiC5C,EAAE,CAAC2D,YAApC,CAArB;;AACA,gBAAID,aAAJ,EAAkB;AACd,mBAAKzC,WAAL,CAAiB+B,GAAjB,CAAqBJ,KAArB,EAA2Bc,aAA3B;AACH;AACJ;;AAED,cAAI,CAAC,KAAK1C,WAAL,CAAiBiC,GAAjB,CAAqB,aAArB,CAAL,EAA0C;AACtC,iBAAKjC,WAAL,CAAiBgC,GAAjB,CAAqB,aAArB,EAAoC,EAApC;AACH;;AAED,cAAMZ,IAAI,GAAG,KAAKpB,WAAL,CAAiBkC,GAAjB,CAAqB,aAArB,CAAb;AACA,cAAMQ,YAAY,GAAG,KAAKzC,WAAL,CAAiBiC,GAAjB,CAAqB,aAArB,CAArB;;AACA,cAAIQ,YAAJ,EAAkB;AACd,iBAAK,IAAI7B,GAAC,GAAG,CAAb,EAAgBA,GAAC,GAAG,EAApB,EAAwBA,GAAC,EAAzB,EAA6B;AACzB,kBAAMH,MAAI,GAAG,IAAI7B,IAAJ,EAAb;;AACA6B,cAAAA,MAAI,CAACF,YAAL,CAAkBrB,WAAlB;;AACA,kBAAMyD,QAAQ,GAAGlC,MAAI,CAACF,YAAL,CAAkBxB,EAAE,CAAC6D,QAArB,CAAjB;;AACAD,cAAAA,QAAQ,CAACF,YAAT,GAAwBA,YAAxB;AACAE,cAAAA,QAAQ,CAACE,kBAAT,GAA8B,KAA9B;AACAF,cAAAA,QAAQ,CAACG,WAAT,GAAuB,IAAvB;AACAH,cAAAA,QAAQ,CAACI,qBAAT,CAA+BhE,EAAE,CAAC6D,QAAH,CAAYI,kBAAZ,CAA+BC,YAA9D;AACA9B,cAAAA,IAAI,CAAC+B,IAAL,CAAUzC,MAAV;AACH;AACJ;;AAED,eAAKP,aAAL,GAAqB;AAAA;AAAA,kCAAQ2B,WAAR,CAAoBC,MAApB,CAA2B,cAA3B,EAA2C/C,EAAE,CAAC2D,YAA9C,CAArB;AACA,cAAMS,SAAS,GAAG,CAAlB;;AACA,eAAK,IAAIvC,GAAC,GAAG,CAAb,EAAgBA,GAAC,GAAGuC,SAApB,EAA+BvC,GAAC,EAAhC,EAAoC;AAChC,gBAAMH,MAAI,GAAG,IAAI7B,IAAJ,EAAb;;AACA6B,YAAAA,MAAI,CAACF,YAAL,CAAkBrB,WAAlB;;AACA,gBAAMyD,SAAQ,GAAGlC,MAAI,CAACF,YAAL,CAAkBxB,EAAE,CAAC6D,QAArB,CAAjB;;AACAD,YAAAA,SAAQ,CAACF,YAAT,GAAwB,KAAKvC,aAA7B;AACAyC,YAAAA,SAAQ,CAACE,kBAAT,GAA8B,KAA9B;;AACAF,YAAAA,SAAQ,CAACI,qBAAT,CAA+BhE,EAAE,CAAC6D,QAAH,CAAYI,kBAAZ,CAA+BC,YAA9D;;AACAN,YAAAA,SAAQ,CAACG,WAAT,GAAuB,IAAvB;AACA,iBAAK7C,SAAL,CAAeqC,GAAf,CAAmB7B,MAAnB;AACH;AACJ;;AAEK2C,QAAAA,QAAQ,CAACzB,IAAD,EAAeO,IAAf,EAA6D;AAAA;;AAAA;AACvE,gBAAIA,IAAI,KAAKrD,MAAb,EAAqB;AACjB,kBAAM+C,MAAM,SAAS;AAAA;AAAA,sCAAQC,WAAR,CAAoBwB,UAApB,CAA+B1B,IAA/B,CAArB;;AACA,cAAA,KAAI,CAAC9B,QAAL,CAAckC,GAAd,CAAkBJ,IAAlB,EAAwBC,MAAxB;AACH,aAHD,MAGO,IAAIM,IAAI,KAAKnD,EAAE,CAAC2D,YAAhB,EAA8B;AACjC,kBAAMD,YAAY,SAAS;AAAA;AAAA,sCAAQZ,WAAR,CAAoByB,SAApB,CAA8B3B,IAA9B,CAA3B;;AACA,cAAA,KAAI,CAAC3B,WAAL,CAAiB+B,GAAjB,CAAqBJ,IAArB,EAA2Bc,YAA3B;AACH;AAPsE;AAQ1E;;AAEDc,QAAAA,YAAY,CAAC5B,IAAD,EAAqB;AAC7B,cAAI,CAAC,KAAK3B,WAAL,CAAiBiC,GAAjB,CAAqBN,IAArB,CAAL,EAAiC;AAC7B;AAAA;AAAA,gCAAM6B,KAAN,yBAAkC7B,IAAlC;;AACA,iBAAKyB,QAAL,CAAczB,IAAd,EAAoB5C,EAAE,CAAC2D,YAAvB;;AACA,mBAAO,IAAP;AACH;;AAED,cAAI,CAAC,KAAK3C,WAAL,CAAiBiC,GAAjB,CAAqBL,IAArB,CAAL,EAAiC;AAC7B,iBAAK5B,WAAL,CAAiBgC,GAAjB,CAAqBJ,IAArB,EAA2B,EAA3B;AACH;;AAED,cAAMR,IAAI,GAAG,KAAKpB,WAAL,CAAiBkC,GAAjB,CAAqBN,IAArB,CAAb;AACA,cAAIlB,IAAI,GAAGU,IAAI,CAACsC,GAAL,EAAX;;AACA,cAAI,CAAChD,IAAL,EAAW;AACPA,YAAAA,IAAI,GAAG,IAAI7B,IAAJ,EAAP;AACA6B,YAAAA,IAAI,CAACF,YAAL,CAAkBrB,WAAlB;AACA,gBAAMyD,QAAQ,GAAGlC,IAAI,CAACF,YAAL,CAAkBxB,EAAE,CAAC6D,QAArB,CAAjB;AACAD,YAAAA,QAAQ,CAACF,YAAT,GAAwB,KAAKzC,WAAL,CAAiBiC,GAAjB,CAAqBN,IAArB,CAAxB;AACAgB,YAAAA,QAAQ,CAACE,kBAAT,GAA8B,KAA9B;AACAF,YAAAA,QAAQ,CAACG,WAAT,GAAuB,IAAvB;AACAH,YAAAA,QAAQ,CAACI,qBAAT,CAA+BhE,EAAE,CAAC6D,QAAH,CAAYI,kBAAZ,CAA+BC,YAA9D;AACH;;AAED,iBAAOxC,IAAP;AACH;;AAEDiD,QAAAA,gBAAgB,CAAC/B,IAAD,EAAelB,IAAf,EAA2B;AACvC,cAAI,CAAC,KAAKV,WAAL,CAAiBiC,GAAjB,CAAqBL,IAArB,CAAL,EAAiC;AAC7B,iBAAK5B,WAAL,CAAiBgC,GAAjB,CAAqBJ,IAArB,EAA2B,EAA3B;AACH;;AAED,cAAMR,IAAI,GAAG,KAAKpB,WAAL,CAAiBkC,GAAjB,CAAqBN,IAArB,CAAb;AACAlB,UAAAA,IAAI,CAACkD,YAAL,CAAkB1E,SAAlB,EAA6B2E,OAA7B,GAAuC,CAAvC;AACAzC,UAAAA,IAAI,CAAC+B,IAAL,CAAUzC,IAAV;AACA;AAAA;AAAA,8BAAMoD,SAAN,CAAgB,KAAK/D,SAArB,EAAgCW,IAAhC;AACH;;AAEDqD,QAAAA,oBAAoB,CAACC,MAAD,EAASC,UAAT,EAA6BC,UAA7B,EAAiGC,QAAjG,EAA6I;AAAA,cAA5CA,QAA4C;AAA5CA,YAAAA,QAA4C,GAAZ,IAAY;AAAA;;AAC7J,eAAKC,eAAL,CAAqBJ,MAArB,EAA6BC,UAA7B,EAAyCC,UAAzC;;AACA,cAAIC,QAAJ,EAAc;AACVA,YAAAA,QAAQ;AACX;AACJ;;AAEDE,QAAAA,cAAc,CAACL,MAAD,EAAS7B,IAAT,EAAuB+B,UAAvB,EAA2FC,QAA3F,EAA8I;AAAA,cAAnDA,QAAmD;AAAnDA,YAAAA,QAAmD,GAAnB,IAAmB;AAAA;;AACxJ,cAAIhC,IAAI,KAAK,CAAb,EAAgB;AACZ,gBAAIA,IAAI,GAAG,CAAX,EAAc;AACVgC,cAAAA,QAAQ,IAAIA,QAAQ,EAApB;AACA;AAAA;AAAA,kCAAMV,KAAN,CAAY,SAAZ,EAAuBtB,IAAvB;AACA,qBAAO,IAAP;AACH;;AACD,gBAAMzB,MAAI,GAAG,KAAK4D,kBAAL,CAAwBN,MAAxB,YAAwC7B,IAAxC,EAAgD+B,UAAhD,EAA4DC,QAA5D,CAAb;;AACA,gBAAIzD,MAAJ,EAAU;AACNA,cAAAA,MAAI,CAAC6D,eAAL,CAAqBpC,IAArB;AACH;;AACD,mBAAOzB,MAAP;AACH;;AAED,cAAMA,IAAI,GAAG,KAAK0D,eAAL,CAAqBJ,MAArB,iBAA0C7B,IAA1C,EAAkD+B,UAAlD,EAA8DC,QAA9D,CAAb;;AACA,cAAIzD,IAAJ,EAAU;AACNA,YAAAA,IAAI,CAAC6D,eAAL,CAAqBpC,IAArB;AACH;;AACD,iBAAOzB,IAAP;AACH;;AAED0D,QAAAA,eAAe,CAACJ,MAAD,EAASC,UAAT,EAA6BC,UAA7B,EAAiGC,QAAjG,EAAoJ;AAAA,cAAnDA,QAAmD;AAAnDA,YAAAA,QAAmD,GAAnB,IAAmB;AAAA;;AAC/J,cAAMzD,IAAI,GAAG,KAAK8C,YAAL,CAAkBS,UAAlB,CAAb;;AACA,cAAI,CAACvD,IAAL,EAAW;AACP;AAAA;AAAA,gCAAM+C,KAAN,8DAA0BQ,UAA1B;AACAE,YAAAA,QAAQ,IAAIA,QAAQ,EAApB;AACA,mBAAO,IAAP;AACH;;AAED,eAAK/D,UAAL,CAAgB+C,IAAhB,CAAqBzC,IAArB;AACA,cAAMkC,QAAQ,GAAGlC,IAAI,CAACkD,YAAL,CAAkB5E,EAAE,CAAC6D,QAArB,CAAjB;;AACA,cAAI,CAACD,QAAL,EAAe;AACX;AAAA;AAAA,gCAAMa,KAAN,4DAAkCQ,UAAlC;AACA,mBAAO,IAAP;AACH;;AAEDrB,UAAAA,QAAQ,CAAClC,IAAT,CAAc8D,QAAd,CAAuBN,UAAU,CAACO,KAAlC,EAAyCP,UAAU,CAACO,KAApD;AACA7B,UAAAA,QAAQ,CAAClC,IAAT,CAAcD,MAAd,GAAuB,KAAKC,IAA5B;AACAkC,UAAAA,QAAQ,CAAClC,IAAT,CAAckD,YAAd,CAA2B1E,SAA3B,EAAsC2E,OAAtC,GAAgD,GAAhD;AAEA,cAAIa,QAAQ,GAAG;AAAEC,YAAAA,CAAC,EAAE,CAAL;AAAQC,YAAAA,CAAC,EAAE;AAAX,WAAf;;AACA,cAAIZ,MAAJ,EAAY;AACRU,YAAAA,QAAQ,GAAG;AAAA;AAAA,oCAAQG,YAAR,CAAqBC,WAArB,CAAiCd,MAAjC,CAAX;AACH;;AAEDpB,UAAAA,QAAQ,CAAClC,IAAT,CAAcC,WAAd,CAA0BuD,UAAU,CAACS,CAAX,GAAeD,QAAQ,CAACC,CAAlD,EAAqDT,UAAU,CAACU,CAAX,GAAeF,QAAQ,CAACE,CAA7E;AACAhC,UAAAA,QAAQ,CAAClC,IAAT,CAAcqE,KAAd,GAAsBb,UAAU,CAACa,KAAjC;AAEAnC,UAAAA,QAAQ,CAACoC,YAAT,CAAsB,CAAtB,EAAyB,MAAzB,EAAiC,KAAjC;AACApC,UAAAA,QAAQ,CAACqC,mBAAT,CAA6B,MAAM;AAC/BrC,YAAAA,QAAQ,CAACqC,mBAAT,CAA6B,IAA7B;AACAd,YAAAA,QAAQ,IAAIA,QAAQ,EAApB;AACA,iBAAKR,gBAAL,CAAsBM,UAAtB,EAAkCvD,IAAlC;AACH,WAJD;AAMA,iBAAOA,IAAP;AACH;;AAED4D,QAAAA,kBAAkB,CAACN,MAAD,EAASC,UAAT,EAA6BC,UAA7B,EAAiGC,QAAjG,EAAoJ;AAAA,cAAnDA,QAAmD;AAAnDA,YAAAA,QAAmD,GAAnB,IAAmB;AAAA;;AAClK,cAAMzD,IAAI,GAAG,KAAKwE,gBAAL,CAAsBjB,UAAtB,CAAb;;AACA,cAAI,CAACvD,IAAL,EAAW;AACP;AAAA;AAAA,gCAAM+C,KAAN,8DAA0BQ,UAA1B;AACAE,YAAAA,QAAQ,IAAIA,QAAQ,EAApB;AACA,mBAAO,IAAP;AACH;;AAED,eAAK/D,UAAL,CAAgB+C,IAAhB,CAAqBzC,IAAI,CAACA,IAA1B;AACAA,UAAAA,IAAI,CAACA,IAAL,CAAU8D,QAAV,CAAmBN,UAAU,CAACO,KAA9B,EAAqCP,UAAU,CAACO,KAAhD;AACA/D,UAAAA,IAAI,CAACA,IAAL,CAAUD,MAAV,GAAmB,KAAKC,IAAxB;AAEA,cAAIgE,QAAQ,GAAG;AAAEC,YAAAA,CAAC,EAAE,CAAL;AAAQC,YAAAA,CAAC,EAAE;AAAX,WAAf;;AACA,cAAIZ,MAAJ,EAAY;AACRU,YAAAA,QAAQ,GAAG;AAAA;AAAA,oCAAQG,YAAR,CAAqBC,WAArB,CAAiCd,MAAjC,CAAX;AACH;;AAEDtD,UAAAA,IAAI,CAACA,IAAL,CAAUC,WAAV,CAAsBuD,UAAU,CAACS,CAAX,GAAeD,QAAQ,CAACC,CAA9C,EAAiDT,UAAU,CAACU,CAAX,GAAeF,QAAQ,CAACE,CAAzE;AACAlE,UAAAA,IAAI,CAACA,IAAL,CAAUqE,KAAV,GAAkBb,UAAU,CAACa,KAA7B;AAEArE,UAAAA,IAAI,CAACyE,IAAL,CAAU,MAAM;AACZhB,YAAAA,QAAQ,IAAIA,QAAQ,EAApB;AACA,iBAAKiB,qBAAL,CAA2BnB,UAA3B,EAAuCvD,IAAI,CAACA,IAA5C;AACH,WAHD;AAKA,iBAAOA,IAAI,CAACA,IAAZ;AACH;;AAEDwE,QAAAA,gBAAgB,CAACtD,IAAD,EAA0B;AACtC,cAAI,CAAC,KAAKhC,UAAL,CAAgBqC,GAAhB,CAAoBL,IAApB,CAAL,EAAgC;AAC5B,iBAAKhC,UAAL,CAAgBoC,GAAhB,CAAoBJ,IAApB,EAA0B,IAAI7C,QAAJ,EAA1B;AACH;;AAED,cAAMqC,IAAI,GAAG,KAAKxB,UAAL,CAAgBsC,GAAhB,CAAoBN,IAApB,CAAb;;AACA,cAAIlB,IAAI,GAAGU,IAAI,CAACc,GAAL,EAAX;;AACA,cAAI,CAACxB,IAAL,EAAW;AACP,gBAAMmB,MAAM,GAAG,KAAK/B,QAAL,CAAcoC,GAAd,CAAkBN,IAAlB,CAAf;;AACA,gBAAIC,MAAJ,EAAY;AACRnB,cAAAA,IAAI,GAAGzB,WAAW,CAAC4C,MAAD,CAAlB;AACH,aAFD,MAEO;AACH,mBAAKwB,QAAL,CAAczB,IAAd,EAAoB9C,MAApB;AACH;AACJ;;AAED,iBAAO4B,IAAI,GAAGA,IAAI,CAACkD,YAAL;AAAA;AAAA,qCAAH,GAAkC,IAA7C;AACH;;AAEDwB,QAAAA,qBAAqB,CAACxD,IAAD,EAAelB,IAAf,EAA2B;AAC5C,cAAI,CAAC,KAAKd,UAAL,CAAgBqC,GAAhB,CAAoBL,IAApB,CAAL,EAAgC;AAC5B,iBAAKhC,UAAL,CAAgBoC,GAAhB,CAAoBJ,IAApB,EAA0B,IAAI7C,QAAJ,EAA1B;AACH;;AAED,cAAMqC,IAAI,GAAG,KAAKxB,UAAL,CAAgBsC,GAAhB,CAAoBN,IAApB,CAAb;;AACAR,UAAAA,IAAI,CAACmB,GAAL,CAAS7B,IAAT;AACA;AAAA;AAAA,8BAAMoD,SAAN,CAAgB,KAAK/D,SAArB,EAAgCW,IAAhC;AACH;;AAEK2E,QAAAA,eAAe,CAACC,QAAD,EAAiB;AAAA;;AAAA;AAClC,gBAAI,CAAC,MAAI,CAACnF,aAAV,EAAyB;AACrB,cAAA,MAAI,CAACA,aAAL,SAA2B;AAAA;AAAA,sCAAQ2B,WAAR,CAAoByB,SAApB,CAA8B,cAA9B,CAA3B;AACH;;AAED,gBAAI,MAAI,CAACpD,aAAT,EAAwB;AACpB,kBAAIO,IAAI,GAAG,MAAI,CAACR,SAAL,CAAegC,GAAf,EAAX;;AACA,kBAAI,CAACxB,IAAL,EAAW;AACPA,gBAAAA,IAAI,GAAG,IAAI7B,IAAJ,EAAP;AACA6B,gBAAAA,IAAI,CAACF,YAAL,CAAkBrB,WAAlB;;AACA,oBAAMyD,UAAQ,GAAGlC,IAAI,CAACF,YAAL,CAAkBxB,EAAE,CAAC6D,QAArB,CAAjB;;AACAD,gBAAAA,UAAQ,CAACF,YAAT,GAAwB,MAAI,CAACvC,aAA7B;AACAyC,gBAAAA,UAAQ,CAACE,kBAAT,GAA8B,KAA9B;;AACAF,gBAAAA,UAAQ,CAACI,qBAAT,CAA+BhE,EAAE,CAAC6D,QAAH,CAAYI,kBAAZ,CAA+BC,YAA9D;;AACAN,gBAAAA,UAAQ,CAACG,WAAT,GAAuB,IAAvB;AACH;;AAEDrC,cAAAA,IAAI,CAACD,MAAL,GAAc,MAAI,CAACf,eAAnB;AACAgB,cAAAA,IAAI,CAACC,WAAL,CAAiB2E,QAAjB;AAEA,kBAAM1C,QAAQ,GAAGlC,IAAI,CAACkD,YAAL,CAAkB5E,EAAE,CAAC6D,QAArB,CAAjB;AACAD,cAAAA,QAAQ,CAACqC,mBAAT,CAA6B,MAAM;AAC/BrC,gBAAAA,QAAQ,CAACqC,mBAAT,CAA6B,IAA7B;;AACA,gBAAA,MAAI,CAAC/E,SAAL,CAAeqC,GAAf,CAAmBK,QAAQ,CAAClC,IAA5B;AACH,eAHD;AAKAkC,cAAAA,QAAQ,CAACoC,YAAT,CAAsB,CAAtB,EAAyB,MAAzB,EAAiC,KAAjC;AACH;AA3BiC;AA4BrC;;AAzUmD,O,UAC7C1E,E", "sourcesContent": ["import { _decorator, Component, Node, Prefab, NodePool, Vec3, sp, SpriteFrame, instantiate, UIOpacity, UITransform } from 'cc';\r\nimport { Tools } from '../../../utils/Tools';\r\nimport { GameIns } from '../../../GameIns';\r\nimport BlastComp from '../../base/BlastComp';\r\n\r\nconst { ccclass, property } = _decorator;\r\n\r\n@ccclass('EnemyEffectLayer')\r\nexport default class EnemyEffectLayer extends Component {\r\n    static me: EnemyEffectLayer;\r\n\r\n    hurtEffectLayer: Node = null;\r\n    hurtNumLayer: Node = null;\r\n\r\n    _blastPool: Map<string, NodePool> = new Map();\r\n    _blastPF: Map<string, Prefab> = new Map();\r\n    _blastArr: Node[] = [];\r\n    m_blastPool: Map<string, Node[]> = new Map();\r\n    m_blastData: Map<string, sp.SkeletonData> = new Map();\r\n    m_dieBomb: NodePool = new NodePool();\r\n    m_dieBombData: sp.SkeletonData = null;\r\n\r\n    m_blastArr = [];\r\n\r\n    onLoad() {\r\n        EnemyEffectLayer.me = this;\r\n    }\r\n\r\n    start() {\r\n        this.hurtEffectLayer = new Node();\r\n        this.hurtEffectLayer.addComponent(UITransform);\r\n        this.hurtEffectLayer.parent = this.node;\r\n        this.hurtEffectLayer.setPosition(0, 0);\r\n\r\n        this.hurtNumLayer = new Node();\r\n        this.hurtNumLayer.addComponent(UITransform);\r\n        this.hurtNumLayer.parent = this.node;\r\n        this.hurtNumLayer.setPosition(0, 0);\r\n    }\r\n\r\n    reset() {\r\n        for (let i = 0; i < this.node.children.length; i++) {\r\n            const child = this.node.children[i];\r\n            if (child !== this.hurtEffectLayer && child !== this.hurtNumLayer) {\r\n                child.parent = null;\r\n                child.destroy();\r\n                i--;\r\n            } else {\r\n                child.destroyAllChildren();\r\n            }\r\n        }\r\n\r\n        this._blastPool.forEach((pool) => {\r\n            Tools.clearNodePool(pool);\r\n        });\r\n        this._blastPool.clear();\r\n        this._blastPF.clear();\r\n        Tools.clearArrayForNode(this._blastArr);\r\n        Tools.clearMapForNodeArr(this.m_blastPool);\r\n        this.m_blastData.clear();\r\n        Tools.clearArrayForNode(this._blastArr);\r\n        Tools.clearNodePool(this.m_dieBomb);\r\n        this.m_dieBombData = null;\r\n    }\r\n\r\n    init(blastTypes: number[] = null) {\r\n        const blastNames = ['blast0', 'blast1', 'blast2', 'blast3', 'blast4', 'blast5', 'blast7', 'blast8'];\r\n        for (const name of blastNames) {\r\n            const prefab = GameIns.loadManager.getRes(name, Prefab) as Prefab;\r\n            if (prefab) {\r\n                this._blastPF.set(name, prefab);\r\n                if (!this._blastPool.has(name)) {\r\n                    this._blastPool.set(name, new NodePool());\r\n                }\r\n\r\n                const pool = this._blastPool.get(name);\r\n                if (blastTypes && blastTypes.length > 0) {\r\n                    const type = parseInt(name.split('blast')[1]);\r\n                    if (Tools.arrContain(blastTypes, type)) {\r\n                        if (type === 0) {\r\n                            const node = instantiate(prefab);\r\n                            pool.put(node);\r\n                        } else {\r\n                            const count = type === 1 ? 12 : 3;\r\n                            for (let i = 0; i < count; i++) {\r\n                                const node = instantiate(prefab);\r\n                                pool.put(node);\r\n                            }\r\n                        }\r\n                    }\r\n                } else {\r\n                    for (let i = 0; i < 5; i++) {\r\n                        const node = instantiate(prefab);\r\n                        pool.put(node);\r\n                    }\r\n                }\r\n            }\r\n        }\r\n\r\n        const skeletonNames = ['skel_blast9', 'skel_bFire_blast', 'skel_bIce_blast'];\r\n        for (const name of skeletonNames) {\r\n            const skeletonData = GameIns.loadManager.getRes(name, sp.SkeletonData) as sp.SkeletonData;\r\n            if (skeletonData) {\r\n                this.m_blastData.set(name, skeletonData);\r\n            }\r\n        }\r\n\r\n        if (!this.m_blastPool.has('skel_blast9')) {\r\n            this.m_blastPool.set('skel_blast9', []);\r\n        }\r\n\r\n        const pool = this.m_blastPool.get('skel_blast9');\r\n        const skeletonData = this.m_blastData.get('skel_blast9');\r\n        if (skeletonData) {\r\n            for (let i = 0; i < 10; i++) {\r\n                const node = new Node();\r\n                node.addComponent(UITransform);\r\n                const skeleton = node.addComponent(sp.Skeleton);\r\n                skeleton.skeletonData = skeletonData;\r\n                skeleton.premultipliedAlpha = false;\r\n                skeleton.enableBatch = true;\r\n                skeleton.setAnimationCacheMode(sp.Skeleton.AnimationCacheMode.SHARED_CACHE);\r\n                pool.push(node);\r\n            }\r\n        }\r\n\r\n        this.m_dieBombData = GameIns.loadManager.getRes('skel_diebomb', sp.SkeletonData) as sp.SkeletonData;\r\n        const bombCount = 8;\r\n        for (let i = 0; i < bombCount; i++) {\r\n            const node = new Node();\r\n            node.addComponent(UITransform);\r\n            const skeleton = node.addComponent(sp.Skeleton);\r\n            skeleton.skeletonData = this.m_dieBombData;\r\n            skeleton.premultipliedAlpha = false;\r\n            skeleton.setAnimationCacheMode(sp.Skeleton.AnimationCacheMode.SHARED_CACHE);\r\n            skeleton.enableBatch = true;\r\n            this.m_dieBomb.put(node);\r\n        }\r\n    }\r\n\r\n    async _loadRes(name: string, type: typeof Prefab | typeof sp.SkeletonData) {\r\n        if (type === Prefab) {\r\n            const prefab = await GameIns.loadManager.loadPrefab(name) as Prefab;\r\n            this._blastPF.set(name, prefab);\r\n        } else if (type === sp.SkeletonData) {\r\n            const skeletonData = await GameIns.loadManager.loadSpine(name) as sp.SkeletonData;\r\n            this.m_blastData.set(name, skeletonData);\r\n        }\r\n    }\r\n\r\n    getBlastNode(name: string): Node {\r\n        if (!this.m_blastData.get(name)) {\r\n            Tools.error(`No blast skeleton: ${name}`);\r\n            this._loadRes(name, sp.SkeletonData);\r\n            return null;\r\n        }\r\n\r\n        if (!this.m_blastPool.has(name)) {\r\n            this.m_blastPool.set(name, []);\r\n        }\r\n\r\n        const pool = this.m_blastPool.get(name);\r\n        let node = pool.pop();\r\n        if (!node) {\r\n            node = new Node();\r\n            node.addComponent(UITransform);\r\n            const skeleton = node.addComponent(sp.Skeleton);\r\n            skeleton.skeletonData = this.m_blastData.get(name);\r\n            skeleton.premultipliedAlpha = false;\r\n            skeleton.enableBatch = true;\r\n            skeleton.setAnimationCacheMode(sp.Skeleton.AnimationCacheMode.SHARED_CACHE);\r\n        }\r\n\r\n        return node;\r\n    }\r\n\r\n    recycleBlastNode(name: string, node: Node) {\r\n        if (!this.m_blastPool.has(name)) {\r\n            this.m_blastPool.set(name, []);\r\n        }\r\n\r\n        const pool = this.m_blastPool.get(name);\r\n        node.getComponent(UIOpacity).opacity = 0;\r\n        pool.push(node);\r\n        Tools.arrRemove(this._blastArr, node);\r\n    }\r\n\r\n    addBlastEffectByName(target, effectName: string, effectData: { x: number; y: number; scale: number; angle: number }, callback: (() => void) | null = null): void {\r\n        this.playBlastEffect(target, effectName, effectData);\r\n        if (callback) {\r\n            callback();\r\n        }\r\n    }\r\n\r\n    addBlastEffect(target, type: number, effectData: { x: number; y: number; scale: number; angle: number }, callback: (() => void) | null = null): Node | null {\r\n        if (type !== 9) {\r\n            if (type > 9) {\r\n                callback && callback();\r\n                Tools.error('爆炸类型不对:', type);\r\n                return null;\r\n            }\r\n            const node = this.playBlastAnimation(target, `blast${type}`, effectData, callback);\r\n            if (node) {\r\n                node.setSiblingIndex(type);\r\n            }\r\n            return node;\r\n        }\r\n\r\n        const node = this.playBlastEffect(target, `skel_blast${type}`, effectData, callback);\r\n        if (node) {\r\n            node.setSiblingIndex(type);\r\n        }\r\n        return node;\r\n    }\r\n\r\n    playBlastEffect(target, effectName: string, effectData: { x: number; y: number; scale: number; angle: number }, callback: (() => void) | null = null): Node | null {\r\n        const node = this.getBlastNode(effectName);\r\n        if (!node) {\r\n            Tools.error(`未找到爆炸动画资源: ${effectName}`);\r\n            callback && callback();\r\n            return null;\r\n        }\r\n\r\n        this.m_blastArr.push(node);\r\n        const skeleton = node.getComponent(sp.Skeleton);\r\n        if (!skeleton) {\r\n            Tools.error(`节点未包含 Skeleton 组件: ${effectName}`);\r\n            return null;\r\n        }\r\n\r\n        skeleton.node.setScale(effectData.scale, effectData.scale);\r\n        skeleton.node.parent = this.node;\r\n        skeleton.node.getComponent(UIOpacity).opacity = 255;\r\n\r\n        let scenePos = { x: 0, y: 0 };\r\n        if (target) {\r\n            scenePos = GameIns.sceneManager.getScenePos(target);\r\n        }\r\n\r\n        skeleton.node.setPosition(effectData.x + scenePos.x, effectData.y + scenePos.y);\r\n        skeleton.node.angle = effectData.angle;\r\n\r\n        skeleton.setAnimation(0, 'play', false);\r\n        skeleton.setCompleteListener(() => {\r\n            skeleton.setCompleteListener(null);\r\n            callback && callback();\r\n            this.recycleBlastNode(effectName, node);\r\n        });\r\n\r\n        return node;\r\n    }\r\n\r\n    playBlastAnimation(target, effectName: string, effectData: { x: number; y: number; scale: number; angle: number }, callback: (() => void) | null = null): Node | null {\r\n        const node = this.getAnimationNode(effectName);\r\n        if (!node) {\r\n            Tools.error(`未找到爆炸动画资源: ${effectName}`);\r\n            callback && callback();\r\n            return null;\r\n        }\r\n\r\n        this.m_blastArr.push(node.node);\r\n        node.node.setScale(effectData.scale, effectData.scale);\r\n        node.node.parent = this.node;\r\n\r\n        let scenePos = { x: 0, y: 0 };\r\n        if (target) {\r\n            scenePos = GameIns.sceneManager.getScenePos(target);\r\n        }\r\n\r\n        node.node.setPosition(effectData.x + scenePos.x, effectData.y + scenePos.y);\r\n        node.node.angle = effectData.angle;\r\n\r\n        node.play(() => {\r\n            callback && callback();\r\n            this.recycleBlastAnimation(effectName, node.node);\r\n        });\r\n\r\n        return node.node;\r\n    }\r\n\r\n    getAnimationNode(name: string): BlastComp {\r\n        if (!this._blastPool.has(name)) {\r\n            this._blastPool.set(name, new NodePool());\r\n        }\r\n\r\n        const pool = this._blastPool.get(name);\r\n        let node = pool.get();\r\n        if (!node) {\r\n            const prefab = this._blastPF.get(name);\r\n            if (prefab) {\r\n                node = instantiate(prefab);\r\n            } else {\r\n                this._loadRes(name, Prefab);\r\n            }\r\n        }\r\n\r\n        return node ? node.getComponent(BlastComp) : null;\r\n    }\r\n\r\n    recycleBlastAnimation(name: string, node: Node) {\r\n        if (!this._blastPool.has(name)) {\r\n            this._blastPool.set(name, new NodePool());\r\n        }\r\n\r\n        const pool = this._blastPool.get(name);\r\n        pool.put(node);\r\n        Tools.arrRemove(this._blastArr, node);\r\n    }\r\n\r\n    async playDieBombAnim(position: Vec3) {\r\n        if (!this.m_dieBombData) {\r\n            this.m_dieBombData = await GameIns.loadManager.loadSpine('skel_diebomb') as sp.SkeletonData;\r\n        }\r\n\r\n        if (this.m_dieBombData) {\r\n            let node = this.m_dieBomb.get();\r\n            if (!node) {\r\n                node = new Node();\r\n                node.addComponent(UITransform);\r\n                const skeleton = node.addComponent(sp.Skeleton);\r\n                skeleton.skeletonData = this.m_dieBombData;\r\n                skeleton.premultipliedAlpha = false;\r\n                skeleton.setAnimationCacheMode(sp.Skeleton.AnimationCacheMode.SHARED_CACHE);\r\n                skeleton.enableBatch = true;\r\n            }\r\n\r\n            node.parent = this.hurtEffectLayer;\r\n            node.setPosition(position);\r\n\r\n            const skeleton = node.getComponent(sp.Skeleton);\r\n            skeleton.setCompleteListener(() => {\r\n                skeleton.setCompleteListener(null);\r\n                this.m_dieBomb.put(skeleton.node);\r\n            });\r\n\r\n            skeleton.setAnimation(0, 'play', false);\r\n        }\r\n    }\r\n}"]}