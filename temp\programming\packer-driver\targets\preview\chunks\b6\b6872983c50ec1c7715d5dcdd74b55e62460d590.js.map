{"version": 3, "sources": ["file:///E:/M2Game/Client/assets/scripts/GameOld/world/weapon/Weapon.ts"], "names": ["_decorator", "Component", "ccclass", "property", "Weapon", "canUse", "use"], "mappings": ";;;;;;;;;;AAASA,MAAAA,U,OAAAA,U;AAAkBC,MAAAA,S,OAAAA,S;;;;;;;;;OACrB;AAAEC,QAAAA,OAAF;AAAWC,QAAAA;AAAX,O,GAAwBH,U;;wBAGRI,M,WADrBF,OAAO,CAAC,QAAD,C,gBAAR,MACsBE,MADtB,SACqCH,SADrC,CAC+C;AAC3C;AACJ;AACA;AACA;AACII,QAAAA,MAAM,GAAY;AACd,iBAAO,IAAP;AACH;AAED;AACJ;AACA;AACA;;;AACIC,QAAAA,GAAG,GAAS,CACX;;AAd0C,O", "sourcesContent": ["import { _decorator, Node, Component } from 'cc';\r\nconst { ccclass, property } = _decorator;\r\n\r\n@ccclass('Weapon')\r\nexport abstract class Weapon extends Component {\r\n    /**\r\n     * Check if the weapon can be used\r\n     * @returns true if the weapon can be used, false otherwise\r\n     */\r\n    canUse(): boolean {\r\n        return true;\r\n    }\r\n\r\n    /**\r\n     * Use the weapon\r\n     * Base implementation that should be overridden by subclasses\r\n     */\r\n    use(): void {\r\n    }\r\n}"]}