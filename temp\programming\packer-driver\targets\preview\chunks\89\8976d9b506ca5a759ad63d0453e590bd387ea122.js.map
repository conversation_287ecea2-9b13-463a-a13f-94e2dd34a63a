{"version": 3, "sources": ["file:///E:/M2Game/Client/assets/scripts/Game/ui/base/PfFrameAnim.ts"], "names": ["_decorator", "Component", "Sprite", "macro", "Tween", "UITransform", "tween", "UIOpacity", "GameConfig", "GameConst", "ccclass", "property", "PfFrameAnim", "atlas", "imageSrc", "count", "duration", "call", "imageArr", "playTimes", "maxPlayTimes", "REPEAT_FOREVER", "time", "index", "willStop", "selfUpdate", "stopCall", "resetCall", "sWidth", "sHeight", "init", "node", "getComponent", "opacity", "i", "push", "getSpriteFrame", "sprite", "addComponent", "sizeMode", "SizeMode", "RAW", "trim", "dstBlendFactor", "setSize", "size", "mode", "CUSTOM", "setContentSize", "for<PERSON>ach", "frame", "insetBottom", "insetTop", "insetLeft", "insetRight", "type", "Type", "SLICED", "setColor", "color", "reset", "spriteFrame", "stop", "fadeOut", "stopAllByTarget", "to", "ActionFrameTime", "start", "setWillStop", "isPlaying", "updateGameLogic", "dt", "width", "height", "update", "GameAble"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;AAASA,MAAAA,U,OAAAA,U;AAAYC,MAAAA,S,OAAAA,S;AAAWC,MAAAA,M,OAAAA,M;AAAcC,MAAAA,K,OAAAA,K;AAAoBC,MAAAA,K,OAAAA,K;AAAOC,MAAAA,W,OAAAA,W;AAAaC,MAAAA,K,OAAAA,K;AAAOC,MAAAA,S,OAAAA,S;;AACtFC,MAAAA,U;;AACEC,MAAAA,S,iBAAAA,S;;;;;;;;;OAEH;AAAEC,QAAAA,OAAF;AAAWC,QAAAA;AAAX,O,GAAwBX,U;;yBAGTY,W,WADpBF,OAAO,CAAC,aAAD,C,UAEHC,QAAQ,CAACT,MAAD,C,2BAFb,MACqBU,WADrB,SACyCX,SADzC,CACmD;AAAA;AAAA;;AAAA;;AAAA,eAI/CY,KAJ+C,GAIvC,IAJuC;AAIjC;AAJiC,eAK/CC,QAL+C,GAKpC,EALoC;AAKhC;AALgC,eAM/CC,KAN+C,GAMvC,CANuC;AAMpC;AANoC,eAO/CC,QAP+C,GAOpC,CAPoC;AAOjC;AAPiC,eAQ/CC,IAR+C,GAQxC,IARwC;AAQlC;AARkC,eAS/CC,QAT+C,GASpC,EAToC;AAShC;AATgC,eAU/CC,SAV+C,GAUnC,CAVmC;AAUhC;AAVgC,eAW/CC,YAX+C,GAWhCjB,KAAK,CAACkB,cAX0B;AAWV;AAXU,eAY/CC,IAZ+C,GAYxC,CAZwC;AAYrC;AAZqC,eAa/CC,KAb+C,GAavC,CAbuC;AAapC;AAboC,eAc/CC,QAd+C,GAcpC,KAdoC;AAc7B;AAd6B,eAe/CC,UAf+C,GAelC,IAfkC;AAe5B;AAf4B,eAgB/CC,QAhB+C,GAgBpC,IAhBoC;AAgB9B;AAhB8B,eAiB/CC,SAjB+C,GAiBnC,IAjBmC;AAiB7B;AAjB6B,eAkB/CC,MAlB+C,GAkBtC,CAAC,CAlBqC;AAkBlC;AAlBkC,eAmB/CC,OAnB+C,GAmBrC,CAAC,CAnBoC;AAAA;;AAmBjC;;AAGd;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACIC,QAAAA,IAAI,CAACjB,KAAD,EAAQC,QAAR,EAAkBC,KAAlB,EAAyBC,QAAzB,EAAmCC,IAAnC,EAAgDS,QAAhD,EAAiEC,SAAjE,EAAmF;AAAA,cAAhDV,IAAgD;AAAhDA,YAAAA,IAAgD,GAAzC,IAAyC;AAAA;;AAAA,cAAnCS,QAAmC;AAAnCA,YAAAA,QAAmC,GAAxB,IAAwB;AAAA;;AAAA,cAAlBC,SAAkB;AAAlBA,YAAAA,SAAkB,GAAN,IAAM;AAAA;;AACnF,eAAKI,IAAL,CAAUC,YAAV,CAAuBzB,SAAvB,EAAkC0B,OAAlC,GAA4C,CAA5C;AACA,eAAKpB,KAAL,GAAaA,KAAb;AACA,eAAKC,QAAL,GAAgBA,QAAhB;AACA,eAAKC,KAAL,GAAaA,KAAb;AACA,eAAKC,QAAL,GAAgBA,QAAhB;AACA,eAAKC,IAAL,GAAYA,IAAZ;AACA,eAAKU,SAAL,GAAiBA,SAAjB;AACA,eAAKD,QAAL,GAAgBA,QAAhB;;AAEA,eAAK,IAAIQ,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAGnB,KAApB,EAA2BmB,CAAC,EAA5B,EAAgC;AAC5B,iBAAKhB,QAAL,CAAciB,IAAd,CAAmB,KAAKtB,KAAL,CAAWuB,cAAX,CAA0BtB,QAAQ,GAAGoB,CAArC,CAAnB;AACH;;AAED,cAAI,CAAC,KAAKG,MAAV,EAAkB;AACd,iBAAKA,MAAL,GAAc,KAAKN,IAAL,CAAUO,YAAV,CAAuBpC,MAAvB,CAAd;AACA,iBAAKmC,MAAL,CAAYE,QAAZ,GAAuBrC,MAAM,CAACsC,QAAP,CAAgBC,GAAvC;AACA,iBAAKJ,MAAL,CAAYK,IAAZ,GAAmB,KAAnB;AACH;AACJ;AAED;AACJ;AACA;;;AACIC,QAAAA,cAAc,GAAG,CACb;AACH;AAED;AACJ;AACA;AACA;AACA;;;AACIC,QAAAA,OAAO,CAACC,IAAD,EAAOC,IAAP,EAAiB;AAAA,cAAVA,IAAU;AAAVA,YAAAA,IAAU,GAAH,CAAG;AAAA;;AACpB,cAAIA,IAAI,KAAK,CAAb,EAAgB;AACZ,iBAAKT,MAAL,CAAYE,QAAZ,GAAuBrC,MAAM,CAACsC,QAAP,CAAgBO,MAAvC;AACA,iBAAKhB,IAAL,CAAUC,YAAV,CAAuB3B,WAAvB,EAAoC2C,cAApC,CAAmDH,IAAnD;AACH,WAHD,MAGO,IAAIC,IAAI,KAAK,CAAb,EAAgB;AACnB,iBAAK5B,QAAL,CAAc+B,OAAd,CAAuBC,KAAD,IAAW;AAC7BA,cAAAA,KAAK,CAACC,WAAN,GAAoB,EAApB;AACAD,cAAAA,KAAK,CAACE,QAAN,GAAiB,EAAjB;AACAF,cAAAA,KAAK,CAACG,SAAN,GAAkB,EAAlB;AACAH,cAAAA,KAAK,CAACI,UAAN,GAAmB,EAAnB;AACH,aALD;AAMA,iBAAKjB,MAAL,CAAYkB,IAAZ,GAAmBrD,MAAM,CAACsD,IAAP,CAAYC,MAA/B;AACA,iBAAKpB,MAAL,CAAYE,QAAZ,GAAuBrC,MAAM,CAACsC,QAAP,CAAgBO,MAAvC;AACA,iBAAKhB,IAAL,CAAUC,YAAV,CAAuB3B,WAAvB,EAAoC2C,cAApC,CAAmDH,IAAnD;AACH;AACJ;AAED;AACJ;AACA;AACA;;;AACIa,QAAAA,QAAQ,CAACC,KAAD,EAAQ;AACZ,eAAKtB,MAAL,CAAYN,IAAZ,CAAiB4B,KAAjB,GAAyBA,KAAzB;AACH;AAED;AACJ;AACA;AACA;;;AACIC,QAAAA,KAAK,CAACxC,YAAD,EAAmB;AAAA,cAAlBA,YAAkB;AAAlBA,YAAAA,YAAkB,GAAH,CAAG;AAAA;;AACpB,eAAKD,SAAL,GAAiB,CAAjB;AACA,eAAKC,YAAL,GAAoBA,YAAY,GAAG,CAAf,GAAmBA,YAAnB,GAAkCjB,KAAK,CAACkB,cAA5D;AACA,eAAKC,IAAL,GAAY,CAAZ;AACA,eAAKC,KAAL,GAAa,CAAb;AACA,eAAKc,MAAL,CAAYwB,WAAZ,GAA0B,KAAK3C,QAAL,CAAc,CAAd,CAA1B;AACA,eAAKa,IAAL,CAAUC,YAAV,CAAuBzB,SAAvB,EAAkC0B,OAAlC,GAA4C,GAA5C;;AACA,cAAI,KAAKN,SAAT,EAAoB;AAChB,iBAAKA,SAAL;AACH;;AACD,eAAKH,QAAL,GAAgB,KAAhB;AACH;AAED;AACJ;AACA;AACA;;;AACIsC,QAAAA,IAAI,CAACC,OAAD,EAAkB;AAAA,cAAjBA,OAAiB;AAAjBA,YAAAA,OAAiB,GAAP,KAAO;AAAA;;AAClB,cAAIA,OAAJ,EAAa;AACT3D,YAAAA,KAAK,CAAC4D,eAAN,CAAsB,KAAKjC,IAA3B;AACAzB,YAAAA,KAAK,CAAC,KAAKyB,IAAL,CAAUC,YAAV,CAAuBzB,SAAvB,CAAD,CAAL,CACK0D,EADL,CACQ,IAAI;AAAA;AAAA,0CAAWC,eADvB,EACwC;AAAEjC,cAAAA,OAAO,EAAE;AAAX,aADxC,EAEKkC,KAFL;AAGH,WALD,MAKO;AACH,iBAAKpC,IAAL,CAAUC,YAAV,CAAuBzB,SAAvB,EAAkC0B,OAAlC,GAA4C,CAA5C;AACH;;AACD,cAAI,KAAKP,QAAT,EAAmB;AACf,iBAAKA,QAAL;AACH;;AACD,eAAKF,QAAL,GAAgB,KAAhB;AACH;AAED;AACJ;AACA;;;AACI4C,QAAAA,WAAW,GAAG;AACV,eAAK5C,QAAL,GAAgB,IAAhB;AACH;AAED;AACJ;AACA;AACA;;;AACI6C,QAAAA,SAAS,GAAG;AACR,iBAAO,KAAKtC,IAAL,CAAUC,YAAV,CAAuBzB,SAAvB,EAAkC0B,OAAlC,GAA4C,GAAnD;AACH;AAED;AACJ;AACA;AACA;;;AACIqC,QAAAA,eAAe,CAACC,EAAD,EAAK;AAChB,cAAI,KAAKxC,IAAL,CAAUC,YAAV,CAAuBzB,SAAvB,EAAkC0B,OAAlC,GAA4C,CAAhD,EAAmD;AAC/C,iBAAKX,IAAL,IAAaiD,EAAb;;AACA,gBAAI,KAAKjD,IAAL,IAAa,KAAKN,QAAtB,EAAgC;AAC5B,mBAAKO,KAAL;;AACA,kBAAI,KAAKA,KAAL,IAAc,KAAKR,KAAvB,EAA8B;AAC1B,qBAAKI,SAAL;AACA,qBAAKI,KAAL,GAAa,CAAb;;AACA,oBAAI,KAAKN,IAAT,EAAe;AACX,uBAAKA,IAAL;AACH;;AACD,oBAAI,KAAKO,QAAT,EAAmB;AACf,uBAAKsC,IAAL;AACH;;AACD,oBAAI,KAAK3C,SAAL,IAAkB,KAAKC,YAA3B,EAAyC;AACrC,uBAAK0C,IAAL;AACH;AACJ,eAZD,MAYO;AACH,qBAAKxC,IAAL,GAAY,CAAZ;AACA,qBAAKe,MAAL,CAAYwB,WAAZ,GAA0B,KAAK3C,QAAL,CAAc,KAAKK,KAAnB,CAA1B;;AACA,oBAAI,KAAKK,MAAL,IAAe,CAAnB,EAAsB;AAClB,uBAAKS,MAAL,CAAYN,IAAZ,CAAiByC,KAAjB,GAAyB,KAAK5C,MAA9B;AACH;;AACD,oBAAI,KAAKC,OAAL,IAAgB,CAApB,EAAuB;AACnB,uBAAKQ,MAAL,CAAYN,IAAZ,CAAiB0C,MAAjB,GAA0B,KAAK5C,OAA/B;AACH;AACJ;AACJ;AACJ;AACJ;AAED;AACJ;AACA;AACA;;;AACI6C,QAAAA,MAAM,CAACH,EAAD,EAAK;AACP,cAAI;AAAA;AAAA,sCAAUI,QAAd,EAAwB;AACpB,iBAAKL,eAAL,CAAqBC,EAArB;AACH;AACJ;;AAxL8C,O;;;;;iBAEtC,I", "sourcesContent": ["import { _decorator, Component, Sprite, Vec2, macro, Color, Size, Tween, UITransform, tween, UIOpacity } from 'cc';\r\nimport GameConfig from '../../const/GameConfig';\r\nimport { GameConst } from '../../const/GameConst';\r\n\r\nconst { ccclass, property } = _decorator;\r\n\r\n@ccclass('PfFrameAnim')\r\nexport default class PfFrameAnim extends Component {\r\n    @property(Sprite)\r\n    sprite = null;\r\n\r\n    atlas = null; // 图集\r\n    imageSrc = ''; // 图片前缀\r\n    count = 0; // 帧数\r\n    duration = 0; // 每帧持续时间\r\n    call = null; // 播放完成回调\r\n    imageArr = []; // 存储帧的数组\r\n    playTimes = 0; // 当前播放次数\r\n    maxPlayTimes = macro.REPEAT_FOREVER; // 最大播放次数\r\n    time = 0; // 当前帧时间\r\n    index = 0; // 当前帧索引\r\n    willStop = false; // 是否即将停止\r\n    selfUpdate = true; // 是否自动更新\r\n    stopCall = null; // 停止时的回调\r\n    resetCall = null; // 重置时的回调\r\n    sWidth = -1; // 自定义宽度\r\n    sHeight = -1; // 自定义高度\r\n\r\n\r\n    /**\r\n     * 初始化帧动画\r\n     * @param {SpriteAtlas} atlas 图集\r\n     * @param {string} imageSrc 图片前缀\r\n     * @param {number} count 帧数\r\n     * @param {number} duration 每帧持续时间\r\n     * @param {Function} call 播放完成回调\r\n     * @param {Function} stopCall 停止回调\r\n     * @param {Function} resetCall 重置回调\r\n     */\r\n    init(atlas, imageSrc, count, duration, call = null, stopCall = null, resetCall = null) {\r\n        this.node.getComponent(UIOpacity).opacity = 0;\r\n        this.atlas = atlas;\r\n        this.imageSrc = imageSrc;\r\n        this.count = count;\r\n        this.duration = duration;\r\n        this.call = call;\r\n        this.resetCall = resetCall;\r\n        this.stopCall = stopCall;\r\n\r\n        for (let i = 0; i < count; i++) {\r\n            this.imageArr.push(this.atlas.getSpriteFrame(imageSrc + i));\r\n        }\r\n\r\n        if (!this.sprite) {\r\n            this.sprite = this.node.addComponent(Sprite);\r\n            this.sprite.sizeMode = Sprite.SizeMode.RAW;\r\n            this.sprite.trim = false;\r\n        }\r\n    }\r\n\r\n    /**\r\n     * 设置混合模式\r\n     */\r\n    dstBlendFactor() {\r\n        // this.sprite.dstBlendFactor = macro.ONE;\r\n    }\r\n\r\n    /**\r\n     * 设置大小\r\n     * @param {Size} size 大小\r\n     * @param {number} mode 模式（1: 自定义大小，2: 切片模式）\r\n     */\r\n    setSize(size, mode = 1) {\r\n        if (mode === 1) {\r\n            this.sprite.sizeMode = Sprite.SizeMode.CUSTOM;\r\n            this.node.getComponent(UITransform).setContentSize(size);\r\n        } else if (mode === 2) {\r\n            this.imageArr.forEach((frame) => {\r\n                frame.insetBottom = 20;\r\n                frame.insetTop = 20;\r\n                frame.insetLeft = 20;\r\n                frame.insetRight = 20;\r\n            });\r\n            this.sprite.type = Sprite.Type.SLICED;\r\n            this.sprite.sizeMode = Sprite.SizeMode.CUSTOM;\r\n            this.node.getComponent(UITransform).setContentSize(size);\r\n        }\r\n    }\r\n\r\n    /**\r\n     * 设置颜色\r\n     * @param {Color} color 颜色\r\n     */\r\n    setColor(color) {\r\n        this.sprite.node.color = color;\r\n    }\r\n\r\n    /**\r\n     * 重置动画\r\n     * @param {number} maxPlayTimes 最大播放次数\r\n     */\r\n    reset(maxPlayTimes = 0) {\r\n        this.playTimes = 0;\r\n        this.maxPlayTimes = maxPlayTimes > 0 ? maxPlayTimes : macro.REPEAT_FOREVER;\r\n        this.time = 0;\r\n        this.index = 0;\r\n        this.sprite.spriteFrame = this.imageArr[0];\r\n        this.node.getComponent(UIOpacity).opacity = 255;\r\n        if (this.resetCall) {\r\n            this.resetCall();\r\n        }\r\n        this.willStop = false;\r\n    }\r\n\r\n    /**\r\n     * 停止动画\r\n     * @param {boolean} fadeOut 是否渐隐停止\r\n     */\r\n    stop(fadeOut = false) {\r\n        if (fadeOut) {\r\n            Tween.stopAllByTarget(this.node);\r\n            tween(this.node.getComponent(UIOpacity))\r\n                .to(4 * GameConfig.ActionFrameTime, { opacity: 0 })\r\n                .start();\r\n        } else {\r\n            this.node.getComponent(UIOpacity).opacity = 0;\r\n        }\r\n        if (this.stopCall) {\r\n            this.stopCall();\r\n        }\r\n        this.willStop = false;\r\n    }\r\n\r\n    /**\r\n     * 设置即将停止\r\n     */\r\n    setWillStop() {\r\n        this.willStop = true;\r\n    }\r\n\r\n    /**\r\n     * 检查是否正在播放\r\n     * @returns {boolean} 是否正在播放\r\n     */\r\n    isPlaying() {\r\n        return this.node.getComponent(UIOpacity).opacity > 100;\r\n    }\r\n\r\n    /**\r\n     * 更新动画逻辑\r\n     * @param {number} dt 时间增量\r\n     */\r\n    updateGameLogic(dt) {\r\n        if (this.node.getComponent(UIOpacity).opacity > 0) {\r\n            this.time += dt;\r\n            if (this.time >= this.duration) {\r\n                this.index++;\r\n                if (this.index >= this.count) {\r\n                    this.playTimes++;\r\n                    this.index = 0;\r\n                    if (this.call) {\r\n                        this.call();\r\n                    }\r\n                    if (this.willStop) {\r\n                        this.stop();\r\n                    }\r\n                    if (this.playTimes >= this.maxPlayTimes) {\r\n                        this.stop();\r\n                    }\r\n                } else {\r\n                    this.time = 0;\r\n                    this.sprite.spriteFrame = this.imageArr[this.index];\r\n                    if (this.sWidth >= 0) {\r\n                        this.sprite.node.width = this.sWidth;\r\n                    }\r\n                    if (this.sHeight >= 0) {\r\n                        this.sprite.node.height = this.sHeight;\r\n                    }\r\n                }\r\n            }\r\n        }\r\n    }\r\n\r\n    /**\r\n     * 每帧更新\r\n     * @param {number} dt 时间增量\r\n     */\r\n    update(dt) {\r\n        if (GameConst.GameAble) {\r\n            this.updateGameLogic(dt);\r\n        }\r\n    }\r\n}"]}