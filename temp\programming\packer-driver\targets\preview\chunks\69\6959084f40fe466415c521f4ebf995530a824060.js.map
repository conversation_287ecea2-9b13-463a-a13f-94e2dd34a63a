{"version": 3, "sources": ["file:///E:/M2Game/Client/assets/scripts/GameOld/world/level/bg_layer/Background.ts"], "names": ["ChangeBgSpeedMsg", "_decorator", "Component", "Enum", "SpriteFrame", "<PERSON><PERSON><PERSON><PERSON>", "Message", "ccclass", "property", "speedModifier", "e<PERSON><PERSON><PERSON>", "BackgroundLayerData", "displayName", "tooltip", "type", "BG_Far", "Background", "onChangeBgSpeedMsgHandler", "e", "console", "log", "onEnable", "on", "onDisable", "off", "update", "deltaTime", "layers", "for<PERSON>ach", "layer", "tick"], "mappings": ";;;mJAKaA,gB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AALJC,MAAAA,U,OAAAA,U;AAAYC,MAAAA,S,OAAAA,S;AAAWC,MAAAA,I,OAAAA,I;AAAMC,MAAAA,W,OAAAA,W;;AAE7BC,MAAAA,e,iBAAAA,e;;AACUC,MAAAA,O,iBAAAA,O;;;;;;;;;OAFb;AAAEC,QAAAA,OAAF;AAAWC,QAAAA;AAAX,O,GAAwBP,U;;kCAIjBD,gB,GAAN,MAAMA,gBAAN,CACP;AAAA;AAAA,eACIS,aADJ,GAC6B,GAD7B;AAAA;;AAAA,O;;wBAIYC,M,0BAAAA,M;AAAAA,QAAAA,M,CAAAA,M;AAAAA,QAAAA,M,CAAAA,M;AAAAA,QAAAA,M,CAAAA,M;AAAAA,QAAAA,M,CAAAA,M;AAAAA,QAAAA,M,CAAAA,M;AAAAA,QAAAA,M,CAAAA,M;AAAAA,QAAAA,M,CAAAA,M;eAAAA,M;;;qCAWCC,mB,WADZJ,OAAO,CAAC,qBAAD,C,UAEHC,QAAQ,CAAC;AAAEI,QAAAA,WAAW,EAAE,oBAAf;AAAqCC,QAAAA,OAAO,EAAE;AAA9C,OAAD,C,UAGRL,QAAQ,CAAC;AAACM,QAAAA,IAAI,EAACX,IAAI,CAACO,MAAD;AAAV,OAAD,C,UAIRF,QAAQ,CAAC;AAACM,QAAAA,IAAI,EAAC,CAACV,WAAD;AAAN,OAAD,C,4BATb,MACaO,mBADb,CACiC;AAAA;AAAA;;AAAA;;AAO7B;AAP6B;AAAA;;AAAA,O;;;;;iBAEH,G;;;;;;;iBAGFD,MAAM,CAACK,M;;;;;;;iBAID,E;;;;4BAKrBC,U,YADZT,OAAO,CAAC,YAAD,C,UAMHC,QAAQ,CAAC;AAACM,QAAAA,IAAI,EAAC;AAAA;AAAA;AAAN,OAAD,C,UAGRN,QAAQ,CAAC;AAACM,QAAAA,IAAI,EAACH;AAAN,OAAD,C,6BATb,MACaK,UADb,SACgCd,SADhC,CAC0C;AAAA;AAAA;;AAAA;;AAAA;;AAAA;;AAAA,eAW9Be,yBAX8B,GAWDC,CAAD,IAAyB;AACzD,iBAAKT,aAAL,GAAqBS,CAAC,CAACT,aAAvB;AACAU,YAAAA,OAAO,CAACC,GAAR,CAAY,uBAAuB,KAAKX,aAAxC;AACH,WAdqC;AAAA;;AAgB5BY,QAAAA,QAAQ,GAAS;AACvB;AAAA;AAAA,kCAAQC,EAAR,CAAWtB,gBAAX,EAA6B,KAAKiB,yBAAlC;AACH;;AAESM,QAAAA,SAAS,GAAS;AACxB;AAAA;AAAA,kCAAQC,GAAR,CAAYxB,gBAAZ,EAA8B,KAAKiB,yBAAnC;AACH;;AAEDQ,QAAAA,MAAM,CAACC,SAAD,EAAoB;AACtB,eAAKC,MAAL,CAAYC,OAAZ,CAAoBC,KAAK,IAAI;AACzBA,YAAAA,KAAK,CAACC,IAAN,CAAWJ,SAAS,GAAG,KAAKjB,aAA5B;AACH,WAFD;AAGH;;AA5BqC,O,iFAErCD,Q;;;;;iBACwB,G;;;;;;;iBAGG,E;;;;;;;iBAGQ,E", "sourcesContent": ["import { _decorator, Component, Enum, Sprite<PERSON>rame, Node } from 'cc';\r\nconst { ccclass, property } = _decorator;\r\nimport { BackgroundLayer } from './BackgroundLayer';\r\nimport { IMessage, Message } from '../../base/Messaging';\r\n\r\nexport class ChangeBgSpeedMsg implements IMessage\r\n{\r\n    speedModifier : number = 1.0;\r\n}\r\n\r\nexport enum eLayer {\r\n    BG_VeryFar = -4,\r\n    BG_Far = -3,\r\n    BG_Close = -2,\r\n    BG_VeryClose = -1,\r\n    Player = 0,\r\n    FG_Close = 1,\r\n    FG_VeryClose = 2,\r\n}\r\n\r\n@ccclass('BackgroundLayerData')\r\nexport class BackgroundLayerData {\r\n    @property({ displayName: \"Scroll Duration(s)\", tooltip: \"Scroll duration in seconds\" })\r\n    scrollDuration : number = 1.0;\r\n\r\n    @property({type:Enum(eLayer)})\r\n    sortingLayer : eLayer = eLayer.BG_Far;\r\n\r\n    // for changing background\r\n    @property({type:[SpriteFrame]})\r\n    spriteFrames: SpriteFrame[] = [];\r\n}\r\n\r\n\r\n@ccclass('Background')\r\nexport class Background extends Component {\r\n    \r\n    @property\r\n    speedModifier : number = 1.0\r\n\r\n    @property({type:[BackgroundLayer]})\r\n    layers: BackgroundLayer[] = []\r\n\r\n    @property({type:BackgroundLayerData})\r\n    layerDatas: BackgroundLayerData[] = []\r\n\r\n    private onChangeBgSpeedMsgHandler = (e: ChangeBgSpeedMsg) => {\r\n        this.speedModifier = e.speedModifier;\r\n        console.log(\"changing speed to \" + this.speedModifier);\r\n    }\r\n\r\n    protected onEnable(): void {\r\n        Message.on(ChangeBgSpeedMsg, this.onChangeBgSpeedMsgHandler);\r\n    }\r\n\r\n    protected onDisable(): void {\r\n        Message.off(ChangeBgSpeedMsg, this.onChangeBgSpeedMsgHandler);\r\n    }\r\n\r\n    update(deltaTime: number) {\r\n        this.layers.forEach(layer => {\r\n            layer.tick(deltaTime * this.speedModifier);\r\n        });\r\n    }\r\n}\r\n\r\n\r\n"]}