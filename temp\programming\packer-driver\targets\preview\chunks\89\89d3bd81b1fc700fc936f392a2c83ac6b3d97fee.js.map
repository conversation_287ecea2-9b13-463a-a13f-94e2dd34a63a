{"version": 3, "sources": ["file:///E:/M2Game/Client/assets/scripts/Game/ui/plane/ShadowPlane.ts"], "names": ["_decorator", "Sprite", "Plane", "GameFunc", "ccclass", "property", "ShadowPlane", "m_screenDatas", "m_config", "m_fireState", "m_changeOver", "attackRatio", "isFrist", "bulletOpacity", "m_opacity", "m_data", "enemy", "onLoad", "start", "new_uuid", "uuid", "init", "isActive", "node", "active"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;AAASA,MAAAA,U,OAAAA,U;AAAYC,MAAAA,M,OAAAA,M;;AACdC,MAAAA,K;;AAEEC,MAAAA,Q,iBAAAA,Q;;;;;;;;;OACH;AAAEC,QAAAA,OAAF;AAAWC,QAAAA;AAAX,O,GAAwBL,U;;yBAGTM,W,WADpBF,OAAO,CAAC,aAAD,C,UAEHC,QAAQ,CAACJ,MAAD,C,2BAFb,MACqBK,WADrB;AAAA;AAAA,0BAC+C;AAAA;AAAA;;AAAA;;AAI3C;AAJ2C,eAK3CC,aAL2C,GAKpB,EALoB;AAAA,eAM3CC,QAN2C,GAM3B,IAN2B;AAAA,eAO3CC,WAP2C,GAOxB,IAPwB;AAAA,eAQ3CC,YAR2C,GAQnB,KARmB;AAAA,eAS3CC,WAT2C,GASrB,CATqB;AAAA,eAU3CC,OAV2C,GAUzB,CAVyB;AAAA,eAW3CC,aAX2C,GAWnB,GAXmB;AAAA,eAY3CC,SAZ2C,GAYvB,GAZuB;AAa3C;AAb2C,eAc3CC,MAd2C;AAAA,eAe3CC,KAf2C;AAAA;;AAiB3CC,QAAAA,MAAM,GAAG,CACL;AACA;AACH;;AAEDC,QAAAA,KAAK,GAAG;AACJ,eAAKF,KAAL,GAAa,KAAb,CADI,CAEJ;;AACA,eAAKG,QAAL,GAAgB;AAAA;AAAA,oCAASC,IAAzB;AACH;;AAEDC,QAAAA,IAAI,CAACC,QAAD,EAA4B;AAAA,cAA3BA,QAA2B;AAA3BA,YAAAA,QAA2B,GAAP,KAAO;AAAA;;AAC5B;AACA;AACA;AACA;AACA;AACA;AACA;AACI,eAAKC,IAAL,CAAUC,MAAV,GAAmB,KAAnB,CARwB,CAS5B;AAEA;AACA;AACA;AACA;AACA;AACH,SA5C0C,CA8C3C;AACA;AAEA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AAEA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AAEA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AAEA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;AAhY2C,O;;;;;iBAEzB,I", "sourcesContent": ["import { _decorator, Sprite } from \"cc\";\r\nimport Plane from \"./Plane\";\r\nimport { GameIns } from \"../../GameIns\";\r\nimport { GameFunc } from \"../../GameFunc\";\r\nconst { ccclass, property } = _decorator;\r\n\r\n@ccclass(\"ShadowPlane\")\r\nexport default class ShadowPlane extends Plane {\r\n    @property(Sprite)\r\n    skinImg: Sprite = null;\r\n\r\n    // m_fires: FireShells[] = [];\r\n    m_screenDatas: any[] = [];\r\n    m_config: any = null;\r\n    m_fireState: any = null;\r\n    m_changeOver: boolean = false;\r\n    attackRatio: number = 1;\r\n    isFrist: number = 0;\r\n    bulletOpacity: number = 255;\r\n    m_opacity: number = 255;\r\n    // m_skillFires: Map<number, FireShells[]> = new Map();\r\n    m_data;\r\n    enemy: boolean;\r\n\r\n    onLoad() {\r\n        // this.m_config = GameIns.mainPlaneManager.mainRecord;\r\n        // this.m_data = GameIns.mainPlaneManager.data;\r\n    }\r\n\r\n    start() {\r\n        this.enemy = false;\r\n        // this.initPlane(true);\r\n        this.new_uuid = GameFunc.uuid;\r\n    }\r\n\r\n    init(isActive: boolean = false) {\r\n        // if (SkillManager.default.me.getMySkillByType(23)) {\r\n        //     this.node.active = true;\r\n        //     if (BattleManager.default.me.isContinue) {\r\n        //         this.hideSelf();\r\n        //     }\r\n        //     this.initPlane(isActive);\r\n        // } else {\r\n            this.node.active = false;\r\n        // }\r\n\r\n        // this.m_skillFires.forEach((fires) => {\r\n        //     fires.forEach((fire) => {\r\n        //         fire.clearData();\r\n        //     });\r\n        // });\r\n    }\r\n\r\n    // async initPlane(isActive: boolean = false) {\r\n    //     this.m_config = MainPlaneManager.default.me.mainRecord;\r\n\r\n    //     await GameIns.default.loadManager.setImage(\r\n    //         this.skinImg,\r\n    //         `${this.m_config.zjimage}_yz`,\r\n    //         `package_mainPlane_trans_${this.m_config.type}`\r\n    //     );\r\n\r\n    //     if (!GameConfig.default.isHD) {\r\n    //         const spriteFrame = this.skinImg.getComponent(Sprite).spriteFrame;\r\n    //         if (spriteFrame) {\r\n    //             this.skinImg.node.width = 0.66667 * spriteFrame.getOriginalSize().width;\r\n    //             this.skinImg.node.height = 0.66667 * spriteFrame.getOriginalSize().height;\r\n    //         }\r\n    //     }\r\n\r\n    //     this.getFireState();\r\n    //     this.changeScreenLv(this.m_data.screenLv);\r\n    //     this.mechaShoadow(isActive);\r\n    // }\r\n\r\n    // mechaShoadow(isActive: boolean = false) {\r\n    //     const isGoldType = GameIns.battleManager.gameType !== GameEnum.GameType.Gold || this.m_config.type !== 710;\r\n    //     const conditionMet = this.m_config.type === 710 && GameIns.gameDataManager.isConditionOver;\r\n\r\n    //     if (isGoldType && !conditionMet && isActive) {\r\n    //         this.skinImg.enabled = true;\r\n    //         this.skinImg.node.destroyAllChildren();\r\n    //     } else {\r\n    //         this.skinImg.enabled = false;\r\n    //         MainPlaneManager.MainPlaneMgr.plane.setUnit(this.skinImg.node, this.m_config.type === 709);\r\n\r\n    //         const grayMaterial = Material.getBuiltinMaterial(\"2d-gray-sprite\");\r\n    //         this.skinImg.node.children.forEach((child) => {\r\n    //             switch (this.m_config.type) {\r\n    //                 case 708:\r\n    //                 case 709:\r\n    //                     child.scale = 0.64;\r\n    //                     break;\r\n    //                 case 710:\r\n    //                     child.scale = 0.66667;\r\n    //                     child.y = -60;\r\n    //                     break;\r\n    //                 case 711:\r\n    //                     child.scale = 0.64;\r\n    //                     break;\r\n    //                 case 712:\r\n    //                     child.scale = 0.64;\r\n    //                     break;\r\n    //                 case 713:\r\n    //                     child.scale = 0.53333;\r\n    //                     child.y = 9;\r\n    //                     break;\r\n    //                 case 714:\r\n    //                 case 715:\r\n    //                     child.scale = 0.64;\r\n    //                     child.y = 9;\r\n    //                     break;\r\n    //                 default:\r\n    //                     child.scale = 1;\r\n    //             }\r\n\r\n    //             child.active = this.m_config.type === 710 || GameIns.gameDataManager.isConditionOver;\r\n    //             child.opacity = 178.5;\r\n\r\n    //             child.children.forEach((grandChild) => {\r\n    //                 const sprite = grandChild.getComponent(Sprite);\r\n    //                 if (sprite) {\r\n    //                     sprite.setMaterial(0, grayMaterial);\r\n    //                 }\r\n    //             });\r\n    //         });\r\n\r\n    //         this.skinImg.node.scale = 1;\r\n    //     }\r\n    // }\r\n\r\n    // // ...前面的代码...\r\n\r\n    // /**\r\n    //  * 显示或隐藏机甲\r\n    //  * @param isVisible 是否显示\r\n    //  */\r\n    // mechaShow(isVisible: boolean) {\r\n    //     if (this.m_config.type === 710 || GameIns.gameDataManager.isConditionOver) {\r\n    //         this.skinImg.enabled = !isVisible;\r\n    //         this.skinImg.node.children.forEach((child) => {\r\n    //             child.active = isVisible;\r\n    //         });\r\n    //     }\r\n    // }\r\n\r\n    // /**\r\n    //  * 根据屏幕等级切换攻击点\r\n    //  * @param level 屏幕等级\r\n    //  */\r\n    // changeScreenLv(level: number) {\r\n    //     if (level !== 0) {\r\n    //         this.m_screenDatas = [];\r\n    //         const key = GameIns.gameDataManager.isConditionOver ? \"transatk\" : \"shiftingatk\";\r\n    //         const attackData = this.m_config[`${key}${level}`];\r\n\r\n    //         for (let i = 0; i < attackData.length; i += 8) {\r\n    //             const segment = attackData.slice(i, i + 8);\r\n    //             this.m_screenDatas.push(segment);\r\n    //         }\r\n\r\n    //         this.m_screenDatas.forEach((data, index) => {\r\n    //             if (this.m_fires[index] == null) {\r\n    //                 this.createAttackPoint(data);\r\n    //             } else {\r\n    //                 this.changeScreen(index, data);\r\n    //             }\r\n    //         });\r\n\r\n    //         for (let i = this.m_screenDatas.length; i < this.m_fires.length; i++) {\r\n    //             this.changeScreen(i, null);\r\n    //         }\r\n    //     }\r\n    // }\r\n\r\n    // /**\r\n    //  * 切换屏幕攻击点\r\n    //  * @param index 索引\r\n    //  * @param data 攻击点数据\r\n    //  */\r\n    // changeScreen(index: number, data: any) {\r\n    //     if (data == null) {\r\n    //         if (index < this.m_fires.length) {\r\n    //             const fireData = this.m_screenDatas[index];\r\n    //             this.m_fires[index].setData(fireData, this.m_fireState, false, this);\r\n    //         }\r\n    //     } else if (index < this.m_fires.length) {\r\n    //         this.m_fires[index].setData(data, this.m_fireState, false, this);\r\n    //     } else {\r\n    //         this.createAttackPoint(data);\r\n    //     }\r\n    // }\r\n\r\n    // /**\r\n    //  * 创建攻击点\r\n    //  * @param data 攻击点数据\r\n    //  */\r\n    // createAttackPoint(data: any) {\r\n    //     const node = new Node();\r\n    //     node.parent = this.node;\r\n    //     const fireShell = node.addComponent(FireShells);\r\n    //     fireShell.setData(data, this.m_fireState, false, this);\r\n    //     this.m_fires.push(fireShell);\r\n    // }\r\n\r\n    // /**\r\n    //  * 获取火力状态\r\n    //  */\r\n    // getFireState() {\r\n    //     const attackLv = SkillManager.default.me.getLv(SkillManager.SkillType.mainAttack);\r\n    //     const hpAttackLv = SkillManager.default.me.getLv(SkillManager.SkillType.mainHpAttack);\r\n    //     const speedLv = SkillManager.default.me.getLv(SkillManager.SkillType.mainSpeed);\r\n    //     let attack = this.m_data.initAtk1 > 0 ? this.m_data.attack1 : this.m_data.attack2;\r\n\r\n    //     if (\r\n    //         MainPlaneManager.MainPlaneMgr.idToType(this.m_data.id) === 711 &&\r\n    //         (GameIns.gameDataManager.isMechaOver || MainPlaneManager.MainPlaneMgr.checkPlayMechaAnim())\r\n    //     ) {\r\n    //         attack = this.m_data.attack2;\r\n    //     }\r\n\r\n    //     const cirtLv = SkillManager.default.me.getLv(SkillManager.SkillType.addCirt);\r\n\r\n    //     this.m_fireState = {\r\n    //         attack: attack * this.attackRatio,\r\n    //         attackLv,\r\n    //         hpAttackLv,\r\n    //         speedLv,\r\n    //         cirtLv,\r\n    //     };\r\n    // }\r\n\r\n    // /**\r\n    //  * 刷新火力状态\r\n    //  */\r\n    // refreshFireState() {\r\n    //     this.getFireState();\r\n    //     this.m_fires.forEach((fire) => {\r\n    //         fire.setState(this.m_fireState, this);\r\n    //     });\r\n\r\n    //     const skillFires = this.m_skillFires.get(28);\r\n    //     if (skillFires) {\r\n    //         skillFires.forEach((fire) => {\r\n    //             fire.setState(this.m_fireState, this, true);\r\n    //         });\r\n    //     }\r\n    // }\r\n\r\n    // /**\r\n    //  * 播放影子瞄准动画\r\n    //  */\r\n    // playShadowAim() {\r\n    //     this.init(true);\r\n    //     this.isFrist = 1;\r\n    //     this.node.y = 0;\r\n    //     this.node.parent = GameIns.planeManager.mainPlane.node;\r\n    //     this.node.zIndex = -1;\r\n    //     this.skinImg.node.scale = 1.332;\r\n    //     this.m_changeOver = false;\r\n\r\n    //     tween(this.skinImg.node)\r\n    //         .to(UIAnimMethods.default.fromTo(0, 1), { opacity: 255 })\r\n    //         .to(UIAnimMethods.default.fromTo(1, 7), { opacity: 255 * this.m_opacity })\r\n    //         .start();\r\n\r\n    //     tween(this.node)\r\n    //         .to(UIAnimMethods.default.fromTo(0, 1), { y: 0 })\r\n    //         .to(UIAnimMethods.default.fromTo(1, 7), { y: -25 })\r\n    //         .to(UIAnimMethods.default.fromTo(7, 10), { y: -20, x: -5 })\r\n    //         .to(UIAnimMethods.default.fromTo(10, 12), { y: -20, x: 0 })\r\n    //         .call(BattleManager.default.me.rogueui.bootDown.bind(BattleManager.default.me.rogueui))\r\n    //         .call(this.playShadowAim2.bind(this))\r\n    //         .start();\r\n    // }\r\n\r\n    // /**\r\n    //  * 播放影子瞄准动画的第二阶段\r\n    //  */\r\n    // playShadowAim2() {\r\n    //     tween(this.node)\r\n    //         .delay(3)\r\n    //         .to(UIAnimMethods.default.fromTo(0, 10), { y: 0 })\r\n    //         .call(this.changeFather.bind(this))\r\n    //         .start();\r\n    // }\r\n\r\n    // /**\r\n    //  * 更改父节点\r\n    //  */\r\n    // changeFather() {\r\n    //     this.node.parent = BattleLayer.default.me.selfPlaneLayer;\r\n    //     this.node.zIndex = -1;\r\n    //     this.node.y = GameIns.planeManager.mainPlane.node.y;\r\n    //     this.m_changeOver = true;\r\n    // }\r\n\r\n    // /**\r\n    //  * 隐藏自身\r\n    //  */\r\n    // hideSelf() {\r\n    //     this.node.y = 0;\r\n    //     this.node.parent = GameIns.planeManager.mainPlane.node;\r\n    //     this.node.opacity = 0;\r\n    //     this.skinImg.node.scale = 0;\r\n    //     this.m_changeOver = false;\r\n    // }\r\n\r\n    // /**\r\n    //  * 显示自身\r\n    //  */\r\n    // showSelf() {\r\n    //     this.isFrist = 1;\r\n    //     if (SkillManager.default.me.getSkillLevelById(23)) {\r\n    //         this.node.active = true;\r\n    //     }\r\n    //     this.node.opacity = this.m_opacity;\r\n    //     this.changeFather();\r\n    // }\r\n\r\n    // /**\r\n    //  * 根据技能添加攻击点\r\n    //  * @param skillId 技能 ID\r\n    //  * @param data 攻击点数据\r\n    //  * @param extra 额外数据\r\n    //  */\r\n    // addAttackPointBySkill(skillId: number, data: any[], extra: any[]) {\r\n    //     const skillFires = this.m_skillFires.get(skillId);\r\n    //     this.getFireState();\r\n    //     this.m_fireState.extra = extra || [];\r\n\r\n    //     if (!skillFires || skillFires.length === 0) {\r\n    //         data.forEach((point) => {\r\n    //             const node = new Node();\r\n    //             node.parent = this.node;\r\n    //             const fireShell = node.addComponent(FireShells);\r\n    //             fireShell.setData(point, this.m_fireState, false, this);\r\n    //             this.m_skillFires.set(skillId, [fireShell]);\r\n    //         });\r\n    //     } else {\r\n    //         const maxLength = Math.max(skillFires.length, data.length);\r\n    //         const updatedFires: FireShells[] = [];\r\n\r\n    //         for (let i = 0; i < maxLength; i++) {\r\n    //             if (data.length > i && skillFires.length > i) {\r\n    //                 skillFires[i].setData(data[i], this.m_fireState, false, this);\r\n    //                 updatedFires.push(skillFires[i]);\r\n    //             } else if (data.length > i) {\r\n    //                 const node = new Node();\r\n    //                 node.parent = this.node;\r\n    //                 const fireShell = node.addComponent(FireShells);\r\n    //                 fireShell.setData(data[i], this.m_fireState, false, this);\r\n    //                 updatedFires.push(fireShell);\r\n    //             } else {\r\n    //                 skillFires[i].node.destroy();\r\n    //             }\r\n    //         }\r\n\r\n    //         this.m_skillFires.set(skillId, updatedFires);\r\n    //     }\r\n    // }\r\n\r\n    // /**\r\n    //  * 退出战斗\r\n    //  */\r\n    // battleQuit() {\r\n    //     this.m_data.die = false;\r\n    //     this.m_skillFires.forEach((fires) => {\r\n    //         fires.forEach((fire) => {\r\n    //             fire.node.parent = null;\r\n    //             fire.node.destroy();\r\n    //         });\r\n    //     });\r\n    //     this.m_skillFires.clear();\r\n    //     this.node.active = false;\r\n    // }\r\n\r\n    // /**\r\n    //  * 更新方法\r\n    //  * @param deltaTime 每帧时间\r\n    //  */\r\n    // update(deltaTime: number) {\r\n    //     if (this.node.active) {\r\n    //         this.node.x = -GameIns.planeManager.mainPlane.node.x;\r\n    //         if (this.m_changeOver) {\r\n    //             this.node.y = GameIns.planeManager.mainPlane.node.y;\r\n    //             this.skinImg.node.scale = GameIns.planeManager.mainPlane.skin.scale;\r\n    //         } else {\r\n    //             this.skinImg.node.scale = GameIns.planeManager.mainPlane.skin.scale * this.isFrist;\r\n    //         }\r\n    //     }\r\n    // }\r\n}"]}