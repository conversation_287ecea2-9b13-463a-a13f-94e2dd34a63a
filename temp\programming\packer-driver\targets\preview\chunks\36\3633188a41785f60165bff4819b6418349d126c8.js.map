{"version": 3, "sources": ["file:///E:/M2Game/Client/assets/scripts/GameOld/world/weapon/Emitter.ts"], "names": ["_decorator", "Prefab", "Weapon", "ccclass", "executeInEditMode", "property", "eEmitterStatus", "Emitter", "type", "displayName", "status", "None", "statusElapsedTime", "totalElapsedTime", "changeStatus", "canEmit", "emit", "tryEmit", "emitSingle", "index", "onObjectInit", "onObjectDestroy", "isInScreen", "update", "deltaTime", "emitterData", "duration", "Completed", "updateStatusNone", "Prewarm", "updateStatusPrewarm", "Emitting", "updateStatusEmitting", "Loop<PERSON>ndReached", "updateStatusLoopEndReached", "updateStatusCompleted", "initialDelay", "isPreWarm", "preWarmDuration"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;AAASA,MAAAA,U,OAAAA,U;AAAkBC,MAAAA,M,OAAAA,M;;AAClBC,MAAAA,M,iBAAAA,M;;;;;;;;;OAEH;AAAEC,QAAAA,OAAF;AAAWC,QAAAA,iBAAX;AAA8BC,QAAAA;AAA9B,O,GAA2CL,U;;gCAErCM,c,0BAAAA,c;AAAAA,QAAAA,c,CAAAA,c;AAAAA,QAAAA,c,CAAAA,c;AAAAA,QAAAA,c,CAAAA,c;AAAAA,QAAAA,c,CAAAA,c;AAAAA,QAAAA,c,CAAAA,c;eAAAA,c;;;yBAMUC,O,WAFrBJ,OAAO,CAAC,SAAD,C,UAIHE,QAAQ,CAAC;AAACG,QAAAA,IAAI,EAAEP,MAAP;AAAeQ,QAAAA,WAAW,EAAE;AAA5B,OAAD,C,gBAHZL,iB,qBADD,MAEsBG,OAFtB;AAAA;AAAA,4BAE6C;AAAA;AAAA;;AAAA;;AAAA;;AAAA,eAQzCG,MARyC,GAQhBJ,cAAc,CAACK,IARC;AAAA,eASzCC,iBATyC,GASb,CATa;AAAA,eAUzCC,gBAVyC,GAUd,CAVc;AAAA;;AAYzC;AACJ;AACA;AACIC,QAAAA,YAAY,CAACJ,MAAD,EAAyB;AACjC,eAAKA,MAAL,GAAcA,MAAd;AACA,eAAKE,iBAAL,GAAyB,CAAzB;AACH;;AAESG,QAAAA,OAAO,GAAY;AACzB;AACA;AACA,iBAAO,IAAP;AACH;AAED;AACJ;AACA;;;AACcC,QAAAA,IAAI,GAAS,CAEtB;;AAESC,QAAAA,OAAO,GAAY;AACzB,cAAI,KAAKF,OAAL,EAAJ,EAAoB;AAChB,iBAAKC,IAAL;AACA,mBAAO,IAAP;AACH;;AACD,iBAAO,KAAP;AACH;;AAESE,QAAAA,UAAU,CAACC,KAAD,EAAe,CAElC,CA3CwC,CA6CzC;;;AACUC,QAAAA,YAAY,GAAS,CAC3B;AACH;;AAESC,QAAAA,eAAe,GAAS,CAC9B;AACH;AAED;AACJ;AACA;;;AACcC,QAAAA,UAAU,GAAa;AAC7B;AACA,iBAAO,IAAP;AACH;;AAESC,QAAAA,MAAM,CAACC,SAAD,EAA0B;AACtC,cAAI,CAAC,KAAKC,WAAV,EAAuB;AACnB;AACH;;AAED,eAAKb,iBAAL,IAA0BY,SAA1B;AACA,eAAKX,gBAAL,IAAyBW,SAAzB,CANsC,CAQtC;;AACA,cAAI,KAAKX,gBAAL,GAAwB,KAAKY,WAAL,CAAiBC,QAA7C,EAAuD;AACnD,iBAAKZ,YAAL,CAAkBR,cAAc,CAACqB,SAAjC;AACA;AACH;;AAED,kBAAQ,KAAKjB,MAAb;AAEI,iBAAKJ,cAAc,CAACK,IAApB;AACI,mBAAKiB,gBAAL;AACA;;AACJ,iBAAKtB,cAAc,CAACuB,OAApB;AACI,mBAAKC,mBAAL;AACA;;AACJ,iBAAKxB,cAAc,CAACyB,QAApB;AACI,mBAAKC,oBAAL;AACA;;AACJ,iBAAK1B,cAAc,CAAC2B,cAApB;AACI,mBAAKC,0BAAL;AACA;;AACJ,iBAAK5B,cAAc,CAACqB,SAApB;AACI,mBAAKQ,qBAAL;AACA;;AACJ;AACI;AAlBR;AAoBH;;AAESP,QAAAA,gBAAgB,GAAG;AACzB,cAAI,KAAKhB,iBAAL,IAA0B,KAAKa,WAAL,CAAiBW,YAA/C,EAA6D;AACzD,iBAAKtB,YAAL,CAAkBR,cAAc,CAACuB,OAAjC;AACH;AACJ;;AAESC,QAAAA,mBAAmB,GAAG;AAC5B,cAAI,CAAC,KAAKL,WAAL,CAAiBY,SAAtB,EACI,KAAKvB,YAAL,CAAkBR,cAAc,CAACyB,QAAjC,EADJ,KAEK;AACD,gBAAI,KAAKnB,iBAAL,IAA0B,KAAKa,WAAL,CAAiBa,eAA/C,EAAgE;AAC5D,mBAAKxB,YAAL,CAAkBR,cAAc,CAACyB,QAAjC;AACH;AACJ;AACJ;;AAESC,QAAAA,oBAAoB,GAAG,CAEhC;;AAESE,QAAAA,0BAA0B,GAAG,CAEtC;;AAESC,QAAAA,qBAAqB,GAAG,CAEjC;;AA5HwC,O;;;;;iBAGlB,I;;sFAEtB9B,Q;;;;;iBAC0B,I", "sourcesContent": ["import { _decorator, Node, Prefab } from 'cc';\r\nimport { Weapon } from './Weapon';\r\nimport { EmitterData } from '../../../Game/data/EmitterData';\r\nconst { ccclass, executeInEditMode, property } = _decorator;\r\n\r\nexport enum eEmitterStatus {\r\n    None, Prewarm, Emitting, LoopEndReached, Completed\r\n}\r\n\r\n@ccclass('Emitter')\r\n@executeInEditMode\r\nexport abstract class Emitter extends Weapon {\r\n\r\n    @property({type: Prefab, displayName: \"Bullet Prefab\"})\r\n    bulletPrefab: Prefab = null;\r\n\r\n    @property\r\n    emitterData: EmitterData = null;\r\n\r\n    status: eEmitterStatus = eEmitterStatus.None;\r\n    statusElapsedTime: number = 0;\r\n    totalElapsedTime: number = 0;\r\n\r\n    /**\r\n     * public apis\r\n     */\r\n    changeStatus(status: eEmitterStatus) {\r\n        this.status = status;\r\n        this.statusElapsedTime = 0;\r\n    }\r\n\r\n    protected canEmit(): boolean {\r\n        // 检查是否可以触发发射\r\n        // Override this method in subclasses to add custom trigger conditions\r\n        return true;\r\n    }\r\n\r\n    /**\r\n     * TODO: implement bullet emission logic in subclasses\r\n     */\r\n    protected emit(): void {\r\n\r\n    }\r\n\r\n    protected tryEmit(): boolean {\r\n        if (this.canEmit()) {\r\n            this.emit();\r\n            return true;\r\n        }\r\n        return false;\r\n    }\r\n\r\n    protected emitSingle(index:number) {\r\n\r\n    }\r\n\r\n    // Implementation of CObject abstract methods\r\n    protected onObjectInit(): void {\r\n        // Override in subclasses if needed\r\n    }\r\n\r\n    protected onObjectDestroy(): void {\r\n        // Clean up any scheduled callbacks\r\n    }\r\n\r\n    /**\r\n     * Return true if this.node is in screen\r\n     */\r\n    protected isInScreen() : boolean {\r\n        // TODO: Get mainCamera.containsNode(this.node)\r\n        return true;\r\n    }\r\n\r\n    protected update(deltaTime: number): void {\r\n        if (!this.emitterData) {\r\n            return;\r\n        }\r\n\r\n        this.statusElapsedTime += deltaTime;\r\n        this.totalElapsedTime += deltaTime;\r\n\r\n        // TODO: 这里待确认duration的机制\r\n        if (this.totalElapsedTime > this.emitterData.duration) {\r\n            this.changeStatus(eEmitterStatus.Completed);\r\n            return;\r\n        }\r\n\r\n        switch (this.status)\r\n        {\r\n            case eEmitterStatus.None:\r\n                this.updateStatusNone();\r\n                break;\r\n            case eEmitterStatus.Prewarm:\r\n                this.updateStatusPrewarm();\r\n                break;\r\n            case eEmitterStatus.Emitting:\r\n                this.updateStatusEmitting();\r\n                break;\r\n            case eEmitterStatus.LoopEndReached:\r\n                this.updateStatusLoopEndReached();\r\n                break;\r\n            case eEmitterStatus.Completed:\r\n                this.updateStatusCompleted();\r\n                break;\r\n            default:\r\n                break;\r\n        }\r\n    }\r\n\r\n    protected updateStatusNone() {\r\n        if (this.statusElapsedTime >= this.emitterData.initialDelay) {\r\n            this.changeStatus(eEmitterStatus.Prewarm);\r\n        }\r\n    }\r\n\r\n    protected updateStatusPrewarm() {\r\n        if (!this.emitterData.isPreWarm)\r\n            this.changeStatus(eEmitterStatus.Emitting);\r\n        else {\r\n            if (this.statusElapsedTime >= this.emitterData.preWarmDuration) {\r\n                this.changeStatus(eEmitterStatus.Emitting);\r\n            }\r\n        }\r\n    }\r\n\r\n    protected updateStatusEmitting() {\r\n        \r\n    }\r\n\r\n    protected updateStatusLoopEndReached() {\r\n        \r\n    }\r\n\r\n    protected updateStatusCompleted() {\r\n        \r\n    }\r\n}\r\n"]}