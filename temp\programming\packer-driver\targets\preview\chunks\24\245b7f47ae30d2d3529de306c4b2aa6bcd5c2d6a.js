System.register(["__unresolved_0", "cc", "__unresolved_1", "__unresolved_2", "__unresolved_3", "__unresolved_4", "__unresolved_5"], function (_export, _context) {
  "use strict";

  var _reporterNs, _cclegacy, __checkObsolete__, __checkObsoleteInNamespace__, _decorator, Node, tween, v3, UITransform, BossHurt, Tools, BossCollider, GameIns, GameConfig, _dec, _class, _crd, ccclass, property, BossBase;

  function _reportPossibleCrUseOfBossHurt(extras) {
    _reporterNs.report("BossHurt", "./BossHurt", _context.meta, extras);
  }

  function _reportPossibleCrUseOfTools(extras) {
    _reporterNs.report("Tools", "../../../utils/Tools", _context.meta, extras);
  }

  function _reportPossibleCrUseOfBossCollider(extras) {
    _reporterNs.report("<PERSON><PERSON><PERSON>ider", "./<PERSON><PERSON><PERSON>ider", _context.meta, extras);
  }

  function _reportPossibleCrUseOfGameIns(extras) {
    _reporterNs.report("GameIns", "../../../GameIns", _context.meta, extras);
  }

  function _reportPossibleCrUseOfGameConfig(extras) {
    _reporterNs.report("GameConfig", "../../../const/GameConfig", _context.meta, extras);
  }

  return {
    setters: [function (_unresolved_) {
      _reporterNs = _unresolved_;
    }, function (_cc) {
      _cclegacy = _cc.cclegacy;
      __checkObsolete__ = _cc.__checkObsolete__;
      __checkObsoleteInNamespace__ = _cc.__checkObsoleteInNamespace__;
      _decorator = _cc._decorator;
      Node = _cc.Node;
      tween = _cc.tween;
      v3 = _cc.v3;
      UITransform = _cc.UITransform;
    }, function (_unresolved_2) {
      BossHurt = _unresolved_2.default;
    }, function (_unresolved_3) {
      Tools = _unresolved_3.Tools;
    }, function (_unresolved_4) {
      BossCollider = _unresolved_4.default;
    }, function (_unresolved_5) {
      GameIns = _unresolved_5.GameIns;
    }, function (_unresolved_6) {
      GameConfig = _unresolved_6.default;
    }],
    execute: function () {
      _crd = true;

      _cclegacy._RF.push({}, "6928eMzVdZBgJ1G/sz/ZgEt", "BossBase", undefined);

      __checkObsolete__(['_decorator', 'Component', 'Vec2', 'Node', 'v2', 'tween', 'v3', 'UITransform']);

      ({
        ccclass,
        property
      } = _decorator);

      _export("default", BossBase = (_dec = ccclass("BossBase"), _dec(_class = class BossBase extends (_crd && BossHurt === void 0 ? (_reportPossibleCrUseOfBossHurt({
        error: Error()
      }), BossHurt) : BossHurt) {
        constructor() {
          super(...arguments);
          this.baseData = null;
          this.propertyRate = [];
          this.tip = "";
          this.exp = 0;
          this.uiNode = null;
          this.unitArr = [];
          this.unitPosArr = [];
          this.spineArr = [];
          this.actArr = [];
          this.collideArr = [];
          this.blastParam = [];
          this.blastShake = [];
          this.onlyLoot = [];
          this.lootArr = [];
          this.lootParam0 = [167, 250, 0.6];
          this.lootParam1 = [250, 350, 1.2];
          this.dieBullet = false;
          this.bullets = [];
          this.m_totalHp = 0;
          this.m_curHp = 0;
          this.m_swordUnit = [];
        }

        /**
         * 设置 Boss 数据
         * @param data Boss 数据
         */
        setData(data) {
          this.baseData = data;
          this.exp = this.baseData.exp;
          this.onlyLoot = this.baseData.onlyLoot;
          this.blastParam = this.baseData.blastParam;
          this.blastShake = this.baseData.blastShake;
          this.lootArr = this.baseData.lootArr;
          this.attack = data.attack;
          this.collideAtk = data.collideAttack;

          if (this.baseData.lootParam0.length > 0) {
            this.lootParam0 = this.baseData.lootParam0;
          }

          if (this.baseData.lootParam1.length > 0) {
            this.lootParam1 = this.baseData.lootParam1;
          }
        }
        /**
         * 添加单元
         * @param unit 单元
         * @param position 单元位置
         */


        addUnit(unit, position) {
          this.unitArr.push(unit);

          if (position) {
            this.unitPosArr.push(position);
          }
        }
        /**
         * 移除单元
         * @param unit 单元
         */


        removeUnit(unit) {
          var index = this.unitArr.indexOf(unit);

          if (index >= 0) {
            this.unitArr.splice(index, 1);

            if (this.unitPosArr.length > index) {
              this.unitPosArr.splice(index, 1);
            }
          }
        }
        /**
         * 清空单元
         */


        clearUnit() {
          this.unitArr.splice(0);
          this.unitPosArr.splice(0);
        }
        /**
         * 添加骨骼动画
         * @param spine 骨骼动画
         */


        addSpine(spine) {
          this.spineArr.push(spine);
        }
        /**
         * 移除骨骼动画
         * @param spine 骨骼动画
         */


        removeSpine(spine) {
          (_crd && Tools === void 0 ? (_reportPossibleCrUseOfTools({
            error: Error()
          }), Tools) : Tools).arrRemove(this.spineArr, spine);
        }
        /**
         * 添加行为
         * @param action 行为
         */


        addAction(action) {
          this.actArr.push(action);
        }
        /**
         * 移除行为
         * @param action 行为
         */


        removeAction(action) {
          (_crd && Tools === void 0 ? (_reportPossibleCrUseOfTools({
            error: Error()
          }), Tools) : Tools).arrRemove(this.actArr, action);
        }
        /**
         * 创建自身碰撞体
         */


        createSelfCollides() {
          for (var collideData of this.baseData.collideArr) {
            var collider = this.addCollider(collideData);
            this.collideArr.push(collider);
          }
        }
        /**
         * 设置自身碰撞体是否可用
         * @param enable 是否可用
         */


        setSelfColliderAble(enable) {
          for (var collider of this.collideArr) {
            if (collider) {
              collider.setCollideAble(enable);
            }
          }
        }
        /**
         * 添加碰撞体
         * @param data 碰撞体数据
         */


        addCollider(data) {
          var node = new Node();
          node.addComponent(UITransform);
          this.node.addChild(node);
          var collider = (_crd && Tools === void 0 ? (_reportPossibleCrUseOfTools({
            error: Error()
          }), Tools) : Tools).addScript(node, _crd && BossCollider === void 0 ? (_reportPossibleCrUseOfBossCollider({
            error: Error()
          }), BossCollider) : BossCollider);
          collider.create(this, data);
          collider.collideAtk = this.baseData.collideAttack;
          return collider;
        }
        /**
         * 设置位置
         * @param x X 坐标
         * @param y Y 坐标
         * @param update 是否更新
         */


        setPos(x, y, update) {
          if (update === void 0) {
            update = false;
          }

          this.node.setPosition(x, y);
        }
        /**
         * 设置属性倍率
         * @param rates 属性倍率
         */


        setPropertyRate(rates) {
          this.propertyRate = rates;
        }
        /**
         * 设置提示信息
         * @param tip 提示信息
         */


        setTip(tip) {
          this.tip = tip;
        }
        /**
         * 变形结束
         */


        transformEnd() {
          if (this.tip !== "") {
            (_crd && GameIns === void 0 ? (_reportPossibleCrUseOfGameIns({
              error: Error()
            }), GameIns) : GameIns).battleManager.bossChangeFinish(this.tip);
          } else {
            (_crd && GameIns === void 0 ? (_reportPossibleCrUseOfGameIns({
              error: Error()
            }), GameIns) : GameIns).battleManager.bossFightStart();
          }
        }
        /**
         * Boss 死亡逻辑
         */


        toDie() {
          if (!this.isDead) {
            this.isDead = true;
            this.setSelfColliderAble(false); // this.checkLoot();

            this.onDie();
          }
        }
        /**
         * 开始战斗
         */


        startBattle() {}
        /**
         * Boss 死亡回调
         */


        onDie() {
          // TaskManager.taskNumberChange(TaskType.KillBoss, 1);
          // TaskManager.taskNumberChange(TaskType.KillPlane, 1);
          // TaskManager.achievementNumberChange(AchievementType.KillBoss, 1);
          // TaskManager.achievementNumberChange(AchievementType.KillEnemy, 1);
          // this.playDieAnim();
          // GameData.killEnemyNumber += 1;
          // for (const plane of EnemyManager.planes) {
          //     plane.die(EnemyDestroyType.Die);
          // }
          // if (this.exp > 0) {
          //     LootManager.addExp(this.exp);
          // }
          // MainPlaneManager.checkKillHp();
          // MainPlaneManager.checkKillAtk();
          this.removeBullets();
        }
        /**
         * 移除子弹
         */


        removeBullets() {
          for (var bullet of this.bullets) {
            bullet.dieRemove();
          }

          this.bullets.splice(0);
        }
        /**
         * 播放死亡动画
         */


        playDieAnim() {// for (let i = 0; i < this.blastParam.length; i++) {
          //     const blast = this.blastParam[i];
          //     const delay = blast[3] * GameConfig.ActionFrameTime;
          //     this.scheduleOnce(() => {
          //         EnemyEffectLayer.addBlastEffect(this, blast[2], {
          //             x: blast[0],
          //             y: blast[1],
          //             scale: blast[4],
          //             angle: blast[5],
          //         });
          //         if (blast[6] > 0) {
          //             GameIns.audioManager.playEffect(`blast${blast[6]}`);
          //         }
          //     }, delay);
          // }
          // for (const shake of this.blastShake) {
          //     const delay = shake.x * GameConfig.ActionFrameTime;
          //     this.scheduleOnce(() => {
          //         MainCamera.shake1(EnemyManager.shakeParam[shake.y]);
          //     }, delay);
          // }
        }
        /**
         * 播放震动动画
         */


        playShakeAnim() {
          var frameTime = (_crd && GameConfig === void 0 ? (_reportPossibleCrUseOfGameConfig({
            error: Error()
          }), GameConfig) : GameConfig).ActionFrameTime;

          if (this.uiNode) {
            tween(this.uiNode).to(2 * frameTime, {
              position: v3(-3, -2)
            }).to(2 * frameTime, {
              position: v3(11, -14),
              angle: 1
            }).to(2 * frameTime, {
              position: v3(7, 4)
            }).to(2 * frameTime, {
              position: v3(20, -9),
              angle: 0
            }).to(2 * frameTime, {
              position: v3(29, 7)
            }).to(frameTime, {
              position: v3(13, -5)
            }).to(frameTime, {
              position: v3(17, 2)
            }).to(frameTime, {
              position: v3(4, -6)
            }).to(frameTime, {
              position: v3(14, 4)
            }).to(frameTime, {
              position: v3(-1, -4)
            }).to(frameTime, {
              position: v3(5, 6)
            }).to(frameTime, {
              position: v3(-3, -5)
            }).to(frameTime, {
              position: v3(1, 3)
            }).to(frameTime, {
              position: v3(-7, -6)
            }).to(frameTime, {
              position: v3(0, 2)
            }).to(frameTime, {
              position: v3(-3, -4)
            }).delay(frameTime).to(frameTime, {
              position: v3(0, 0)
            }).start();
          }
        }
        /**
         * 播放白屏死亡动画
         */


        playDieWhiteAnim() {// this.scheduleOnce(() => {
          //     EffectLayer.showWhiteScreen(4 * GameConfig.ActionFrameTime, 255);
          //     if (this.uiNode) {
          //         this.uiNode.opacity = 0;
          //     }
          // }, 41 * GameConfig.ActionFrameTime);
        }
        /**
         * 死亡动画结束回调
         */


        onDieAnimEnd() {} // /**
        //  * 改变血量
        //  * @param delta 血量变化值
        //  */
        // hpChange(delta: number) {
        //     this.m_curHp += delta;
        //     if (this.m_curHp < 0) {
        //         this.m_curHp = 0;
        //     }
        //     BossBattleManager.hpChange(delta, this.node);
        // }
        // /**
        //  * 检查掉落物品
        //  */
        // checkLoot() {
        //     if (BattleManager.isGameType(GameType.Boss)) {
        //         BossBattleManager.lootForDie(this.node.convertToWorldSpaceAR(Vec2.ZERO));
        //     } else {
        //         let allBossesDead = true;
        //         for (const boss of BossManager.bosses) {
        //             if (!boss.isDead) {
        //                 allBossesDead = false;
        //                 break;
        //             }
        //         }
        //         if (this.isDead && allBossesDead) {
        //             const loot = LootManager.checkLoot(EnemyScale.Large, true);
        //             LootManager.addBossProp(this.node.position, this.lootParam1, loot);
        //         }
        //     }
        // }
        // /**
        //  * 更新游戏逻辑
        //  * @param deltaTime 每帧时间
        //  */
        // updateGameLogic(deltaTime: number) {
        //     for (const collider of this.collideArr) {
        //         collider.updateGameLogic(deltaTime);
        //     }
        // }
        // /**
        //  * 获取所有单元
        //  */
        // getUnits(): any[] {
        //     return [];
        // }
        // /**
        //  * 获取所有碰撞体
        //  */
        // getCollides(): any[] {
        //     return this.collideArr;
        // }
        // /**
        //  * 获取血量百分比
        //  */
        // getHpPercent(): number {
        //     return this.m_curHp / this.m_totalHp;
        // }

        /**
         * 添加子弹
         * @param bullet 子弹对象
         */


        addBullet(bullet) {
          if (this.bullets) {
            this.bullets.push(bullet);
          }
        } // /**
        //  * 移除子弹
        //  * @param bullet 子弹对象
        //  */
        // removeBullet(bullet: any) {
        //     if (this.bullets) {
        //         const index = this.bullets.indexOf(bullet);
        //         if (index >= 0) {
        //             this.bullets.splice(index, 1);
        //         }
        //     }
        // }
        // /**
        //  * 初始化剑气
        //  */
        // initSword(): Node {
        //     const swordNode = instantiate(GameIns.loadManager.getRes("swordBlast", Prefab));
        //     swordNode.name = "sword";
        //     swordNode.getComponent(SwordBlast).init();
        //     return swordNode;
        // }
        // /**
        //  * 播放剑气待机动画
        //  * @param range 范围
        //  * @param play 是否播放动画
        //  */
        // playSwordIdleAnim(range: number[], play: boolean = true) {
        //     this.m_swordUnit.splice(0);
        //     const availableUnits = [];
        //     const availablePositions = [];
        //     for (const unit of this.unitArr) {
        //         if (!unit.isDead && unit.getCollideAble && unit.getCollideAble()) {
        //             availableUnits.push(unit);
        //             const position = this.unitPosArr[this.unitArr.indexOf(unit)];
        //             if (position) {
        //                 availablePositions.push(position);
        //             }
        //         }
        //     }
        //     if (availableUnits.length > 0) {
        //         const selectedUnits = [];
        //         const selectedPositions = [];
        //         const count = Tools.random_int(range[0], range[1]);
        //         for (let i = 0; i < count; i++) {
        //             const index = Tools.random_int(0, availableUnits.length - 1);
        //             this.m_swordUnit.push(availableUnits[index]);
        //             selectedUnits.push(availableUnits[index]);
        //             availableUnits.splice(index, 1);
        //             if (availablePositions[index]) {
        //                 selectedPositions.push(availablePositions[index]);
        //                 availablePositions.splice(index, 1);
        //             }
        //             if (availableUnits.length === 0) {
        //                 break;
        //             }
        //         }
        //         if (play) {
        //             for (let i = 0; i < this.m_swordUnit.length; i++) {
        //                 const unit = this.m_swordUnit[i];
        //                 let sword = unit.node.getChildByName("sword");
        //                 if (!sword) {
        //                     sword = this.initSword();
        //                     unit.node.addChild(sword, 10);
        //                 }
        //                 sword.angle = Tools.random_int(0, 359);
        //                 sword.scale = Tools.random_int(8, 11) / 10;
        //                 const position = unit.getColliderPos ? unit.getColliderPos() : selectedPositions[i];
        //                 if (position) {
        //                     sword.x = position.x;
        //                     sword.y = position.y;
        //                 }
        //                 const swordComponent = sword.getComponent(SwordBlast);
        //                 if (swordComponent) {
        //                     swordComponent.playIdleAnim();
        //                 }
        //             }
        //         }
        //     }
        // }
        // /**
        //  * 播放剑气受伤动画
        //  * @param delay 延迟时间
        //  */
        // playSwordHurtAnim(delay: number = 0) {
        //     for (const unit of this.m_swordUnit) {
        //         const sword = unit.node.getChildByName("sword");
        //         if (sword) {
        //             const swordComponent = sword.getComponent(SwordBlast);
        //             this.scheduleOnce(() => {
        //                 if (swordComponent) {
        //                     swordComponent.playHurtAnim();
        //                 }
        //             }, delay);
        //         }
        //     }
        // }
        // /**
        //  * 剑气伤害
        //  * @param damage 伤害值
        //  */
        // swordHurt(damage: number) {
        //     for (const unit of this.m_swordUnit) {
        //         if (unit && unit.hurt) {
        //             unit.hurt(damage);
        //         }
        //     }
        //     this.m_swordUnit.splice(0);
        // }
        // /**
        //  * 剑气结束
        //  */
        // swordOver() {
        //     for (const spine of this.spineArr) {
        //         if (spine && spine.node && spine.node.active) {
        //             spine.timeScale = 1;
        //         }
        //     }
        //     this.spineArr.splice(0);
        // }

        /**
         * 暂停
         */


        pause() {}
        /**
         * 恢复
         */


        resume() {}

      }) || _class));

      _cclegacy._RF.pop();

      _crd = false;
    }
  };
});
//# sourceMappingURL=245b7f47ae30d2d3529de306c4b2aa6bcd5c2d6a.js.map