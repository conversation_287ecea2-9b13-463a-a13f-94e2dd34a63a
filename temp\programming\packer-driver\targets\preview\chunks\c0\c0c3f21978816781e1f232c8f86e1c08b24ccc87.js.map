{"version": 3, "sources": ["file:///E:/M2Game/Client/assets/scripts/Game/ui/plane/boss/BossUnitBase.ts"], "names": ["_decorator", "Vec2", "tween", "misc", "UIOpacity", "v2", "BossHurt", "ColliderComp", "GameConfig", "GameIns", "GameConst", "Bullet", "EnemyEffectLayer", "ccclass", "property", "BossUnitBase", "owner", "collide<PERSON>omp", "_curHp", "_maxHp", "defence", "blastParam", "blastShake", "_whiteNode", "_winkCount", "_bW<PERSON><PERSON><PERSON>e", "_winkAct", "initWinkWhite", "whiteNode", "to", "opacity", "ActionFrameTime", "getComponent", "initCollide", "data", "addComp", "init", "setData", "node", "position", "x", "y", "m_comps", "for<PERSON>ach", "comp", "setPropertyRate", "rates", "length", "attack", "_collideAtk", "curHp", "value", "maxHp", "getCollideAble", "enabled", "setCollideAble", "setCollideOffset", "setOffset", "getAngleToOwner", "angle", "parent", "getScenePos", "pos", "rotate", "degreesToRadians", "scaleX", "scaleY", "name", "scale", "updateGameLogic", "deltaTime", "isDead", "update", "refreshCollideOffset", "offsetX", "offsetY", "currentNode", "onCollide", "collider", "active", "entity", "damage", "getAttack", "Math", "max", "playerWARatio", "hurtEffectManager", "createHurtNumByType", "hurt", "changeHp", "onHurt", "<PERSON><PERSON><PERSON><PERSON>", "amount", "change", "newHp", "onHpChange", "checkHp", "hpChange", "die", "m_colliderPreTime", "delete", "new_uuid", "onDie", "playDieAnim", "clone", "start", "param", "index", "effectData", "callback", "onDieAnimEnd", "scheduleOnce", "me", "addBlastEffect", "getHpPercent"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAASA,MAAAA,U,OAAAA,U;AAA6BC,MAAAA,I,OAAAA,I;AAAMC,MAAAA,K,OAAAA,K;AAAOC,MAAAA,I,OAAAA,I;AAAMC,MAAAA,S,OAAAA,S;AAAWC,MAAAA,E,OAAAA,E;;AAC7DC,MAAAA,Q;;AACEC,MAAAA,Y,iBAAAA,Y;;AACFC,MAAAA,U;;AACEC,MAAAA,O,iBAAAA,O;;AACAC,MAAAA,S,iBAAAA,S;;AACFC,MAAAA,M;;AACAC,MAAAA,gB;;;;;;;;;OAED;AAAEC,QAAAA,OAAF;AAAWC,QAAAA;AAAX,O,GAAwBd,U;;yBAGTe,Y,WADpBF,OAAO,CAAC,cAAD,C,gBAAR,MACqBE,YADrB;AAAA;AAAA,gCACmD;AAAA;AAAA;AAAA,eAC/CC,KAD+C,GAClC,IADkC;AAAA,eAE/CC,WAF+C,GAEnB,IAFmB;AAAA,eAG/CC,MAH+C,GAG9B,CAH8B;AAAA,eAI/CC,MAJ+C,GAI9B,CAJ8B;AAAA,eAK/CC,OAL+C,GAK7B,CAL6B;AAAA,eAM/CC,UAN+C,GAM3B,EAN2B;AAAA,eAO/CC,UAP+C,GAO3B,EAP2B;AAAA,eAQ/CC,UAR+C,GAQ5B,IAR4B;AAAA,eAS/CC,UAT+C,GAS1B,CAT0B;AAAA,eAU/CC,WAV+C,GAUxB,KAVwB;AAAA,eAW/CC,QAX+C,GAW/B,IAX+B;AAAA;;AAa/CC,QAAAA,aAAa,CAACC,SAAD,EAAwB;AACjC,eAAKL,UAAL,GAAkBK,SAAlB;AACA,eAAKF,QAAL,GAAgBxB,KAAK,GAChB2B,EADW,CACR,CADQ,EACL;AAAEC,YAAAA,OAAO,EAAE;AAAX,WADK,EAEXD,EAFW,CAER,IAAI;AAAA;AAAA,wCAAWE,eAFP,EAEwB;AAAED,YAAAA,OAAO,EAAE;AAAX,WAFxB,CAAhB;;AAGA,cAAI,KAAKP,UAAT,EAAqB;AACjB,iBAAKA,UAAL,CAAgBS,YAAhB,CAA6B5B,SAA7B,EAAwC0B,OAAxC,GAAkD,CAAlD;AACH;AACJ;;AAEDG,QAAAA,WAAW,CAACC,IAAD,EAAkB;AACzB,cAAI,CAAC,KAAKjB,WAAV,EAAuB;AACnB,iBAAKA,WAAL,GAAmB,KAAKkB,OAAL;AAAA;AAAA,8CAA2B;AAAA;AAAA,+CAA3B,CAAnB;AACH;;AACD,eAAKlB,WAAL,CAAiBmB,IAAjB,CAAsB,IAAtB;AACA,eAAKnB,WAAL,CAAiBoB,OAAjB,CAAyBH,IAAzB,EAA+B,KAAKlB,KAApC,EAA2CX,EAAE,CAAC,KAAKiC,IAAL,CAAUC,QAAV,CAAmBC,CAApB,EAAuB,KAAKF,IAAL,CAAUC,QAAV,CAAmBE,CAA1C,CAA7C;AACA,eAAKC,OAAL,CAAaC,OAAb,CAAsBC,IAAD,IAAU;AAC3BA,YAAAA,IAAI,CAACR,IAAL,CAAU,IAAV;AACH,WAFD;AAGH;;AAEDS,QAAAA,eAAe,CAACC,KAAD,EAAwB;AACnC,cAAIA,KAAK,CAACC,MAAN,GAAe,CAAnB,EAAsB;AAClB,iBAAK7B,MAAL,IAAe4B,KAAK,CAAC,CAAD,CAApB;AACA,iBAAK3B,MAAL,GAAc,KAAKD,MAAnB;AACA,iBAAK8B,MAAL,IAAeF,KAAK,CAAC,CAAD,CAApB;AACA,iBAAKG,WAAL,IAAoBH,KAAK,CAAC,CAAD,CAAzB;AACH;AACJ;;AAEQ,YAALI,KAAK,GAAW;AAChB,iBAAO,KAAKhC,MAAZ;AACH;;AAEQ,YAALgC,KAAK,CAACC,KAAD,EAAgB;AACrB,eAAKjC,MAAL,GAAciC,KAAd;AACH;;AAEQ,YAALC,KAAK,GAAW;AAChB,iBAAO,KAAKjC,MAAZ;AACH;;AAEQ,YAALiC,KAAK,CAACD,KAAD,EAAgB;AACrB,eAAKhC,MAAL,GAAcgC,KAAd;AACH;;AAEDE,QAAAA,cAAc,GAAY;AACtB,iBAAO,KAAKpC,WAAL,GAAmB,KAAKA,WAAL,CAAiBqC,OAApC,GAA8C,KAArD;AACH;;AAEDC,QAAAA,cAAc,CAACD,OAAD,EAAyB;AACnC,cAAI,KAAKrC,WAAT,EAAsB;AAClB,iBAAKA,WAAL,CAAiBqC,OAAjB,GAA2BA,OAA3B;AACH;AACJ;;AAEDE,QAAAA,gBAAgB,CAAChB,CAAD,EAAYC,CAAZ,EAA6B;AACzC,cAAI,KAAKxB,WAAT,EAAsB;AAClB,iBAAKA,WAAL,CAAiBwC,SAAjB,CAA2BjB,CAA3B,EAA8BC,CAA9B;AACH;AACJ;;AAEDiB,QAAAA,eAAe,GAAW;AACtB,cAAIC,KAAK,GAAG,CAAZ;AACA,cAAIC,MAAM,GAAG,KAAKtB,IAAL,CAAUsB,MAAvB;;AACA,iBAAOA,MAAM,IAAIA,MAAM,KAAK,KAAK5C,KAAL,CAAWsB,IAAvC,EAA6C;AACzCqB,YAAAA,KAAK,IAAIC,MAAM,CAACD,KAAhB;AACAC,YAAAA,MAAM,GAAGA,MAAM,CAACA,MAAhB;AACH;;AACD,iBAAOD,KAAP;AACH;;AAEDE,QAAAA,WAAW,GAAS;AAChB,cAAIC,GAAG,GAAG,IAAI7D,IAAJ,CAAS,KAAKqC,IAAL,CAAUC,QAAV,CAAmBC,CAA5B,EAA+B,KAAKF,IAAL,CAAUC,QAAV,CAAmBE,CAAlD,EAAqDsB,MAArD,CACN5D,IAAI,CAAC6D,gBAAL,CAAsB,KAAKN,eAAL,EAAtB,CADM,CAAV;AAGA,cAAIE,MAAM,GAAG,KAAKtB,IAAL,CAAUsB,MAAvB;AACA,cAAIK,MAAM,GAAG,CAAb;AACA,cAAIC,MAAM,GAAG,CAAb;;AAEA,iBAAON,MAAM,IAAIA,MAAM,CAACO,IAAP,KAAgB,YAAjC,EAA+C;AAC3CF,YAAAA,MAAM,IAAIL,MAAM,CAACQ,KAAP,CAAa5B,CAAvB;AACA0B,YAAAA,MAAM,IAAIN,MAAM,CAACQ,KAAP,CAAa3B,CAAvB;AACAqB,YAAAA,GAAG,CAACtB,CAAJ,IAASoB,MAAM,CAACrB,QAAP,CAAgBC,CAAzB;AACAsB,YAAAA,GAAG,CAACrB,CAAJ,IAASmB,MAAM,CAACrB,QAAP,CAAgBE,CAAzB;AACAmB,YAAAA,MAAM,GAAGA,MAAM,CAACA,MAAhB;AACH;;AAEDE,UAAAA,GAAG,CAACtB,CAAJ,IAASyB,MAAT;AACAH,UAAAA,GAAG,CAACrB,CAAJ,IAASyB,MAAT;AACA,iBAAOJ,GAAP;AACH;;AAEDO,QAAAA,eAAe,CAACC,SAAD,EAA0B;AACrC,cAAI,CAAC,KAAKC,MAAV,EAAkB;AACd,iBAAK7B,OAAL,CAAaC,OAAb,CAAsBC,IAAD,IAAU;AAC3BA,cAAAA,IAAI,CAAC4B,MAAL,CAAYF,SAAZ;AACH,aAFD;;AAIA,gBAAI,KAAK7C,WAAT,EAAsB;AAClB,mBAAKD,UAAL;;AACA,kBAAI,KAAKA,UAAL,GAAkB,EAAtB,EAA0B;AACtB,qBAAKA,UAAL,GAAkB,CAAlB;AACA,qBAAKC,WAAL,GAAmB,KAAnB;AACH;AACJ;AACJ;AACJ;;AAEDgD,QAAAA,oBAAoB,GAAS;AACzB,cAAIC,OAAO,GAAG,CAAd;AACA,cAAIC,OAAO,GAAG,CAAd;AACA,cAAIV,MAAM,GAAG,CAAb;AACA,cAAIC,MAAM,GAAG,CAAb;AACA,cAAIU,WAAW,GAAG,KAAKtC,IAAvB;;AAEA,iBAAOsC,WAAW,IAAI,KAAK5D,KAApB,IAA6B,KAAKA,KAAL,CAAWsB,IAAX,KAAoBsC,WAAxD,EAAqE;AACjEF,YAAAA,OAAO,IAAIE,WAAW,CAACrC,QAAZ,CAAqBC,CAAhC;AACAmC,YAAAA,OAAO,IAAIC,WAAW,CAACrC,QAAZ,CAAqBE,CAAhC;AACAwB,YAAAA,MAAM,IAAIW,WAAW,CAACR,KAAZ,CAAkB5B,CAA5B;AACA0B,YAAAA,MAAM,IAAIU,WAAW,CAACR,KAAZ,CAAkB3B,CAA5B;AACAmC,YAAAA,WAAW,GAAGA,WAAW,CAAChB,MAA1B;AACH;;AAED,eAAKJ,gBAAL,CAAsBkB,OAAO,GAAGT,MAAhC,EAAwCU,OAAO,GAAGT,MAAlD;AACH;;AAEDW,QAAAA,SAAS,CAACC,QAAD,EAA+B;AACpC,cAAI,CAAC,KAAKP,MAAN,IAAgB,KAAKQ,MAArB,IAA+BD,QAAQ,CAACE,MAAT;AAAA;AAAA,+BAAnC,EAAsE;AAClE,gBAAIC,MAAM,GAAGH,QAAQ,CAACE,MAAT,CAAgBE,SAAhB,CAA0B,IAA1B,CAAb;AACAD,YAAAA,MAAM,GAAGE,IAAI,CAACC,GAAL,CAASH,MAAM,GAAG,EAAlB,EAAsBA,MAAM,GAAG,KAAK7D,OAApC,IAA+C;AAAA;AAAA,wCAAUiE,aAAlE;AACA;AAAA;AAAA,oCAAQC,iBAAR,CAA0BC,mBAA1B,CACI,KAAKtE,WADT,EAEI6D,QAAQ,CAACE,MAFb,EAGIC,MAHJ,EAII;AAAEzC,cAAAA,CAAC,EAAE,CAAL;AAAQC,cAAAA,CAAC,EAAE;AAAX,aAJJ;AAMA,iBAAK+C,IAAL,CAAUP,MAAV;AACH;AACJ;;AAEDO,QAAAA,IAAI,CAACP,MAAD,EAA0B;AAC1B,cAAI,KAAKV,MAAL,IAAe,CAAC,KAAKQ,MAAzB,EAAiC;AAC7B,mBAAO,KAAP;AACH;;AACD,eAAKU,QAAL,CAAc,CAACR,MAAf;AACA,eAAKS,MAAL;AACA,iBAAO,IAAP;AACH;;AAEDA,QAAAA,MAAM,GAAS;AACX,eAAKC,SAAL;AACH;;AAEDF,QAAAA,QAAQ,CAACG,MAAD,EAAuB;AAC3B,cAAIC,MAAM,GAAGD,MAAb;AACA,cAAIE,KAAK,GAAG,KAAK5E,MAAL,GAAc0E,MAA1B;;AAEA,cAAIE,KAAK,GAAG,CAAZ,EAAe;AACXD,YAAAA,MAAM,GAAG,CAAC,KAAK3E,MAAf;AACH;;AAED,eAAKA,MAAL,GAAc4E,KAAd;;AACA,cAAI,KAAK5E,MAAL,GAAc,CAAlB,EAAqB;AACjB,iBAAKA,MAAL,GAAc,CAAd;AACH;;AAED,eAAK6E,UAAL,CAAgBF,MAAhB;AACA,eAAKG,OAAL;AACH;;AAEDD,QAAAA,UAAU,CAACF,MAAD,EAAuB;AAC7B,cAAI,KAAK7E,KAAL,IAAc,KAAKA,KAAL,CAAWiF,QAA7B,EAAuC;AACnC,iBAAKjF,KAAL,CAAWiF,QAAX,CAAoBJ,MAApB;AACH;AACJ;;AAEDG,QAAAA,OAAO,GAAY;AACf,cAAI,KAAK9C,KAAL,IAAc,CAAlB,EAAqB;AACjB,iBAAKgD,GAAL;AACA,mBAAO,IAAP;AACH;;AACD,iBAAO,KAAP;AACH;;AAEDA,QAAAA,GAAG,GAAS;AACR,cAAI,CAAC,KAAK3B,MAAV,EAAkB;AAAA;;AACd,iBAAKA,MAAL,GAAc,IAAd;AACA,iBAAKhB,cAAL,CAAoB,KAApB;AACA;AAAA;AAAA,oCAAQ+B,iBAAR,CAA0Ba,iBAA1B,CAA4CC,MAA5C,sBAAmD,KAAKnF,WAAxD,qBAAmD,kBAAkB+D,MAAlB,CAAyBqB,QAA5E;AACA,iBAAKC,KAAL;AACH;AACJ;;AAEDA,QAAAA,KAAK,GAAS;AACV,eAAKC,WAAL;AACH;;AAEDZ,QAAAA,SAAS,GAAS;AACd,cAAI,CAAC,KAAKlE,WAAV,EAAuB;AACnB,iBAAKA,WAAL,GAAmB,IAAnB;;AACA,gBAAI,KAAKF,UAAL,IAAmB,KAAKG,QAA5B,EAAsC;AAClC,mBAAKA,QAAL,CAAc8E,KAAd,CAAoB,KAAKjF,UAAzB,EAAqCkF,KAArC;AACH;AACJ;AACJ;;AAEDF,QAAAA,WAAW,GAAS;AAChB,eAAKlF,UAAL,CAAgBsB,OAAhB,CAAwB,CAAC+D,KAAD,EAAQC,KAAR,KAAkB;AACtC,gBAAMC,UAAU,GAAG;AACfpE,cAAAA,CAAC,EAAEkE,KAAK,CAAC,CAAD,CADO;AAEfjE,cAAAA,CAAC,EAAEiE,KAAK,CAAC,CAAD,CAFO;AAGftC,cAAAA,KAAK,EAAEsC,KAAK,CAAC,CAAD,CAHG;AAIf/C,cAAAA,KAAK,EAAE+C,KAAK,CAAC,CAAD;AAJG,aAAnB;AAMA,gBAAMG,QAAQ,GAAGF,KAAK,KAAK,KAAKtF,UAAL,CAAgB0B,MAAhB,GAAyB,CAAnC,GAAuC,MAAM,KAAK+D,YAAL,EAA7C,GAAmE,IAApF;AAEA,iBAAKC,YAAL,CAAkB,MAAM;AACpB;AAAA;AAAA,wDAAiBC,EAAjB,CAAoBC,cAApB,CAAmC,KAAK3E,IAAxC,EAA6CoE,KAAK,CAAC,CAAD,CAAlD,EAAuDE,UAAvD,EAAmEC,QAAnE,EADoB,CAEpB;AACA;AACA;AACH,aALD,EAKGH,KAAK,CAAC,CAAD,CAAL,GAAW;AAAA;AAAA,0CAAW3E,eALzB;AAMH,WAfD,EADgB,CAkBhB;AACA;AACA;AACH;;AAED+E,QAAAA,YAAY,GAAS,CAAE;;AAEvBI,QAAAA,YAAY,GAAW;AACnB,iBAAO,KAAKhE,KAAL,GAAa,KAAKE,KAAzB;AACH;;AAvP8C,O", "sourcesContent": ["import { _decorator, Component, Node, Vec2, tween, misc, UIOpacity, v2 } from 'cc';\r\nimport BossH<PERSON> from './BossHurt';\r\nimport { ColliderComp } from '../../base/ColliderComp';\r\nimport GameConfig from '../../../const/GameConfig';\r\nimport { GameIns } from '../../../GameIns';\r\nimport { GameConst } from '../../../const/GameConst';\r\nimport Bullet from '../../bullet/Bullet';\r\nimport EnemyEffectLayer from '../enemy/EnemyEffectLayer';\r\n\r\nconst { ccclass, property } = _decorator;\r\n\r\n@ccclass('BossUnitBase')\r\nexport default class BossUnitBase extends BossHurt {\r\n    owner: any = null;\r\n    collideComp: ColliderComp = null;\r\n    _curHp: number = 0;\r\n    _maxHp: number = 0;\r\n    defence: number = 0;\r\n    blastParam: any[] = [];\r\n    blastShake: any[] = [];\r\n    _whiteNode: Node = null;\r\n    _winkCount: number = 0;\r\n    _bWinkWhite: boolean = false;\r\n    _winkAct: any = null;\r\n\r\n    initWinkWhite(whiteNode: Node): void {\r\n        this._whiteNode = whiteNode;\r\n        this._winkAct = tween()\r\n            .to(0, { opacity: 255 })\r\n            .to(3 * GameConfig.ActionFrameTime, { opacity: 0 });\r\n        if (this._whiteNode) {\r\n            this._whiteNode.getComponent(UIOpacity).opacity = 0;\r\n        }\r\n    }\r\n\r\n    initCollide(data: any): void {\r\n        if (!this.collideComp) {\r\n            this.collideComp = this.addComp(ColliderComp, new ColliderComp());\r\n        }\r\n        this.collideComp.init(this);\r\n        this.collideComp.setData(data, this.owner, v2(this.node.position.x, this.node.position.y));\r\n        this.m_comps.forEach((comp) => {\r\n            comp.init(this);\r\n        });\r\n    }\r\n\r\n    setPropertyRate(rates: number[]): void {\r\n        if (rates.length > 2) {\r\n            this._curHp *= rates[0];\r\n            this._maxHp = this._curHp;\r\n            this.attack *= rates[1];\r\n            this._collideAtk *= rates[2];\r\n        }\r\n    }\r\n\r\n    get curHp(): number {\r\n        return this._curHp;\r\n    }\r\n\r\n    set curHp(value: number) {\r\n        this._curHp = value;\r\n    }\r\n\r\n    get maxHp(): number {\r\n        return this._maxHp;\r\n    }\r\n\r\n    set maxHp(value: number) {\r\n        this._maxHp = value;\r\n    }\r\n\r\n    getCollideAble(): boolean {\r\n        return this.collideComp ? this.collideComp.enabled : false;\r\n    }\r\n\r\n    setCollideAble(enabled: boolean): void {\r\n        if (this.collideComp) {\r\n            this.collideComp.enabled = enabled;\r\n        }\r\n    }\r\n\r\n    setCollideOffset(x: number, y: number): void {\r\n        if (this.collideComp) {\r\n            this.collideComp.setOffset(x, y);\r\n        }\r\n    }\r\n\r\n    getAngleToOwner(): number {\r\n        let angle = 0;\r\n        let parent = this.node.parent;\r\n        while (parent && parent !== this.owner.node) {\r\n            angle += parent.angle;\r\n            parent = parent.parent;\r\n        }\r\n        return angle;\r\n    }\r\n\r\n    getScenePos(): Vec2 {\r\n        let pos = new Vec2(this.node.position.x, this.node.position.y).rotate(\r\n            misc.degreesToRadians(this.getAngleToOwner())\r\n        );\r\n        let parent = this.node.parent;\r\n        let scaleX = 1;\r\n        let scaleY = 1;\r\n\r\n        while (parent && parent.name !== 'enemyPlane') {\r\n            scaleX *= parent.scale.x;\r\n            scaleY *= parent.scale.y;\r\n            pos.x += parent.position.x;\r\n            pos.y += parent.position.y;\r\n            parent = parent.parent;\r\n        }\r\n\r\n        pos.x *= scaleX;\r\n        pos.y *= scaleY;\r\n        return pos;\r\n    }\r\n\r\n    updateGameLogic(deltaTime: number): void {\r\n        if (!this.isDead) {\r\n            this.m_comps.forEach((comp) => {\r\n                comp.update(deltaTime);\r\n            });\r\n\r\n            if (this._bWinkWhite) {\r\n                this._winkCount++;\r\n                if (this._winkCount > 10) {\r\n                    this._winkCount = 0;\r\n                    this._bWinkWhite = false;\r\n                }\r\n            }\r\n        }\r\n    }\r\n\r\n    refreshCollideOffset(): void {\r\n        let offsetX = 0;\r\n        let offsetY = 0;\r\n        let scaleX = 1;\r\n        let scaleY = 1;\r\n        let currentNode = this.node;\r\n\r\n        while (currentNode && this.owner && this.owner.node !== currentNode) {\r\n            offsetX += currentNode.position.x;\r\n            offsetY += currentNode.position.y;\r\n            scaleX *= currentNode.scale.x;\r\n            scaleY *= currentNode.scale.y;\r\n            currentNode = currentNode.parent;\r\n        }\r\n\r\n        this.setCollideOffset(offsetX * scaleX, offsetY * scaleY);\r\n    }\r\n\r\n    onCollide(collider: ColliderComp): void {\r\n        if (!this.isDead && this.active && collider.entity instanceof Bullet) {\r\n            let damage = collider.entity.getAttack(this);\r\n            damage = Math.max(damage / 10, damage - this.defence) * GameConst.playerWARatio;\r\n            GameIns.hurtEffectManager.createHurtNumByType(\r\n                this.collideComp,\r\n                collider.entity,\r\n                damage,\r\n                { x: 0, y: 0 }\r\n            );\r\n            this.hurt(damage);\r\n        }\r\n    }\r\n\r\n    hurt(damage: number): boolean {\r\n        if (this.isDead || !this.active) {\r\n            return false;\r\n        }\r\n        this.changeHp(-damage);\r\n        this.onHurt();\r\n        return true;\r\n    }\r\n\r\n    onHurt(): void {\r\n        this.winkWhite();\r\n    }\r\n\r\n    changeHp(amount: number): void {\r\n        let change = amount;\r\n        let newHp = this._curHp + amount;\r\n\r\n        if (newHp < 0) {\r\n            change = -this._curHp;\r\n        }\r\n\r\n        this._curHp = newHp;\r\n        if (this._curHp < 0) {\r\n            this._curHp = 0;\r\n        }\r\n\r\n        this.onHpChange(change);\r\n        this.checkHp();\r\n    }\r\n\r\n    onHpChange(change: number): void {\r\n        if (this.owner && this.owner.hpChange) {\r\n            this.owner.hpChange(change);\r\n        }\r\n    }\r\n\r\n    checkHp(): boolean {\r\n        if (this.curHp <= 0) {\r\n            this.die();\r\n            return true;\r\n        }\r\n        return false;\r\n    }\r\n\r\n    die(): void {\r\n        if (!this.isDead) {\r\n            this.isDead = true;\r\n            this.setCollideAble(false);\r\n            GameIns.hurtEffectManager.m_colliderPreTime.delete(this.collideComp?.entity.new_uuid);\r\n            this.onDie();\r\n        }\r\n    }\r\n\r\n    onDie(): void {\r\n        this.playDieAnim();\r\n    }\r\n\r\n    winkWhite(): void {\r\n        if (!this._bWinkWhite) {\r\n            this._bWinkWhite = true;\r\n            if (this._whiteNode && this._winkAct) {\r\n                this._winkAct.clone(this._whiteNode).start();\r\n            }\r\n        }\r\n    }\r\n\r\n    playDieAnim(): void {\r\n        this.blastParam.forEach((param, index) => {\r\n            const effectData = {\r\n                x: param[0],\r\n                y: param[1],\r\n                scale: param[4],\r\n                angle: param[5],\r\n            };\r\n            const callback = index === this.blastParam.length - 1 ? () => this.onDieAnimEnd() : null;\r\n\r\n            this.scheduleOnce(() => {\r\n                EnemyEffectLayer.me.addBlastEffect(this.node,param[2], effectData, callback);\r\n                // if (param[6] > 0) {\r\n                //     AudioManager.me.playEffect(`blast${param[6]}`);\r\n                // }\r\n            }, param[3] * GameConfig.ActionFrameTime);\r\n        });\r\n\r\n        // this.scheduleOnce(() => {\r\n        //     MainCamera.me.shake1(this.blastShake);\r\n        // }, 0.1);\r\n    }\r\n\r\n    onDieAnimEnd(): void {}\r\n\r\n    getHpPercent(): number {\r\n        return this.curHp / this.maxHp;\r\n    }\r\n}"]}