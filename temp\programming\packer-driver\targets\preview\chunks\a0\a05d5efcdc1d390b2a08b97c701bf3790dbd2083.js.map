{"version": 3, "sources": ["file:///E:/M2Game/Client/assets/scripts/Game/ui/base/ImageSequence.ts"], "names": ["_decorator", "Component", "Sprite", "tween", "<PERSON><PERSON><PERSON><PERSON>", "ccclass", "property", "ImageSequence", "m_frameTime", "m_imgNum", "m_textures", "m_playTime", "m_times", "m_isPlay", "m_finish<PERSON><PERSON>back", "_finishCall", "onLoad", "img", "sprite", "getComponent", "addComponent", "playing", "setData", "textures", "speed", "setSpeed", "length", "Math", "floor", "play", "times", "finishCallback", "node", "spriteFrame", "Promise", "resolve", "delayPlay", "delay", "call", "setStart", "start", "stop", "clear", "play<PERSON><PERSON>sh", "update", "deltaTime", "frameIndex"], "mappings": ";;;;;;;;;;;;;;;;;;;;AAASA,MAAAA,U,OAAAA,U;AAAYC,MAAAA,S,OAAAA,S;AAAWC,MAAAA,M,OAAAA,M;AAAQC,MAAAA,K,OAAAA,K;AAAOC,MAAAA,O,OAAAA,O;;;;;;;;;OAEzC;AAAEC,QAAAA,OAAF;AAAWC,QAAAA;AAAX,O,GAAwBN,U;;yBAGTO,a,WADpBF,OAAO,CAAC,eAAD,C,UAEHC,QAAQ,CAACJ,MAAD,C,2BAFb,MACqBK,aADrB,SAC2CN,SAD3C,CACqD;AAAA;AAAA;;AAAA;;AAErC;AAFqC,eAIjDO,WAJiD,GAInC,CAJmC;AAIhC;AAJgC,eAKjDC,QALiD,GAKtC,CALsC;AAKnC;AALmC,eAMjDC,UANiD,GAMpC,EANoC;AAMhC;AANgC,eAOjDC,UAPiD,GAOpC,CAPoC;AAOjC;AAPiC,eAQjDC,OARiD,GAQvC,CARuC;AAQpC;AARoC,eASjDC,QATiD,GAStC,KATsC;AAS/B;AAT+B,eAUjDC,gBAViD,GAU9B,IAV8B;AAUxB;AAVwB,eAWjDC,WAXiD,GAWnC,IAXmC;AAAA;;AAW7B;;AAGpB;AACJ;AACA;AACIC,QAAAA,MAAM,GAAG;AACL,cAAI,KAAKC,GAAL,IAAY,IAAhB,EAAsB;AAClB,gBAAIC,MAAM,GAAG,KAAKC,YAAL,CAAkBjB,MAAlB,CAAb;;AACA,gBAAIgB,MAAM,IAAI,IAAd,EAAoB;AAChBA,cAAAA,MAAM,GAAG,KAAKE,YAAL,CAAkBlB,MAAlB,CAAT;AACH;;AACD,iBAAKe,GAAL,GAAWC,MAAX;AACH;AACJ;AAED;AACJ;AACA;;;AACe,YAAPG,OAAO,GAAG;AACV,iBAAO,KAAKR,QAAZ;AACH;AAED;AACJ;AACA;AACA;AACA;;;AACIS,QAAAA,OAAO,CAACC,QAAD,EAAWC,KAAX,EAAuB;AAAA,cAAZA,KAAY;AAAZA,YAAAA,KAAY,GAAJ,EAAI;AAAA;;AAC1B,eAAKC,QAAL,CAAcD,KAAd;;AACA,cAAID,QAAJ,EAAc;AACV,iBAAKd,QAAL,GAAgBc,QAAQ,CAACG,MAAzB;AACA,iBAAKhB,UAAL,GAAkBa,QAAlB;AACH;AACJ;AAED;AACJ;AACA;AACA;;;AACIE,QAAAA,QAAQ,CAACD,KAAD,EAAQ;AACZ,eAAKhB,WAAL,GAAmBmB,IAAI,CAACC,KAAL,CAAW,OAAOJ,KAAlB,CAAnB;AACH;AAED;AACJ;AACA;AACA;AACA;AACA;;;AACIK,QAAAA,IAAI,CAACC,KAAD,EAAQC,cAAR,EAA+B;AAAA,cAAvBA,cAAuB;AAAvBA,YAAAA,cAAuB,GAAN,IAAM;AAAA;;AAC/B,cAAI,CAAC3B,OAAO,CAAC,IAAD,CAAR,IAAkB,CAACA,OAAO,CAAC,KAAK4B,IAAN,CAA9B,EAA2C;AACvC;AACH;;AACD,eAAKpB,OAAL,GAAekB,KAAf;AACA,eAAKnB,UAAL,GAAkB,CAAlB;AACA,eAAKE,QAAL,GAAgB,IAAhB;;AACA,cAAI,KAAKI,GAAT,EAAc;AACV,iBAAKA,GAAL,CAASgB,WAAT,GAAuB,KAAKvB,UAAL,CAAgB,CAAhB,CAAvB;AACH,WAT8B,CAU/B;;;AACA,eAAKI,gBAAL,GAAwB,IAAxB;AACA,eAAKC,WAAL,GAAmBgB,cAAnB;AAEA,iBAAO,IAAIG,OAAJ,CAAaC,OAAD,IAAa;AAC5B,iBAAKrB,gBAAL,GAAwB,MAAM;AAC1BqB,cAAAA,OAAO,CAAC,IAAD,CAAP;AACH,aAFD;AAGH,WAJM,CAAP;AAKH;AAED;AACJ;AACA;AACA;AACA;;;AACIC,QAAAA,SAAS,CAACC,KAAD,EAAQP,KAAR,EAAe;AAAA;;AACpB3B,UAAAA,KAAK,CAAC,IAAD,CAAL,CACKkC,KADL,CACWA,KAAK,GAAG,EADnB,EAEKC,IAFL,iCAEU,aAAY;AACd,kBAAM,KAAI,CAACT,IAAL,CAAUC,KAAV,CAAN;;AACA,YAAA,KAAI,CAACS,QAAL;AACH,WALL,GAMKC,KANL;AAOH;AAED;AACJ;AACA;;;AACIC,QAAAA,IAAI,GAAG;AACH,eAAK5B,QAAL,GAAgB,KAAhB,CADG,CAEH;AACH;AAED;AACJ;AACA;;;AACI0B,QAAAA,QAAQ,GAAG;AACP,cAAI,KAAKtB,GAAT,EAAc;AACV,iBAAKA,GAAL,CAASgB,WAAT,GAAuB,KAAKvB,UAAL,CAAgB,CAAhB,CAAvB;AACH;AACJ;AAED;AACJ;AACA;;;AACIgC,QAAAA,KAAK,GAAG;AACJ,eAAKD,IAAL;;AACA,cAAI,KAAKxB,GAAT,EAAc;AACV,iBAAKA,GAAL,CAASgB,WAAT,GAAuB,IAAvB;AACH;;AACD,eAAKvB,UAAL,GAAkB,EAAlB;AACH;AAED;AACJ;AACA;;;AACIiC,QAAAA,UAAU,GAAG;AACT,eAAKF,IAAL;;AACA,cAAI,KAAK3B,gBAAT,EAA2B;AACvB,iBAAKA,gBAAL;AACA,iBAAKA,gBAAL,GAAwB,IAAxB;AACH;;AACD,cAAI,KAAKC,WAAT,EAAsB;AAClB,iBAAKA,WAAL;;AACA,iBAAKA,WAAL,GAAmB,IAAnB;AACH;AACJ;AAED;AACJ;AACA;AACA;;;AACI6B,QAAAA,MAAM,CAACC,SAAD,EAAY;AACd,cAAI,KAAKhC,QAAL,IAAiB,KAAKJ,QAAL,KAAkB,CAAvC,EAA0C;AACtC,gBAAIoC,SAAS,GAAG,GAAhB,EAAqB;AACjBA,cAAAA,SAAS,GAAG,iBAAZ,CADiB,CACc;AAClC;;AACD,iBAAKlC,UAAL,IAAmBgB,IAAI,CAACC,KAAL,CAAWiB,SAAS,GAAG,IAAvB,CAAnB;AACA,gBAAMC,UAAU,GAAGnB,IAAI,CAACC,KAAL,CAAW,KAAKjB,UAAL,GAAkB,KAAKH,WAAlC,IAAiD,KAAKC,QAAzE;;AACA,gBAAI,KAAKQ,GAAT,EAAc;AACV,mBAAKA,GAAL,CAASgB,WAAT,GAAuB,KAAKvB,UAAL,CAAgBoC,UAAhB,CAAvB;AACH;;AACD,gBACI,KAAKlC,OAAL,GAAe,CAAf,IACAe,IAAI,CAACC,KAAL,CAAW,KAAKjB,UAAL,GAAkB,KAAKH,WAAlC,IAAiD,CAAjD,IAAsD,KAAKI,OAAL,GAAe,KAAKH,QAF9E,EAGE;AACE,mBAAKkC,UAAL;AACH;AACJ;AACJ;;AAjKgD,O;;;;;iBAE3C,I", "sourcesContent": ["import { _decorator, Component, Sprite, tween, isValid } from 'cc';\r\n\r\nconst { ccclass, property } = _decorator;\r\n\r\n@ccclass('ImageSequence')\r\nexport default class ImageSequence extends Component {\r\n    @property(Sprite)\r\n    img = null; // 用于显示序列帧的 Sprite 组件\r\n\r\n    m_frameTime = 0; // 每帧的时间间隔（毫秒）\r\n    m_imgNum = 0; // 图片数量\r\n    m_textures = []; // 图片纹理数组\r\n    m_playTime = 0; // 播放时间累计\r\n    m_times = 0; // 播放次数\r\n    m_isPlay = false; // 是否正在播放\r\n    m_finishCallback = null; // 播放完成回调\r\n    _finishCall = null; // 自定义完成回调\r\n\r\n\r\n    /**\r\n     * 加载时初始化 Sprite 组件\r\n     */\r\n    onLoad() {\r\n        if (this.img == null) {\r\n            let sprite = this.getComponent(Sprite);\r\n            if (sprite == null) {\r\n                sprite = this.addComponent(Sprite);\r\n            }\r\n            this.img = sprite;\r\n        }\r\n    }\r\n\r\n    /**\r\n     * 获取是否正在播放\r\n     */\r\n    get playing() {\r\n        return this.m_isPlay;\r\n    }\r\n\r\n    /**\r\n     * 设置序列帧数据\r\n     * @param {Array} textures 图片纹理数组\r\n     * @param {number} speed 播放速度（帧率）\r\n     */\r\n    setData(textures, speed = 30) {\r\n        this.setSpeed(speed);\r\n        if (textures) {\r\n            this.m_imgNum = textures.length;\r\n            this.m_textures = textures;\r\n        }\r\n    }\r\n\r\n    /**\r\n     * 设置播放速度\r\n     * @param {number} speed 播放速度（帧率）\r\n     */\r\n    setSpeed(speed) {\r\n        this.m_frameTime = Math.floor(1000 / speed);\r\n    }\r\n\r\n    /**\r\n     * 播放序列帧动画\r\n     * @param {number} times 播放次数\r\n     * @param {Function} finishCallback 播放完成回调\r\n     * @returns {Promise<void>}\r\n     */\r\n    play(times, finishCallback = null) {\r\n        if (!isValid(this) || !isValid(this.node)) {\r\n            return;\r\n        }\r\n        this.m_times = times;\r\n        this.m_playTime = 0;\r\n        this.m_isPlay = true;\r\n        if (this.img) {\r\n            this.img.spriteFrame = this.m_textures[0];\r\n        }\r\n        // this.node.opacity = 255;\r\n        this.m_finishCallback = null;\r\n        this._finishCall = finishCallback;\r\n\r\n        return new Promise((resolve) => {\r\n            this.m_finishCallback = () => {\r\n                resolve(true);\r\n            };\r\n        });\r\n    }\r\n\r\n    /**\r\n     * 延迟播放序列帧动画\r\n     * @param {number} delay 延迟时间（帧数）\r\n     * @param {number} times 播放次数\r\n     */\r\n    delayPlay(delay, times) {\r\n        tween(this)\r\n            .delay(delay / 30)\r\n            .call(async () => {\r\n                await this.play(times);\r\n                this.setStart();\r\n            })\r\n            .start();\r\n    }\r\n\r\n    /**\r\n     * 停止播放\r\n     */\r\n    stop() {\r\n        this.m_isPlay = false;\r\n        // this.node.opacity = 0;\r\n    }\r\n\r\n    /**\r\n     * 设置为第一帧\r\n     */\r\n    setStart() {\r\n        if (this.img) {\r\n            this.img.spriteFrame = this.m_textures[0];\r\n        }\r\n    }\r\n\r\n    /**\r\n     * 清除序列帧数据\r\n     */\r\n    clear() {\r\n        this.stop();\r\n        if (this.img) {\r\n            this.img.spriteFrame = null;\r\n        }\r\n        this.m_textures = [];\r\n    }\r\n\r\n    /**\r\n     * 播放完成时的处理\r\n     */\r\n    playFinish() {\r\n        this.stop();\r\n        if (this.m_finishCallback) {\r\n            this.m_finishCallback();\r\n            this.m_finishCallback = null;\r\n        }\r\n        if (this._finishCall) {\r\n            this._finishCall();\r\n            this._finishCall = null;\r\n        }\r\n    }\r\n\r\n    /**\r\n     * 更新序列帧动画\r\n     * @param {number} deltaTime 帧间隔时间\r\n     */\r\n    update(deltaTime) {\r\n        if (this.m_isPlay && this.m_imgNum !== 0) {\r\n            if (deltaTime > 0.2) {\r\n                deltaTime = 0.016666666666667; // 限制最大帧间隔\r\n            }\r\n            this.m_playTime += Math.floor(deltaTime * 1000);\r\n            const frameIndex = Math.floor(this.m_playTime / this.m_frameTime) % this.m_imgNum;\r\n            if (this.img) {\r\n                this.img.spriteFrame = this.m_textures[frameIndex];\r\n            }\r\n            if (\r\n                this.m_times > 0 &&\r\n                Math.floor(this.m_playTime / this.m_frameTime) + 1 >= this.m_times * this.m_imgNum\r\n            ) {\r\n                this.playFinish();\r\n            }\r\n        }\r\n    }\r\n}"]}