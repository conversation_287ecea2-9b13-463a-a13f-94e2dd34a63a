{"version": 3, "sources": ["file:///E:/M2Game/Client/assets/scripts/Game/ui/plane/enemy/EnemyAnim.ts"], "names": ["_decorator", "Component", "Tween", "Animation", "GameConfig", "ccclass", "property", "EnemyAnim", "_animCallMap", "Map", "_animEventCallMap", "_tailFireArr", "_curAnim", "onLoad", "init", "tailFireData", "_initTailFire", "frameTime", "ActionFrameTime", "fireIndex", "dataIndex", "fireNode", "node", "getChildByName", "i", "children", "length", "child", "name", "stopAllByTarget", "fireData", "tailFire", "active", "onFinished", "event", "for<PERSON>ach", "callback", "anim<PERSON><PERSON>", "onShoot", "get", "onShield", "playAnim", "setAnimEventCall", "eventName", "set", "pauseAnim", "anim", "pause", "resumeAnim", "stopAnim"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;AAASA,MAAAA,U,OAAAA,U;AAAYC,MAAAA,S,OAAAA,S;AAAWC,MAAAA,K,OAAAA,K;AAAYC,MAAAA,S,OAAAA,S;;AACrCC,MAAAA,U;;;;;;;;;OACD;AAAEC,QAAAA,OAAF;AAAWC,QAAAA;AAAX,O,GAAwBN,U;;yBAGTO,S,WADpBF,OAAO,CAAC,WAAD,C,UAEHC,QAAQ,CAACH,SAAD,C,2BAFb,MACqBI,SADrB,SACuCN,SADvC,CACiD;AAAA;AAAA;;AAAA;;AAAA,eAIrCO,YAJqC,GAIC,IAAIC,GAAJ,EAJD;AAAA,eAKrCC,iBALqC,GAKM,IAAID,GAAJ,EALN;AAAA,eAMrCE,YANqC,GAMd,EANc;AAAA,eAOrCC,QAPqC,GAOlB,EAPkB;AAAA;;AAS7C;AACJ;AACA;AACIC,QAAAA,MAAM,GAAS,CACX;AACH;AAED;AACJ;AACA;AACA;;;AACIC,QAAAA,IAAI,CAACC,YAAD,EAA4B;AAC5B,eAAKC,aAAL,CAAmBD,YAAnB;AACH;AAED;AACJ;AACA;AACA;;;AACYC,QAAAA,aAAa,CAACD,YAAD,EAA4B;AAC7C,cAAME,SAAS,GAAG;AAAA;AAAA,wCAAWC,eAA7B;AACA,cAAIC,SAAS,GAAG,CAAhB;AACA,cAAIC,SAAS,GAAG,CAAhB;;AAEA,iBAAO,IAAP,EAAa;AACT,gBAAMC,QAAQ,GAAG,KAAKC,IAAL,CAAUC,cAAV,UAAgCJ,SAAhC,CAAjB;AACA,gBAAI,CAACE,QAAL,EAAe;;AAEf,iBAAK,IAAIG,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAGH,QAAQ,CAACI,QAAT,CAAkBC,MAAtC,EAA8CF,CAAC,EAA/C,EAAmD;AAC/C,kBAAMG,KAAK,GAAGN,QAAQ,CAACI,QAAT,CAAkBD,CAAlB,CAAd;;AACA,kBAAIG,KAAK,CAACC,IAAN,KAAe,MAAnB,EAA2B;AACvB1B,gBAAAA,KAAK,CAAC2B,eAAN,CAAsBF,KAAtB;AACA,oBAAMG,QAAQ,GAAGf,YAAY,CAACK,SAAS,EAAV,CAA7B;;AACA,oBAAIU,QAAJ,EAAc,CACV;AACA;AACA;AACA;AACA;AACH;AACJ;AACJ;;AACDX,YAAAA,SAAS;AACZ;;AAED,eAAK,IAAIK,EAAC,GAAGL,SAAb,EAAwBK,EAAC,GAAG,KAAKb,YAAL,CAAkBe,MAA9C,EAAsDF,EAAC,EAAvD,EAA2D;AACvD,gBAAMO,QAAQ,GAAG,KAAKpB,YAAL,CAAkBa,EAAlB,CAAjB;AACAtB,YAAAA,KAAK,CAAC2B,eAAN,CAAsBE,QAAtB;AACAA,YAAAA,QAAQ,CAACC,MAAT,GAAkB,KAAlB;AACH;AACJ;AAED;AACJ;AACA;AACA;;;AACYC,QAAAA,UAAU,CAACC,KAAD,EAAc;AAC5B,eAAK1B,YAAL,CAAkB2B,OAAlB,CAA0B,CAACC,QAAD,EAAWC,QAAX,KAAwB;AAC9C,gBAAI,KAAKzB,QAAL,KAAkByB,QAAlB,IAA8BD,QAAlC,EAA4C;AACxCA,cAAAA,QAAQ;AACX;AACJ,WAJD;AAKH;AAED;AACJ;AACA;;;AACIE,QAAAA,OAAO,GAAS;AACZ,cAAMF,QAAQ,GAAG,KAAK1B,iBAAL,CAAuB6B,GAAvB,CAA2B,OAA3B,CAAjB;;AACA,cAAIH,QAAJ,EAAc;AACVA,YAAAA,QAAQ;AACX;AACJ;AAED;AACJ;AACA;;;AACII,QAAAA,QAAQ,GAAS;AACb,cAAMJ,QAAQ,GAAG,KAAK1B,iBAAL,CAAuB6B,GAAvB,CAA2B,QAA3B,CAAjB;;AACA,cAAIH,QAAJ,EAAc;AACVA,YAAAA,QAAQ;AACX;AACJ;AAED;AACJ;AACA;AACA;AACA;;;AACIK,QAAAA,QAAQ,CAACJ,QAAD,EAAmBD,QAAnB,EAA8C;AAClD;AACA;AACA;AACAA,UAAAA,QAAQ,QAAR,IAAAA,QAAQ;AACX;AAED;AACJ;AACA;AACA;AACA;;;AACIM,QAAAA,gBAAgB,CAACC,SAAD,EAAoBP,QAApB,EAA8C;AAC1D,eAAK1B,iBAAL,CAAuBkC,GAAvB,CAA2BD,SAA3B,EAAsCP,QAAtC;AACH;AAED;AACJ;AACA;;;AACIS,QAAAA,SAAS,GAAS;AACd,eAAKC,IAAL,CAAUC,KAAV;AACH;AAED;AACJ;AACA;;;AACIC,QAAAA,UAAU,GAAS,CACf;AACH;AAED;AACJ;AACA;;;AACIC,QAAAA,QAAQ,GAAS,CACb;AACH;;AArI4C,O;;;;;iBAE3B,I", "sourcesContent": ["import { _decorator, Component, Tween,Node, Animation } from 'cc';\r\nimport GameConfig from '../../../const/GameConfig';\r\nconst { ccclass, property } = _decorator;\r\n\r\n@ccclass('EnemyAnim')\r\nexport default class EnemyAnim extends Component {\r\n    @property(Animation)\r\n    anim: Animation = null;\r\n\r\n    private _animCallMap: Map<string, Function> = new Map();\r\n    private _animEventCallMap: Map<string, Function> = new Map();\r\n    private _tailFireArr: Node[] = [];\r\n    private _curAnim: string = \"\";\r\n\r\n    /**\r\n     * 加载时的初始化\r\n     */\r\n    onLoad(): void {\r\n        // this.anim.on(\"finished\", this.onFinished, this);\r\n    }\r\n\r\n    /**\r\n     * 初始化动画\r\n     * @param tailFireData 尾焰数据\r\n     */\r\n    init(tailFireData: any[]): void {\r\n        this._initTailFire(tailFireData);\r\n    }\r\n\r\n    /**\r\n     * 初始化尾焰动画\r\n     * @param tailFireData 尾焰数据\r\n     */\r\n    private _initTailFire(tailFireData: any[]): void {\r\n        const frameTime = GameConfig.ActionFrameTime;\r\n        let fireIndex = 0;\r\n        let dataIndex = 0;\r\n\r\n        while (true) {\r\n            const fireNode = this.node.getChildByName(`fire${fireIndex}`);\r\n            if (!fireNode) break;\r\n\r\n            for (let i = 0; i < fireNode.children.length; i++) {\r\n                const child = fireNode.children[i];\r\n                if (child.name !== \"icon\") {\r\n                    Tween.stopAllByTarget(child);\r\n                    const fireData = tailFireData[dataIndex++];\r\n                    if (fireData) {\r\n                        // const scaleSequence = sequence(\r\n                        //     scaleTo(frameTime * fireData[5], fireData[6]),\r\n                        //     scaleTo(frameTime * fireData[5], 1)\r\n                        // );\r\n                        // child.runAction(repeatForever(scaleSequence));\r\n                    }\r\n                }\r\n            }\r\n            fireIndex++;\r\n        }\r\n\r\n        for (let i = fireIndex; i < this._tailFireArr.length; i++) {\r\n            const tailFire = this._tailFireArr[i];\r\n            Tween.stopAllByTarget(tailFire);\r\n            tailFire.active = false;\r\n        }\r\n    }\r\n\r\n    /**\r\n     * 动画播放完成的回调\r\n     * @param event 动画事件\r\n     */\r\n    private onFinished(event): void {\r\n        this._animCallMap.forEach((callback, animName) => {\r\n            if (this._curAnim === animName && callback) {\r\n                callback();\r\n            }\r\n        });\r\n    }\r\n\r\n    /**\r\n     * 射击动画事件\r\n     */\r\n    onShoot(): void {\r\n        const callback = this._animEventCallMap.get(\"shoot\");\r\n        if (callback) {\r\n            callback();\r\n        }\r\n    }\r\n\r\n    /**\r\n     * 护盾动画事件\r\n     */\r\n    onShield(): void {\r\n        const callback = this._animEventCallMap.get(\"shield\");\r\n        if (callback) {\r\n            callback();\r\n        }\r\n    }\r\n\r\n    /**\r\n     * 播放动画\r\n     * @param animName 动画名称\r\n     * @param callback 动画完成后的回调\r\n     */\r\n    playAnim(animName: string, callback?: Function): void {\r\n        // this._curAnim = animName;\r\n        // this.anim.play();\r\n        // this._animCallMap.set(animName, callback);\r\n        callback?.();\r\n    }\r\n\r\n    /**\r\n     * 设置动画事件回调\r\n     * @param eventName 动画事件名称\r\n     * @param callback 回调函数\r\n     */\r\n    setAnimEventCall(eventName: string, callback: Function): void {\r\n        this._animEventCallMap.set(eventName, callback);\r\n    }\r\n\r\n    /**\r\n     * 暂停动画\r\n     */\r\n    pauseAnim(): void {\r\n        this.anim.pause();\r\n    }\r\n\r\n    /**\r\n     * 恢复动画\r\n     */\r\n    resumeAnim(): void {\r\n        // this.anim.resume();\r\n    }\r\n\r\n    /**\r\n     * 停止动画\r\n     */\r\n    stopAnim(): void {\r\n        // this.anim.stop();\r\n    }\r\n}"]}