{"version": 3, "sources": ["file:///E:/M2Game/Client/assets/scripts/GameOld/world/weapon/EmitterArc.ts"], "names": ["_decorator", "instantiate", "Emitter", "DefaultMovable", "DefaultMoveModifier", "eSolverTarget", "eEasing", "ccclass", "property", "EmitterArc", "canEmit", "emitterData", "bulletPrefab", "count", "emit", "i", "direction", "getSpawnDirection", "position", "getSpawnPosition", "createBullet", "index", "angleOffset", "arc", "radian", "angle", "Math", "PI", "x", "cos", "y", "sin", "radius", "console", "warn", "bulletNode", "error", "emitterPos", "node", "getWorldPosition", "setWorldPosition", "z", "movable", "getComponent", "addComponent", "vx", "emitPower", "vy", "atan2", "addSolver", "<PERSON><PERSON>", "Linear", "Vx", "Vy", "parent", "<PERSON><PERSON><PERSON><PERSON>"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAASA,MAAAA,U,OAAAA,U;AAAYC,MAAAA,W,OAAAA,W;;AACZC,MAAAA,O,iBAAAA,O;;AAEAC,MAAAA,c,iBAAAA,c;;AACAC,MAAAA,mB,iBAAAA,mB;;AACAC,MAAAA,a,iBAAAA,a;AAAeC,MAAAA,O,iBAAAA,O;;;;;;;;;OAClB;AAAEC,QAAAA,OAAF;AAAWC,QAAAA;AAAX,O,GAAwBR,U;;4BAGjBS,U,WADZF,OAAO,CAAC,YAAD,C,gBAAR,MACaE,UADb;AAAA;AAAA,8BACwC;AAEpCC,QAAAA,OAAO,GAAY;AACf,cAAI,CAAC,KAAKC,WAAV,EACI,OAAO,KAAP;;AAEJ,cAAI,CAAC,KAAKC,YAAN,IAAsB,KAAKD,WAAL,CAAiBE,KAAjB,IAA0B,CAApD,EAAuD;AACnD,mBAAO,KAAP;AACH;;AAED,iBAAO,IAAP;AACH;AAED;AACJ;AACA;;;AACIC,QAAAA,IAAI,GAAS;AACT,eAAK,IAAIC,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAG,KAAKJ,WAAL,CAAiBE,KAArC,EAA4CE,CAAC,EAA7C,EAAiD;AAC7C,gBAAMC,SAAS,GAAG,KAAKC,iBAAL,CAAuBF,CAAvB,CAAlB;AACA,gBAAMG,QAAQ,GAAG,KAAKC,gBAAL,CAAsBJ,CAAtB,CAAjB;AACA,iBAAKK,YAAL,CAAkBJ,SAAlB,EAA6BE,QAA7B;AACH;AACJ;AAED;AACJ;AACA;AACA;AACA;;;AACID,QAAAA,iBAAiB,CAACI,KAAD,EAA0C;AACvD;AACA,cAAMC,WAAW,GAAG,KAAKX,WAAL,CAAiBE,KAAjB,GAAyB,CAAzB,GAA8B,KAAKF,WAAL,CAAiBY,GAAjB,IAAwB,KAAKZ,WAAL,CAAiBE,KAAjB,GAAyB,CAAjD,CAAD,GAAwDQ,KAAxD,GAAgE,KAAKV,WAAL,CAAiBY,GAAjB,GAAuB,CAApH,GAAwH,CAA5I;AACA,cAAMC,MAAM,GAAG,CAAC,KAAKb,WAAL,CAAiBc,KAAjB,GAAyBH,WAA1B,KAA0CI,IAAI,CAACC,EAAL,GAAU,GAApD,CAAf;AACA,iBAAO;AACHC,YAAAA,CAAC,EAAEF,IAAI,CAACG,GAAL,CAASL,MAAT,CADA;AAEHM,YAAAA,CAAC,EAAEJ,IAAI,CAACK,GAAL,CAASP,MAAT;AAFA,WAAP;AAIH;AAED;AACJ;AACA;AACA;AACA;;;AACIL,QAAAA,gBAAgB,CAACE,KAAD,EAA0C;AACtD,cAAI,KAAKV,WAAL,CAAiBqB,MAAjB,IAA2B,CAA/B,EAAkC;AAC9B,mBAAO;AAAEJ,cAAAA,CAAC,EAAE,CAAL;AAAQE,cAAAA,CAAC,EAAE;AAAX,aAAP;AACH;;AAED,cAAMd,SAAS,GAAG,KAAKC,iBAAL,CAAuBI,KAAvB,CAAlB;AACA,iBAAO;AACHO,YAAAA,CAAC,EAAEZ,SAAS,CAACY,CAAV,GAAc,KAAKjB,WAAL,CAAiBqB,MAD/B;AAEHF,YAAAA,CAAC,EAAEd,SAAS,CAACc,CAAV,GAAc,KAAKnB,WAAL,CAAiBqB;AAF/B,WAAP;AAIH;;AAEDZ,QAAAA,YAAY,CAACJ,SAAD,EAAsCE,QAAtC,EAA0E;AAAA;;AAClF,cAAI,CAAC,KAAKN,YAAV,EAAwB;AACpBqB,YAAAA,OAAO,CAACC,IAAR,CAAa,uCAAb;AACA,mBAAO,IAAP;AACH,WAJiF,CAMlF;;;AACA,cAAMC,UAAU,GAAGlC,WAAW,CAAC,KAAKW,YAAN,CAA9B;;AACA,cAAI,CAACuB,UAAL,EAAiB;AACbF,YAAAA,OAAO,CAACG,KAAR,CAAc,iDAAd;AACA,mBAAO,IAAP;AACH,WAXiF,CAalF;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;;;AACA,cAAMC,UAAU,GAAG,KAAKC,IAAL,CAAUC,gBAAV,EAAnB;AACAJ,UAAAA,UAAU,CAACK,gBAAX,CACIH,UAAU,CAACT,CAAX,GAAeV,QAAQ,CAACU,CAD5B,EAEIS,UAAU,CAACP,CAAX,GAAeZ,QAAQ,CAACY,CAF5B,EAGIO,UAAU,CAACI,CAHf,EAvBkF,CA6BlF;;AACA,cAAIC,OAAO,GAAGP,UAAU,CAACQ,YAAX;AAAA;AAAA,+CAAd;;AACA,cAAI,CAACD,OAAL,EAAc;AACVA,YAAAA,OAAO,GAAGP,UAAU,CAACS,YAAX;AAAA;AAAA,iDAAV;AACH,WAjCiF,CAmClF;;;AACAF,UAAAA,OAAO,CAACG,EAAR,GAAa7B,SAAS,CAACY,CAAV,GAAc,KAAKjB,WAAL,CAAiBmC,SAA5C;AACAJ,UAAAA,OAAO,CAACK,EAAR,GAAa/B,SAAS,CAACc,CAAV,GAAc,KAAKnB,WAAL,CAAiBmC,SAA5C;AACAJ,UAAAA,OAAO,CAACjB,KAAR,GAAgBC,IAAI,CAACsB,KAAL,CAAWhC,SAAS,CAACc,CAArB,EAAwBd,SAAS,CAACY,CAAlC,IAAuC,GAAvC,GAA6CF,IAAI,CAACC,EAAlE;AAEAe,UAAAA,OAAO,CAACO,SAAR,CAAkB;AAAA;AAAA,0DAAwB;AAAA;AAAA,8CAAcC,KAAtC,EAA6C,CAA7C,EAAgD,GAAhD,EAAqD;AAAA;AAAA,kCAAQC,MAA7D,CAAlB;AACAT,UAAAA,OAAO,CAACO,SAAR,CAAkB;AAAA;AAAA,0DAAwB;AAAA;AAAA,8CAAcG,EAAtC,EAA0C,CAA1C,EAA6C,CAA7C,EAAgD;AAAA;AAAA,kCAAQD,MAAxD,CAAlB;AACAT,UAAAA,OAAO,CAACO,SAAR,CAAkB;AAAA;AAAA,0DAAwB;AAAA;AAAA,8CAAcI,EAAtC,EAA0C,CAAC,GAA3C,EAAgD,CAAhD,EAAmD;AAAA;AAAA,kCAAQF,MAA3D,CAAlB;AAEA,oCAAKb,IAAL,CAAUgB,MAAV,+BAAkBC,QAAlB,CAA2BpB,UAA3B;AAEA,iBAAOA,UAAP;AACH;;AAvGmC,O", "sourcesContent": ["import { _decorator, instantiate } from 'cc';\r\nimport { Emitter } from './Emitter';\r\nimport { Bullet } from './Bullet';\r\nimport { DefaultMovable } from '../move/DefaultMovable';\r\nimport { DefaultMoveModifier } from '../move/DefaultMoveModifier';\r\nimport { eSolverTarget, eEasing } from '../move/IMovable';\r\nconst { ccclass, property } = _decorator;\r\n\r\n@ccclass('EmitterArc')\r\nexport class EmitterArc extends Emitter {\r\n\r\n    canEmit(): boolean {\r\n        if (!this.emitterData)\r\n            return false;\r\n\r\n        if (!this.bulletPrefab || this.emitterData.count <= 0) {\r\n            return false;\r\n        }\r\n\r\n        return true;\r\n    }\r\n\r\n    /**\r\n     * Implementation of emitBullet for arc-based emission\r\n     */\r\n    emit(): void {\r\n        for (let i = 0; i < this.emitterData.count; i++) {\r\n            const direction = this.getSpawnDirection(i);\r\n            const position = this.getSpawnPosition(i);\r\n            this.createBullet(direction, position);\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Calculate the direction for a bullet at the given index\r\n     * @param index The index of the bullet (0 to count-1)\r\n     * @returns Direction vector {x, y}\r\n     */\r\n    getSpawnDirection(index: number): { x: number, y: number } {\r\n        // 计算发射方向\r\n        const angleOffset = this.emitterData.count > 1 ? (this.emitterData.arc / (this.emitterData.count - 1)) * index - this.emitterData.arc / 2 : 0;\r\n        const radian = (this.emitterData.angle + angleOffset) * (Math.PI / 180);\r\n        return {\r\n            x: Math.cos(radian),\r\n            y: Math.sin(radian)\r\n        };\r\n    }\r\n\r\n    /**\r\n     * Get the spawn position for a bullet at the given index\r\n     * @param index The index of the bullet (0 to count-1)\r\n     * @returns Position offset from emitter center\r\n     */\r\n    getSpawnPosition(index: number): { x: number, y: number } {\r\n        if (this.emitterData.radius <= 0) {\r\n            return { x: 0, y: 0 };\r\n        }\r\n\r\n        const direction = this.getSpawnDirection(index);\r\n        return {\r\n            x: direction.x * this.emitterData.radius,\r\n            y: direction.y * this.emitterData.radius\r\n        };\r\n    }\r\n\r\n    createBullet(direction: { x: number, y: number }, position: { x: number, y: number }) {\r\n        if (!this.bulletPrefab) {\r\n            console.warn(\"EmitterArc: No bullet prefab assigned\");\r\n            return null;\r\n        }\r\n\r\n        // Instantiate the bullet from prefab\r\n        const bulletNode = instantiate(this.bulletPrefab);\r\n        if (!bulletNode) {\r\n            console.error(\"EmitterArc: Failed to instantiate bullet prefab\");\r\n            return null;\r\n        }\r\n\r\n        // Get the bullet component\r\n        // const bullet = bulletNode.getComponent(Bullet);\r\n        // if (!bullet) {\r\n        //     console.error(\"EmitterArc: Bullet prefab does not have Bullet component\");\r\n        //     bulletNode.destroy();\r\n        //     return null;\r\n        // }\r\n\r\n        // Set bullet position relative to emitter\r\n        const emitterPos = this.node.getWorldPosition();\r\n        bulletNode.setWorldPosition(\r\n            emitterPos.x + position.x,\r\n            emitterPos.y + position.y,\r\n            emitterPos.z\r\n        );\r\n\r\n        // Add or get the DefaultMovable component for movement\r\n        let movable = bulletNode.getComponent(DefaultMovable);\r\n        if (!movable) {\r\n            movable = bulletNode.addComponent(DefaultMovable);\r\n        }\r\n\r\n        // Set initial velocity based on direction and speed multiplier\r\n        movable.vx = direction.x * this.emitterData.emitPower;\r\n        movable.vy = direction.y * this.emitterData.emitPower;\r\n        movable.angle = Math.atan2(direction.y, direction.x) * 180 / Math.PI;\r\n        \r\n        movable.addSolver(new DefaultMoveModifier(eSolverTarget.Angle, 0, 180, eEasing.Linear));\r\n        movable.addSolver(new DefaultMoveModifier(eSolverTarget.Vx, 0, 2, eEasing.Linear));\r\n        movable.addSolver(new DefaultMoveModifier(eSolverTarget.Vy, -500, 2, eEasing.Linear));\r\n\r\n        this.node.parent?.addChild(bulletNode);\r\n\r\n        return bulletNode;\r\n    }\r\n}"]}