System.register(["__unresolved_0", "cc", "__unresolved_1", "__unresolved_2", "__unresolved_3", "__unresolved_4", "__unresolved_5", "__unresolved_6", "__unresolved_7"], function (_export, _context) {
  "use strict";

  var _reporterNs, _cclegacy, __checkObsolete__, __checkObsoleteInNamespace__, _decorator, GameConst, MainPlane, Entity, ColliderComp, GameIns, CircleScreen, GameEnum, _dec, _class, _crd, ccclass, property, FireShells;

  function _reportPossibleCrUseOfGameConst(extras) {
    _reporterNs.report("GameConst", "../../../const/GameConst", _context.meta, extras);
  }

  function _reportPossibleCrUseOfMainPlane(extras) {
    _reporterNs.report("MainPlane", "./MainPlane", _context.meta, extras);
  }

  function _reportPossibleCrUseOfEntity(extras) {
    _reporterNs.report("Entity", "../../base/Entity", _context.meta, extras);
  }

  function _reportPossibleCrUseOfColliderComp(extras) {
    _reporterNs.report("ColliderComp", "../../base/ColliderComp", _context.meta, extras);
  }

  function _reportPossibleCrUseOfGameIns(extras) {
    _reporterNs.report("GameIns", "../../../GameIns", _context.meta, extras);
  }

  function _reportPossibleCrUseOfCircleScreen(extras) {
    _reporterNs.report("CircleScreen", "../../bulletDanmu/CircleScreen", _context.meta, extras);
  }

  function _reportPossibleCrUseOfGameEnum(extras) {
    _reporterNs.report("GameEnum", "../../../const/GameEnum", _context.meta, extras);
  }

  return {
    setters: [function (_unresolved_) {
      _reporterNs = _unresolved_;
    }, function (_cc) {
      _cclegacy = _cc.cclegacy;
      __checkObsolete__ = _cc.__checkObsolete__;
      __checkObsoleteInNamespace__ = _cc.__checkObsoleteInNamespace__;
      _decorator = _cc._decorator;
    }, function (_unresolved_2) {
      GameConst = _unresolved_2.GameConst;
    }, function (_unresolved_3) {
      MainPlane = _unresolved_3.MainPlane;
    }, function (_unresolved_4) {
      Entity = _unresolved_4.default;
    }, function (_unresolved_5) {
      ColliderComp = _unresolved_5.ColliderComp;
    }, function (_unresolved_6) {
      GameIns = _unresolved_6.GameIns;
    }, function (_unresolved_7) {
      CircleScreen = _unresolved_7.default;
    }, function (_unresolved_8) {
      GameEnum = _unresolved_8.default;
    }],
    execute: function () {
      _crd = true;

      _cclegacy._RF.push({}, "f1d6eo10WxBro1Q6EMq3D9r", "FireShells", undefined);

      __checkObsolete__(['_decorator', 'Game']);

      ({
        ccclass,
        property
      } = _decorator);

      _export("default", FireShells = (_dec = ccclass("FireShells"), _dec(_class = class FireShells extends (_crd && Entity === void 0 ? (_reportPossibleCrUseOfEntity({
        error: Error()
      }), Entity) : Entity) {
        constructor() {
          super(...arguments);
          this.m_enemy = true;
          this.m_screen = null;
          this.m_inGroup = true;
          this.m_groupTime = 0;
          this.m_coolTime = 0;
          this.m_groupNum = 0;
          this.m_waitFrame = 0;
          this.sceneEntity = null;
          this.coolTime = 0;
          this.m_isMainPlane = false;
          this.m_updateStrict = false;
          this.m_batSkill = false;
          this.test = false;
          this.count = 0;
          this.countTime = 0;
          this.state = {};
          this.props = {};
          this.m_danmuConfig = null;
        }

        /**
         * 设置数据
         * @param data 配置数据
         * @param state 状态数据
         * @param enemy 敌人实体
         * @param sceneEntity 场景实体
         */
        setData(data, state, enemy, sceneEntity) {
          if (!data || data.length === 0) {
            var defaultProps = [0, 0, 0, 0, 0, 0, 0, 0];
            this.props = {
              x: defaultProps[0],
              y: defaultProps[1],
              cooltime: defaultProps[2],
              screenId: defaultProps[3],
              screenNum: defaultProps[4],
              screenTime: defaultProps[5],
              attack: defaultProps[6],
              wait: defaultProps[7]
            }; // if (this.m_screen && (this.m_screen instanceof LaserScreen || this.m_screen instanceof SwordScreen)) {
            //     this.m_screen.removeBullet();
            // }

            return;
          }

          this.state = {
            attack: 0
          };
          this.sceneEntity = sceneEntity;
          this.m_isMainPlane = false;
          this.m_updateStrict = false;

          if (this.sceneEntity && this.sceneEntity instanceof (_crd && MainPlane === void 0 ? (_reportPossibleCrUseOfMainPlane({
            error: Error()
          }), MainPlane) : MainPlane)) {
            this.m_isMainPlane = true;

            if (this.sceneEntity.m_config && (this.sceneEntity.m_config.id === 701 || this.sceneEntity.m_config.id === 703)) {
              this.m_updateStrict = true;
            }
          }

          var [x, y, cooltime, screenId, screenNum, screenTime, attack, wait] = data;
          this.props = {
            x,
            y,
            cooltime,
            screenId,
            screenNum,
            screenTime,
            attack,
            wait: wait || 0
          };
          this.node.setPosition(x, y);
          this.m_enemy = enemy;

          if (this.props.screenId !== 0) {
            this.coolTime = this.props.cooltime;
            this.m_danmuConfig = (_crd && GameIns === void 0 ? (_reportPossibleCrUseOfGameIns({
              error: Error()
            }), GameIns) : GameIns).bulletManager.getConfig(this.props.screenId);
            this.m_screen = this.getScreenComp();
            this.m_screen.init(this);
            this.setState(state, sceneEntity);
            this.clearData();
            this.m_batSkill = this.m_danmuConfig.bustyle === 43;
          }
        }
        /**
        * 设置目标
        * @param target 目标实体
        * @param collider 目标的碰撞组件
        */


        setTarget(target, collider) {
          if (collider === void 0) {
            collider = null;
          }

          if (target !== null && collider === null) {
            collider = target.getComp(_crd && ColliderComp === void 0 ? (_reportPossibleCrUseOfColliderComp({
              error: Error()
            }), ColliderComp) : ColliderComp);
          } // if (this.m_screen instanceof AimBaseScreen) {
          //     this.m_screen.setTarget(target, collider);
          // }

        }
        /**
         * 清理数据
         */


        clearData() {
          this.m_inGroup = true;
          this.m_groupTime = 0;
          this.m_coolTime = 0;
          this.m_groupNum = 0;
          this.m_waitFrame = 0;
        }
        /**
        * 设置状态
        * @param state 状态数据
        * @param sceneEntity 场景实体
        * @param isStrict 是否严格模式
        * @param isMainPlane 是否为主飞机
        */


        setState(state, sceneEntity, isStrict, isMainPlane) {
          if (isStrict === void 0) {
            isStrict = false;
          }

          if (isMainPlane === void 0) {
            isMainPlane = false;
          }

          for (var key in state) {
            this.state[key] = state[key];

            switch (key) {
              case "attack":
              case "attackLv":
              case "hpAttackLv":
                var attackValue = 0; // if (sceneEntity instanceof WinePlane) {
                //     const attackPromote = WinePlaneManager.me.getAttackPromote();
                //     const techPromote = WinePlaneManager.me.techPromote.get(WinePlaneManager.TurretPromote.atk);
                //     attackValue = (this.state.attack + attackPromote) * this.props.attack / 100 * (techPromote ? 1 + techPromote / 100 : 1);
                //     if (GameIns.skillManager.getLv(SkillManager.SkillType.wineAttack) > 0) {
                //         const skillLevel = GameIns.skillManager.getLv(SkillManager.SkillType.wineAttack);
                //         const skillData = GameIns.skillManager.getSkillRecord(skillLevel).skillcs;
                //         attackValue *= skillData[0];
                //     }
                // } else {

                var multiplier = 1;
                var bonus = 0;

                if (sceneEntity instanceof (_crd && MainPlane === void 0 ? (_reportPossibleCrUseOfMainPlane({
                  error: Error()
                }), MainPlane) : MainPlane)) {
                  var hpAttackLv = this.state.hpAttackLv || 0;
                  var hpBonus = (_crd && GameIns === void 0 ? (_reportPossibleCrUseOfGameIns({
                    error: Error()
                  }), GameIns) : GameIns).skillManager.getValueOfNumber(hpAttackLv, 0);
                  multiplier += (1 - (_crd && GameIns === void 0 ? (_reportPossibleCrUseOfGameIns({
                    error: Error()
                  }), GameIns) : GameIns).mainPlaneManager.data.hp / (_crd && GameIns === void 0 ? (_reportPossibleCrUseOfGameIns({
                    error: Error()
                  }), GameIns) : GameIns).mainPlaneManager.data.maxhp) * hpBonus;
                  bonus = (_crd && GameIns === void 0 ? (_reportPossibleCrUseOfGameIns({
                    error: Error()
                  }), GameIns) : GameIns).skillManager.doubleSkillMap.get(2) || 0;
                }

                var attackLv = this.state.attackLv || 0;
                var attackMultiplier = (_crd && GameIns === void 0 ? (_reportPossibleCrUseOfGameIns({
                  error: Error()
                }), GameIns) : GameIns).skillManager.getValueOfNumber(attackLv, 1, isStrict);
                attackValue = this.state.attack * this.props.attack / 100 * (attackMultiplier + bonus) * multiplier;
                attackValue += attackValue * ((_crd && GameIns === void 0 ? (_reportPossibleCrUseOfGameIns({
                  error: Error()
                }), GameIns) : GameIns).mainPlaneManager.data.intensifyAtk[0] || 0);

                if (sceneEntity instanceof (_crd && MainPlane === void 0 ? (_reportPossibleCrUseOfMainPlane({
                  error: Error()
                }), MainPlane) : MainPlane)) {
                  sceneEntity.m_data.trueAtk = attackValue;
                }

                var laserSpeedUp = (_crd && GameIns === void 0 ? (_reportPossibleCrUseOfGameIns({
                  error: Error()
                }), GameIns) : GameIns).mainPlaneManager.data.laserSpeedUp / 100 || 0;
                attackValue += this.state.attack * laserSpeedUp; // }

                this.m_screen.setBulletState({
                  attack: attackValue
                }, this.sceneEntity);
                break;
              //         case "speedLv":
              //             if (sceneEntity instanceof CopyPlane) {
              //                 if (GameIns.skillManager.getLv(SkillManager.SkillType.copySpeed) > 0) {
              //                     const skillLevel = GameIns.skillManager.getLv(SkillManager.SkillType.copySpeed);
              //                     const skillData = GameIns.skillManager.getSkillRecord(skillLevel);
              //                     this.coolTime = skillData ? this.props.cooltime * skillData.skillcs[0] : this.props.cooltime;
              //                 } else {
              //                     this.coolTime = this.props.cooltime;
              //                 }
              //             } else {
              //                 let speedMultiplier = GameIns.skillManager.getValueOfNumber(this.state.speedLv, 1, isStrict);
              //                 if (sceneEntity instanceof MainPlane) {
              //                     const bonus = GameIns.skillManager.doubleSkillMap.get(1) || 0;
              //                     speedMultiplier += bonus;
              //                 }
              //                 this.coolTime = speedMultiplier * this.props.cooltime;
              //             }
              //             break;
              //         case "through":
              //             this.m_screen.setBulletState({ through: this.state[key] }, this.sceneEntity);
              //             break;
              //         case "extra":
              //             this.m_screen.setBulletState({ extra: this.state[key] }, this.sceneEntity);
              //             break;
              //         case "cirtLv":
              //             let critChance, critDamage, critBonus, critMultiplier;
              //             if (sceneEntity instanceof MainPlane) {
              //                 critChance = GameIns.mainPlaneManager.getMainPlaneCirt();
              //                 critDamage = [0, 0];
              //                 critBonus = ConfigDataManager.CDMgr.getGlobalNumber(GameEnum.GBKey.InitCirtHurt);
              //                 critMultiplier = GameIns.skillManager.doubleSkillMap.get(16) || 1;
              //                 if (GameIns.skillManager.getLv(SkillManager.SkillType.addCirt) > 0) {
              //                     critDamage = this.addCirt(GameIns.skillManager.getLv(SkillManager.SkillType.addCirt));
              //                 }
              //                 const critPromote = GameIns.mainPlaneManager.attributePromote.get(MainPlaneManager.attribute.cirt) || 0;
              //                 const critAtkPromote = GameIns.mainPlaneManager.attributePromote.get(MainPlaneManager.attribute.cirtAtk) || 0;
              //                 const critState = [
              //                     critChance + critDamage[1] + critPromote / 100,
              //                     critDamage[0] * critMultiplier + critAtkPromote / 100 + critBonus,
              //                 ];
              //                 this.m_screen.setBulletState({ cirt: critState }, this.sceneEntity);
              //             } else if (sceneEntity instanceof CopyPlane) {
              //                 critDamage = GameIns.skillManager.getLv(SkillManager.SkillType.addCirt) > 0
              //                     ? this.addCirt(GameIns.skillManager.getLv(SkillManager.SkillType.addCirt))
              //                     : [0, 0];
              //                 const critState = [critDamage[1], critDamage[0]];
              //                 this.m_screen.setBulletState({ cirt: critState }, this.sceneEntity);
              //             }
              //             break;
              //         case "catapultLv":
              //             if (sceneEntity instanceof MainPlane) {
              //                 const catapultLevel = GameIns.skillManager.getLv(SkillManager.SkillType.catapult);
              //                 const catapultData = catapultLevel > 0 ? GameIns.skillManager.getSkillRecord(catapultLevel).skillcs : [];
              //                 this.m_screen.setBulletState({ catapult: catapultData }, this.sceneEntity);
              //             }
              //             break;
              //         case "atkChallenge":
              //             const atkChallenge = state.atkChallenge * this.state.attack * GameData.me.getAtkAddCount();
              //             this.m_screen.setBulletState({ atkChallenge }, this.sceneEntity);
              //             break;
              //         case "fireIntensify":
              //             this.m_screen.setBulletState({ fireIntensify: this.state.fireIntensify }, this.sceneEntity);
              //             break;
            }
          }
        }
        /**
         * 判断是否为跟随子弹
         * @param bulletType 子弹类型
         * @returns 是否为跟随子弹
         */


        static isFollowBullet(bulletType) {
          return bulletType === 22 || bulletType === 30;
        }
        /**
         * 获取屏幕组件
         * @returns 屏幕组件实例
         */


        getScreenComp() {
          switch (this.m_danmuConfig.bustyle) {
            // case 0:
            //     return new SingleLineScreen(this.props.screenId, this.m_enemy);
            // case 1:
            //     return new AimSingleLineScreen(this.props.screenId, this.m_enemy);
            // case 22:
            // case 32:
            // case 33:
            // case 27:
            //     return new CircleScreen(this.props.screenId, this.m_enemy);
            // case 28:
            //     return new LeftRightProScreen(this.props.screenId, this.m_enemy);
            case 2:
            case 50:
            case 59:
              return new (_crd && CircleScreen === void 0 ? (_reportPossibleCrUseOfCircleScreen({
                error: Error()
              }), CircleScreen) : CircleScreen)(this.props.screenId, this.m_enemy);
            // case 3:
            //     return new LoftScreen(this.props.screenId, this.m_enemy);
            // case 9:
            //     return new LineScreen(this.props.screenId, this.m_enemy);
            // case 10:
            //     return new AimLineScreen(this.props.screenId, this.m_enemy);
            // case 26:
            //     return new AroundBallScreen(this.props.screenId, this.m_enemy);
            // case 11:
            //     return new AimCircleScreen(this.props.screenId, this.m_enemy);
            // case 13:
            //     return new GridScreen(this.props.screenId, this.m_enemy);
            // case 24:
            //     return new AimLaserScreen(this.props.screenId, this.m_enemy);
            // case 19:
            //     return new WhipScreen(this.props.screenId, this.m_enemy);
            // case 29:
            //     return new AroundBallScreen(this.props.screenId, this.m_enemy);
            // case 23:
            //     return new LaserScreen(this.props.screenId, this.m_enemy);
            // case 30:
            //     return new CircleScreen(this.props.screenId, this.m_enemy);
            // case 31:
            //     return new LightingBallScreen(this.props.screenId, this.m_enemy);
            // case 7:
            //     return new FormulaScreen(this.props.screenId, this.m_enemy);
            // case 25:
            //     return new CircleZoomScreen(this.props.screenId, this.m_enemy);
            // case 34:
            //     return new SidePointScreen(this.props.screenId, this.m_enemy);
            // case 35:
            //     return new LeftRightScreen(this.props.screenId, this.m_enemy);
            // case 36:
            //     return new WavesScreen(this.props.screenId, this.m_enemy);
            // case 37:
            //     return new CircleScreen(this.props.screenId, this.m_enemy);
            // case 40:
            //     return new LJAimProScreen(this.props.screenId, this.m_enemy);
            // case 41:
            //     return new SwordScreen(this.props.screenId, this.m_enemy);
            // case 42:
            //     return new FooScreen(this.props.screenId, this.m_enemy);
            // case 43:
            //     return new SingleLineScreen(this.props.screenId, this.m_enemy);
            // case 46:
            //     return new PeachScreen(this.props.screenId, this.m_enemy);
            // case 47:
            //     return new PeachRotateScreen(this.props.screenId, this.m_enemy);
            // case 48:
            //     return new KnifeScreen(this.props.screenId, this.m_enemy);
            // case 49:
            //     return new LoftScreen1(this.props.screenId, this.m_enemy);
            // case 53:
            //     return new AngleDeltaScreen(this.props.screenId, this.m_enemy);
            // case 100:
            //     return new LashScreen(this.props.screenId);
            // default:
            //     return null;
          }
        }
        /**
         * 发射子弹
         */


        fire() {
          this.m_screen.toFire(this.m_groupNum); // switch (this.m_danmuConfig.bustyle) {
          //     case 27:
          //         frameWork.audioManager.playEffect("b_ice");
          //         break;
          //     case 28:
          //         frameWork.audioManager.playEffectForCD("b_fire", this.coolTime);
          //         break;
          //     case 31:
          //         frameWork.audioManager.playEffect("flashstart");
          //         break;
          //     case 22:
          //         frameWork.audioManager.playEffectForCD("targetFly", this.coolTime);
          //         break;
          // }
        }
        /**
         * 设置冷却时间
         * @param coolTime 冷却时间
         */


        setCoolTime(coolTime) {
          if (coolTime < 0) {
            this.coolTime = 1000;
          } else {
            this.m_coolTime = 0;
            this.coolTime = this.props.cooltime;
          }
        }
        /**
         * 更新逻辑
         * @param deltaTime 时间增量
         */


        update(deltaTime) {
          // 检查游戏是否处于可运行状态
          if (!(_crd && GameConst === void 0 ? (_reportPossibleCrUseOfGameConst({
            error: Error()
          }), GameConst) : GameConst).GameAble) {
            return;
          } // 如果不在战斗中，或者场景实体无效，且不是特殊技能状态，则直接返回


          if (!(_crd && GameIns === void 0 ? (_reportPossibleCrUseOfGameIns({
            error: Error()
          }), GameIns) : GameIns).gameRuleManager.isInBattle() && (!this.sceneEntity || !this.sceneEntity.node || !this.sceneEntity.node.active) && !this.m_batSkill) {
            return;
          } // 限制 deltaTime 的最大值，避免因帧率过低导致的时间跳跃


          if (deltaTime > 0.2) {
            deltaTime = 0.016666666666667; // 约等于 1/60 秒
          } // 如果启用了严格更新模式，进一步限制 deltaTime


          if (this.m_updateStrict && deltaTime > 0.016666666666667) {
            deltaTime = 0.016666666666667;
          } // // 如果主飞机未显示，且子弹发射器的 UI 节点无效，直接返回
          // if (!GameIns.mainPlaneManager.isShow && !GameIns.bulletManager.fireShellUINode) {
          //     return;
          // }
          // 如果场景实体无效，处理等待帧逻辑


          if (!this.sceneEntity) {
            if (this.m_waitFrame < this.props.wait) {
              this.m_waitFrame++;
              return;
            }
          } // 如果游戏状态不是战斗状态，处理特殊逻辑


          if ((_crd && GameIns === void 0 ? (_reportPossibleCrUseOfGameIns({
            error: Error()
          }), GameIns) : GameIns).gameRuleManager.gameState !== (_crd && GameEnum === void 0 ? (_reportPossibleCrUseOfGameEnum({
            error: Error()
          }), GameEnum) : GameEnum).GameState.Battle) {
            if ((_crd && GameIns === void 0 ? (_reportPossibleCrUseOfGameIns({
              error: Error()
            }), GameIns) : GameIns).gameRuleManager.isGameWillOver() && this.m_screen) {
              this.m_screen.update(deltaTime);
            } // 如果不是主飞机，或者主飞机的剑气子弹未激活，直接返回


            if (!this.m_isMainPlane || !(_crd && GameIns === void 0 ? (_reportPossibleCrUseOfGameIns({
              error: Error()
            }), GameIns) : GameIns).mainPlaneManager.swordBullet) {
              return;
            } // 激活剑气子弹并触发发射逻辑


            (_crd && GameIns === void 0 ? (_reportPossibleCrUseOfGameIns({
              error: Error()
            }), GameIns) : GameIns).mainPlaneManager.swordBullet = false;
            this.fire();
            return;
          } // 如果主飞机未启用发射功能，直接返回


          if (!(_crd && GameIns === void 0 ? (_reportPossibleCrUseOfGameIns({
            error: Error()
          }), GameIns) : GameIns).mainPlaneManager.fireEnable) {
            return;
          } // 如果屏幕 ID 为 0，直接返回


          if (this.props.screenId === 0) {
            return;
          } // 处理等待帧逻辑


          if (this.m_waitFrame < this.props.wait) {
            this.m_waitFrame++;
            return;
          } // 更新屏幕组件


          if (this.m_screen) {
            this.m_screen.update(deltaTime);
          } // 处理冷却时间逻辑


          if (!this.m_inGroup && this.props.cooltime >= 0) {
            this.m_coolTime += deltaTime;

            if (this.m_coolTime >= this.coolTime) {
              this.m_inGroup = true;
              this.m_coolTime -= this.coolTime;
            }
          } // 处理分组发射逻辑


          if (this.m_inGroup) {
            this.m_groupTime += deltaTime;

            if (this.m_groupTime >= this.props.screenTime) {
              this.fire();
              this.m_groupNum++;
              this.m_groupTime = 0;
            }

            if (this.m_groupNum >= this.props.screenNum) {
              this.m_inGroup = false;
              this.m_groupNum = 0;
            }
          }
        }
        /**
         * 添加暴击
         * @param level 技能等级
         * @returns 暴击数据
         */


        addCirt(level) {// return GameIns.skillManager.getSkillRecord(level).skillcs;
        }
        /**
         * 更改屏幕数据
         * @param key 数据键
         * @param value 数据值
         */


        changeScreenData(key, value) {
          if (this.m_screen && this.m_screen.onChangeData) {
            this.m_screen.onChangeData(key, value);
          }
        }
        /**
         * 获取子弹类型
         * @returns 子弹类型
         */


        getType() {
          return this.m_danmuConfig ? this.m_danmuConfig.bustyle : 0;
        }
        /**
         * 移除子弹
         * @param bulletType 子弹类型
         */


        removeBullet(bulletType) {
          if (this.m_danmuConfig.bustyle === bulletType && this.m_screen.removeBullet) {
            this.m_screen.removeBullet();
          }
        }
        /**
         * 获取屏幕组件
         * @returns 屏幕组件实例
         */


        getScreen() {
          return this.m_screen;
        }

      }) || _class));

      _cclegacy._RF.pop();

      _crd = false;
    }
  };
});
//# sourceMappingURL=e3ca120aa89aa938b25302a4e0ff354dbb40bbf0.js.map