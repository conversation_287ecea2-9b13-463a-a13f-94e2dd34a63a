System.register(["__unresolved_0", "cc", "__unresolved_1", "__unresolved_2", "__unresolved_3", "__unresolved_4"], function (_export, _context) {
  "use strict";

  var _reporterNs, _cclegacy, __checkObsolete__, __checkObsoleteInNamespace__, _decorator, Vec2, EnemyAttrBaseCom, Tools, GameIns, BattleLayer, _dec, _class, _crd, ccclass, property, EnemyAttrShieldCom;

  function _reportPossibleCrUseOfEnemyAttrBaseCom(extras) {
    _reporterNs.report("EnemyAttrBaseCom", "./EnemyAttrBaseCom", _context.meta, extras);
  }

  function _reportPossibleCrUseOfTools(extras) {
    _reporterNs.report("Tools", "../../../utils/Tools", _context.meta, extras);
  }

  function _reportPossibleCrUseOfGameIns(extras) {
    _reporterNs.report("GameIns", "../../../GameIns", _context.meta, extras);
  }

  function _reportPossibleCrUseOfBattleLayer(extras) {
    _reporterNs.report("BattleLayer", "../../layer/BattleLayer", _context.meta, extras);
  }

  return {
    setters: [function (_unresolved_) {
      _reporterNs = _unresolved_;
    }, function (_cc) {
      _cclegacy = _cc.cclegacy;
      __checkObsolete__ = _cc.__checkObsolete__;
      __checkObsoleteInNamespace__ = _cc.__checkObsoleteInNamespace__;
      _decorator = _cc._decorator;
      Vec2 = _cc.Vec2;
    }, function (_unresolved_2) {
      EnemyAttrBaseCom = _unresolved_2.default;
    }, function (_unresolved_3) {
      Tools = _unresolved_3.Tools;
    }, function (_unresolved_4) {
      GameIns = _unresolved_4.GameIns;
    }, function (_unresolved_5) {
      BattleLayer = _unresolved_5.default;
    }],
    execute: function () {
      _crd = true;

      _cclegacy._RF.push({}, "dc5584eWn9FRKl60scOWx1F", "EnemyAttrShieldCom", undefined);

      __checkObsolete__(['_decorator', 'Vec2']);

      ({
        ccclass,
        property
      } = _decorator);

      _export("default", EnemyAttrShieldCom = (_dec = ccclass('EnemyAttrShieldCom'), _dec(_class = class EnemyAttrShieldCom extends (_crd && EnemyAttrBaseCom === void 0 ? (_reportPossibleCrUseOfEnemyAttrBaseCom({
        error: Error()
      }), EnemyAttrBaseCom) : EnemyAttrBaseCom) {
        constructor() {
          super(...arguments);
          this._imageId = 1;
          // 护盾图片 ID
          this.imageRatio = 0;
          // 护盾缩放比例
          this._offsetY = 0;
          // 护盾的 Y 偏移量
          this._collide = [];
          // 碰撞数据
          this._cdTime = 0;
          // 护盾冷却时间
          this._hpRatio = 0;
          // 护盾血量比例
          this._shieldEntity = null;
          // 护盾实体
          this._offset = Vec2.ZERO;
          // 护盾偏移量
          this._initHp = 0;
          // 护盾初始血量
          this._timeCount = 0;
        }

        // 冷却计时器

        /**
         * 初始化护盾属性组件
         * @param {Object} enemyEntity 敌人实体
         * @param {Object} attrData 属性数据
         */
        init(enemyEntity, attrData) {
          this.enemyEntity = enemyEntity;
          this.attrData = attrData;
          var params = attrData.param.split('*');
          this._imageId = Number(params[0]);
          this.imageRatio = Number(params[1]);
          this._offsetY = Number(params[2]);
          this._collide = (_crd && Tools === void 0 ? (_reportPossibleCrUseOfTools({
            error: Error()
          }), Tools) : Tools).stringToNumber(params[3], ',');
          this._cdTime = Number(params[4]);
          this._hpRatio = Number(params[5]);
          this._offset = new Vec2(0, this._offsetY);
          this._initHp = this.enemyEntity.getMaxHp() * this._hpRatio;
          this.reset();
        }
        /**
         * 重置护盾状态
         */


        reset() {
          this._timeCount = 0;
        }
        /**
         * 更新游戏逻辑
         * @param {number} deltaTime 帧间隔时间
         */


        updateGameLogic(deltaTime) {
          if (this._shieldEntity) {
            this._shieldEntity.updateGameLogic(deltaTime);

            if (this._shieldEntity.isDead) {
              this._timeCount += deltaTime;

              if (this._timeCount >= this._cdTime) {
                this._timeCount = 0;

                this._shieldEntity.init(this._imageId, this.imageRatio, this._initHp, [1, 0, this._collide[0], this._collide[1], this._collide[2]]);

                this._shieldEntity.node.active = true;

                this._shieldEntity.playShowAnim();
              }
            } else {
              this._shieldEntity.node.position = this.enemyEntity.node.position.add(this._offset);
            }
          }
        }
        /**
         * 处理护盾死亡逻辑
         */


        die() {
          if (this._shieldEntity) {
            this._shieldEntity.stopAllAnim();

            (_crd && GameIns === void 0 ? (_reportPossibleCrUseOfGameIns({
              error: Error()
            }), GameIns) : GameIns).enemyManager.removeShield(this._shieldEntity);
            this._shieldEntity = null;
          }
        }
        /**
         * 添加护盾
         */


        addShield() {
          if (!this._shieldEntity) {
            this._shieldEntity = (_crd && GameIns === void 0 ? (_reportPossibleCrUseOfGameIns({
              error: Error()
            }), GameIns) : GameIns).enemyManager.addShield();

            this._shieldEntity.init(this._imageId, this.imageRatio, this._initHp, [1, 0, this._collide[0], this._collide[1], this._collide[2]]);

            this._shieldEntity.node.parent = (_crd && BattleLayer === void 0 ? (_reportPossibleCrUseOfBattleLayer({
              error: Error()
            }), BattleLayer) : BattleLayer).me.enemyEffectLayer;
            this._shieldEntity.node.position = this.enemyEntity.node.position.add(this._offset);
          }

          this._shieldEntity.playShowAnim();
        }
        /**
         * 显示护盾属性
         */


        showAttrShield() {
          this.addShield();
        }

      }) || _class));

      _cclegacy._RF.pop();

      _crd = false;
    }
  };
});
//# sourceMappingURL=9fdf18bfb968910ab104d60bff7e6a3c4c895c68.js.map