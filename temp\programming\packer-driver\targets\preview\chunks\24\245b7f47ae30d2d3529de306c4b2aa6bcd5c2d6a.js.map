{"version": 3, "sources": ["file:///E:/M2Game/Client/assets/scripts/Game/ui/plane/boss/BossBase.ts"], "names": ["_decorator", "Node", "tween", "v3", "UITransform", "BossHurt", "Tools", "BossCollider", "GameIns", "GameConfig", "ccclass", "property", "BossBase", "baseData", "propertyRate", "tip", "exp", "uiNode", "unitArr", "unitPosArr", "spineArr", "actArr", "collideArr", "blastParam", "blastShake", "onlyLoot", "lootArr", "lootParam0", "lootParam1", "dieBullet", "bullets", "m_totalHp", "m_curHp", "m_swordUnit", "setData", "data", "attack", "collideAtk", "collide<PERSON><PERSON><PERSON>", "length", "addUnit", "unit", "position", "push", "removeUnit", "index", "indexOf", "splice", "clearUnit", "addSpine", "spine", "removeSpine", "arr<PERSON><PERSON><PERSON>", "addAction", "action", "removeAction", "createSelfCollides", "collideData", "collider", "addCollider", "setSelfColliderAble", "enable", "setCollideAble", "node", "addComponent", "<PERSON><PERSON><PERSON><PERSON>", "addScript", "create", "setPos", "x", "y", "update", "setPosition", "setPropertyRate", "rates", "setTip", "transformEnd", "battleManager", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "bossFightStart", "to<PERSON><PERSON>", "isDead", "onDie", "startBattle", "removeBullets", "bullet", "dieRemove", "playDieAnim", "playShakeAnim", "frameTime", "ActionFrameTime", "to", "angle", "delay", "start", "playDieWhiteAnim", "onDieAnimEnd", "addBullet", "pause", "resume"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAASA,MAAAA,U,OAAAA,U;AAA6BC,MAAAA,I,OAAAA,I;AAAUC,MAAAA,K,OAAAA,K;AAAOC,MAAAA,E,OAAAA,E;AAAIC,MAAAA,W,OAAAA,W;;AACpDC,MAAAA,Q;;AACEC,MAAAA,K,iBAAAA,K;;AACFC,MAAAA,Y;;AACEC,MAAAA,O,iBAAAA,O;;AACFC,MAAAA,U;;;;;;;;;OACD;AAAEC,QAAAA,OAAF;AAAWC,QAAAA;AAAX,O,GAAwBX,U;;yBAGTY,Q,WADpBF,OAAO,CAAC,UAAD,C,gBAAR,MACqBE,QADrB;AAAA;AAAA,gCAC+C;AAAA;AAAA;AAAA,eAC3CC,QAD2C,GAC3B,IAD2B;AAAA,eAE3CC,YAF2C,GAElB,EAFkB;AAAA,eAG3CC,GAH2C,GAG7B,EAH6B;AAAA,eAI3CC,GAJ2C,GAI7B,CAJ6B;AAAA,eAK3CC,MAL2C,GAK5B,IAL4B;AAAA,eAM3CC,OAN2C,GAM1B,EAN0B;AAAA,eAO3CC,UAP2C,GAOtB,EAPsB;AAAA,eAQ3CC,QAR2C,GAQzB,EARyB;AAAA,eAS3CC,MAT2C,GAS3B,EAT2B;AAAA,eAU3CC,UAV2C,GAUvB,EAVuB;AAAA,eAW3CC,UAX2C,GAWvB,EAXuB;AAAA,eAY3CC,UAZ2C,GAYvB,EAZuB;AAAA,eAa3CC,QAb2C,GAazB,EAbyB;AAAA,eAc3CC,OAd2C,GAc1B,EAd0B;AAAA,eAe3CC,UAf2C,GAepB,CAAC,GAAD,EAAM,GAAN,EAAW,GAAX,CAfoB;AAAA,eAgB3CC,UAhB2C,GAgBpB,CAAC,GAAD,EAAM,GAAN,EAAW,GAAX,CAhBoB;AAAA,eAiB3CC,SAjB2C,GAiBtB,KAjBsB;AAAA,eAkB3CC,OAlB2C,GAkB1B,EAlB0B;AAAA,eAmB3CC,SAnB2C,GAmBvB,CAnBuB;AAAA,eAoB3CC,OApB2C,GAoBzB,CApByB;AAAA,eAqB3CC,WArB2C,GAqBtB,EArBsB;AAAA;;AAuB3C;AACJ;AACA;AACA;AACIC,QAAAA,OAAO,CAACC,IAAD,EAAY;AACf,eAAKtB,QAAL,GAAgBsB,IAAhB;AACA,eAAKnB,GAAL,GAAW,KAAKH,QAAL,CAAcG,GAAzB;AACA,eAAKS,QAAL,GAAgB,KAAKZ,QAAL,CAAcY,QAA9B;AACA,eAAKF,UAAL,GAAkB,KAAKV,QAAL,CAAcU,UAAhC;AACA,eAAKC,UAAL,GAAkB,KAAKX,QAAL,CAAcW,UAAhC;AACA,eAAKE,OAAL,GAAe,KAAKb,QAAL,CAAca,OAA7B;AACA,eAAKU,MAAL,GAAcD,IAAI,CAACC,MAAnB;AACA,eAAKC,UAAL,GAAkBF,IAAI,CAACG,aAAvB;;AAEA,cAAI,KAAKzB,QAAL,CAAcc,UAAd,CAAyBY,MAAzB,GAAkC,CAAtC,EAAyC;AACrC,iBAAKZ,UAAL,GAAkB,KAAKd,QAAL,CAAcc,UAAhC;AACH;;AACD,cAAI,KAAKd,QAAL,CAAce,UAAd,CAAyBW,MAAzB,GAAkC,CAAtC,EAAyC;AACrC,iBAAKX,UAAL,GAAkB,KAAKf,QAAL,CAAce,UAAhC;AACH;AACJ;AAED;AACJ;AACA;AACA;AACA;;;AACIY,QAAAA,OAAO,CAACC,IAAD,EAAYC,QAAZ,EAA6B;AAChC,eAAKxB,OAAL,CAAayB,IAAb,CAAkBF,IAAlB;;AACA,cAAIC,QAAJ,EAAc;AACV,iBAAKvB,UAAL,CAAgBwB,IAAhB,CAAqBD,QAArB;AACH;AACJ;AAED;AACJ;AACA;AACA;;;AACIE,QAAAA,UAAU,CAACH,IAAD,EAAY;AAClB,cAAMI,KAAK,GAAG,KAAK3B,OAAL,CAAa4B,OAAb,CAAqBL,IAArB,CAAd;;AACA,cAAII,KAAK,IAAI,CAAb,EAAgB;AACZ,iBAAK3B,OAAL,CAAa6B,MAAb,CAAoBF,KAApB,EAA2B,CAA3B;;AACA,gBAAI,KAAK1B,UAAL,CAAgBoB,MAAhB,GAAyBM,KAA7B,EAAoC;AAChC,mBAAK1B,UAAL,CAAgB4B,MAAhB,CAAuBF,KAAvB,EAA8B,CAA9B;AACH;AACJ;AACJ;AAED;AACJ;AACA;;;AACIG,QAAAA,SAAS,GAAG;AACR,eAAK9B,OAAL,CAAa6B,MAAb,CAAoB,CAApB;AACA,eAAK5B,UAAL,CAAgB4B,MAAhB,CAAuB,CAAvB;AACH;AAED;AACJ;AACA;AACA;;;AACIE,QAAAA,QAAQ,CAACC,KAAD,EAAa;AACjB,eAAK9B,QAAL,CAAcuB,IAAd,CAAmBO,KAAnB;AACH;AAED;AACJ;AACA;AACA;;;AACIC,QAAAA,WAAW,CAACD,KAAD,EAAa;AACpB;AAAA;AAAA,8BAAME,SAAN,CAAgB,KAAKhC,QAArB,EAA+B8B,KAA/B;AACH;AAED;AACJ;AACA;AACA;;;AACIG,QAAAA,SAAS,CAACC,MAAD,EAAc;AACnB,eAAKjC,MAAL,CAAYsB,IAAZ,CAAiBW,MAAjB;AACH;AAED;AACJ;AACA;AACA;;;AACIC,QAAAA,YAAY,CAACD,MAAD,EAAc;AACtB;AAAA;AAAA,8BAAMF,SAAN,CAAgB,KAAK/B,MAArB,EAA6BiC,MAA7B;AACH;AAED;AACJ;AACA;;;AACIE,QAAAA,kBAAkB,GAAG;AACjB,eAAK,IAAMC,WAAX,IAA0B,KAAK5C,QAAL,CAAcS,UAAxC,EAAoD;AAChD,gBAAMoC,QAAQ,GAAG,KAAKC,WAAL,CAAiBF,WAAjB,CAAjB;AACA,iBAAKnC,UAAL,CAAgBqB,IAAhB,CAAqBe,QAArB;AACH;AACJ;AAED;AACJ;AACA;AACA;;;AACIE,QAAAA,mBAAmB,CAACC,MAAD,EAAkB;AACjC,eAAK,IAAMH,QAAX,IAAuB,KAAKpC,UAA5B,EAAwC;AACpC,gBAAIoC,QAAJ,EAAc;AACVA,cAAAA,QAAQ,CAACI,cAAT,CAAwBD,MAAxB;AACH;AACJ;AACJ;AAED;AACJ;AACA;AACA;;;AACIF,QAAAA,WAAW,CAACxB,IAAD,EAA0B;AACjC,cAAM4B,IAAI,GAAG,IAAI9D,IAAJ,EAAb;AACA8D,UAAAA,IAAI,CAACC,YAAL,CAAkB5D,WAAlB;AACA,eAAK2D,IAAL,CAAUE,QAAV,CAAmBF,IAAnB;AACA,cAAML,QAAQ,GAAG;AAAA;AAAA,8BAAMQ,SAAN,CAAgBH,IAAhB;AAAA;AAAA,2CAAjB;AACAL,UAAAA,QAAQ,CAACS,MAAT,CAAgB,IAAhB,EAAsBhC,IAAtB;AACAuB,UAAAA,QAAQ,CAACrB,UAAT,GAAsB,KAAKxB,QAAL,CAAcyB,aAApC;AACA,iBAAOoB,QAAP;AACH;AAED;AACJ;AACA;AACA;AACA;AACA;;;AACIU,QAAAA,MAAM,CAACC,CAAD,EAAYC,CAAZ,EAAuBC,MAAvB,EAAgD;AAAA,cAAzBA,MAAyB;AAAzBA,YAAAA,MAAyB,GAAP,KAAO;AAAA;;AAClD,eAAKR,IAAL,CAAUS,WAAV,CAAsBH,CAAtB,EAAyBC,CAAzB;AACH;AAED;AACJ;AACA;AACA;;;AACIG,QAAAA,eAAe,CAACC,KAAD,EAAkB;AAC7B,eAAK5D,YAAL,GAAoB4D,KAApB;AACH;AAED;AACJ;AACA;AACA;;;AACIC,QAAAA,MAAM,CAAC5D,GAAD,EAAc;AAChB,eAAKA,GAAL,GAAWA,GAAX;AACH;AAED;AACJ;AACA;;;AACI6D,QAAAA,YAAY,GAAG;AACX,cAAI,KAAK7D,GAAL,KAAa,EAAjB,EAAqB;AACjB;AAAA;AAAA,oCAAQ8D,aAAR,CAAsBC,gBAAtB,CAAuC,KAAK/D,GAA5C;AACH,WAFD,MAEO;AACH;AAAA;AAAA,oCAAQ8D,aAAR,CAAsBE,cAAtB;AACH;AACJ;AAED;AACJ;AACA;;;AACIC,QAAAA,KAAK,GAAG;AACJ,cAAI,CAAC,KAAKC,MAAV,EAAkB;AACd,iBAAKA,MAAL,GAAc,IAAd;AACA,iBAAKrB,mBAAL,CAAyB,KAAzB,EAFc,CAGd;;AACA,iBAAKsB,KAAL;AACH;AACJ;AAED;AACJ;AACA;;;AACIC,QAAAA,WAAW,GAAG,CAAG;AAEjB;AACJ;AACA;;;AACID,QAAAA,KAAK,GAAG;AACJ;AACA;AACA;AACA;AAEA;AACA;AAEA;AACA;AACA;AAEA;AACA;AACA;AAEA;AACA;AACA,eAAKE,aAAL;AACH;AAED;AACJ;AACA;;;AACIA,QAAAA,aAAa,GAAG;AACZ,eAAK,IAAMC,MAAX,IAAqB,KAAKvD,OAA1B,EAAmC;AAC/BuD,YAAAA,MAAM,CAACC,SAAP;AACH;;AACD,eAAKxD,OAAL,CAAaiB,MAAb,CAAoB,CAApB;AACH;AAED;AACJ;AACA;;;AACIwC,QAAAA,WAAW,GAAG,CACV;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AACH;AAED;AACJ;AACA;;;AACIC,QAAAA,aAAa,GAAG;AACZ,cAAMC,SAAS,GAAG;AAAA;AAAA,wCAAWC,eAA7B;;AACA,cAAI,KAAKzE,MAAT,EAAiB;AACbf,YAAAA,KAAK,CAAC,KAAKe,MAAN,CAAL,CACK0E,EADL,CACQ,IAAIF,SADZ,EACuB;AAAE/C,cAAAA,QAAQ,EAAEvC,EAAE,CAAC,CAAC,CAAF,EAAK,CAAC,CAAN;AAAd,aADvB,EAEKwF,EAFL,CAEQ,IAAIF,SAFZ,EAEuB;AAAE/C,cAAAA,QAAQ,EAAEvC,EAAE,CAAC,EAAD,EAAK,CAAC,EAAN,CAAd;AAAyByF,cAAAA,KAAK,EAAE;AAAhC,aAFvB,EAGKD,EAHL,CAGQ,IAAIF,SAHZ,EAGuB;AAAE/C,cAAAA,QAAQ,EAAEvC,EAAE,CAAC,CAAD,EAAI,CAAJ;AAAd,aAHvB,EAIKwF,EAJL,CAIQ,IAAIF,SAJZ,EAIuB;AAAE/C,cAAAA,QAAQ,EAAEvC,EAAE,CAAC,EAAD,EAAK,CAAC,CAAN,CAAd;AAAwByF,cAAAA,KAAK,EAAE;AAA/B,aAJvB,EAKKD,EALL,CAKQ,IAAIF,SALZ,EAKuB;AAAE/C,cAAAA,QAAQ,EAAEvC,EAAE,CAAC,EAAD,EAAK,CAAL;AAAd,aALvB,EAMKwF,EANL,CAMQF,SANR,EAMmB;AAAE/C,cAAAA,QAAQ,EAAEvC,EAAE,CAAC,EAAD,EAAK,CAAC,CAAN;AAAd,aANnB,EAOKwF,EAPL,CAOQF,SAPR,EAOmB;AAAE/C,cAAAA,QAAQ,EAAEvC,EAAE,CAAC,EAAD,EAAK,CAAL;AAAd,aAPnB,EAQKwF,EARL,CAQQF,SARR,EAQmB;AAAE/C,cAAAA,QAAQ,EAAEvC,EAAE,CAAC,CAAD,EAAI,CAAC,CAAL;AAAd,aARnB,EASKwF,EATL,CASQF,SATR,EASmB;AAAE/C,cAAAA,QAAQ,EAAEvC,EAAE,CAAC,EAAD,EAAK,CAAL;AAAd,aATnB,EAUKwF,EAVL,CAUQF,SAVR,EAUmB;AAAE/C,cAAAA,QAAQ,EAAEvC,EAAE,CAAC,CAAC,CAAF,EAAK,CAAC,CAAN;AAAd,aAVnB,EAWKwF,EAXL,CAWQF,SAXR,EAWmB;AAAE/C,cAAAA,QAAQ,EAAEvC,EAAE,CAAC,CAAD,EAAI,CAAJ;AAAd,aAXnB,EAYKwF,EAZL,CAYQF,SAZR,EAYmB;AAAE/C,cAAAA,QAAQ,EAAEvC,EAAE,CAAC,CAAC,CAAF,EAAK,CAAC,CAAN;AAAd,aAZnB,EAaKwF,EAbL,CAaQF,SAbR,EAamB;AAAE/C,cAAAA,QAAQ,EAAEvC,EAAE,CAAC,CAAD,EAAI,CAAJ;AAAd,aAbnB,EAcKwF,EAdL,CAcQF,SAdR,EAcmB;AAAE/C,cAAAA,QAAQ,EAAEvC,EAAE,CAAC,CAAC,CAAF,EAAK,CAAC,CAAN;AAAd,aAdnB,EAeKwF,EAfL,CAeQF,SAfR,EAemB;AAAE/C,cAAAA,QAAQ,EAAEvC,EAAE,CAAC,CAAD,EAAI,CAAJ;AAAd,aAfnB,EAgBKwF,EAhBL,CAgBQF,SAhBR,EAgBmB;AAAE/C,cAAAA,QAAQ,EAAEvC,EAAE,CAAC,CAAC,CAAF,EAAK,CAAC,CAAN;AAAd,aAhBnB,EAiBK0F,KAjBL,CAiBWJ,SAjBX,EAkBKE,EAlBL,CAkBQF,SAlBR,EAkBmB;AAAE/C,cAAAA,QAAQ,EAAEvC,EAAE,CAAC,CAAD,EAAI,CAAJ;AAAd,aAlBnB,EAmBK2F,KAnBL;AAoBH;AACJ;AAED;AACJ;AACA;;;AACIC,QAAAA,gBAAgB,GAAG,CACf;AACA;AACA;AACA;AACA;AACA;AACH;AAED;AACJ;AACA;;;AACIC,QAAAA,YAAY,GAAG,CAAG,CAtTyB,CAwT3C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACJ;AACA;AACA;;;AACIC,QAAAA,SAAS,CAACZ,MAAD,EAAc;AACnB,cAAI,KAAKvD,OAAT,EAAkB;AACd,iBAAKA,OAAL,CAAaa,IAAb,CAAkB0C,MAAlB;AACH;AACJ,SAjY0C,CAmY3C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACJ;AACA;;;AACIa,QAAAA,KAAK,GAAG,CAAG;AAEX;AACJ;AACA;;;AACIC,QAAAA,MAAM,GAAG,CAAG;;AAhhB+B,O", "sourcesContent": ["import { _decorator, Component, Vec2, Node, v2, tween, v3, UITransform} from 'cc';\r\nimport BossHurt from './BossHurt';\r\nimport { Tools } from '../../../utils/Tools';\r\nimport BossCollider from './BossCollider';\r\nimport { GameIns } from '../../../GameIns';\r\nimport GameConfig from '../../../const/GameConfig';\r\nconst { ccclass, property } = _decorator;\r\n\r\n@ccclass(\"BossBase\")\r\nexport default class BossBase extends BossHurt {\r\n    baseData: any = null;\r\n    propertyRate: number[] = [];\r\n    tip: string = \"\";\r\n    exp: number = 0;\r\n    uiNode: Node = null;\r\n    unitArr: any[] = [];\r\n    unitPosArr: Vec2[] = [];\r\n    spineArr: any[] = [];\r\n    actArr: any[] = [];\r\n    collideArr: any[] = [];\r\n    blastParam: any[] = [];\r\n    blastShake: any[] = [];\r\n    onlyLoot: any[] = [];\r\n    lootArr: any[] = [];\r\n    lootParam0: number[] = [167, 250, 0.6];\r\n    lootParam1: number[] = [250, 350, 1.2];\r\n    dieBullet: boolean = false;\r\n    bullets: any[] = [];\r\n    m_totalHp: number = 0;\r\n    m_curHp: number = 0;\r\n    m_swordUnit: any[] = [];\r\n\r\n    /**\r\n     * 设置 Boss 数据\r\n     * @param data Boss 数据\r\n     */\r\n    setData(data: any) {\r\n        this.baseData = data;\r\n        this.exp = this.baseData.exp;\r\n        this.onlyLoot = this.baseData.onlyLoot;\r\n        this.blastParam = this.baseData.blastParam;\r\n        this.blastShake = this.baseData.blastShake;\r\n        this.lootArr = this.baseData.lootArr;\r\n        this.attack = data.attack;\r\n        this.collideAtk = data.collideAttack;\r\n\r\n        if (this.baseData.lootParam0.length > 0) {\r\n            this.lootParam0 = this.baseData.lootParam0;\r\n        }\r\n        if (this.baseData.lootParam1.length > 0) {\r\n            this.lootParam1 = this.baseData.lootParam1;\r\n        }\r\n    }\r\n\r\n    /**\r\n     * 添加单元\r\n     * @param unit 单元\r\n     * @param position 单元位置\r\n     */\r\n    addUnit(unit: any, position?: Vec2) {\r\n        this.unitArr.push(unit);\r\n        if (position) {\r\n            this.unitPosArr.push(position);\r\n        }\r\n    }\r\n\r\n    /**\r\n     * 移除单元\r\n     * @param unit 单元\r\n     */\r\n    removeUnit(unit: any) {\r\n        const index = this.unitArr.indexOf(unit);\r\n        if (index >= 0) {\r\n            this.unitArr.splice(index, 1);\r\n            if (this.unitPosArr.length > index) {\r\n                this.unitPosArr.splice(index, 1);\r\n            }\r\n        }\r\n    }\r\n\r\n    /**\r\n     * 清空单元\r\n     */\r\n    clearUnit() {\r\n        this.unitArr.splice(0);\r\n        this.unitPosArr.splice(0);\r\n    }\r\n\r\n    /**\r\n     * 添加骨骼动画\r\n     * @param spine 骨骼动画\r\n     */\r\n    addSpine(spine: any) {\r\n        this.spineArr.push(spine);\r\n    }\r\n\r\n    /**\r\n     * 移除骨骼动画\r\n     * @param spine 骨骼动画\r\n     */\r\n    removeSpine(spine: any) {\r\n        Tools.arrRemove(this.spineArr, spine);\r\n    }\r\n\r\n    /**\r\n     * 添加行为\r\n     * @param action 行为\r\n     */\r\n    addAction(action: any) {\r\n        this.actArr.push(action);\r\n    }\r\n\r\n    /**\r\n     * 移除行为\r\n     * @param action 行为\r\n     */\r\n    removeAction(action: any) {\r\n        Tools.arrRemove(this.actArr, action);\r\n    }\r\n\r\n    /**\r\n     * 创建自身碰撞体\r\n     */\r\n    createSelfCollides() {\r\n        for (const collideData of this.baseData.collideArr) {\r\n            const collider = this.addCollider(collideData);\r\n            this.collideArr.push(collider);\r\n        }\r\n    }\r\n\r\n    /**\r\n     * 设置自身碰撞体是否可用\r\n     * @param enable 是否可用\r\n     */\r\n    setSelfColliderAble(enable: boolean) {\r\n        for (const collider of this.collideArr) {\r\n            if (collider) {\r\n                collider.setCollideAble(enable);\r\n            }\r\n        }\r\n    }\r\n\r\n    /**\r\n     * 添加碰撞体\r\n     * @param data 碰撞体数据\r\n     */\r\n    addCollider(data: any): BossCollider {\r\n        const node = new Node();\r\n        node.addComponent(UITransform);\r\n        this.node.addChild(node);\r\n        const collider = Tools.addScript(node, BossCollider);\r\n        collider.create(this, data);\r\n        collider.collideAtk = this.baseData.collideAttack;\r\n        return collider;\r\n    }\r\n\r\n    /**\r\n     * 设置位置\r\n     * @param x X 坐标\r\n     * @param y Y 坐标\r\n     * @param update 是否更新\r\n     */\r\n    setPos(x: number, y: number, update: boolean = false) {\r\n        this.node.setPosition(x, y);\r\n    }\r\n\r\n    /**\r\n     * 设置属性倍率\r\n     * @param rates 属性倍率\r\n     */\r\n    setPropertyRate(rates: number[]) {\r\n        this.propertyRate = rates;\r\n    }\r\n\r\n    /**\r\n     * 设置提示信息\r\n     * @param tip 提示信息\r\n     */\r\n    setTip(tip: string) {\r\n        this.tip = tip;\r\n    }\r\n\r\n    /**\r\n     * 变形结束\r\n     */\r\n    transformEnd() {\r\n        if (this.tip !== \"\") {\r\n            GameIns.battleManager.bossChangeFinish(this.tip);\r\n        } else {\r\n            GameIns.battleManager.bossFightStart();\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Boss 死亡逻辑\r\n     */\r\n    toDie() {\r\n        if (!this.isDead) {\r\n            this.isDead = true;\r\n            this.setSelfColliderAble(false);\r\n            // this.checkLoot();\r\n            this.onDie();\r\n        }\r\n    }\r\n\r\n    /**\r\n     * 开始战斗\r\n     */\r\n    startBattle() { }\r\n\r\n    /**\r\n     * Boss 死亡回调\r\n     */\r\n    onDie() {\r\n        // TaskManager.taskNumberChange(TaskType.KillBoss, 1);\r\n        // TaskManager.taskNumberChange(TaskType.KillPlane, 1);\r\n        // TaskManager.achievementNumberChange(AchievementType.KillBoss, 1);\r\n        // TaskManager.achievementNumberChange(AchievementType.KillEnemy, 1);\r\n\r\n        // this.playDieAnim();\r\n        // GameData.killEnemyNumber += 1;\r\n\r\n        // for (const plane of EnemyManager.planes) {\r\n        //     plane.die(EnemyDestroyType.Die);\r\n        // }\r\n\r\n        // if (this.exp > 0) {\r\n        //     LootManager.addExp(this.exp);\r\n        // }\r\n\r\n        // MainPlaneManager.checkKillHp();\r\n        // MainPlaneManager.checkKillAtk();\r\n        this.removeBullets();\r\n    }\r\n\r\n    /**\r\n     * 移除子弹\r\n     */\r\n    removeBullets() {\r\n        for (const bullet of this.bullets) {\r\n            bullet.dieRemove();\r\n        }\r\n        this.bullets.splice(0);\r\n    }\r\n\r\n    /**\r\n     * 播放死亡动画\r\n     */\r\n    playDieAnim() {\r\n        // for (let i = 0; i < this.blastParam.length; i++) {\r\n        //     const blast = this.blastParam[i];\r\n        //     const delay = blast[3] * GameConfig.ActionFrameTime;\r\n\r\n        //     this.scheduleOnce(() => {\r\n        //         EnemyEffectLayer.addBlastEffect(this, blast[2], {\r\n        //             x: blast[0],\r\n        //             y: blast[1],\r\n        //             scale: blast[4],\r\n        //             angle: blast[5],\r\n        //         });\r\n\r\n        //         if (blast[6] > 0) {\r\n        //             GameIns.audioManager.playEffect(`blast${blast[6]}`);\r\n        //         }\r\n        //     }, delay);\r\n        // }\r\n\r\n        // for (const shake of this.blastShake) {\r\n        //     const delay = shake.x * GameConfig.ActionFrameTime;\r\n        //     this.scheduleOnce(() => {\r\n        //         MainCamera.shake1(EnemyManager.shakeParam[shake.y]);\r\n        //     }, delay);\r\n        // }\r\n    }\r\n\r\n    /**\r\n     * 播放震动动画\r\n     */\r\n    playShakeAnim() {\r\n        const frameTime = GameConfig.ActionFrameTime;\r\n        if (this.uiNode) {\r\n            tween(this.uiNode)\r\n                .to(2 * frameTime, { position: v3(-3, -2) })\r\n                .to(2 * frameTime, { position: v3(11, -14), angle: 1 })\r\n                .to(2 * frameTime, { position: v3(7, 4) })\r\n                .to(2 * frameTime, { position: v3(20, -9), angle: 0 })\r\n                .to(2 * frameTime, { position: v3(29, 7) })\r\n                .to(frameTime, { position: v3(13, -5) })\r\n                .to(frameTime, { position: v3(17, 2) })\r\n                .to(frameTime, { position: v3(4, -6) })\r\n                .to(frameTime, { position: v3(14, 4) })\r\n                .to(frameTime, { position: v3(-1, -4) })\r\n                .to(frameTime, { position: v3(5, 6) })\r\n                .to(frameTime, { position: v3(-3, -5) })\r\n                .to(frameTime, { position: v3(1, 3) })\r\n                .to(frameTime, { position: v3(-7, -6) })\r\n                .to(frameTime, { position: v3(0, 2) })\r\n                .to(frameTime, { position: v3(-3, -4) })\r\n                .delay(frameTime)\r\n                .to(frameTime, { position: v3(0, 0) })\r\n                .start();\r\n        }\r\n    }\r\n\r\n    /**\r\n     * 播放白屏死亡动画\r\n     */\r\n    playDieWhiteAnim() {\r\n        // this.scheduleOnce(() => {\r\n        //     EffectLayer.showWhiteScreen(4 * GameConfig.ActionFrameTime, 255);\r\n        //     if (this.uiNode) {\r\n        //         this.uiNode.opacity = 0;\r\n        //     }\r\n        // }, 41 * GameConfig.ActionFrameTime);\r\n    }\r\n\r\n    /**\r\n     * 死亡动画结束回调\r\n     */\r\n    onDieAnimEnd() { }\r\n\r\n    // /**\r\n    //  * 改变血量\r\n    //  * @param delta 血量变化值\r\n    //  */\r\n    // hpChange(delta: number) {\r\n    //     this.m_curHp += delta;\r\n    //     if (this.m_curHp < 0) {\r\n    //         this.m_curHp = 0;\r\n    //     }\r\n    //     BossBattleManager.hpChange(delta, this.node);\r\n    // }\r\n\r\n    // /**\r\n    //  * 检查掉落物品\r\n    //  */\r\n    // checkLoot() {\r\n    //     if (BattleManager.isGameType(GameType.Boss)) {\r\n    //         BossBattleManager.lootForDie(this.node.convertToWorldSpaceAR(Vec2.ZERO));\r\n    //     } else {\r\n    //         let allBossesDead = true;\r\n    //         for (const boss of BossManager.bosses) {\r\n    //             if (!boss.isDead) {\r\n    //                 allBossesDead = false;\r\n    //                 break;\r\n    //             }\r\n    //         }\r\n\r\n    //         if (this.isDead && allBossesDead) {\r\n    //             const loot = LootManager.checkLoot(EnemyScale.Large, true);\r\n    //             LootManager.addBossProp(this.node.position, this.lootParam1, loot);\r\n    //         }\r\n    //     }\r\n    // }\r\n\r\n    // /**\r\n    //  * 更新游戏逻辑\r\n    //  * @param deltaTime 每帧时间\r\n    //  */\r\n    // updateGameLogic(deltaTime: number) {\r\n    //     for (const collider of this.collideArr) {\r\n    //         collider.updateGameLogic(deltaTime);\r\n    //     }\r\n    // }\r\n\r\n    // /**\r\n    //  * 获取所有单元\r\n    //  */\r\n    // getUnits(): any[] {\r\n    //     return [];\r\n    // }\r\n\r\n    // /**\r\n    //  * 获取所有碰撞体\r\n    //  */\r\n    // getCollides(): any[] {\r\n    //     return this.collideArr;\r\n    // }\r\n\r\n    // /**\r\n    //  * 获取血量百分比\r\n    //  */\r\n    // getHpPercent(): number {\r\n    //     return this.m_curHp / this.m_totalHp;\r\n    // }\r\n\r\n    /**\r\n     * 添加子弹\r\n     * @param bullet 子弹对象\r\n     */\r\n    addBullet(bullet: any) {\r\n        if (this.bullets) {\r\n            this.bullets.push(bullet);\r\n        }\r\n    }\r\n\r\n    // /**\r\n    //  * 移除子弹\r\n    //  * @param bullet 子弹对象\r\n    //  */\r\n    // removeBullet(bullet: any) {\r\n    //     if (this.bullets) {\r\n    //         const index = this.bullets.indexOf(bullet);\r\n    //         if (index >= 0) {\r\n    //             this.bullets.splice(index, 1);\r\n    //         }\r\n    //     }\r\n    // }\r\n\r\n    // /**\r\n    //  * 初始化剑气\r\n    //  */\r\n    // initSword(): Node {\r\n    //     const swordNode = instantiate(GameIns.loadManager.getRes(\"swordBlast\", Prefab));\r\n    //     swordNode.name = \"sword\";\r\n    //     swordNode.getComponent(SwordBlast).init();\r\n    //     return swordNode;\r\n    // }\r\n\r\n    // /**\r\n    //  * 播放剑气待机动画\r\n    //  * @param range 范围\r\n    //  * @param play 是否播放动画\r\n    //  */\r\n    // playSwordIdleAnim(range: number[], play: boolean = true) {\r\n    //     this.m_swordUnit.splice(0);\r\n    //     const availableUnits = [];\r\n    //     const availablePositions = [];\r\n\r\n    //     for (const unit of this.unitArr) {\r\n    //         if (!unit.isDead && unit.getCollideAble && unit.getCollideAble()) {\r\n    //             availableUnits.push(unit);\r\n    //             const position = this.unitPosArr[this.unitArr.indexOf(unit)];\r\n    //             if (position) {\r\n    //                 availablePositions.push(position);\r\n    //             }\r\n    //         }\r\n    //     }\r\n\r\n    //     if (availableUnits.length > 0) {\r\n    //         const selectedUnits = [];\r\n    //         const selectedPositions = [];\r\n    //         const count = Tools.random_int(range[0], range[1]);\r\n\r\n    //         for (let i = 0; i < count; i++) {\r\n    //             const index = Tools.random_int(0, availableUnits.length - 1);\r\n    //             this.m_swordUnit.push(availableUnits[index]);\r\n    //             selectedUnits.push(availableUnits[index]);\r\n    //             availableUnits.splice(index, 1);\r\n\r\n    //             if (availablePositions[index]) {\r\n    //                 selectedPositions.push(availablePositions[index]);\r\n    //                 availablePositions.splice(index, 1);\r\n    //             }\r\n\r\n    //             if (availableUnits.length === 0) {\r\n    //                 break;\r\n    //             }\r\n    //         }\r\n\r\n    //         if (play) {\r\n    //             for (let i = 0; i < this.m_swordUnit.length; i++) {\r\n    //                 const unit = this.m_swordUnit[i];\r\n    //                 let sword = unit.node.getChildByName(\"sword\");\r\n    //                 if (!sword) {\r\n    //                     sword = this.initSword();\r\n    //                     unit.node.addChild(sword, 10);\r\n    //                 }\r\n    //                 sword.angle = Tools.random_int(0, 359);\r\n    //                 sword.scale = Tools.random_int(8, 11) / 10;\r\n\r\n    //                 const position = unit.getColliderPos ? unit.getColliderPos() : selectedPositions[i];\r\n    //                 if (position) {\r\n    //                     sword.x = position.x;\r\n    //                     sword.y = position.y;\r\n    //                 }\r\n\r\n    //                 const swordComponent = sword.getComponent(SwordBlast);\r\n    //                 if (swordComponent) {\r\n    //                     swordComponent.playIdleAnim();\r\n    //                 }\r\n    //             }\r\n    //         }\r\n    //     }\r\n    // }\r\n\r\n    // /**\r\n    //  * 播放剑气受伤动画\r\n    //  * @param delay 延迟时间\r\n    //  */\r\n    // playSwordHurtAnim(delay: number = 0) {\r\n    //     for (const unit of this.m_swordUnit) {\r\n    //         const sword = unit.node.getChildByName(\"sword\");\r\n    //         if (sword) {\r\n    //             const swordComponent = sword.getComponent(SwordBlast);\r\n    //             this.scheduleOnce(() => {\r\n    //                 if (swordComponent) {\r\n    //                     swordComponent.playHurtAnim();\r\n    //                 }\r\n    //             }, delay);\r\n    //         }\r\n    //     }\r\n    // }\r\n\r\n    // /**\r\n    //  * 剑气伤害\r\n    //  * @param damage 伤害值\r\n    //  */\r\n    // swordHurt(damage: number) {\r\n    //     for (const unit of this.m_swordUnit) {\r\n    //         if (unit && unit.hurt) {\r\n    //             unit.hurt(damage);\r\n    //         }\r\n    //     }\r\n    //     this.m_swordUnit.splice(0);\r\n    // }\r\n\r\n    // /**\r\n    //  * 剑气结束\r\n    //  */\r\n    // swordOver() {\r\n    //     for (const spine of this.spineArr) {\r\n    //         if (spine && spine.node && spine.node.active) {\r\n    //             spine.timeScale = 1;\r\n    //         }\r\n    //     }\r\n    //     this.spineArr.splice(0);\r\n    // }\r\n\r\n    /**\r\n     * 暂停\r\n     */\r\n    pause() { }\r\n\r\n    /**\r\n     * 恢复\r\n     */\r\n    resume() { }\r\n}"]}