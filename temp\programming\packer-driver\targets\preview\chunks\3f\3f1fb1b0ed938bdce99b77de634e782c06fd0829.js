System.register(["__unresolved_0", "cc", "__unresolved_1", "__unresolved_2", "__unresolved_3", "__unresolved_4", "__unresolved_5", "__unresolved_6", "__unresolved_7", "__unresolved_8", "__unresolved_9", "__unresolved_10", "__unresolved_11", "__unresolved_12", "__unresolved_13", "__unresolved_14", "__unresolved_15"], function (_export, _context) {
  "use strict";

  var _reporterNs, _cclegacy, __checkObsolete__, __checkObsoleteInNamespace__, _decorator, Node, Sprite, Animation, Label, Vec2, tween, UIOpacity, instantiate, sp, UITransform, Tween, Color, view, v3, Plane, ColliderComp, GameConst, GameIns, MainSkillBase, GameEnum, GameConfig, <PERSON><PERSON><PERSON><PERSON>, <PERSON>MapRun, GameFunc, FireShells, <PERSON>et, EnemyEntity, BossUnit, GameEvent, _dec, _dec2, _dec3, _dec4, _dec5, _dec6, _dec7, _dec8, _dec9, _dec10, _dec11, _dec12, _dec13, _dec14, _dec15, _dec16, _dec17, _dec18, _dec19, _dec20, _dec21, _dec22, _class, _class2, _descriptor, _descriptor2, _descriptor3, _descriptor4, _descriptor5, _descriptor6, _descriptor7, _descriptor8, _descriptor9, _descriptor10, _descriptor11, _descriptor12, _descriptor13, _descriptor14, _descriptor15, _descriptor16, _descriptor17, _descriptor18, _descriptor19, _descriptor20, _descriptor21, _class3, _crd, ccclass, property, AnimState, MainPlane;

  function asyncGeneratorStep(gen, resolve, reject, _next, _throw, key, arg) { try { var info = gen[key](arg); var value = info.value; } catch (error) { reject(error); return; } if (info.done) { resolve(value); } else { Promise.resolve(value).then(_next, _throw); } }

  function _asyncToGenerator(fn) { return function () { var self = this, args = arguments; return new Promise(function (resolve, reject) { var gen = fn.apply(self, args); function _next(value) { asyncGeneratorStep(gen, resolve, reject, _next, _throw, "next", value); } function _throw(err) { asyncGeneratorStep(gen, resolve, reject, _next, _throw, "throw", err); } _next(undefined); }); }; }

  function _initializerDefineProperty(target, property, descriptor, context) { if (!descriptor) return; Object.defineProperty(target, property, { enumerable: descriptor.enumerable, configurable: descriptor.configurable, writable: descriptor.writable, value: descriptor.initializer ? descriptor.initializer.call(context) : void 0 }); }

  function _applyDecoratedDescriptor(target, property, decorators, descriptor, context) { var desc = {}; Object.keys(descriptor).forEach(function (key) { desc[key] = descriptor[key]; }); desc.enumerable = !!desc.enumerable; desc.configurable = !!desc.configurable; if ('value' in desc || desc.initializer) { desc.writable = true; } desc = decorators.slice().reverse().reduce(function (desc, decorator) { return decorator(target, property, desc) || desc; }, desc); if (context && desc.initializer !== void 0) { desc.value = desc.initializer ? desc.initializer.call(context) : void 0; desc.initializer = undefined; } if (desc.initializer === void 0) { Object.defineProperty(target, property, desc); desc = null; } return desc; }

  function _initializerWarningHelper(descriptor, context) { throw new Error('Decorating class property failed. Please ensure that ' + 'transform-class-properties is enabled and runs after the decorators transform.'); }

  function _reportPossibleCrUseOfPlane(extras) {
    _reporterNs.report("Plane", "../Plane", _context.meta, extras);
  }

  function _reportPossibleCrUseOfColliderComp(extras) {
    _reporterNs.report("ColliderComp", "../../base/ColliderComp", _context.meta, extras);
  }

  function _reportPossibleCrUseOfGameConst(extras) {
    _reporterNs.report("GameConst", "../../../const/GameConst", _context.meta, extras);
  }

  function _reportPossibleCrUseOfGameIns(extras) {
    _reporterNs.report("GameIns", "../../../GameIns", _context.meta, extras);
  }

  function _reportPossibleCrUseOfMainSkillBase(extras) {
    _reporterNs.report("MainSkillBase", "./MainSkillBase", _context.meta, extras);
  }

  function _reportPossibleCrUseOfMainData(extras) {
    _reporterNs.report("MainData", "../../../data/MainData", _context.meta, extras);
  }

  function _reportPossibleCrUseOfGameEnum(extras) {
    _reporterNs.report("GameEnum", "../../../const/GameEnum", _context.meta, extras);
  }

  function _reportPossibleCrUseOfGameConfig(extras) {
    _reporterNs.report("GameConfig", "../../../const/GameConfig", _context.meta, extras);
  }

  function _reportPossibleCrUseOfBattleLayer(extras) {
    _reporterNs.report("BattleLayer", "../../layer/BattleLayer", _context.meta, extras);
  }

  function _reportPossibleCrUseOfGameMapRun(extras) {
    _reporterNs.report("GameMapRun", "../../map/GameMapRun", _context.meta, extras);
  }

  function _reportPossibleCrUseOfGameFunc(extras) {
    _reporterNs.report("GameFunc", "../../../GameFunc", _context.meta, extras);
  }

  function _reportPossibleCrUseOfFireShells(extras) {
    _reporterNs.report("FireShells", "./FireShells", _context.meta, extras);
  }

  function _reportPossibleCrUseOfBullet(extras) {
    _reporterNs.report("Bullet", "../../bullet/Bullet", _context.meta, extras);
  }

  function _reportPossibleCrUseOfEnemyEntity(extras) {
    _reporterNs.report("EnemyEntity", "../enemy/EnemyEntity", _context.meta, extras);
  }

  function _reportPossibleCrUseOfBossUnit(extras) {
    _reporterNs.report("BossUnit", "../boss/BossUnit", _context.meta, extras);
  }

  function _reportPossibleCrUseOfGameEvent(extras) {
    _reporterNs.report("GameEvent", "../../../event/GameEvent", _context.meta, extras);
  }

  return {
    setters: [function (_unresolved_) {
      _reporterNs = _unresolved_;
    }, function (_cc) {
      _cclegacy = _cc.cclegacy;
      __checkObsolete__ = _cc.__checkObsolete__;
      __checkObsoleteInNamespace__ = _cc.__checkObsoleteInNamespace__;
      _decorator = _cc._decorator;
      Node = _cc.Node;
      Sprite = _cc.Sprite;
      Animation = _cc.Animation;
      Label = _cc.Label;
      Vec2 = _cc.Vec2;
      tween = _cc.tween;
      UIOpacity = _cc.UIOpacity;
      instantiate = _cc.instantiate;
      sp = _cc.sp;
      UITransform = _cc.UITransform;
      Tween = _cc.Tween;
      Color = _cc.Color;
      view = _cc.view;
      v3 = _cc.v3;
    }, function (_unresolved_2) {
      Plane = _unresolved_2.default;
    }, function (_unresolved_3) {
      ColliderComp = _unresolved_3.ColliderComp;
    }, function (_unresolved_4) {
      GameConst = _unresolved_4.GameConst;
    }, function (_unresolved_5) {
      GameIns = _unresolved_5.GameIns;
    }, function (_unresolved_6) {
      MainSkillBase = _unresolved_6.default;
    }, function (_unresolved_7) {
      GameEnum = _unresolved_7.default;
    }, function (_unresolved_8) {
      GameConfig = _unresolved_8.default;
    }, function (_unresolved_9) {
      BattleLayer = _unresolved_9.default;
    }, function (_unresolved_10) {
      GameMapRun = _unresolved_10.default;
    }, function (_unresolved_11) {
      GameFunc = _unresolved_11.GameFunc;
    }, function (_unresolved_12) {
      FireShells = _unresolved_12.default;
    }, function (_unresolved_13) {
      Bullet = _unresolved_13.default;
    }, function (_unresolved_14) {
      EnemyEntity = _unresolved_14.default;
    }, function (_unresolved_15) {
      BossUnit = _unresolved_15.default;
    }, function (_unresolved_16) {
      GameEvent = _unresolved_16.default;
    }],
    execute: function () {
      _crd = true;

      _cclegacy._RF.push({}, "81e41b6hKRNqYJbS6uK9LDf", "MainPlane", undefined);

      __checkObsolete__(['_decorator', 'Component', 'Node', 'Sprite', 'Animation', 'Label', 'Vec2', 'tween', 'UIOpacity', 'instantiate', 'sp', 'UITransform', 'Tween', 'Color', 'view', 'v3', 'v2', 'SpriteFrame']);

      ({
        ccclass,
        property
      } = _decorator);
      /**
       * 动画状态枚举
       */

      AnimState = /*#__PURE__*/function (AnimState) {
        AnimState[AnimState["idle"] = 0] = "idle";
        AnimState[AnimState["crazy"] = 1] = "crazy";
        AnimState[AnimState["willCancelCrazy"] = 2] = "willCancelCrazy";
        return AnimState;
      }(AnimState || {});

      _export("MainPlane", MainPlane = (_dec = ccclass("MainPlane"), _dec2 = property(Sprite), _dec3 = property(sp.Skeleton), _dec4 = property(Sprite), _dec5 = property(Node), _dec6 = property(Node), _dec7 = property(Node), _dec8 = property(sp.Skeleton), _dec9 = property(Node), _dec10 = property(Node), _dec11 = property(Sprite), _dec12 = property(Sprite), _dec13 = property(Node), _dec14 = property(Node), _dec15 = property(Animation), _dec16 = property(Animation), _dec17 = property(Animation), _dec18 = property(Node), _dec19 = property(Node), _dec20 = property(Node), _dec21 = property(Node), _dec22 = property(Node), _dec(_class = (_class2 = (_class3 = class MainPlane extends (_crd && Plane === void 0 ? (_reportPossibleCrUseOfPlane({
        error: Error()
      }), Plane) : Plane) {
        constructor() {
          super(...arguments);

          _initializerDefineProperty(this, "skinImg", _descriptor, this);

          _initializerDefineProperty(this, "skinAnim", _descriptor2, this);

          _initializerDefineProperty(this, "streak", _descriptor3, this);

          _initializerDefineProperty(this, "planeNode", _descriptor4, this);

          _initializerDefineProperty(this, "point", _descriptor5, this);

          _initializerDefineProperty(this, "skin", _descriptor6, this);

          _initializerDefineProperty(this, "blast", _descriptor7, this);

          _initializerDefineProperty(this, "frontNode", _descriptor8, this);

          _initializerDefineProperty(this, "backNode", _descriptor9, this);

          _initializerDefineProperty(this, "shadow", _descriptor10, this);

          _initializerDefineProperty(this, "dg", _descriptor11, this);

          _initializerDefineProperty(this, "bl", _descriptor12, this);

          _initializerDefineProperty(this, "air", _descriptor13, this);

          _initializerDefineProperty(this, "attspeAnim", _descriptor14, this);

          _initializerDefineProperty(this, "scrAnim", _descriptor15, this);

          _initializerDefineProperty(this, "auxAnim", _descriptor16, this);

          _initializerDefineProperty(this, "avatarNode", _descriptor17, this);

          _initializerDefineProperty(this, "suitAnimBot", _descriptor18, this);

          _initializerDefineProperty(this, "suitAnimTop", _descriptor19, this);

          _initializerDefineProperty(this, "suitEffect", _descriptor20, this);

          _initializerDefineProperty(this, "mechaAnimNode", _descriptor21, this);

          // // 飞机皮肤相关
          // skinImg = null; // 飞机皮肤图片
          // skinAnim = null; // 飞机皮肤动画
          // streak = null; // 飞机拖尾效果
          // // 飞机节点
          // planeNode = null; // 飞机主节点
          // point = null; // 飞机中心点
          // skin = null; // 飞机皮肤节点
          // blast = null; // 爆炸效果节点
          // frontNode = null; // 前置节点
          // backNode = null; // 后置节点
          // shadow = null; // 飞机阴影
          // // 飞机状态相关
          // dg = null; // 防御技能节点
          // bl = null; // 蓝色特效节点
          // air = null; // 空气特效节点
          // attspeAnim = null; // 攻击特效动画
          // scrAnim = null; // 屏幕特效动画
          // auxAnim = null; // 辅助特效动画
          // // 飞机附加节点
          // avatarNode = null; // 飞机头像节点
          this.parkourTop = null;
          // 跑酷顶部节点
          this.parkourMid = null;
          // 跑酷中部节点
          this.parkourBot = null;
          // 跑酷底部节点
          this.below = null;
          // 飞机下方特效节点
          this.challengeAnim = null;
          // 挑战动画节点
          this.relifeComp = null;
          // 复活组件
          // 血条相关
          this.hpbarBack = null;
          // 血条背景
          this.hpbarMid = null;
          // 血条中间部分
          this.hpbarFont = null;
          // 血条前景
          this.hpfont = null;
          // 血量文字
          this.hpBar = null;
          // 血条节点
          this.hpMidActin = null;
          // 血条动画
          // 动画相关
          this.challengeAnimName = null;
          // 挑战动画名称
          // suitAnimBot = null; // 套装底部动画
          // suitAnimTop = null; // 套装顶部动画
          // suitEffect = null; // 套装特效
          // mechaAnimNode = null; // 机甲动画节点
          this.m_spines = [null, null, null, null];
          // 骨骼动画数组
          this.m_fireAnim = [];
          // 射击动画数组
          // 飞机状态
          this.m_screenDatas = [];
          // 屏幕数据
          this.m_moveEnable = true;
          // 是否允许移动
          this.m_config = null;
          // 飞机配置
          this.m_collideComp = null;
          // 碰撞组件
          this.bShootingSound = false;
          // 是否播放射击音效
          this.m_fires = [];
          // 射击点数组
          this.m_skillFires = new Map();
          // 技能射击点
          this.m_shieldKnifeFire = null;
          // 护盾刀火力
          this._hurtAct = null;
          // 受伤动画
          this._hurtActTime = 0;
          // 受伤动画时间
          this._hurtActDuration = 0.5;
          // 受伤动画持续时间
          // 飞机移动
          this.m_moveX = 0;
          // X 轴移动
          this.m_moveY = 0;
          // Y 轴移动
          // 飞机状态
          this.m_fireState = null;
          // 射击状态
          this.m_audioName = "";
          // 音效名称
          this._mapTween = null;
          // 地图动画
          this._collideHurtTime = 0;
          // 碰撞伤害时间
          // 飞机变形相关
          this.mainTransLevel = new Map();
          // 主变形等级
          this.m_fireHand = null;
          // 火焰手节点
          this.m_fireLight = null;
          // 火焰光节点
          this.isPlayFireAnim = false;
          // 是否播放火焰动画
          // 飞机初始位置
          this.initPos = 50;
          // 初始位置
          this.downSuitCall = null;
          // 套装下降回调
          this.mechaCall1 = null;
          // 机甲回调 1
          this.mechaCall2 = null;
          // 机甲回调 2
          this.roguelikeInCall = null;
          // Roguelike 进入回调
          this.roguelikeOutCall = null;
          // Roguelike 退出回调
          // 技能相关
          this.m_skill = null;
          // 技能
          this.skillNode = null;
          // 技能节点
          this.m_skinShield = null;
          // 皮肤护盾
          this.m_skinCircle = null;
          // 皮肤圆圈
          this.pfb = null;
          // 预制体
          this.mechaAtlas_Anim = null;
          // 机甲图集动画
          this.unitPrefab = [];
          // 单位预制体
          this.animArr = [];
          // 动画数组
          this.mechaUnitOverLight = null;
          // 机甲单位结束光效
          // 飞机初始位置
          this.initPosSuitBot = 0;
          // 套装底部初始位置
          this.initPosSuitTop = 0;
          // 套装顶部初始位置
          this.initPosMechaAnim = 0;
          // 机甲动画初始位置
          this.initPosSkinAnim = 0;
          // 皮肤动画初始位置
          // 技能相关
          this.skillBatDis = [];
          // 技能蝙蝠距离
          this.mechaScale = [0.8, 0.75, 1, 1];
          // 机甲缩放
          this.skillTargetNdoes = null;
          // 技能目标节点
          this.skillBatPoint = Vec2.ZERO;
          // 技能蝙蝠点
          // 加载状态
          this._loadFinish = false;
          // 是否加载完成
          this._loadTotal = 0;
          // 加载总数
          this._loadCount = 0;
          // 已加载数量
          // 地图高度
          this.mapH = {
            701: 0,
            702: -23,
            703: -30,
            705: 0,
            706: 1
          };
          // 动画相关
          this._ligatureAnim = null;
          // 连线动画
          this._goldAnimArr = [];
          // 金币动画数组
          this.m_data = void 0;
          this.m_animState = AnimState.idle;
        }

        // 动画状态
        get loadFinish() {
          return this._loadFinish;
        }

        checkLoadFinish() {
          this._loadCount++;

          if (this._loadCount >= this._loadTotal) {
            this._loadFinish = true;
          }

          (_crd && GameIns === void 0 ? (_reportPossibleCrUseOfGameIns({
            error: Error()
          }), GameIns) : GameIns).battleManager.addLoadingPercent(5 / this._loadTotal);
        } //     /**
        //  * 隐藏套装 Buff 动画
        //  */
        //     hideSuitBuff() {
        //         this.suitAnimBot.active = false;
        //         this.suitAnimTop.active = false;
        //     }
        //     /**
        //      * 显示套装 Buff 动画
        //      */
        //     showSuitBuff() {
        //         this.suitAnimBot.active = true;
        //         this.suitAnimTop.active = true;
        //     }
        //     /**
        //      * 播放火焰手动画
        //      */
        //     playFireHandAnim() {
        //         if (this.m_fireHand == null) {
        //             this.m_fireHand = new Node();
        //             const sprite = this.m_fireHand.addComponent(Sprite);
        //             m.default.loadManager.setImage(sprite, "zj_8_zd_1b", "mainBullet");
        //         }
        //         if (this.m_fireLight == null) {
        //             this.m_fireLight = new Node();
        //             const sprite = this.m_fireLight.addComponent(Sprite);
        //             m.default.loadManager.setImage(sprite, "zj_8_zd_1b", "mainBullet");
        //         }
        //         if (!this.isPlayFireAnim) {
        //             this.m_fireHand.position = v2(0, this.initPos);
        //             this.m_fireLight.position = v2(0, this.initPos);
        //             this.m_fireHand.active = true;
        //             this.m_fireHand.scale = 0.3;
        //             this.m_fireHand.getComponent(UIOpacity).opacity = 255;
        //             tween(this.m_fireHand)
        //                 .to(_.default.fromTo(0, 8), { scale: 0.5, opacity: 255 })
        //                 .to(_.default.fromTo(8, 15), { scale: 30, opacity: 0 })
        //                 .call(() => {
        //                     this.m_fireHand.active = false;
        //                 })
        //                 .start();
        //             tween(this.m_fireLight)
        //                 .delay(8 / 30)
        //                 .call(() => {
        //                     this.m_fireLight.scaleX = 0.08;
        //                     this.m_fireLight.scaleY = 1;
        //                     this.m_fireLight.getComponent(UIOpacity).opacity = 255;
        //                     this.m_fireLight.active = true;
        //                 })
        //                 .to(_.default.fromTo(0, 6), {
        //                     scaleX: 0.38,
        //                     scaleY: 1.1,
        //                     y: this.m_fireLight.y + 20,
        //                 })
        //                 .call(() => {
        //                     this.m_fireLight.active = false;
        //                 })
        //                 .start();
        //         }
        //     }

        /**
         * 生命周期方法：onLoad
         * 初始化主飞机的配置和数据
         */


        onLoad() {
          this.m_config = (_crd && GameIns === void 0 ? (_reportPossibleCrUseOfGameIns({
            error: Error()
          }), GameIns) : GameIns).mainPlaneManager.mainRecord; // 获取主飞机的配置记录

          this.m_data = (_crd && GameIns === void 0 ? (_reportPossibleCrUseOfGameIns({
            error: Error()
          }), GameIns) : GameIns).mainPlaneManager.data; // 获取主飞机的数据信息
        }
        /**
         * 生命周期方法：start
         * 初始化主飞机的组件和状态
         */


        start() {
          var self = this; // 监听主飞机血量变化事件

          (_crd && GameIns === void 0 ? (_reportPossibleCrUseOfGameIns({
            error: Error()
          }), GameIns) : GameIns).eventManager.on((_crd && GameEvent === void 0 ? (_reportPossibleCrUseOfGameEvent({
            error: Error()
          }), GameEvent) : GameEvent).MainHpChange, this.UpdateHp, this); // 监听复活事件

          (_crd && GameIns === void 0 ? (_reportPossibleCrUseOfGameIns({
            error: Error()
          }), GameIns) : GameIns).eventManager.on((_crd && GameEvent === void 0 ? (_reportPossibleCrUseOfGameEvent({
            error: Error()
          }), GameEvent) : GameEvent).MainRelife, this.onRelife, this); // 初始化主飞机配置和状态

          this.m_config = (_crd && GameIns === void 0 ? (_reportPossibleCrUseOfGameIns({
            error: Error()
          }), GameIns) : GameIns).mainPlaneManager.mainRecord;
          this.enemy = false;
          this.m_data.die = false;
          (_crd && GameIns === void 0 ? (_reportPossibleCrUseOfGameIns({
            error: Error()
          }), GameIns) : GameIns).gameDataManager.battlePlaneActive = true; // 初始化碰撞组件

          if (!this.m_collideComp) {
            this.m_collideComp = this.addComp(_crd && ColliderComp === void 0 ? (_reportPossibleCrUseOfColliderComp({
              error: Error()
            }), ColliderComp) : ColliderComp, new (_crd && ColliderComp === void 0 ? (_reportPossibleCrUseOfColliderComp({
              error: Error()
            }), ColliderComp) : ColliderComp)());
          }

          this.m_collideComp.enabled = true; // 初始化动画状态

          this.m_animState = AnimState.idle; // 初始化所有组件

          this.m_comps.forEach(comp => {
            comp.init(self);
          }); // 初始化飞机

          this.initPlane(); // 禁用射击

          this.setFireEnable(false); // // 获取护甲伤害比例
          // Number(ConfigDataManager.getGlobalData(GameEnum.GBKey.AmorDemageRatio));
        }
        /**
         * 生命周期方法：update
         * 更新主飞机的逻辑
         * @param {number} dt 时间增量
         */


        update(dt) {
          if (!(_crd && GameConst === void 0 ? (_reportPossibleCrUseOfGameConst({
            error: Error()
          }), GameConst) : GameConst).GameAble) return; // 限制帧率

          if (dt > 0.2) dt = 0.016666666666667; // 游戏状态为战斗时更新逻辑

          if ((_crd && GameIns === void 0 ? (_reportPossibleCrUseOfGameIns({
            error: Error()
          }), GameIns) : GameIns).gameRuleManager.gameState === (_crd && GameEnum === void 0 ? (_reportPossibleCrUseOfGameEnum({
            error: Error()
          }), GameEnum) : GameEnum).GameState.Battle && (_crd && GameIns === void 0 ? (_reportPossibleCrUseOfGameIns({
            error: Error()
          }), GameIns) : GameIns).mainPlaneManager.fireEnable) {
            if (!this.bShootingSound) {
              this.bShootingSound = true;

              if (this.m_audioName !== "") {
                if (this.m_config.type === 708 && (_crd && GameIns === void 0 ? (_reportPossibleCrUseOfGameIns({
                  error: Error()
                }), GameIns) : GameIns).mainPlaneManager.checkLockSkill()) {
                  this.m_audioName = "dragon_fire";
                } else if (this.m_config.type === 711 && (_crd && GameIns === void 0 ? (_reportPossibleCrUseOfGameIns({
                  error: Error()
                }), GameIns) : GameIns).mainPlaneManager.checkLockSkill()) {
                  this.m_audioName = "";
                } else {
                  this.m_audioName = this.m_config.zjshoot;
                } // if (this.m_audioName !== "") {
                //     GameIns.audioManager.playEffect(this.m_audioName, true);
                // }

              }
            } // 更新所有组件


            this.m_comps.forEach(comp => {
              comp.update(dt);
            });
          } else if ((_crd && GameIns === void 0 ? (_reportPossibleCrUseOfGameIns({
            error: Error()
          }), GameIns) : GameIns).gameRuleManager.gameState !== (_crd && GameEnum === void 0 ? (_reportPossibleCrUseOfGameEnum({
            error: Error()
          }), GameEnum) : GameEnum).GameState.Over && (_crd && GameIns === void 0 ? (_reportPossibleCrUseOfGameIns({
            error: Error()
          }), GameIns) : GameIns).gameRuleManager.gameState !== (_crd && GameEnum === void 0 ? (_reportPossibleCrUseOfGameEnum({
            error: Error()
          }), GameEnum) : GameEnum).GameState.Pause && !(_crd && GameIns === void 0 ? (_reportPossibleCrUseOfGameIns({
            error: Error()
          }), GameIns) : GameIns).mainPlaneManager.fireEnable) {
            if (this.bShootingSound) {
              this.bShootingSound = false;

              if (this.m_audioName !== "") {// GameIns.audioManager.stop(this.m_audioName);
              }
            }
          } // 更新飞机移动


          if (this.m_moveX !== 0 || this.m_moveY !== 0) {
            var posX = this.node.position.x + this.m_moveX;
            var posY = this.node.position.y + this.m_moveY; // 限制飞机移动范围

            posX = Math.min(360, posX);
            posX = Math.max(-360, posX);
            posY = Math.min(0, posY);
            posY = Math.max(-(_crd && GameConst === void 0 ? (_reportPossibleCrUseOfGameConst({
              error: Error()
            }), GameConst) : GameConst).ViewHeight, posY);
            this.node.setPosition(posX, posY);
            this.setDirection(this.m_moveX, dt);
          } // 重置移动值


          this.m_moveX = 0;
          this.m_moveY = 0; // 更新受伤动画时间

          this._hurtActTime += dt; // 更新碰撞伤害时间

          if (this._collideHurtTime > 0) {
            this._collideHurtTime -= dt;
          }
        }
        /**
         * 添加火焰动画
         */


        addFireAnim() {
          var _this = this;

          return _asyncToGenerator(function* () {
            for (var i = 0; i < _this.m_config.zjdmtxzb.length; i += 2) {
              var x = _this.m_config.zjdmtxzb[i];
              var y = _this.m_config.zjdmtxzb[i + 1];
              var fireNode = new Node();
              fireNode.addComponent(UITransform);
              fireNode.addComponent(UIOpacity);
              fireNode.setPosition(x, y);
              fireNode.parent = _this.skin;

              _this.m_fireAnim.push(fireNode);

              fireNode.getComponent(UIOpacity).opacity = 0;
              var skeletonData = yield (_crd && GameIns === void 0 ? (_reportPossibleCrUseOfGameIns({
                error: Error()
              }), GameIns) : GameIns).loadManager.loadSpine("mainPlane/firePoint/skel_mainfire");
              var skeleton = fireNode.addComponent(sp.Skeleton);
              skeleton.skeletonData = skeletonData;
              skeleton.setAnimation(0, "play", true);
            }
          })();
        } //     /**
        //      * 设置拖尾效果是否可见
        //      * @param {boolean} visible 是否可见
        //      */
        //     setStreakVisible(visible) {
        //         this.streak.node.active = visible;
        //     }

        /**
         * 添加拖尾效果
         */


        addStreak() {// this.streak.node.stopAllActions();
          // const tween = tween(this.streak.node)
          //     .to(1, { opacity: 255, scale: 1 })
          //     .to(3, { opacity: 204, scale: 0.88 })
          //     .to(6, { opacity: 255, scale: 1 });
          // tween(this.streak.node).repeatForever(tween).start();
        } //     /**
        //      * 获取防御技能
        //      */
        //     getaddDefenseSkill() {
        //         this.dg.node.active = true;
        //         this.dg.node.y = this.mapH[GameIns.mainPlaneManager.idToType(this.m_config.id)];
        //         this.bl.active = true;
        //         this.air.active = true;
        //         this.bl.stopAllActions();
        //         this.air.stopAllActions();
        //         tween(this.bl)
        //             .repeatForever(
        //                 tween(this.bl)
        //                     .to(1, { scale: 1 })
        //                     .to(8, { scale: 0.9 })
        //                     .to(22, { scale: 1 })
        //             )
        //             .start();
        //         tween(this.air)
        //             .repeatForever(
        //                 tween(this.air)
        //                     .to(1, { opacity: 255, scale: 1 })
        //                     .to(4, { opacity: 102, scale: 1.07 })
        //                     .to(11, { opacity: 255, scale: 1 })
        //             )
        //             .start();
        //     }
        //     /**
        //      * 取消防御技能
        //      */
        //     cancelDefenseSkill() {
        //         this.dg.node.active = false;
        //         this.bl.active = false;
        //         this.air.active = false;
        //         this.bl.stopAllActions();
        //         this.air.stopAllActions();
        //     }
        //     /**
        //      * 初始化狂暴动画
        //      */
        //     async initCrazyAnim() {
        //         const skeletonData = await GameIns.loadManager.loadSpine(this.m_config.rampagedh);
        //         this.skinAnim.skeletonData = skeletonData;
        //         if (this.m_config.type === 712) {
        //             const shieldNode = new Node("shield");
        //             this.m_skinShield = shieldNode.addComponent(sp.Skeleton);
        //             shieldNode.parent = this.skinAnim.node;
        //             this.m_skinShield.premultipliedAlpha = false;
        //             const shieldSkeletonData = await GameIns.loadManager.loadSpine(this.m_config.rampagedh + "_shield");
        //             this.m_skinShield.skeletonData = shieldSkeletonData;
        //             const circleNode = new Node("circle");
        //             this.m_skinCircle = circleNode.addComponent(sp.Skeleton);
        //             circleNode.parent = this.skinAnim.node;
        //             this.m_skinCircle.premultipliedAlpha = false;
        //             const circleSkeletonData = await GameIns.loadManager.loadSpine(this.m_config.rampagedh + "_circle");
        //             this.m_skinCircle.skeletonData = circleSkeletonData;
        //         } else {
        //             Tools.removeChildByName(this.skinAnim.node, "shield");
        //             Tools.removeChildByName(this.skinAnim.node, "circle");
        //             this.m_skinCircle = null;
        //             this.m_skinShield = null;
        //         }
        //     }

        /**
         * 更新血量显示
         */


        UpdateHp() {
          if (this.hpBar && this.hpbarFont && this.hpbarMid) {
            // 停止当前血条动画
            if (this.hpMidActin) {
              this.hpMidActin.stop();
              this.hpMidActin = null;
            } // 更新血条前景的填充范围


            this.hpbarFont.fillRange = (_crd && GameIns === void 0 ? (_reportPossibleCrUseOfGameIns({
              error: Error()
            }), GameIns) : GameIns).mainPlaneManager.data.hp / (_crd && GameIns === void 0 ? (_reportPossibleCrUseOfGameIns({
              error: Error()
            }), GameIns) : GameIns).mainPlaneManager.data.maxhp; // 计算血条动画时间

            var duration = Math.abs(this.hpbarMid.fillRange - this.hpbarFont.fillRange); // 血条中间部分的动画

            this.hpMidActin = tween(this.hpbarMid).to(duration, {
              fillRange: this.hpbarFont.fillRange
            }).call(() => {
              this.hpMidActin = null;
            }).start(); // 更新血量文字

            this.hpfont.string = (_crd && GameIns === void 0 ? (_reportPossibleCrUseOfGameIns({
              error: Error()
            }), GameIns) : GameIns).mainPlaneManager.data.hp.toFixed(0);
          }
        }
        /**
         * 开始战斗
         */


        beginBattle() {
          // 初始化主飞机技能
          this.initMainSkill(this.m_config); // 开始技能战斗

          if (this.m_skill) {
            this.m_skill.beginBattle();
          } // 组装主飞机


          this.pieceTogetherMainPlane();
        }
        /**
         * 退出战斗
         */


        battleQuit() {
          this.m_data.die = false; // GameIns.gameDataManager.battlePlaneActive = true;
          // 清理技能火力点

          this.m_skillFires.forEach(fires => {
            fires.forEach(fire => {
              fire.node.parent = null;
              fire.node.destroy();
            });
          });
          this.m_skillFires.clear(); // 重置飞机状态

          this.skin.getComponent(UIOpacity).opacity = 255;
          this.suitEffect.active = false;
          if (this.downSuitCall) this.downSuitCall();
          (_crd && GameIns === void 0 ? (_reportPossibleCrUseOfGameIns({
            error: Error()
          }), GameIns) : GameIns).mainPlaneManager.hideSkillNode();
          this.quitGameSetPic(); // this.clearMechaOverPic(true);
          // 结束技能战斗

          if (this.m_skill) {
            this.m_skill.quiteBattle();
            this.m_skill.endBattle();
          } // 重置动画节点位置


          this.skinAnim.node.y = this.initPosSkinAnim;
          this.mechaAnimNode.y = this.initPosMechaAnim;
          this.suitAnimBot.y = this.initPosSuitBot;
          this.suitAnimTop.y = this.initPosSuitTop; // 特殊处理某些飞机类型

          if (this.m_config.type !== 710) {
            if (this.m_config.type === 711) {
              this.skinAnim.node.y -= 2;
              this.mechaAnimNode.y -= 2;
            } else {
              this.skinAnim.node.y -= 100;
              this.mechaAnimNode.y -= 100;
            }
          }
        }
        /**
         * 初始化主飞机
         */


        initPlane() {
          var _this2 = this;

          return _asyncToGenerator(function* () {
            // 清空拖尾、阴影和护盾的图像
            _this2.streak.spriteFrame = null;
            _this2.shadow.spriteFrame = null;
            _this2.dg.spriteFrame = null; // 获取主飞机的配置数据

            _this2.m_config = (_crd && GameIns === void 0 ? (_reportPossibleCrUseOfGameIns({
              error: Error()
            }), GameIns) : GameIns).mainPlaneManager.mainRecord; // 加载飞机资源

            yield (_crd && GameIns === void 0 ? (_reportPossibleCrUseOfGameIns({
              error: Error()
            }), GameIns) : GameIns).loadManager.loadAtlas("mainPlane/package_mainPlane_trans_" + _this2.m_config.type); // 设置飞机音效
            // this.m_audioName = this.m_config.zjshoot;
            // // 设置飞机缩放
            // if (this.m_config.type === 710) {
            //     this.skinAnim.node.scale = 1;
            //     this.mechaAnimNode.scale = 1;
            // } else {
            // this.skinAnim.node.scale = 1.25;
            // this.mechaAnimNode.scale = 1.25;
            //     if (this.m_config.type === 713) {
            //         this.mechaAnimNode.scale = 1;
            //     }
            // }
            // 添加火焰动画

            yield _this2.addFireAnim(); // 初始化碰撞组件

            if (_this2.m_collideComp) {
              _this2.m_collideComp.setData(_this2.m_config.body);
            } // 播放攻击点动画


            _this2.playPointAnim(); // 获取火力状态


            _this2.getFireState(); // 初始化狂暴动画
            // await this.initCrazyAnim();
            // 根据屏幕等级更改屏幕数据


            _this2.changeScreenLv(_this2.m_data.screenLv); // // 刷新拖尾效果
            // this.refreshStreak();
            // // 设置阴影
            // const isMechaOver = GameIns.gameDataManager.isMechaOver || GameIns.mainPlaneManager.checkPlayMechaAnim();
            // this.shadow.spriteFrame = GameIns.loadManager.getImage(
            //     isMechaOver ? `${this.m_config.yz}2` : this.m_config.yz,
            //     `package_mainPlane_trans_${this.m_config.type}`
            // );
            // this.shadow.node.getComponent(UIOpacity).opacity = 255;
            // 初始化图片


            _this2.initPic(); // // 设置防御技能图片
            // this.dg.spriteFrame = GameIns.loadManager.getImage(
            //     `${this.m_config.zjjdimage[0]}_dg`,
            //     `package_mainPlane_trans_${this.m_config.type}`
            // );
            // if (this.dg.spriteFrame) {
            //     this.dg.node.width = this.dg.spriteFrame.getOriginalSize().width;
            //     this.dg.node.height = this.dg.spriteFrame.getOriginalSize().height;
            // }


            _this2.dg.node.active = false;
            _this2.bl.active = false;
            _this2.air.active = false; // // 清理机甲图片
            // this.clearMechaOverPic();
            // // 设置套装动画初始位置
            // if ([712, 714, 715].includes(this.m_config.type)) {
            //     this.initPosSuitBot = 0;
            //     this.initPosSuitTop = 0;
            // } else {
            //     this.initPosSuitBot = this.suitAnimBot.y;
            //     this.initPosSuitTop = this.suitAnimTop.y;
            // }
          })();
        } //     /**
        //      * 刷新拖尾效果
        //      */
        //     refreshStreak() {
        //         switch (this.m_config.type) {
        //             case 713:
        //                 if (GameIns.gameDataManager.isMechaOver) {
        //                     this.streak.spriteFrame = GameIns.loadManager.getImage(
        //                         this.m_config.wy + "2",
        //                         "package_mainPlane_trans_" + this.m_config.type
        //                     );
        //                     this.streak.node.y = -288;
        //                 } else {
        //                     this.streak.spriteFrame = GameIns.loadManager.getImage(
        //                         this.m_config.wy,
        //                         "package_mainPlane_trans_" + this.m_config.type
        //                     );
        //                     this.streak.node.y = -37;
        //                 }
        //                 break;
        //             case 714:
        //             case 715:
        //                 if (GameIns.gameDataManager.isMechaOver) {
        //                     this.streak.spriteFrame = GameIns.loadManager.getImage(
        //                         this.m_config.wy + "2",
        //                         "package_mainPlane_trans_" + this.m_config.type
        //                     );
        //                     this.streak.node.y = -80;
        //                 } else {
        //                     this.streak.spriteFrame = GameIns.loadManager.getImage(
        //                         this.m_config.wy,
        //                         "package_mainPlane_trans_" + this.m_config.type
        //                     );
        //                     this.streak.node.y = -37;
        //                 }
        //                 break;
        //             default:
        //                 this.streak.spriteFrame = GameIns.loadManager.getImage(
        //                     this.m_config.wy,
        //                     "package_mainPlane_trans_" + this.m_config.type
        //                 );
        //                 this.streak.node.y = this.m_config.type === 712 ? -50 : -77;
        //         }
        //     }

        /**
         * 初始化主飞机的主要技能
         * @param {Object} config 飞机配置
         */


        initMainSkill(config) {
          this._loadFinish = true;
          this._loadTotal = 0;
          this._loadCount = 0;

          if (this.m_skill) {
            this.m_skill.quiteBattle();
            this.removeComp(_crd && MainSkillBase === void 0 ? (_reportPossibleCrUseOfMainSkillBase({
              error: Error()
            }), MainSkillBase) : MainSkillBase);
            this.m_skill = null;
          }

          this.animArr = []; // switch (config.type) {
          //     case 708:
          //         this._loadFinish = false;
          //         const dragonAssets = ["dragonAnim1", "dragonAnim2", "dragonAnim3"];
          //         this.loadMechaRes("dragon", dragonAssets);
          //         this.m_skill = new MainSkillDragon();
          //         this.addComp(MainSkillDragon, this.m_skill);
          //         this.m_skill.setData(config);
          //         this.initPosMechaAnim = 87.857;
          //         this.initPosSkinAnim = -13.646;
          //         break;
          //     case 709:
          //         this._loadFinish = false;
          //         const batAssets = ["batAnim1", "batAnim2", "batAnim3", "batAnim4"];
          //         this.animArr = [
          //             "animation/MainPlane/mecha/batAnim1",
          //             "animation/MainPlane/mecha/batAnim2",
          //             "animation/MainPlane/mecha/batAnim3",
          //             "animation/MainPlane/mecha/batAnim4",
          //         ];
          //         this.loadMechaRes("bat", batAssets);
          //         this.m_skill = new MainSkillBat();
          //         this.addComp(MainSkillBat, this.m_skill);
          //         this.m_skill.setData(config);
          //         this.initPosMechaAnim = 84.506;
          //         this.initPosSkinAnim = 42.646;
          //         break;
          //     case 710:
          //         this._loadFinish = false;
          //         const diaochanAssets = ["diaochanAnim1", "diaochanAnim2", "diaochanAnim3", "diaochanAnim4"];
          //         this.animArr = [
          //             "animation/MainPlane/mecha/diaochanAnim1",
          //             "animation/MainPlane/mecha/diaochanAnim2",
          //             "animation/MainPlane/mecha/diaochanAnim4",
          //         ];
          //         this.loadMechaRes("diaochan", diaochanAssets);
          //         this.initPosMechaAnim = -97;
          //         this.initPosSkinAnim = -33;
          //         break;
          //     case 711:
          //         this._loadFinish = false;
          //         const swordAssets = ["swordAnim1"];
          //         this.animArr = ["animation/MainPlane/mecha/sword/swordAnim1"];
          //         this.loadMechaRes("sword", swordAssets);
          //         this.m_skill = new MainSkillSword();
          //         this.addComp(MainSkillSword, this.m_skill);
          //         this.m_skill.setData(config);
          //         this.initPosMechaAnim = -15;
          //         this.initPosSkinAnim = -2;
          //         break;
          //     case 712:
          //         this._loadFinish = false;
          //         const shieldAssets = ["shieldAnim"];
          //         this.animArr = ["animation/MainPlane/mecha/shield/shieldAnim"];
          //         this.loadMechaRes("shield", shieldAssets);
          //         this.m_skill = new MainSkillShield();
          //         this.addComp(MainSkillShield, this.m_skill);
          //         this.m_skill.setData(config);
          //         this.initPosMechaAnim = 100;
          //         this.initPosSkinAnim = 100;
          //         break;
          //     case 713:
          //         this._loadFinish = false;
          //         const deerAssets = ["deerAnim"];
          //         this.animArr = ["animation/MainPlane/mecha/deer/deerAnim"];
          //         this.loadMechaRes("deer", deerAssets);
          //         this.m_skill = new MainSkillDeer();
          //         this.addComp(MainSkillDeer, this.m_skill);
          //         this.m_skill.setData(config);
          //         this.initPosMechaAnim = 100;
          //         this.initPosSkinAnim = 100;
          //         break;
          //     case 714:
          //         this._loadFinish = false;
          //         const clothAssets = ["clothAnim"];
          //         this.animArr = ["animation/MainPlane/mecha/cloth/clothAnim"];
          //         this.loadMechaRes("cloth", clothAssets);
          //         this.m_skill = new MainSkillCloth();
          //         this.addComp(MainSkillCloth, this.m_skill);
          //         this.m_skill.setData(config);
          //         this.initPosMechaAnim = 100;
          //         this.initPosSkinAnim = 100;
          //         break;
          //     case 715:
          //         this._loadFinish = false;
          //         const legclothAssets = ["legclothAnim"];
          //         this.animArr = ["animation/MainPlane/mecha/legcloth/legclothAnim"];
          //         this.loadMechaRes("legcloth", legclothAssets);
          //         this.m_skill = new MainSkillLegCloth();
          //         this.addComp(MainSkillLegCloth, this.m_skill);
          //         this.m_skill.setData(config);
          //         this.initPosMechaAnim = 100;
          //         this.initPosSkinAnim = 100;
          //         break;
          // }
        } //     /**
        //      * 加载机甲资源
        //      * @param {string} type 机甲类型
        //      * @param {Array<string>} assets 资源名称数组
        //      */
        //     loadMechaRes(type, assets) {
        //         const self = this;
        //         // 增加加载总数
        //         this._loadTotal++;
        //         // 加载机甲单位预制体
        //         GameIns.loadManager.loadPrefab1("mechaUnit", (prefab) => {
        //             self.pfb = prefab;
        //             self.checkLoadFinish();
        //         });
        //         // 加载机甲动画图集
        //         this._loadTotal++;
        //         GameIns.loadManager.loadAtlas1(type, (atlas) => {
        //             self.mechaAtlas_Anim = atlas;
        //             self.checkLoadFinish();
        //         });
        //         // 加载机甲结束光效预制体
        //         this._loadTotal++;
        //         GameIns.loadManager.loadPrefab1("unitOverLight", (prefab) => {
        //             self.mechaUnitOverLight = prefab;
        //             self.checkLoadFinish();
        //         });
        //         // 加载其他资源
        //         this.unitPrefab = [];
        //         assets.forEach((asset) => {
        //             this._loadTotal++;
        //             GameIns.loadManager.loadPrefab1(asset, (prefab) => {
        //                 self.unitPrefab.push(prefab);
        //                 self.checkLoadFinish();
        //             });
        //         });
        // }

        /**
         * 组装主飞机的节点
         */


        pieceTogetherMainPlane() {
          var self = this; // // 加载跑酷底部节点
          // if (!this.parkourBot) {
          //     this.instantiateNode("paoku_1", Animation, "suitAnimTop", this.planeNode, (node) => {
          //         self.parkourBot = node;
          //         self.parkourBot.node.scale = 1.8;
          //         self.parkourBot.node.getComponent(UIOpacity).opacity = 0;
          //         self.parkourBot.node.active = false;
          //     });
          // }
          // // 加载跑酷中部节点
          // if (!this.parkourMid) {
          //     this.instantiateNode("paoku_2", Animation, "paoku_1", this.planeNode, (node) => {
          //         self.parkourMid = node;
          //         self.parkourMid.node.scale = 1.8;
          //         self.parkourMid.node.getComponent(UIOpacity).opacity = 0;
          //         self.parkourMid.node.active = false;
          //     });
          // }
          // // 加载跑酷顶部节点
          // if (!this.parkourTop) {
          //     this.instantiateNode("paoku_3", Animation, "skin", this.planeNode, (node) => {
          //         self.parkourTop = node;
          //         self.parkourTop.node.scale = 1.8;
          //         self.parkourTop.node.getComponent(UIOpacity).opacity = 0;
          //         self.parkourTop.node.active = false;
          //     });
          // }
          // // 加载飞机下方特效节点
          // if (!this.below) {
          //     this.instantiateNode("below", sp.Skeleton, "bglight", this.node, (node) => {
          //         self.below = node;
          //         self.below.node.active = false;
          //     });
          // }
          // // 加载复活组件
          // if (!this.relifeComp) {
          //     this.instantiateNode("relief", MainRelifeComp, "laserPoint", this.node, (node) => {
          //         self.relifeComp = node;
          //         self.relifeComp.node.active = false;
          //     });
          // }
          // // 加载挑战动画节点
          // if (!this.challengeAnim) {
          //     this.instantiateNode("challengeAnim", sp.Skeleton, "", this.node, (node) => {
          //         self.challengeAnim = node;
          //         self.challengeAnim.node.active = false;
          //         if (self.challengeAnimName) {
          //             self.playChallengeAnim(self.challengeAnimName);
          //         }
          //     });
          // }
          // 加载血条节点

          if (!this.hpBar) {
            this.instantiateNode("hpBar", Node, "challengeAnim", this.node, node => {
              self.hpBar = node; // if (self.m_config.type === 712) {
              //     self.hpBar.y = GameIns.gameDataManager.isMechaOver ? 90 : 30;
              // } else {

              self.hpBar.y = 19; // }

              self.hpbarBack = self.hpBar.getChildByName("hpbarback").getComponent(Sprite);
              self.hpbarMid = self.hpBar.getChildByName("hpbarmid").getComponent(Sprite);
              self.hpbarFont = self.hpBar.getChildByName("hpbarfont").getComponent(Sprite);
              self.hpfont = self.hpBar.getChildByName("hpfont").getComponent(Label); // if (!GameIns.gameDataManager.guideStage) {
              //     self.node.scale = 0.4;
              // }

              var scale = 1 / self.node.getScale().x * 0.45;
              self.hpbarBack.node.setScale(scale, 1.2 * -scale);
              self.hpbarMid.node.setScale(scale, 1.2 * scale);
              self.hpbarFont.node.setScale(scale, 1.2 * -scale);
              var hpScale = 0.6 / self.node.scale.x * 0.66;
              self.hpfont.node.setScale(hpScale, hpScale);
            });
          }
        }
        /**
         * 动态实例化节点
         * @param {string} prefabName 预制体名称
         * @param {Function} componentType 组件类型
         * @param {string} siblingName 同级节点名称
         * @param {Node} parentNode 父节点
         * @param {Function} callback 回调函数
         */


        instantiateNode(prefabName, componentType, siblingName, parentNode, callback) {
          var self = this; // 增加加载总数

          this._loadTotal++;
          this._loadFinish = false; // 加载预制体

          (_crd && GameIns === void 0 ? (_reportPossibleCrUseOfGameIns({
            error: Error()
          }), GameIns) : GameIns).loadManager.loadPrefab1(prefabName, prefab => {
            if (prefab) {
              var node = instantiate(prefab);
              var component = componentType === Node ? node : node.getComponent(componentType);
              node.parent = null; // 设置节点插入位置

              var siblingIndex = parentNode.childrenCount;

              if (siblingName && siblingName !== "") {
                var siblingNode = parentNode.getChildByName(siblingName);

                if (siblingNode) {
                  siblingIndex = siblingNode.getSiblingIndex();
                }
              }

              parentNode.insertChild(node, siblingIndex); // 执行回调

              if (callback) {
                callback(component);
              } // 检查加载完成


              self.checkLoadFinish();
            }
          });
        } //     /**
        //      * 拆解主飞机的节点
        //      */
        //     BreakUpMainPlane() {
        //         // 清理金币动画
        //         if (this._goldAnimArr.length > 0) {
        //             this._goldAnimArr.splice(0);
        //             this.skin.getChildByName("goldAnim").destroyAllChildren();
        //         }
        //         // 清理连线动画
        //         if (this._ligatureAnim) {
        //             this._ligatureAnim.node.destroy();
        //             this._ligatureAnim = null;
        //         }
        //         // 清理跑酷节点
        //         if (this.parkourBot) {
        //             this.parkourBot.node.destroy();
        //             this.parkourBot = null;
        //         }
        //         if (this.parkourMid) {
        //             this.parkourMid.node.destroy();
        //             this.parkourMid = null;
        //         }
        //         if (this.parkourTop) {
        //             this.parkourTop.node.destroy();
        //             this.parkourTop = null;
        //         }
        //         // 清理其他节点
        //         if (this.below) {
        //             this.below.node.destroy();
        //             this.below = null;
        //         }
        //         if (this.relifeComp) {
        //             this.relifeComp.node.destroy();
        //             this.relifeComp = null;
        //         }
        //         if (this.challengeAnim) {
        //             this.challengeAnim.node.destroy();
        //             this.challengeAnim = null;
        //         }
        //         // 停止血条动画并清理
        //         if (this.hpMidActin) {
        //             this.hpMidActin.stop();
        //             this.hpMidActin = null;
        //         }
        //         if (this.hpBar) {
        //             this.hpBar.destroy();
        //             this.hpBar = null;
        //             this.hpbarBack = null;
        //             this.hpbarMid = null;
        //             this.hpbarFont = null;
        //             this.hpfont = null;
        //         }
        //         // 延迟释放资源
        //         setTimeout(() => {
        //             GameIns.loadManager.releaseAsset(loader.getRes("animation/MainPlane/paoku_1", AnimationClip), false);
        //             GameIns.loadManager.releaseAsset(loader.getRes("animation/MainPlane/paoku_2", AnimationClip), false);
        //             GameIns.loadManager.releaseAsset(loader.getRes("animation/MainPlane/paoku_3", AnimationClip), false);
        //             GameIns.loadManager.releaseRes("package_mainPlane", SpriteAtlas);
        //             GameIns.loadManager.releaseResArr(
        //                 ["below", "relief", "challengeAnim", "paoku_1", "paoku_2", "paoku_3", "hpBar"],
        //                 Prefab,
        //                 false
        //             );
        //             GameIns.loadManager.releaseRes("skel_mainblow", sp.SkeletonData);
        //             GameIns.loadManager.releaseRes("skel_relief", sp.SkeletonData);
        //             GameIns.loadManager.releaseRes("skel_challengeAnim", sp.SkeletonData);
        //             Tools.warn("release BreakUpMainPlane");
        //         }, 20);
        //     }

        /**
         * 初始化主飞机
         */


        init() {
          this.node.active = true;
          this.shadow.node.getComponent(UIOpacity).opacity = 0; // 获取火力状态

          this.getFireState(); // 初始化碰撞组件

          if (!this.m_collideComp) {
            this.m_collideComp = this.addComp(_crd && ColliderComp === void 0 ? (_reportPossibleCrUseOfColliderComp({
              error: Error()
            }), ColliderComp) : ColliderComp, new (_crd && ColliderComp === void 0 ? (_reportPossibleCrUseOfColliderComp({
              error: Error()
            }), ColliderComp) : ColliderComp)());
          }

          this.m_collideComp.enabled = false; // // 设置阴影节点是否可见
          // const shadowVisible = BattleManager.shadowAble(BattleManager.mainStage);
          // this.shadow.node.active = shadowVisible;
          // // 特殊处理某些飞机类型
          // if (this.m_config.type === 710) {
          //     GameIns.gameDataManager.isMechaOver = true;
          //     this.setMechaOverPic(true);
          //     this.skinAnim.node.y = this.initPosSkinAnim;
          //     this.mechaAnimNode.y = this.initPosMechaAnim;
          //     this.suitAnimBot.y = this.initPosSuitBot;
          //     this.suitAnimTop.y = this.initPosSuitTop;
          // }
          // 初始化动画状态

          if (!this._hurtAct) {
            this._hurtAct = tween().to(0, {
              color: new Color(255, 0, 0)
            }) // 变为红色
            .to(0.13, {
              color: new Color(255, 255, 255)
            }); // 恢复为正常颜色
            // .repeatForever()
          } // 初始化火力点


          this.m_skillFires.forEach(fires => {
            fires.forEach(fire => {
              fire.clearData();
            });
          }); // 更新屏幕等级

          this.changeScreenLv(this.m_data.screenLv); // // 初始化影子飞机
          // if (PlaneManager.shadowPlane) {
          //     PlaneManager.shadowPlane.init();
          // }
          // // 设置机甲状态
          // if (GameIns.gameDataManager.isConditionOver && BattleManager.isContinue && GameIns.gameDataManager.isMechaOver) {
          //     this.mechaAnimNode.destroyAllChildren();
          //     this.setMechaOverPic();
          // }
          // // 刷新拖尾效果
          // this.refreshStreak();
        }
        /**
         * 处理复活逻辑
         */


        onRelife() {
          (_crd && GameIns === void 0 ? (_reportPossibleCrUseOfGameIns({
            error: Error()
          }), GameIns) : GameIns).gameDataManager.reviveCount += 1; // 增加复活次数

          this.relife(1); // 调用复活方法
        }
        /**
         * 执行复活
         * @param {number} reviveType 复活类型
         */


        relife(reviveType) {
          // GameIns.gameDataManager.addBattleReviveStage(
          //     100 * BattleManager.mainStage + BattleManager.subStage,
          //     reviveType + 1
          // );
          // ReportManager.sendReportRevive(
          //     BattleManager.mainStage,
          //     BattleManager.subStage,
          //     reviveType
          // );
          // this.playRelifeAim(); // 播放复活动画
          this.m_data.die = false; // 设置飞机为非死亡状态

          this.m_data.revive = true; // 设置复活状态

          this.scheduleOnce(() => {
            this.m_data.revive = false;
          }, 0.5);
          (_crd && GameIns === void 0 ? (_reportPossibleCrUseOfGameIns({
            error: Error()
          }), GameIns) : GameIns).gameDataManager.battlePlaneActive = true; // 激活主飞机

          (_crd && GameIns === void 0 ? (_reportPossibleCrUseOfGameIns({
            error: Error()
          }), GameIns) : GameIns).mainPlaneManager.data.hp = (_crd && GameIns === void 0 ? (_reportPossibleCrUseOfGameIns({
            error: Error()
          }), GameIns) : GameIns).mainPlaneManager.data.maxhp; // 恢复满血

          (_crd && GameIns === void 0 ? (_reportPossibleCrUseOfGameIns({
            error: Error()
          }), GameIns) : GameIns).gameDataManager.setMainPlaneHp((_crd && GameIns === void 0 ? (_reportPossibleCrUseOfGameIns({
            error: Error()
          }), GameIns) : GameIns).mainPlaneManager.data.hp); // 更新血量显示

          (_crd && GameIns === void 0 ? (_reportPossibleCrUseOfGameIns({
            error: Error()
          }), GameIns) : GameIns).eventManager.emit((_crd && GameEvent === void 0 ? (_reportPossibleCrUseOfGameEvent({
            error: Error()
          }), GameEvent) : GameEvent).MainHpChange); // 触发血量更新事件
          // this.showSuitBuff(); // 显示套装 Buff
          // if (this.m_skill) {
          //     this.m_skill.startBattle(true); // 开始技能战斗
          // }
          // const mechaUI = GameIns.uiManager.getDialog(MechaUI);
          // if (mechaUI) {
          //     mechaUI.node.active = true; // 显示机甲 UI
          // }
        }
        /**
         * 播放复活动画
         */


        playRelifeAim() {
          this.m_collideComp.enabled = false; // 禁用碰撞组件

          this.scheduleOnce(() => {
            Tween.stopAllByTarget(this.skin.getComponent(UIOpacity));
            this.skin.getComponent(UIOpacity).opacity = 255;
            this.m_collideComp.enabled = true; // 恢复碰撞组件
          }, 2);
          this.skin.getComponent(UIOpacity).opacity = 255;

          if (this.relifeComp) {
            this.relifeComp.node.active = true;
            this.relifeComp.setCompleteListener(this.hideRelifeAim.bind(this));
          }

          tween(this.skin.getComponent(UIOpacity)).to(0.08, {
            opacity: 10
          }).to(0.08, {
            opacity: 255
          }).union().repeatForever().start();
        }
        /**
         * 隐藏复活动画
         */


        hideRelifeAim() {
          if (this.relifeComp) {
            this.relifeComp.node.active = false;
          }
        } //     /**
        //      * 获取复活碰撞器
        //      * @returns {Collider} 复活碰撞器
        //      */
        //     getRelifeCollider() {
        //         if (this.relifeComp && this.relifeComp.node && this.relifeComp.node.active) {
        //             return this.relifeComp.collider;
        //         }
        //         return null;
        //     }

        /**
         * 改变屏幕等级
         * @param {number} level 屏幕等级
         */


        changeScreenLv(level) {
          if (level === 0) return;
          this.m_screenDatas = [];
          var attackKey = (_crd && GameIns === void 0 ? (_reportPossibleCrUseOfGameIns({
            error: Error()
          }), GameIns) : GameIns).gameDataManager.isConditionOver ? "transatk" : "shiftingatk";
          var attackData = this.m_config["" + attackKey + level];

          for (var i = 0; i < attackData.length; i += 8) {
            var screenData = attackData.slice(i, i + 8);
            this.m_screenDatas.push(screenData);
          }

          this.m_shieldKnifeFire = null;
          (_crd && GameIns === void 0 ? (_reportPossibleCrUseOfGameIns({
            error: Error()
          }), GameIns) : GameIns).mainPlaneManager.mainPlaneFireCounts = this.m_screenDatas.length;
          this.m_screenDatas.forEach((data, index) => {
            if (this.m_fires[index] == null) {
              this.createAttackPoint(data);
            } else {
              this.changeScreen(index, data);
            }

            if (this.m_config.type === 712 && this.m_fires[index].getType() === 48) {
              this.m_shieldKnifeFire = this.m_fires[index];
            }
          });

          for (var _i = this.m_screenDatas.length; _i < this.m_fires.length; _i++) {
            this.changeScreen(_i, null);
          }
        } //     /**
        //      * 进入狂暴状态
        //      */
        //     toCrazyState() {
        //         this.skinAnim.node.active = true;
        //         this.skinImg.node.active = false;
        //         this.removeAllFire();
        //         GameIns.mainPlaneManager.crazyScreens.forEach((data, index) => {
        //             this.changeScreen(index, data);
        //         });
        //         if (this.m_animState !== AnimState.crazy) {
        //             GameIns.audioManager.playEffect("goBallistic");
        //             this.m_animState = AnimState.crazy;
        //             this.skinAnim.setAnimation(0, "tocrazy", false);
        //             if (this.m_skinShield) this.m_skinShield.setAnimation(0, "tocrazy", false);
        //             if (this.m_skinCircle) this.m_skinCircle.setAnimation(0, "tocrazy", false);
        //             this.skinAnim.setCompleteListener(() => {
        //                 this.skinAnim.setCompleteListener(null);
        //                 this.skinAnim.setAnimation(0, "crazyidle", true);
        //                 if (this.m_skinShield) this.m_skinShield.setAnimation(0, "crazyidle", true);
        //                 if (this.m_skinCircle) this.m_skinCircle.setAnimation(0, "crazyidle", true);
        //             });
        //         }
        //     }
        //     /**
        //      * 取消狂暴状态
        //      */
        //     cancelCrazyState() {
        //         this.removeAllFire();
        //         this.changeScreenLv(this.m_data.screenLv);
        //         if (this.m_animState === AnimState.crazy) {
        //             this.m_animState = AnimState.willCancelCrazy;
        //             this.skinAnim.setAnimation(0, "toidle", false);
        //             if (this.m_skinShield) this.m_skinShield.setAnimation(0, "toidle", false);
        //             if (this.m_skinCircle) this.m_skinCircle.setAnimation(0, "toidle", false);
        //             this.skinAnim.setCompleteListener(() => {
        //                 this.skinAnim.setCompleteListener(null);
        //                 this.skinAnim.setAnimation(0, "idle", true);
        //                 if (this.m_skinShield) this.m_skinShield.setAnimation(0, "idle", true);
        //                 if (this.m_skinCircle) this.m_skinCircle.setAnimation(0, "idle", true);
        //                 this.m_animState = AnimState.idle;
        //                 this.skinAnim.node.active = false;
        //                 this.skinImg.node.active = true;
        //             });
        //         }
        //     }
        //     /**
        //      * 强化屏幕效果
        //      */
        //     intensifyScreen() {
        //         this.m_fireState.fireIntensify = true;
        //         this.refreshFireState();
        //     }
        //     /**
        //      * 取消强化屏幕效果
        //      */
        //     cancelIntensify() {
        //         this.m_fireState.fireIntensify = false;
        //         this.refreshFireState();
        //     }

        /**
         * 获取火力状态
         */


        getFireState() {
          // const attackLevel = SkillManager.me.getLv(SkillType.mainAttack);
          // const hpAttackLevel = SkillManager.me.getLv(SkillType.mainHpAttack);
          // const speedLevel = SkillManager.me.getLv(SkillType.mainSpeed);
          var baseAttack = this.m_data.initAtk1 > 0 ? this.m_data.attack1 : this.m_data.attack2; // if (
          //     GameIns.mainPlaneManager.idToType(this.m_data.id) === 711 &&
          //     (GameIns.gameDataManager.isMechaOver || GameIns.mainPlaneManager.checkPlayMechaAnim())
          // ) {
          //     baseAttack = this.m_data.attack2;
          // }
          // const cirtLevel = SkillManager.me.getLv(SkillType.addCirt);

          var atkChallenge = this.m_data.atkAddRatio;
          this.m_fireState = {
            attack: baseAttack,
            attackLv: 0,
            hpAttackLv: 0,
            speedLv: 0,
            cirtLv: 0,
            //cirtLevel,
            catapultLv: 0,
            //SkillManager.me.getLv(SkillType.catapult),
            atkChallenge: atkChallenge,
            fireIntensify: this.m_data.intensifyAtk
          };
        }
        /**
         * 获取攻击力
         * @returns {number} 当前攻击力
         */


        getAttack() {
          return this.m_fireState.attack;
        } //     /**
        //      * 挑战模式下增加攻击力
        //      */
        //     atkAddByChallenge() {
        //         const attackLevel = SkillManager.me.getLv(SkillType.mainAttack);
        //         const fireState = {
        //             attack: this.m_data.initAtk1 > 0 ? this.m_data.attack1 : this.m_data.attack2,
        //             attackLv: attackLevel,
        //             hpAttackLv: SkillManager.me.getLv(SkillType.mainHpAttack),
        //             atkChallenge: this.m_data.atkAddRatio,
        //         };
        //         this.m_fires.forEach((fire) => {
        //             fire.setState(fireState, this);
        //         });
        //     }
        //     /**
        //      * 设置冷却时间
        //      * @param {number} time 冷却时间
        //      */
        //     setCoolTime(time) {
        //         const skillFires = this.m_skillFires.get(SkillType.feibiao);
        //         if (skillFires) {
        //             skillFires.forEach((fire) => {
        //                 fire.setCoolTime(time);
        //             });
        //         }
        //     }
        //     /**
        //      * 刷新火力状态
        //      */
        //     refreshFireState() {
        //         this.getFireState();
        //         this.m_fires.forEach((fire) => {
        //             fire.setState(this.m_fireState, this);
        //         });
        //         const skillFires = this.m_skillFires.get(28);
        //         if (skillFires) {
        //             skillFires.forEach((fire) => {
        //                 fire.setState(this.m_fireState, this, true);
        //             });
        //         }
        //     }
        //     /**
        //      * 增加暴击
        //      * @param {number} value 暴击值
        //      */
        //     addCirt(value) {
        //         this.m_fires.forEach((fire) => {
        //             fire.addCirt(value);
        //         });
        //     }

        /**
         * 改变屏幕上的火力点
         * @param {number} index 火力点索引
         * @param {Array|null} data 火力点数据
         */


        changeScreen(index, data) {
          if (data == null) {
            if (index < this.m_fires.length) {
              var fire = this.m_fires[index];
              fire.setData(null, this.m_fireState, false, this);
              fire.node.active = false;
            }
          } else {
            if (index < this.m_fires.length) {
              var _fire = this.m_fires[index];
              _fire.node.active = true;

              _fire.setData(data, this.m_fireState, false, this);
            } else {
              this.createAttackPoint(data);
            }
          }
        } //     /**
        //      * 移除所有火力点
        //      */
        //     removeAllFire() {
        //         this.m_fires.forEach((fire) => {
        //             fire.setData(null, this.m_fireState, false, this);
        //         });
        //     }

        /**
         * 创建攻击点
         * @param {Array} data 攻击点数据
         * @returns {FireShells} 创建的攻击点
         */


        createAttackPoint(data) {
          var fireNode = new Node("fire");
          fireNode.parent = this.node;
          var fire = fireNode.addComponent(_crd && FireShells === void 0 ? (_reportPossibleCrUseOfFireShells({
            error: Error()
          }), FireShells) : FireShells);
          fire.setData(data, this.m_fireState, false, this);
          this.m_fires.push(fire);
          return fire;
        } //     /**
        //      * 根据技能添加攻击点
        //      * @param {number} skillType 技能类型
        //      * @param {Array} data 攻击点数据
        //      * @param {Array} extra 额外数据
        //      */
        //     addAttackPointBySkill(skillType, data, extra = []) {
        //         const skillFires = this.m_skillFires.get(skillType);
        //         this.getFireState();
        //         this.m_fireState.extra = extra;
        //         if (!skillFires || skillFires.length === 0) {
        //             data.forEach((pointData) => {
        //                 const fireNode = new Node("fire-skill");
        //                 fireNode.parent = this.node;
        //                 const fire = fireNode.addComponent(FireShells);
        //                 fire.setData(pointData, this.m_fireState, false, this);
        //                 const fires = this.m_skillFires.get(skillType) || [];
        //                 fires.push(fire);
        //                 this.m_skillFires.set(skillType, fires);
        //             });
        //         } else {
        //             const maxLength = Math.max(skillFires.length, data.length);
        //             const updatedFires = [];
        //             for (let i = 0; i < maxLength; i++) {
        //                 if (i < data.length && i < skillFires.length) {
        //                     skillFires[i].setData(data[i], this.m_fireState, false, this);
        //                     updatedFires.push(skillFires[i]);
        //                 } else if (i < data.length) {
        //                     const fireNode = new Node("fire-skill");
        //                     fireNode.parent = this.node;
        //                     const fire = fireNode.addComponent(FireShells);
        //                     fire.setData(data[i], this.m_fireState, false, this);
        //                     updatedFires.push(fire);
        //                 } else if (i < skillFires.length) {
        //                     skillFires[i].node.destroy();
        //                 }
        //             }
        //             this.m_skillFires.set(skillType, updatedFires);
        //         }
        //     }
        //     /**
        //      * 添加普通攻击点
        //      * @param {FireShells} fire 火力点对象
        //      * @param {boolean} isTrueAttack 是否为真实攻击
        //      */
        //     addAttackPoint(fire, isTrueAttack = false) {
        //         fire.node.parent = this.node;
        //         const attack = isTrueAttack
        //             ? WinePlaneManager.me.lvRecord.atk_true
        //             : this.m_data.initAtk1 > 0
        //                 ? this.m_data.attack1
        //                 : this.m_data.attack2;
        //         fire.setState({ ...this.m_fireState, attack }, this, false, isTrueAttack);
        //     }
        //     /**
        //      * 根据机甲添加攻击点
        //      * @param {FireShells} fire 火力点对象
        //      * @param {Node|null} parent 父节点
        //      */
        //     addAttackPointByMecha(fire, parent = null) {
        //         if (parent) {
        //             fire.node.parent = parent;
        //         }
        //         const attack =
        //             this.m_data.initAtk1 > 0 ? this.m_data.attack1 : this.m_data.attack2;
        //         fire.setState({ ...this.m_fireState, attack }, null, false);
        //     }
        //     /**
        //      * 移除攻击点
        //      * @param {FireShells} fire 火力点对象
        //      */
        //     removeAttackPoint(fire) {
        //         fire.node.parent = null;
        //     }

        /**
         * 播放攻击点动画
         */


        playPointAnim() {// this.point.setScale(1,1);
          // this.point.parent.getComponent(UIOpacity).opacity = 255;
          // this.point.getComponent(UIOpacity).opacity = 255;
          // this.point.parent.y = this.m_config.body[2];
          // Tween.stopAllByTarget(this.point)
          // const ani = tween(this.point)
          //     .to(2 / 30, { scale: 0.85, opacity: 125 })
          //     .to(2 / 30, { scale: 1, opacity: 255 });
          // ani.repeatForever().start();
        }
        /**
         * 设置飞机的方向
         * @param {number} moveX 水平方向的移动量
         * @param {number} deltaTime 时间增量
         */


        setDirection(moveX, deltaTime) {// if (moveX > 0) {
          //     this.skin.scaleX = 1; // 向右
          // } else if (moveX < 0) {
          //     this.skin.scaleX = -1; // 向左
          // }
        }
        /**
         * 减少血量
         * @param {number} damage 受到的伤害值
         */


        cutHp(damage) {
          (_crd && GameIns === void 0 ? (_reportPossibleCrUseOfGameIns({
            error: Error()
          }), GameIns) : GameIns).mainPlaneManager.hurtTotal += damage;
          var newHp = (_crd && GameIns === void 0 ? (_reportPossibleCrUseOfGameIns({
            error: Error()
          }), GameIns) : GameIns).mainPlaneManager.data.hp - damage;
          (_crd && GameIns === void 0 ? (_reportPossibleCrUseOfGameIns({
            error: Error()
          }), GameIns) : GameIns).mainPlaneManager.data.hp = Math.max(0, newHp);

          if (newHp < 0) {
            (_crd && GameIns === void 0 ? (_reportPossibleCrUseOfGameIns({
              error: Error()
            }), GameIns) : GameIns).mainPlaneManager.hurtTotal += newHp;
          }

          (_crd && GameIns === void 0 ? (_reportPossibleCrUseOfGameIns({
            error: Error()
          }), GameIns) : GameIns).eventManager.emit((_crd && GameEvent === void 0 ? (_reportPossibleCrUseOfGameEvent({
            error: Error()
          }), GameEvent) : GameEvent).MainHpChange);

          if ((_crd && GameIns === void 0 ? (_reportPossibleCrUseOfGameIns({
            error: Error()
          }), GameIns) : GameIns).mainPlaneManager.data.hp <= 0 && !this.m_data.die) {
            this.toDie();
          }
        }
        /**
         * 增加血量
         * @param {number} heal 恢复的血量值
         */


        addHp(heal) {
          (_crd && GameIns === void 0 ? (_reportPossibleCrUseOfGameIns({
            error: Error()
          }), GameIns) : GameIns).mainPlaneManager.data.hp = Math.min((_crd && GameIns === void 0 ? (_reportPossibleCrUseOfGameIns({
            error: Error()
          }), GameIns) : GameIns).mainPlaneManager.data.maxhp, (_crd && GameIns === void 0 ? (_reportPossibleCrUseOfGameIns({
            error: Error()
          }), GameIns) : GameIns).mainPlaneManager.data.hp + heal);
          (_crd && GameIns === void 0 ? (_reportPossibleCrUseOfGameIns({
            error: Error()
          }), GameIns) : GameIns).eventManager.emit((_crd && GameEvent === void 0 ? (_reportPossibleCrUseOfGameEvent({
            error: Error()
          }), GameEvent) : GameEvent).MainHpChange);
        }
        /**
         * 碰撞处理
         * @param {Object} collision 碰撞对象
         */


        onCollide(collision) {
          if (this.m_skill && this.m_skill.invincible) return;
          var damage = 0;

          if (collision.entity instanceof (_crd && Bullet === void 0 ? (_reportPossibleCrUseOfBullet({
            error: Error()
          }), Bullet) : Bullet)) {
            damage = collision.entity.getAttack(this);
          } else if (collision.entity instanceof (_crd && EnemyEntity === void 0 ? (_reportPossibleCrUseOfEnemyEntity({
            error: Error()
          }), EnemyEntity) : EnemyEntity) || collision.entity instanceof (_crd && BossUnit === void 0 ? (_reportPossibleCrUseOfBossUnit({
            error: Error()
          }), BossUnit) : BossUnit)) {
            damage = collision.entity.getColliderAtk();
          }

          if (damage > 0) {
            this.cutHp(damage);

            if ((_crd && GameIns === void 0 ? (_reportPossibleCrUseOfGameIns({
              error: Error()
            }), GameIns) : GameIns).mainPlaneManager.data.hp <= 0 && !this.m_data.die) {
              this.toDie();
            }
          }
        }
        /**
         * 控制飞机移动
         * @param {number} moveX 水平方向的移动量
         * @param {number} moveY 垂直方向的移动量
         */


        onControl(moveX, moveY) {
          if (!this.m_data.die && this.m_moveEnable) {
            this.m_moveX += moveX;
            this.m_moveY += moveY;
          }
        } //     /**
        //      * 战斗胜利处理
        //      */
        //     onSucc() {
        //         this.m_collideComp.enabled = false; // 禁用碰撞组件
        //         if (this.m_skill) {
        //             this.m_skill.quiteBattle(); // 停止技能战斗
        //         }
        //     }

        /**
         * 设置飞机死亡状态
         */


        toDie() {
          this.m_data.die = true; // 标记为死亡状态

          this.m_collideComp.enabled = false; // 禁用碰撞组件

          if (this.hpbarMid) {
            tween(this.hpbarMid).stop();
            this.hpbarMid.fillRange = 0; // 设置血条为0
          }

          this._playDieAnim(); // 播放死亡动画
          // GameIns.eventManager.emit(GameEvent.MainPlaneDie); // 触发飞机死亡事件


          (_crd && GameIns === void 0 ? (_reportPossibleCrUseOfGameIns({
            error: Error()
          }), GameIns) : GameIns).gameDataManager.battlePlaneActive = false;
        }
        /**
         * 播放死亡动画
         */


        _playDieAnim() {
          this.blast.node.getComponent(UIOpacity).opacity = 255; // 显示爆炸效果

          this.blast.setCompleteListener(this._dieAnimEnd.bind(this)); // 设置动画完成回调

          this.blast.setAnimation(0, "play", false); // 播放爆炸动画
        }
        /**
         * 主飞机死亡动画结束后的处理逻辑
         */


        _dieAnimEnd() {
          // 隐藏爆炸动画节点
          this.blast.node.getComponent(UIOpacity).opacity = 0; // 如果主飞机还有剩余生命次数

          if (this.m_data.lifeNum > 0) {
            // 减少生命次数
            this.m_data.lifeNum--; // 更新剩余生命次数到全局数据
            // GameIns.gameDataManager.setLifeNum(this.m_data.lifeNum);
            // 触发复活逻辑

            this.relife(0);
          } else {
            // 如果没有剩余生命次数，检查是否可以复活
            var reviveCount = (_crd && GameIns === void 0 ? (_reportPossibleCrUseOfGameIns({
              error: Error()
            }), GameIns) : GameIns).gameDataManager.reviveCount;

            if (this.m_data.relifeNum - reviveCount <= 0) {
              // // 如果是远征模式，结束当前章节
              // if (ExpeditionManager.isExpedition) {
              //     ExpeditionManager.sectionOver(false);
              // } else {
              //     // 否则触发战斗失败逻辑
              (_crd && GameIns === void 0 ? (_reportPossibleCrUseOfGameIns({
                error: Error()
              }), GameIns) : GameIns).battleManager.battleFail(); // }
            } else {
              // 如果可以复活，触发战斗死亡逻辑
              (_crd && GameIns === void 0 ? (_reportPossibleCrUseOfGameIns({
                error: Error()
              }), GameIns) : GameIns).battleManager.battleDie();
            }
          }
        }
        /**
         * 启用或禁用火力
         * @param {boolean} enable 是否启用火力
         */


        setFireEnable(enable) {
          if (this.m_config && this.m_config.type === 710) return;

          if (enable) {
            this.m_fireAnim.forEach(anim => {
              anim.getComponent(UIOpacity).opacity = 255; // 显示火力动画
            });
            this.point.parent.getComponent(UIOpacity).opacity = 255;
          } else {
            this.m_fireAnim.forEach(anim => {
              anim.getComponent(UIOpacity).opacity = 0; // 隐藏火力动画
            });
            this.point.parent.getComponent(UIOpacity).opacity = 0;
          }
        }
        /**
         * 设置飞机是否可移动
         * @param {boolean} enable 是否可移动
         */


        setMoveAble(enable) {
          this.m_moveEnable = enable;
        }
        /**
         * 设置碰撞是否可用
         * @param {boolean} enable 是否启用碰撞
         */


        setColAble(enable) {
          this.m_collideComp.enabled = enable;
        }
        /**
         * 开始射击
         */


        beginFire() {
          (_crd && GameIns === void 0 ? (_reportPossibleCrUseOfGameIns({
            error: Error()
          }), GameIns) : GameIns).mainPlaneManager.fireEnable = true;
        }
        /**
         * 停止射击
         */


        stopFire() {
          (_crd && GameIns === void 0 ? (_reportPossibleCrUseOfGameIns({
            error: Error()
          }), GameIns) : GameIns).mainPlaneManager.fireEnable = false;
        } //     /**
        //      * 飞机准备阶段
        //      */
        //     planeHold() {
        //         this.willBegine(); // 准备开始
        //         this.node.parent = BattleLayer.me.selfPlaneLayer; // 设置父节点
        //         this.skin.getComponent(UIOpacity).opacity = 255;
        //         this.skin.scale = 1;
        //         this.node.scale = GameIns.battleManager.getRatio();
        //         tween(this.node)
        //             .delay(1)
        //             .call(() => {
        //                 this.begine(); // 开始战斗
        //             })
        //             .start();
        //     }

        /**
         * 准备开始战斗
         */


        willBegine() {
          if ((_crd && GameIns === void 0 ? (_reportPossibleCrUseOfGameIns({
            error: Error()
          }), GameIns) : GameIns).battleManager.isContinue) {
            var hp = (_crd && GameIns === void 0 ? (_reportPossibleCrUseOfGameIns({
              error: Error()
            }), GameIns) : GameIns).gameDataManager.getMainPlaneHp();
            (_crd && GameIns === void 0 ? (_reportPossibleCrUseOfGameIns({
              error: Error()
            }), GameIns) : GameIns).mainPlaneManager.data.hp = hp; // 恢复血量

            this.UpdateHp(); // 更新血量显示
          }
        }
        /**
         * 开始战斗
         * @param {boolean} isContinue 是否继续战斗
         */


        begine(isContinue) {
          if (isContinue === void 0) {
            isContinue = false;
          }

          if (isContinue) {
            this.beginFire();
            this.m_moveEnable = true; // WinePlaneManager.me.beginBattle();

            if (this.m_collideComp) {
              this.m_collideComp.enabled = true;
            }

            if (this.m_skill) {
              this.m_skill.startBattle();
            }
          } else {
            // switch (GameIns.battleManager.gameType) {
            // case GameEnum.GameType.Gold:
            //     GoldBattleManager.onStartBattle();
            //     break;
            // case GameType.Boss:
            //     BossBattleManager.startBattle();
            //     break;
            // default:
            //     if (ExpeditionManager.isExpedition) {
            //         ExpeditionManager.startBattle();
            //     } else {
            (_crd && GameIns === void 0 ? (_reportPossibleCrUseOfGameIns({
              error: Error()
            }), GameIns) : GameIns).battleManager.startBattle(); // }
            // }
          }

          (_crd && GameIns === void 0 ? (_reportPossibleCrUseOfGameIns({
            error: Error()
          }), GameIns) : GameIns).gameDataManager.setMainPlaneHp((_crd && GameIns === void 0 ? (_reportPossibleCrUseOfGameIns({
            error: Error()
          }), GameIns) : GameIns).mainPlaneManager.data.hp); // const isMechaOver = GameIns.gameDataManager.isMechaOver || GameIns.mainPlaneManager.checkPlayMechaAnim();
          // this.shadow.spriteFrame = GameIns.loadManager.getImage(
          //     isMechaOver ? this.m_config.yz + "2" : this.m_config.yz,
          //     "package_mainPlane_trans_" + this.m_config.type
          // );
          // this.shadow.node.getComponent(UIOpacity).opacity = 255;
        } //     /**
        //      * 隐藏主飞机的特效
        //      * @param {Function} callback 回调函数
        //      */
        //     hideMainEffect(callback) {
        //         if (this.parkourBot) {
        //             tween(this.parkourBot.node)
        //                 .to(0.3, { opacity: 0 })
        //                 .start();
        //             tween(this.parkourTop.node)
        //                 .to(0.3, { opacity: 0 })
        //                 .start();
        //             tween(this.parkourMid.node)
        //                 .delay(8 / 30)
        //                 .to(0.3, { opacity: 0 })
        //                 .call(() => {
        //                     if (callback) callback();
        //                 })
        //                 .start();
        //         } else if (callback) {
        //             callback();
        //         }
        //     }

        /**
         * 主飞机入场动画
         */


        planeIn() {
          var self = this; // 初始化飞机状态

          this.willBegine();
          var frameTime = (_crd && GameConfig === void 0 ? (_reportPossibleCrUseOfGameConfig({
            error: Error()
          }), GameConfig) : GameConfig).ActionFrameTime; // 播放入场音效
          // frameWork.audioManager.playEffect("planeFristIn");
          // 设置初始位置和状态

          this.node.getComponent(UIOpacity).opacity = 255;
          this.node.parent = (_crd && BattleLayer === void 0 ? (_reportPossibleCrUseOfBattleLayer({
            error: Error()
          }), BattleLayer) : BattleLayer).me.selfPlaneLayer;
          this.skin.getComponent(UIOpacity).opacity = 255;
          this.skin.setScale(1, 1);
          var posY = -view.getVisibleSize().height - (this.m_config.type === 711 ? 1000 : 80);
          this.node.setPosition(0, posY);
          this.node.setScale((_crd && GameIns === void 0 ? (_reportPossibleCrUseOfGameIns({
            error: Error()
          }), GameIns) : GameIns).battleManager.getRatio(), (_crd && GameIns === void 0 ? (_reportPossibleCrUseOfGameIns({
            error: Error()
          }), GameIns) : GameIns).battleManager.getRatio());
          this.stopFire();
          this.m_moveEnable = false;
          Tween.stopAllByTarget(this.node); // 设置地图速度变化动画

          this._mapTween = tween((_crd && GameMapRun === void 0 ? (_reportPossibleCrUseOfGameMapRun({
            error: Error()
          }), GameMapRun) : GameMapRun).instance).to((_crd && GameFunc === void 0 ? (_reportPossibleCrUseOfGameFunc({
            error: Error()
          }), GameFunc) : GameFunc).fromTo(0, 10) / (_crd && GameIns === void 0 ? (_reportPossibleCrUseOfGameIns({
            error: Error()
          }), GameIns) : GameIns).battleManager.animSpeed, {
            changeMapSpeedRatio: 2100,
            changeSkySpeedRatio: 2100
          }).delay((_crd && GameFunc === void 0 ? (_reportPossibleCrUseOfGameFunc({
            error: Error()
          }), GameFunc) : GameFunc).fromTo(10, 40) / (_crd && GameIns === void 0 ? (_reportPossibleCrUseOfGameIns({
            error: Error()
          }), GameIns) : GameIns).battleManager.animSpeed).to((_crd && GameFunc === void 0 ? (_reportPossibleCrUseOfGameFunc({
            error: Error()
          }), GameFunc) : GameFunc).fromTo(40, 60) / (_crd && GameIns === void 0 ? (_reportPossibleCrUseOfGameIns({
            error: Error()
          }), GameIns) : GameIns).battleManager.animSpeed, {
            changeMapSpeedRatio: 1400,
            changeSkySpeedRatio: 1400
          }).to((_crd && GameFunc === void 0 ? (_reportPossibleCrUseOfGameFunc({
            error: Error()
          }), GameFunc) : GameFunc).fromTo(60, 80) / (_crd && GameIns === void 0 ? (_reportPossibleCrUseOfGameIns({
            error: Error()
          }), GameIns) : GameIns).battleManager.animSpeed, {
            changeMapSpeedRatio: 1050,
            changeSkySpeedRatio: 1050
          }).to((_crd && GameFunc === void 0 ? (_reportPossibleCrUseOfGameFunc({
            error: Error()
          }), GameFunc) : GameFunc).fromTo(80, 110) / (_crd && GameIns === void 0 ? (_reportPossibleCrUseOfGameIns({
            error: Error()
          }), GameIns) : GameIns).battleManager.animSpeed, {
            changeMapSpeedRatio: 700,
            changeSkySpeedRatio: 700
          }).to((_crd && GameFunc === void 0 ? (_reportPossibleCrUseOfGameFunc({
            error: Error()
          }), GameFunc) : GameFunc).fromTo(110, 150) / (_crd && GameIns === void 0 ? (_reportPossibleCrUseOfGameIns({
            error: Error()
          }), GameIns) : GameIns).battleManager.animSpeed, {
            changeMapSpeedRatio: 350,
            changeSkySpeedRatio: 350
          }).to((_crd && GameFunc === void 0 ? (_reportPossibleCrUseOfGameFunc({
            error: Error()
          }), GameFunc) : GameFunc).fromTo(150, 200) / (_crd && GameIns === void 0 ? (_reportPossibleCrUseOfGameIns({
            error: Error()
          }), GameIns) : GameIns).battleManager.animSpeed, {
            changeMapSpeedRatio: 0,
            changeSkySpeedRatio: 0
          }).call(() => {
            self._mapTween = null;
          }).start(); // 飞机入场动画

          this.scheduleOnce(() => {
            var targetY = -view.getVisibleSize().height * 0.7;
            var targetX = this.node.position.x;
            tween(this.node).to(20 * frameTime / (_crd && GameIns === void 0 ? (_reportPossibleCrUseOfGameIns({
              error: Error()
            }), GameIns) : GameIns).battleManager.animSpeed, {
              position: v3(targetX, targetY - 17)
            }).to(11 * frameTime / (_crd && GameIns === void 0 ? (_reportPossibleCrUseOfGameIns({
              error: Error()
            }), GameIns) : GameIns).battleManager.animSpeed, {
              position: v3(targetX, targetY + 57)
            }).to(10 * frameTime / (_crd && GameIns === void 0 ? (_reportPossibleCrUseOfGameIns({
              error: Error()
            }), GameIns) : GameIns).battleManager.animSpeed, {
              position: v3(targetX, targetY + 76)
            }).to(27 * frameTime / (_crd && GameIns === void 0 ? (_reportPossibleCrUseOfGameIns({
              error: Error()
            }), GameIns) : GameIns).battleManager.animSpeed, {
              position: v3(targetX, targetY)
            }).call(() => {
              self.begine();
            }).start();
            tween(this.skin).to(20 * frameTime / (_crd && GameIns === void 0 ? (_reportPossibleCrUseOfGameIns({
              error: Error()
            }), GameIns) : GameIns).battleManager.animSpeed, {
              scale: v3(1.9, 1.9)
            }).to(11 * frameTime / (_crd && GameIns === void 0 ? (_reportPossibleCrUseOfGameIns({
              error: Error()
            }), GameIns) : GameIns).battleManager.animSpeed, {
              scale: v3(1.4, 1.4)
            }).to(10 * frameTime / (_crd && GameIns === void 0 ? (_reportPossibleCrUseOfGameIns({
              error: Error()
            }), GameIns) : GameIns).battleManager.animSpeed, {
              scale: v3(1, 1)
            }).to(27 * frameTime / (_crd && GameIns === void 0 ? (_reportPossibleCrUseOfGameIns({
              error: Error()
            }), GameIns) : GameIns).battleManager.animSpeed, {
              scale: v3(1, 1)
            }).start(); // if (this.hpBar) {
            //     tween(this.hpBar)
            //         .to(0, { opacity: 0 })
            //         .delay(31 * frameTime / GameIns.battleManager.animSpeed)
            //         .to(10 * frameTime / GameIns.battleManager.animSpeed, { opacity: 255 })
            //         .start();
            // }

            this.scheduleOnce(() => {
              tween(this.streak.node).to(9 * frameTime / (_crd && GameIns === void 0 ? (_reportPossibleCrUseOfGameIns({
                error: Error()
              }), GameIns) : GameIns).battleManager.animSpeed, {
                scale: v3(1, 4)
              }).to(7 * frameTime / (_crd && GameIns === void 0 ? (_reportPossibleCrUseOfGameIns({
                error: Error()
              }), GameIns) : GameIns).battleManager.animSpeed, {
                scale: v3(1, 2)
              }).to(5 * frameTime / (_crd && GameIns === void 0 ? (_reportPossibleCrUseOfGameIns({
                error: Error()
              }), GameIns) : GameIns).battleManager.animSpeed, {
                scale: v3(1, 1)
              }).call(() => {
                self.addStreak();
              }).start();
            }, 2 * frameTime / (_crd && GameIns === void 0 ? (_reportPossibleCrUseOfGameIns({
              error: Error()
            }), GameIns) : GameIns).battleManager.animSpeed);
          }, 7 * frameTime / (_crd && GameIns === void 0 ? (_reportPossibleCrUseOfGameIns({
            error: Error()
          }), GameIns) : GameIns).battleManager.animSpeed); // // 更新影子动画
          // tween(this.shadow.node)
          //     .to(GameFunc.fromTo(0, 24) / GameIns.battleManager.animSpeed, { y: 0, x: 0, scale: 1.184 })
          //     .to(GameFunc.fromTo(34, 56) / GameIns.battleManager.animSpeed, { y: -100, x: 40, scale: 0.86 })
          //     .to(GameFunc.fromTo(56, 86) / GameIns.battleManager.animSpeed, { y: -180, x: 108, scale: 0.62, opacity: 255 })
          //     .start();
        } //     /**
        //      * 飞机出场动画
        //      */
        //     planeOut() {
        //         tween(this.node)
        //             .to(0.5, { y: winSize.height })
        //             .call(() => {
        //                 this.battleOver();
        //             })
        //             .start();
        //     }
        //     /**
        //      * 播放下方瞄准特效
        //      */
        //     playBelowAim() {
        //         this.below.node.active = true;
        //         this.below.node.scale = 2.2 * this.node.scale;
        //         this.below.animation = "play";
        //         this.below.setCompleteListener(() => {
        //             this.below.node.active = false;
        //         });
        //     }
        //     /**
        //      * 飞机降落动画
        //      */
        //     planeLand() {
        //         PlaneManager.me.shadowPlane?.hideSelf();
        //         const targetNode = GameIns.battleManager.rogueui.node.getChildByName("back").getChildByName("smain");
        //         this.node.parent = null;
        //         targetNode.insertChild(this.node, 1);
        //         this.node.position = GameIns.battleManager.rogueui.mainLandTarget.position;
        //         CopyPlaneManager.me.copyPlanes.forEach((plane) => {
        //             plane.node.parent = null;
        //             targetNode.insertChild(plane.node, 1);
        //             plane.node.y = GameIns.battleManager.rogueui.mainLandTarget.y - 100;
        //         });
        //         this.streak.node.active = false;
        //     }
        //     /**
        //      * 飞机进入战斗准备状态
        //      */
        //     mainPlaneInBoot() {
        //         const targetY = GameIns.battleManager.rogueui.mainLandTarget.y;
        //         this.node.parent = null;
        //         const backNode = GameIns.battleManager.rogueui.node.getChildByName("back");
        //         const siblingIndex = backNode.getChildByName("fc").getSiblingIndex();
        //         backNode.insertChild(this.node, siblingIndex + 1);
        //         PlaneManager.me.shadowPlane?.node.setSiblingIndex(siblingIndex + 1);
        //         CopyPlaneManager.me.copyPlanes.forEach((plane) => {
        //             plane.node.parent = null;
        //             backNode.insertChild(plane.node, siblingIndex + 1);
        //             plane.node.y = -100;
        //         });
        //         this.node.y = this.node.y - GameIns.battleManager.rogueui.node.y + backNode.y + winSize.height / 2;
        //         this.node.scale = GameIns.battleManager.getRatio();
        //         this.streak.node.stopAllActions();
        //         this.shadow.node.y = -180;
        //         this.shadow.node.x = 108;
        //         tween(this.node)
        //             .to(1.5, { y: targetY + 117 + 350, x: 0 })
        //             .call(() => {
        //                 PlaneManager.me.shadowPlane?.mechaShow(false);
        //                 this.begine();
        //             })
        //             .to(1, { y: targetY })
        //             .call(this.playBelowAim.bind(this))
        //             .start();
        //     }
        //     /**
        //      * 战斗结束处理
        //      */
        //     battleOver() {
        //         BattleManager.isGameType(GameType.Gold) || GameIns.battleManager.battleOver();
        //     }
        //     /**
        //      * 播放连线动画
        //      */
        //     playLigatureAnim() {
        //         if (!this._ligatureAnim) {
        //             const animNode = instantiate(GameConst.frameAnim);
        //             this.skin.addChild(animNode);
        //             this._ligatureAnim = animNode.getComponent(PfFrameAnim);
        //             this._ligatureAnim.init(
        //                 GameIns.loadManager.getRes("package_mainPlane", SpriteAtlas),
        //                 "all_",
        //                 8,
        //                 2 * GameConfig.ActionFrameTime,
        //                 () => {
        //                     this._ligatureAnim.stop();
        //                 }
        //             );
        //         }
        //         this._ligatureAnim.reset();
        //     }
        //     /**
        //      * 播放获取金币动画
        //      */
        //     playGetGoldAnim() {
        //         const atlas = GameIns.loadManager.getRes("package_mainPlane", SpriteAtlas);
        //         if (atlas) {
        //             let anim = this._goldAnimArr.pop();
        //             if (!anim) {
        //                 const animNode = instantiate(GameConst.frameAnim);
        //                 this.skin.getChildByName("goldAnim").addChild(animNode);
        //                 animNode.y = this.point.y;
        //                 anim = animNode.getComponent(PfFrameAnim);
        //                 anim.init(atlas, "ag_", 6, GameConfig.ActionFrameTime, () => {
        //                     anim.stop();
        //                     this._goldAnimArr.push(anim);
        //                 });
        //             }
        //             anim.reset();
        //         } else {
        //             GameIns.loadManager.loadAtlas("package_mainPlane");
        //         }
        //     }
        //     /**
        //      * 设置皮肤颜色
        //      * @param {Color} color 颜色值
        //      */
        //     set skinColor(color) {
        //         this.skinImg.node.color = color;
        //     }
        //     /**
        //      * 播放皮肤光效动画
        //      */
        //     playSkinLightAnim() {
        //         tween(this.skinImg.node)
        //             .to(16 * GameConfig.ActionFrameTime, { color: color(255, 255, 255) })
        //             .start();
        //     }
        //     /**
        //      * 飞机变形
        //      * @param {Object} data 变形数据
        //      * @param {number} index 当前变形阶段
        //      */
        //     planeTransform(data, index) {
        //         const transformData = this.m_config[`trans${data.mainTrans}`];
        //         const src = this.m_config.transSrc;
        //         const ext = this.m_config.transExt;
        //         this.skinAnim.node.getComponent(UIOpacity).opacity = 0;
        //         this.skinImg.node.active = true;
        //         this.avatarNodeActive = this.m_config.type !== 710 || BattleManager.gameType !== GameType.Gold;
        //         if (index >= transformData.length) return;
        //         switch (transformData[index]) {
        //             case 0:
        //                 this.playTrans0(data, index + 1);
        //                 break;
        //             case 1:
        //                 this.setTrans1Img(this.mainTransLevel.get(1) || 2, src, ext);
        //                 this.playTrans1(data, index + 1);
        //                 break;
        //             case 2:
        //                 this.setTrans2Img(src);
        //                 this.planeTransform(data, index + 1);
        //                 break;
        //             case 3:
        //                 this.setTrans3Img(this.mainTransLevel.get(3) || 2, src, ext);
        //                 this.playTrans3(data, index + 1);
        //                 break;
        //             case 4:
        //                 this.setTrans4Img(this.mainTransLevel.get(4) || 2, src, ext);
        //                 this.playTrans4(data, index + 1);
        //                 break;
        //         }
        //     }
        //     /**
        //      * 播放变形阶段 0 的动画
        //      * @param {Object} data 变形数据
        //      * @param {number} nextIndex 下一阶段索引
        //      */
        //     playTrans0(data, nextIndex) {
        //         this.avatarNodeActive = false;
        //         this.skinImg.node.active = false;
        //         this.skinAnim.node.getComponent(UIOpacity).opacity = 255;
        //         this.skinAnim.animation = "levelup";
        //         this.skinAnim.setCompleteListener(() => {
        //             this.planeTransform(data, nextIndex);
        //         });
        //     }
        //     /**
        //      * 设置变形阶段 1 的图片
        //      * @param {number} level 当前阶段等级
        //      * @param {Array} src 图片资源
        //      * @param {Array} ext 图片扩展信息
        //      */
        //     setTrans1Img(level, src, ext) {
        //         const imgName = `${src[1]}${level}`;
        //         const whiteImgName = `${imgName}-white`;
        //         const position = ext[0].split(",");
        //         const sprite = this.attspeAnim.node.getChildByName("attspe").getComponent(HDSprite);
        //         const whiteSprite = this.attspeAnim.node.getChildByName("attspe").getChildByName("white").getComponent(HDSprite);
        //         sprite.spriteFrame = GameIns.loadManager.getImage(imgName, `package_mainPlane_trans_${this.m_config.type}`);
        //         whiteSprite.spriteFrame = GameIns.loadManager.getImage(whiteImgName, `package_mainPlane_trans_${this.m_config.type}`);
        //         this.attspeAnim.node.position = new Vec2(Number(position[0]), Number(position[1]));
        //     }
        //     /**
        //      * 播放变形阶段 1 的动画
        //      * @param {Object} data 变形数据
        //      * @param {number} nextIndex 下一阶段索引
        //      */
        //     playTrans1(data, nextIndex) {
        //         this.attspeAnim.stop("atspeTrans701");
        //         this.attspeAnim.node.active = true;
        //         this.attspeAnim.play("atspeTrans701");
        //         this.attspeAnim.once(Animation.EventType.FINISHED, () => {
        //             this.planeTransform(data, nextIndex);
        //         });
        //     }
        //     /**
        //      * 设置变形阶段 2 的图片
        //      * @param {Array} src 图片资源
        //      */
        //     setTrans2Img(src) {
        //         this.skinImg.spriteFrame = GameIns.loadManager.getImage(
        //             src[0],
        //             `package_mainPlane_trans_${this.m_config.type}`
        //         );
        //         this.skinImg.node.width = this.skinImg.spriteFrame.getOriginalSize().width;
        //         this.skinImg.node.height = this.skinImg.spriteFrame.getOriginalSize().height;
        //     }
        //     /**
        //      * 设置变形阶段 3 的图片
        //      * @param {number} level 当前阶段等级
        //      * @param {Array} src 图片资源
        //      * @param {Array} ext 图片扩展信息
        //      */
        //     setTrans3Img(level, src, ext) {
        //         --level;
        //         this.scrAnim.node.children.forEach((child, index) => {
        //             const scr = child.getChildByName("scr");
        //             const white = scr.getChildByName("white");
        //             const imgName = `${src[2]}${level}`;
        //             const whiteImgName = `${imgName}-white`;
        //             const position = ext[1].split("|")[level].split(",");
        //             scr.getComponent(HDSprite).spriteFrame = GameIns.loadManager.getImage(
        //                 imgName,
        //                 `package_mainPlane_trans_${this.m_config.type}`
        //             );
        //             white.getComponent(HDSprite).spriteFrame = GameIns.loadManager.getImage(
        //                 whiteImgName,
        //                 `package_mainPlane_trans_${this.m_config.type}`
        //             );
        //             child.position = new Vec2(
        //                 Number(position[0]) * Math.pow(-1, index),
        //                 Number(position[1])
        //             );
        //         });
        //     }
        //     /**
        //      * 播放变形阶段 3 的动画
        //      * @param {Object} data 变形数据
        //      * @param {number} nextIndex 下一阶段索引
        //      */
        //     playTrans3(data, nextIndex) {
        //         this.scrAnim.stop("scrpar701");
        //         this.scrAnim.node.active = true;
        //         this.scrAnim.play("scrpar701");
        //         this.scrAnim.once(Animation.EventType.FINISHED, () => {
        //             this.planeTransform(data, nextIndex);
        //         });
        //     }
        //     /**
        //      * 设置变形阶段 4 的图片
        //      * @param {number} level 当前阶段等级
        //      * @param {Array} src 图片资源
        //      * @param {Array} ext 图片扩展信息
        //      */
        //     setTrans4Img(level, src, ext) {
        //         --level;
        //         this.auxAnim.node.children.forEach((child, index) => {
        //             const aux = child.getChildByName("aux");
        //             const white = aux.getChildByName("white");
        //             const imgName = `${src[3]}${level}`;
        //             const whiteImgName = `${imgName}-white`;
        //             const position = ext[2].split("|")[level - 1].split(",");
        //             aux.getComponent(HDSprite).spriteFrame = GameIns.loadManager.getImage(
        //                 imgName,
        //                 `package_mainPlane_trans_${this.m_config.type}`
        //             );
        //             white.getComponent(HDSprite).spriteFrame = GameIns.loadManager.getImage(
        //                 whiteImgName,
        //                 `package_mainPlane_trans_${this.m_config.type}`
        //             );
        //             child.position = new Vec2(
        //                 Number(position[0]) * Math.pow(-1, index),
        //                 Number(position[1])
        //             );
        //         });
        //     }
        //     /**
        //      * 播放变形阶段 4 的动画
        //      * @param {Object} data 变形数据
        //      * @param {number} nextIndex 下一阶段索引
        //      */
        //     playTrans4(data, nextIndex) {
        //         this.auxAnim.stop("auxpar701");
        //         this.auxAnim.node.active = true;
        //         this.auxAnim.play("auxpar701");
        //         this.auxAnim.once(Animation.EventType.FINISHED, () => {
        //             this.planeTransform(data, nextIndex);
        //         });
        //     }

        /**
         * 初始化变形图片
         */


        initPic() {
          this.mainTransLevel = new Map();
          var attspe = this.attspeAnim.node.getChildByName("attspe");
          attspe.getComponent(Sprite).spriteFrame = (_crd && GameIns === void 0 ? (_reportPossibleCrUseOfGameIns({
            error: Error()
          }), GameIns) : GameIns).loadManager.getImage(this.m_config.transSrc[1] + "1", "mainPlane/package_mainPlane_trans_" + this.m_config.type);
          var position = this.m_config.transExt[0].split(",");
          attspe.setPosition(Number(position[0]), Number(position[1]));
          this.scrAnim.node.children.forEach(child => {
            var scr = child.getChildByName("scr");
            scr.getComponent(Sprite).spriteFrame = (_crd && GameIns === void 0 ? (_reportPossibleCrUseOfGameIns({
              error: Error()
            }), GameIns) : GameIns).loadManager.getImage(this.m_config.transSrc[2] + "1", "mainPlane/package_mainPlane_trans_" + this.m_config.type);
            ;
            var pos = this.m_config.transExt[1].split(",");
            child.setPosition(Number(pos[0]), Number(pos[1]));
          });
          this.auxAnim.node.children.forEach(child => {
            var aux = child.getChildByName("aux");
            aux.getComponent(Sprite).spriteFrame = (_crd && GameIns === void 0 ? (_reportPossibleCrUseOfGameIns({
              error: Error()
            }), GameIns) : GameIns).loadManager.getImage(this.m_config.transSrc[3] + "1", "mainPlane/package_mainPlane_trans_" + this.m_config.type);
            ;
            var pos = this.m_config.transExt[2].split(",");
            child.setPosition(Number(pos[0]), Number(pos[1]));
          });
        } //     /**
        //      * 播放充能动画
        //      * @param {Vec2} position 动画位置
        //      * @param {number} index 动画索引
        //      */
        //     playChargeAnim(position, index) {
        //         return new Promise((resolve) => {
        //             let spine = this.m_spines[index];
        //             if (!spine) {
        //                 const node = new Node();
        //                 spine = node.addComponent(sp.Skeleton);
        //                 this.m_spines[index] = spine;
        //                 GameIns.loadManager.loadSpine("skel_charge").then((skeletonData) => {
        //                     spine.skeletonData = skeletonData;
        //                     this._playChargeAnimInternal(spine, position, resolve);
        //                 });
        //             } else {
        //                 this._playChargeAnimInternal(spine, position, resolve);
        //             }
        //         });
        //     }
        //     /**
        //      * 内部方法：播放充能动画
        //      * @param {sp.Skeleton} spine 动画组件
        //      * @param {Vec2} position 动画位置
        //      * @param {Function} resolve Promise 的 resolve 方法
        //      */
        //     _playChargeAnimInternal(spine, position, resolve) {
        //         spine.node.parent = this.node;
        //         spine.node.position = position;
        //         spine.node.active = true;
        //         spine.premultipliedAlpha = false;
        //         spine.animation = "play1";
        //         spine.paused = false;
        //         spine.setCompleteListener(() => {
        //             spine.node.active = false;
        //             spine.setCompleteListener(null);
        //             resolve();
        //         });
        //     }
        //     /**
        //      * 停止充能动画
        //      * @param {number} index 动画索引
        //      */
        //     stopChargeAnim(index) {
        //         const spine = this.m_spines[index];
        //         if (spine) {
        //             spine.paused = true;
        //             spine.node.active = false;
        //             spine.setCompleteListener(null);
        //         }
        //     }
        //     /**
        //      * 播放火力动画
        //      * @param {Function} callback 动画完成后的回调
        //      * @param {number} index 动画索引
        //      */
        //     playFireAnim(callback, index) {
        //         const spine = this.m_spines[index];
        //         if (spine) {
        //             spine.animation = "play2";
        //             spine.paused = false;
        //             spine.setEventListener(() => {
        //                 callback();
        //                 spine.setEventListener(null);
        //             });
        //             spine.setCompleteListener(() => {
        //                 spine.node.active = false;
        //                 spine.setCompleteListener(null);
        //             });
        //         }
        //     }

        /**
         * 播放挑战动画
         * @param {string} animationName 动画名称
         */


        playChallengeAnim(animationName) {
          if (this.challengeAnim) {
            this.challengeAnim.node.active = true;
            this.challengeAnim.animation = animationName;
            this.challengeAnim.setCompleteListener(() => {
              this.challengeAnim.node.active = false;
              this.challengeAnim.setCompleteListener(null);
            });
            this.challengeAnimName = null;
          } else {
            this.challengeAnimName = animationName;
          }
        } //     /**
        //      * 播放套装技能特效
        //      */
        //     playSuitSkillEffect() {
        //         this.suitEffect.stopAllActions();
        //         tween(this.suitEffect)
        //             .to(0, { scale: 0.75, opacity: 177 })
        //             .to(3 * GameConfig.ActionFrameTime, { scale: 1.6, opacity: 230 })
        //             .to(14 * GameConfig.ActionFrameTime, { scale: 2, opacity: 0 })
        //             .start();
        //     }
        //     /**
        //      * 初始化套装特效
        //      */
        //     initSuitEffect() {
        //         const suitType = EquipManager.getFullSuitType();
        //         if (suitType) {
        //             this.suitEffect.active = true;
        //             this.suitEffect.getComponent(UIOpacity).opacity = 0;
        //             const colors = [color(255, 66, 255), color(255, 222, 66), color(255, 222, 66)];
        //             const quality = EquipManager.getSuitQuality(suitType);
        //             this.suitEffect.color = colors[quality - 1];
        //         }
        //     }
        //     /**
        //      * 释放技能
        //      */
        //     fireSkill() {
        //         if (this.m_skill) {
        //             this.m_skill.fire();
        //         }
        //     }
        //     /**
        //      * 获取技能进度
        //      * @returns {number} 技能进度
        //      */
        //     getSkillProgress() {
        //         return this.m_skill ? this.m_skill.getProgress() : 0;
        //     }
        //     /**
        //      * 获取技能冷却时间
        //      * @returns {number} 技能冷却时间
        //      */
        //     getSkillCD() {
        //         return this.m_skill ? this.m_skill.cdTime : 0;
        //     }
        //     /**
        //      * 获取技能对象
        //      * @returns {Object|null} 技能对象
        //      */
        //     getSkill() {
        //         return this.m_skill;
        //     }
        //     /**
        //      * 设置皮肤是否激活
        //      * @param {boolean} isActive 是否激活
        //      */
        //     setSkinAcitive(isActive) {
        //         this.skinImg.node.active = !isActive;
        //         this.avatarNodeActive = !isActive;
        //         this.skinAnim.node.getComponent(UIOpacity).opacity = isActive ? 255 : 0;
        //     }
        //     /**
        //      * 播放机甲动画
        //      */
        //     playMechaAnim() {
        //         const type = this.m_config.type;
        //         m.default.audioManager.playEffect(`turret_fit${type}`);
        //         this.setSkinAcitive(true);
        //         this.skinAnim.animation = "tocrazy";
        //         if (this.m_skinShield) {
        //             this.m_skinShield.setAnimation(0, "tocrazy", false);
        //         }
        //         if (this.m_skinCircle) {
        //             this.m_skinCircle.setAnimation(0, "tocrazy", false);
        //         }
        //         switch (type) {
        //             case 708:
        //                 this.planeNode.scale = this.mechaScale[0];
        //                 break;
        //             case 709:
        //                 this.planeNode.scale = this.mechaScale[1];
        //                 break;
        //             case 710:
        //                 this.planeNode.scale = this.mechaScale[2];
        //                 break;
        //             case 711:
        //                 this.planeNode.scale = this.mechaScale[3];
        //                 break;
        //             default:
        //                 this.planeNode.scale = 1;
        //         }
        //         this.mechaOver();
        //         this.skinAnim.setCompleteListener(() => {
        //             this.skinAnim.setCompleteListener(null);
        //             this.skinAnim.setAnimation(0, "crazyidle", true);
        //             if (this.m_skinShield) {
        //                 this.m_skinShield.setAnimation(0, "crazyidle", true);
        //             }
        //             if (this.m_skinCircle) {
        //                 this.m_skinCircle.setAnimation(0, "crazyidle", true);
        //             }
        //             u.MainPlaneMgr.isEndChange = true;
        //             N.default.me.mapEndChange();
        //         });
        //     }
        //     /**
        //      * 获取单位的起始位置
        //      * @param {number} index 单位索引
        //      * @returns {Vec2} 起始位置
        //      */
        //     getUnitStartPosByindex(index) {
        //         const worldPos = C.default.me.winePlanes[index].node.convertToWorldSpaceAR(Vec2.ZERO);
        //         return this.node.convertToNodeSpaceAR(worldPos);
        //     }
        //     /**
        //      * 加载单位预制体
        //      */
        //     loadUnitPrefab() {
        //         return new Promise((resolve) => {
        //             m.default.loadManager.loadPrefab("mechaUnit")
        //                 .then((prefab) => {
        //                     this.pfb = prefab;
        //                     resolve();
        //                 })
        //                 .catch(() => {
        //                     at.GFunc.wxLoadErr();
        //                 });
        //         });
        //     }
        //     /**
        //      * 播放单位动画
        //      * @param {number} index 单位索引
        //      * @param {string} skillName 技能名称
        //      */
        //     playUnitAnim(index, skillName) {
        //         const prefabInstance = instantiate(this.pfb);
        //         prefabInstance.parent = this.node;
        //         prefabInstance.scale = 0.85;
        //         const signNode = prefabInstance.getChildByName("sign");
        //         signNode.getComponent(L.default).spriteFrame = m.default.loadManager.getImage(`tu_${skillName}`, "mecha_commonImg");
        //         // 动画逻辑根据索引处理
        //         switch (index) {
        //             case 0:
        //                 // 动画逻辑 0
        //                 break;
        //             case 1:
        //                 // 动画逻辑 1
        //                 break;
        //             case 2:
        //                 // 动画逻辑 2
        //                 break;
        //             case 3:
        //                 // 动画逻辑 3
        //                 break;
        //             default:
        //                 break;
        //         }
        //     }
        //     /**
        //      * 播放单位结束动画
        //      */
        //     unitOverAnim() {
        //         this.scheduleOnce(() => {
        //             const scale = this.m_config.type === 708 ? 0.75 : 0.65;
        //             const overLight = instantiate(this.mechaUnitOverLight);
        //             overLight.parent = this.node;
        //             overLight.scale = 2 * scale;
        //             const anim = overLight.getComponent(Animation);
        //             if (anim) {
        //                 anim.play();
        //                 anim.once(Animation.EventType.FINISHED, () => {
        //                     overLight.destroy();
        //                 });
        //             } else {
        //                 overLight.destroy();
        //             }
        //         }, 0.1);
        //     }
        //     /**
        //      * 机甲结束逻辑
        //      */
        //     mechaOver() {
        //         return new Promise((resolve) => {
        //             if (u.MainPlaneMgr.isPlane(710)) {
        //                 resolve();
        //                 return;
        //             }
        //             setTimeout(() => {
        //                 E.GData.isMechaOver = true;
        //                 if (this.m_config.type === 712) {
        //                     this.hpBar.y = 90;
        //                 }
        //                 const dialog = m.default.uiManager.getDialog(et.default) || m.default.uiManager.createDialog(et.default);
        //                 dialog.showFont(() => {
        //                     this.skinAnim.animation = "ingametoidle";
        //                     if (this.m_skinShield) {
        //                         this.m_skinShield.setAnimation(0, "ingametoidle", false);
        //                     }
        //                     if (this.m_skinCircle) {
        //                         this.m_skinCircle.setAnimation(0, "ingametoidle", false);
        //                     }
        //                     this.skinAnim.setCompleteListener(() => {
        //                         this.skinAnim.setCompleteListener(null);
        //                         this.setMechaOverPic();
        //                     });
        //                     tween(this.skinAnim.node)
        //                         .to(0.5, { y: this.initPosSkinAnim - 100 })
        //                         .start();
        //                     tween(this.mechaAnimNode)
        //                         .to(0.5, { y: this.initPosMechaAnim - 100 })
        //                         .start();
        //                     resolve();
        //                 });
        //             }, 1750);
        //         });
        //     }
        //     /**
        //      * 设置技能可以释放
        //      */
        //     setCanFireSkill() {
        //         if (this.m_skill) {
        //             this.m_skill.setCanFire();
        //         }
        //     }
        //     /**
        //      * 设置机甲结束后的图片
        //      * @param {boolean} isInit 是否初始化
        //      */
        //     setMechaOverPic(isInit = false) {
        //         if (this.m_config.type !== 710) {
        //             this.skinAnim.node.getComponent(UIOpacity).opacity = 0;
        //             this.avatarNodeActive = false;
        //             this.setUnit(this.mechaAnimNode, isInit);
        //             if (PlaneManager.me.shadowPlane) {
        //                 PlaneManager.me.shadowPlane.mechaShow(true);
        //             }
        //             this.refreshStreak();
        //         }
        //     }
        //     /**
        //      * 设置单位
        //      * @param {Node} parent 父节点
        //      * @param {boolean} isInit 是否初始化
        //      */
        //     setUnit(parent, isInit = false) {
        //         let shouldInstantiate = true;
        //         if (this.m_config.type === 710) {
        //             if (parent.childrenCount > 0) {
        //                 shouldInstantiate = false;
        //             }
        //         } else {
        //             parent.destroyAllChildren();
        //         }
        //         if (shouldInstantiate) {
        //             for (const prefab of this.unitPrefab) {
        //                 const instance = instantiate(prefab);
        //                 instance.parent = parent;
        //                 instance.getComponent(Animation).play();
        //                 const name = instance.name;
        //                 instance.zIndex = -Number(name[name.length - 1]);
        //                 if (isInit) {
        //                     this.mechaAvatar(instance);
        //                 }
        //             }
        //             if (this.m_config.type === 711 && BattleManager.isContinue) {
        //                 // Additional logic for type 711
        //             }
        //         }
        //     }
        //     /**
        //      * 设置机甲头像图片
        //      * @param {Node} node 节点
        //      * @param {string} leftName 左侧图片名称
        //      * @param {string} rightName 右侧图片名称
        //      * @param {number} level 等级
        //      * @param {string} prefix 图片前缀
        //      */
        //     setMechaAvatarImg(node, leftName, rightName, level, prefix) {
        //         return new Promise((resolve) => {
        //             const leftNode = node.getChildByName(leftName);
        //             if (leftNode) {
        //                 GameIns.loadManager.loadAtlas("diaochan").then((atlas) => {
        //                     const leftSprite = leftNode.getComponent(Sprite);
        //                     const rightSprite = node.getChildByName(rightName).getComponent(Sprite);
        //                     const adjustedLevel = this.mainTransLevel.get(level) - 2 || 0;
        //                     leftSprite.spriteFrame = atlas.getSpriteFrame(`${prefix}${adjustedLevel}`);
        //                     rightSprite.spriteFrame = atlas.getSpriteFrame(`${prefix}${adjustedLevel}`);
        //                     resolve();
        //                 });
        //             } else {
        //                 resolve();
        //             }
        //         });
        //     }
        //     /**
        //      * 设置机甲头像
        //      * @param {Node} node 节点
        //      */
        //     mechaAvatar(node) {
        //         this.setMechaAvatarImg(node, "zuojian_zuo", "zuojian_you", 3, "zuojian_");
        //         this.setMechaAvatarImg(node, "zuofa_zuo", "zuofa_you", 1, "zuofa_");
        //         this.setMechaAvatarImg(node, "zuoxiaotui_zuo", "zuoxiaotui_you", 1, "zuoxiaotui_");
        //     }

        /**
         * 退出游戏时设置图片
         */


        quitGameSetPic() {// this.skinAnim.node.getComponent(UIOpacity).opacity = 0;
          // this.avatarNodeActive = true;
          // this.mechaAnimNode.destroyAllChildren();
        } //     /**
        //      * 清除机甲结束后的图片
        //      * @param {boolean} isInit 是否初始化
        //      */
        //     clearMechaOverPic(isInit = false) {
        //         if (this.m_skill) {
        //             this.m_skill.removeSkill();
        //         }
        //         this.skinAnim.node.getComponent(UIOpacity).opacity = 0;
        //         this.avatarNodeActive = true;
        //         this.mechaAnimNode.destroyAllChildren();
        //         for (const prefab of this.unitPrefab) {
        //             GameIns.loadManager.releaseAsset(prefab, false);
        //         }
        //         this.unitPrefab.splice(0);
        //         GameIns.loadManager.releaseAsset(this.pfb, false);
        //         GameIns.loadManager.releaseAsset(this.mechaAtlas_Anim, true);
        //         GameIns.loadManager.releaseAsset(this.mechaUnitOverLight, false);
        //         this.pfb = null;
        //         this.mechaAtlas_Anim = null;
        //         this.mechaUnitOverLight = null;
        //         GameIns.loadManager.releaseRes("mecha_commonImg", SpriteAtlas, true);
        //         GameIns.loadManager.releaseOriginalArr(this.animArr, AnimationClip, false);
        //         GameIns.loadManager.releaseOriginalArr(
        //             [
        //                 "animation/MainPlane/mecha/batUnitAnim",
        //                 "animation/MainPlane/mecha/dragonUnitAnim",
        //                 "animation/MainPlane/mecha/sword/swordUnitAnim",
        //                 "animation/MainPlane/mecha/unitOverLight",
        //             ],
        //             AnimationClip,
        //             false
        //         );
        //     }
        //     /**
        //      * 设置蝙蝠技能数据
        //      * @param {number} distanceX X轴距离
        //      * @param {number} distanceY Y轴距离
        //      * @param {number} speed 速度
        //      * @param {Array} targetNodes 目标节点数组
        //      * @param {Vec2} startPoint 起始点
        //      */
        //     batSkillData(distanceX, distanceY, speed, targetNodes, startPoint) {
        //         this.skillBatDis = [distanceX, distanceY, speed];
        //         this.skillTargetNodes = targetNodes;
        //         this.skillBatPoint = startPoint;
        //     }
        //     /**
        //      * 播放机甲进入动画1
        //      */
        //     mechaInAnim1() {
        //         if (!E.GData.isMechaOver) {
        //             const unlocks = this.m_config.unlock;
        //             const winePlanes = C.default.me.winePlanes;
        //             unlocks.forEach((unlock, index) => {
        //                 if (winePlanes[index]) {
        //                     winePlanes[index].playBotAnim();
        //                 }
        //             });
        //         }
        //     }
        //     /**
        //      * 播放机甲进入动画2
        //      */
        //     mechaInAnim2() {
        //         if (E.GData.isMechaOver) {
        //             this.begine();
        //         } else {
        //             const unlocks = this.m_config.unlock;
        //             const winePlanes = C.default.me.winePlanes;
        //             if (winePlanes[0]) {
        //                 unlocks.forEach((unlock, index) => {
        //                     this.playUnitAnim(index, winePlanes[index].m_config.Onskill);
        //                 });
        //             } else {
        //                 this.playMechaAnim();
        //                 this.unitOverAnim();
        //             }
        //         }
        //     }
        //     /**
        //      * 进入Roguelike模式动画
        //      */
        //     roguelikeInAnim() {
        //         this.mechaAnimNode.active = false;
        //         this.skinAnim.node.getComponent(UIOpacity).opacity = 255;
        //         this.skinAnim.animation = "land";
        //         if (this.m_skinShield) {
        //             this.m_skinShield.setAnimation(0, "land", false);
        //         }
        //         if (this.m_skinCircle) {
        //             this.m_skinCircle.setAnimation(0, "land", false);
        //         }
        //         if (U.default.me.shadowPlane) {
        //             U.default.me.shadowPlane.node.active = false;
        //         }
        //         this.skinAnim.setCompleteListener(() => {
        //             this.skinAnim.setCompleteListener(null);
        //             this.avatarNodeActive = true;
        //             this.skinAnim.node.getComponent(UIOpacity).opacity = 0;
        //         });
        //         switch (this.m_config.type) {
        //             case 712:
        //             case 713:
        //                 this.skinAnim.node.y = 0;
        //                 break;
        //             case 714:
        //             case 715:
        //                 this.skinAnim.node.y = -16;
        //                 break;
        //             default:
        //                 this.skinAnim.node.y = this.initPosSkinAnim;
        //         }
        //         this.mechaAnimNode.y = this.initPosMechaAnim;
        //         this.suitAnimBot.y = this.initPosSuitBot;
        //         this.suitAnimTop.y = this.initPosSuitTop;
        //         this.planeNode.scale = 1;
        //     }
        //     /**
        //      * 退出Roguelike模式动画
        //      */
        //     roguelikeOutAnim() {
        //         this.skinAnim.node.getComponent(UIOpacity).opacity = 255;
        //         this.avatarNodeActive = false;
        //         this.skinAnim.animation = "launch";
        //         if (this.m_skinShield) {
        //             this.m_skinShield.setAnimation(0, "launch", false);
        //         }
        //         if (this.m_skinCircle) {
        //             this.m_skinCircle.setAnimation(0, "launch", false);
        //         }
        //         this.skinAnim.setCompleteListener(() => {
        //             if (F.default.me.getMySkillByType(23)) {
        //                 if (U.default.me.shadowPlane) {
        //                     U.default.me.shadowPlane.node.active = true;
        //                     U.default.me.shadowPlane.mechaShow(true);
        //                 }
        //             }
        //             this.skinAnim.setCompleteListener(null);
        //             this.mechaAnimNode.active = true;
        //             this.skinAnim.node.getComponent(UIOpacity).opacity = 0;
        //         });
        //         if (this.m_config.type !== 710) {
        //             if (this.skinAnim) {
        //                 tween(this.skinAnim.node)
        //                     .to(5 / 30, { y: this.initPosSkinAnim - (this.m_config.type === 711 ? 0 : 100) })
        //                     .start();
        //             }
        //             if (this.mechaAnimNode) {
        //                 tween(this.mechaAnimNode)
        //                     .to(5 / 30, { y: this.initPosMechaAnim - (this.m_config.type === 711 ? 0 : 100) })
        //                     .start();
        //             }
        //             if (this.suitAnimBot) {
        //                 tween(this.suitAnimBot)
        //                     .to(5 / 30, { y: this.initPosSuitBot - (this.m_config.type === 712 || this.m_config.type === 714 || this.m_config.type === 715 ? 0 : 100) })
        //                     .start();
        //             }
        //             if (this.suitAnimTop) {
        //                 tween(this.suitAnimTop)
        //                     .to(5 / 30, { y: this.initPosSuitTop - (this.m_config.type === 712 || this.m_config.type === 714 || this.m_config.type === 715 ? 0 : 100) })
        //                     .start();
        //             }
        //         }
        //         if (this.planeNode) {
        //             if (E.GData.isMechaOver) {
        //                 switch (this.m_config.type) {
        //                     case 708:
        //                         tween(this.planeNode).to(5 / 30, { scale: this.mechaScale[0] }).start();
        //                         break;
        //                     case 709:
        //                         tween(this.planeNode).to(5 / 30, { scale: this.mechaScale[1] }).start();
        //                         break;
        //                     case 710:
        //                         tween(this.planeNode).to(5 / 30, { scale: this.mechaScale[2] }).start();
        //                         break;
        //                     case 711:
        //                         tween(this.planeNode).to(5 / 30, { scale: this.mechaScale[3] }).start();
        //                         break;
        //                     default:
        //                         this.planeNode.scale = 1;
        //                 }
        //             } else {
        //                 this.planeNode.scale = 1;
        //             }
        //         }
        //     }


      }, _class3.PrefabName = "MainPlane", _class3), (_descriptor = _applyDecoratedDescriptor(_class2.prototype, "skinImg", [_dec2], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function initializer() {
          return null;
        }
      }), _descriptor2 = _applyDecoratedDescriptor(_class2.prototype, "skinAnim", [_dec3], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function initializer() {
          return null;
        }
      }), _descriptor3 = _applyDecoratedDescriptor(_class2.prototype, "streak", [_dec4], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function initializer() {
          return null;
        }
      }), _descriptor4 = _applyDecoratedDescriptor(_class2.prototype, "planeNode", [_dec5], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function initializer() {
          return null;
        }
      }), _descriptor5 = _applyDecoratedDescriptor(_class2.prototype, "point", [_dec6], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function initializer() {
          return null;
        }
      }), _descriptor6 = _applyDecoratedDescriptor(_class2.prototype, "skin", [_dec7], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function initializer() {
          return null;
        }
      }), _descriptor7 = _applyDecoratedDescriptor(_class2.prototype, "blast", [_dec8], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function initializer() {
          return null;
        }
      }), _descriptor8 = _applyDecoratedDescriptor(_class2.prototype, "frontNode", [_dec9], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function initializer() {
          return null;
        }
      }), _descriptor9 = _applyDecoratedDescriptor(_class2.prototype, "backNode", [_dec10], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function initializer() {
          return null;
        }
      }), _descriptor10 = _applyDecoratedDescriptor(_class2.prototype, "shadow", [_dec11], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function initializer() {
          return null;
        }
      }), _descriptor11 = _applyDecoratedDescriptor(_class2.prototype, "dg", [_dec12], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function initializer() {
          return null;
        }
      }), _descriptor12 = _applyDecoratedDescriptor(_class2.prototype, "bl", [_dec13], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function initializer() {
          return null;
        }
      }), _descriptor13 = _applyDecoratedDescriptor(_class2.prototype, "air", [_dec14], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function initializer() {
          return null;
        }
      }), _descriptor14 = _applyDecoratedDescriptor(_class2.prototype, "attspeAnim", [_dec15], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function initializer() {
          return null;
        }
      }), _descriptor15 = _applyDecoratedDescriptor(_class2.prototype, "scrAnim", [_dec16], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function initializer() {
          return null;
        }
      }), _descriptor16 = _applyDecoratedDescriptor(_class2.prototype, "auxAnim", [_dec17], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function initializer() {
          return null;
        }
      }), _descriptor17 = _applyDecoratedDescriptor(_class2.prototype, "avatarNode", [_dec18], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function initializer() {
          return null;
        }
      }), _descriptor18 = _applyDecoratedDescriptor(_class2.prototype, "suitAnimBot", [_dec19], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function initializer() {
          return null;
        }
      }), _descriptor19 = _applyDecoratedDescriptor(_class2.prototype, "suitAnimTop", [_dec20], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function initializer() {
          return null;
        }
      }), _descriptor20 = _applyDecoratedDescriptor(_class2.prototype, "suitEffect", [_dec21], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function initializer() {
          return null;
        }
      }), _descriptor21 = _applyDecoratedDescriptor(_class2.prototype, "mechaAnimNode", [_dec22], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function initializer() {
          return null;
        }
      })), _class2)) || _class));

      _cclegacy._RF.pop();

      _crd = false;
    }
  };
});
//# sourceMappingURL=3f1fb1b0ed938bdce99b77de634e782c06fd0829.js.map