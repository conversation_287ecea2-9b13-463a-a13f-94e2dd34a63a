{"version": 3, "sources": ["file:///E:/M2Game/Client/assets/scripts/Game/ui/plane/enemy/EnemyPlaneRole.ts"], "names": ["_decorator", "Component", "Sprite", "Vec2", "Node", "tween", "UITransform", "Tween", "v3", "instantiate", "GameConfig", "GameIns", "Tools", "EnemyAnim", "ccclass", "property", "EnemyPlaneRole", "_data", "_param", "_target", "_curUId", "_anim", "_curAnim", "_anim<PERSON><PERSON><PERSON><PERSON><PERSON>", "_animWhitePool", "_animWhiteOffset", "ZERO", "_tailFireArr", "_rotateComp", "_warnLine", "_warnAble", "_winkCount", "_bW<PERSON><PERSON><PERSON>e", "_winkAct", "_initOver", "_sneakGoUpAnimArr", "_sneakGoUpDelayArr", "_sneakAnim", "_cloakeAnim", "preLoadUI", "data", "_initSneak", "_initUI", "fireNode", "node", "getChildByName", "addComponent", "<PERSON><PERSON><PERSON><PERSON>", "name", "i", "tailFire", "push", "active", "init", "target", "param", "_reset", "isAm", "playAnim", "opacity", "white", "pedestal", "spriteFrame", "stopAnim", "reset", "updateGameLogic", "dt", "enabled", "isPreload", "role", "to", "ActionFrameTime", "id", "destroy", "pf", "enemyManager", "getPlaneRole", "image", "animNode", "getComponent", "extraParam", "setPlaneFrame", "frameTime", "length", "anchorY", "stopAllByTarget", "setPosition", "setContentSize", "angle", "scale", "repeatF<PERSON><PERSON>", "start", "setEventCallback", "eventName", "callback", "setAnimEventCall", "startAttack", "setAngleAble", "attackOver", "<PERSON><PERSON><PERSON><PERSON>", "clone", "anim<PERSON><PERSON>", "log", "pauseAnim", "resumeAnim", "_initSneakAnim", "setSneakDir", "playSneakAnim", "playSneakGoUpAnim", "anim", "scheduleOnce", "play", "blueShow", "playCloakeAnim", "playCloakeHideAnim", "playCloakeShowAnim", "playAtkWarnAnim", "y", "scaleY", "height", "width", "stopAllActions", "delay", "call"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAASA,MAAAA,U,OAAAA,U;AAAYC,MAAAA,S,OAAAA,S;AAAWC,MAAAA,M,OAAAA,M;AAAQC,MAAAA,I,OAAAA,I;AAAMC,MAAAA,I,OAAAA,I;AAAMC,MAAAA,K,OAAAA,K;AAAWC,MAAAA,W,OAAAA,W;AAAaC,MAAAA,K,OAAAA,K;AAAOC,MAAAA,E,OAAAA,E;AAAYC,MAAAA,W,OAAAA,W;;AACxFC,MAAAA,U;;AACEC,MAAAA,O,iBAAAA,O;;AACAC,MAAAA,K,iBAAAA,K;;AACFC,MAAAA,S;;;;;;;;;OAED;AAAEC,QAAAA,OAAF;AAAWC,QAAAA;AAAX,O,GAAwBf,U;;yBAGTgB,c,WAChBD,QAAQ,CAACb,MAAD,C,UAGRa,QAAQ,CAACb,MAAD,C,UAGRa,QAAQ,CAACb,MAAD,C,EARZY,O,qBAAD,MACqBE,cADrB,SAC4Cf,SAD5C,CACsD;AAAA;AAAA;;AAAA;;AAAA;;AAAA;;AAAA,eAUlDgB,KAVkD,GAU1C,IAV0C;AAAA,eAWlDC,MAXkD,GAWzC,EAXyC;AAAA,eAYlDC,OAZkD,GAYxC,IAZwC;AAAA,eAalDC,OAbkD,GAaxC,CAAC,CAbuC;AAAA,eAclDC,KAdkD,GAc1C,IAd0C;AAAA,eAelDC,QAfkD,GAevC,EAfuC;AAAA,eAgBlDC,aAhBkD,GAgBlC,EAhBkC;AAAA,eAiBlDC,cAjBkD,GAiBjC,EAjBiC;AAAA,eAkBlDC,gBAlBkD,GAkB/BtB,IAAI,CAACuB,IAlB0B;AAAA,eAmBlDC,YAnBkD,GAmB5B,EAnB4B;AAAA,eAoBlDC,WApBkD,GAoBpC,IApBoC;AAAA,eAqBlDC,SArBkD,GAqBtC,IArBsC;AAAA,eAsBlDC,SAtBkD,GAsBtC,KAtBsC;AAAA,eAuBlDC,UAvBkD,GAuBrC,CAvBqC;AAAA,eAwBlDC,WAxBkD,GAwBpC,KAxBoC;AAAA,eAyBlDC,QAzBkD,GAyBvC,IAzBuC;AAAA,eA0BlDC,SA1BkD,GA0BtC,KA1BsC;AAAA,eA2BlDC,iBA3BkD,GA2B9B,EA3B8B;AAAA,eA4BlDC,kBA5BkD,GA4B7B,EA5B6B;AAAA,eA6BlDC,UA7BkD,GA6BrC,IA7BqC;AAAA,eA8BlDC,WA9BkD,GA8BpC,IA9BoC;AAAA;;AAiClD;AACJ;AACA;AACA;AACIC,QAAAA,SAAS,CAACC,IAAD,EAAO;AACZ,cAAI,KAAKvB,KAAL,GAAauB,IAAjB,EAAuB;AACnB,iBAAKC,UAAL;;AACA,iBAAKC,OAAL,CAAa,IAAb;AACH,WAHD,MAGO;AACH,gBAAIC,QAAQ,GAAG,KAAKC,IAAL,CAAUC,cAAV,CAAyB,MAAzB,CAAf;;AACA,gBAAI,CAACF,QAAL,EAAe;AACXA,cAAAA,QAAQ,GAAG,IAAIvC,IAAJ,EAAX;AACAuC,cAAAA,QAAQ,CAACG,YAAT,CAAsBxC,WAAtB;AACA,mBAAKsC,IAAL,CAAUG,QAAV,CAAmBJ,QAAnB;AACAA,cAAAA,QAAQ,CAACK,IAAT,GAAgB,MAAhB;AACH;;AACD,iBAAK,IAAIC,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAG,CAApB,EAAuBA,CAAC,EAAxB,EAA4B;AACxB,kBAAMC,QAAQ,GAAG,IAAI9C,IAAJ,EAAjB;AACA8C,cAAAA,QAAQ,CAACJ,YAAT,CAAsBxC,WAAtB;AACAqC,cAAAA,QAAQ,CAACI,QAAT,CAAkBG,QAAlB;AACAA,cAAAA,QAAQ,CAACJ,YAAT,CAAsB5C,MAAtB,EAJwB,CAKxB;;AACA,mBAAKyB,YAAL,CAAkBwB,IAAlB,CAAuBD,QAAvB;;AACAA,cAAAA,QAAQ,CAACE,MAAT,GAAkB,KAAlB;AACH;AACJ;AACJ;AAED;AACJ;AACA;AACA;AACA;AACA;;;AACUC,QAAAA,IAAI,CAACb,IAAD,EAAOc,MAAP,EAAeC,KAAf,EAA2B;AAAA;;AAAA;AAAA,gBAAZA,KAAY;AAAZA,cAAAA,KAAY,GAAJ,EAAI;AAAA;;AACjC,YAAA,KAAI,CAACC,MAAL;;AACA,YAAA,KAAI,CAACvC,KAAL,GAAauB,IAAb;AACA,YAAA,KAAI,CAACtB,MAAL,GAAcqC,KAAd;AACA,YAAA,KAAI,CAACpC,OAAL,GAAemC,MAAf;;AAEA,gBAAI,CAAC,KAAI,CAACrC,KAAL,CAAWwC,IAAhB,EAAsB;AAClB,cAAA,KAAI,CAAChB,UAAL;;AACA,oBAAM,KAAI,CAACC,OAAL,EAAN;AACH,aAHD,MAGO;AACH,kBAAI,KAAI,CAACrB,KAAT,EAAgB;AACZ,gBAAA,KAAI,CAACoB,UAAL;;AACA,gBAAA,KAAI,CAACiB,QAAL,CAAc,OAAd;;AACA,gBAAA,KAAI,CAACrC,KAAL,CAAWuB,IAAX,CAAgBe,OAAhB,GAA0B,GAA1B;AACH,eAJD,MAIO;AACH,gBAAA,KAAI,CAACzB,SAAL,GAAiB,IAAjB;AACH;AACJ;AAjBgC;AAkBpC;AAED;AACJ;AACA;;;AACIsB,QAAAA,MAAM,GAAG;AACL,eAAKI,KAAL,CAAWhB,IAAX,CAAgBe,OAAhB,GAA0B,CAA1B;AACA,eAAKE,QAAL,CAAcC,WAAd,GAA4B,IAA5B;AACA,eAAKxC,QAAL,GAAgB,EAAhB;AACA,eAAKY,SAAL,GAAiB,KAAjB;AACA,eAAK6B,QAAL;AACA,cAAI,KAAK1C,KAAT,EAAgB,KAAKA,KAAL,CAAWuB,IAAX,CAAgBe,OAAhB,GAA0B,CAA1B;AAChB,cAAI,KAAKtB,UAAT,EAAqB,KAAKA,UAAL,CAAgBO,IAAhB,CAAqBQ,MAArB,GAA8B,KAA9B;AACrB,cAAI,KAAKxB,WAAT,EAAsB,KAAKA,WAAL,CAAiBoC,KAAjB;AACtB,eAAKlC,SAAL,GAAiB,KAAjB;AACA,cAAI,KAAKD,SAAT,EAAoB,KAAKA,SAAL,CAAeuB,MAAf,GAAwB,KAAxB;AACvB;AAED;AACJ;AACA;AACA;;;AACIa,QAAAA,eAAe,CAACC,EAAD,EAAK;AAChB,cAAI,KAAKtC,WAAL,IAAoB,KAAKA,WAAL,CAAiBuC,OAAzC,EAAkD;AAC9C,iBAAKvC,WAAL,CAAiBqC,eAAjB,CAAiCC,EAAjC;AACH;;AACD,cAAI,KAAKlC,WAAT,EAAsB;AAClB,iBAAKD,UAAL;;AACA,gBAAI,KAAKA,UAAL,GAAkB,CAAtB,EAAyB;AACrB,mBAAKA,UAAL,GAAkB,CAAlB;AACA,mBAAKC,WAAL,GAAmB,KAAnB;AACH;AACJ;AACJ;AAED;AACJ;AACA;AACA;;;AACUU,QAAAA,OAAO,CAAC0B,SAAD,EAAoB;AAAA;;AAAA;AAAA,gBAAnBA,SAAmB;AAAnBA,cAAAA,SAAmB,GAAP,KAAO;AAAA;;AAC7B,gBAAI,MAAI,CAACnD,KAAL,CAAWwC,IAAf,EAAqB;AACjB,cAAA,MAAI,CAACY,IAAL,CAAUP,WAAV,GAAwB,IAAxB;AACA,cAAA,MAAI,CAACF,KAAL,CAAWE,WAAX,GAAyB,IAAzB;AACA,cAAA,MAAI,CAAC7B,QAAL,GAAgB5B,KAAK,GAAGiE,EAAR,CAAW,CAAX,EAAc;AAAEX,gBAAAA,OAAO,EAAE;AAAX,eAAd,EAAgCW,EAAhC,CAAmC,IAAI;AAAA;AAAA,4CAAWC,eAAlD,EAAmE;AAAEZ,gBAAAA,OAAO,EAAE;AAAX,eAAnE,CAAhB;;AAEA,kBAAI,MAAI,CAACvC,OAAL,KAAiB,MAAI,CAACH,KAAL,CAAWuD,EAA5B,IAAkC,MAAI,CAACnD,KAA3C,EAAkD;AAC9C,gBAAA,MAAI,CAACA,KAAL,CAAWuB,IAAX,CAAgBe,OAAhB,GAA0B,GAA1B;AACH,eAFD,MAEO;AACH,gBAAA,MAAI,CAACvC,OAAL,GAAe,MAAI,CAACH,KAAL,CAAWuD,EAA1B;;AACA,oBAAI,MAAI,CAACnD,KAAT,EAAgB;AACZ,kBAAA,MAAI,CAACA,KAAL,CAAWuB,IAAX,CAAgB6B,OAAhB;;AACA,kBAAA,MAAI,CAACpD,KAAL,GAAa,IAAb;AACH;;AACD,oBAAIqD,EAAE,SAAS;AAAA;AAAA,wCAAQC,YAAR,CAAqBC,YAArB,CAAkC,MAAI,CAAC3D,KAAL,CAAW4D,KAA7C,CAAf;AACA,oBAAMC,QAAQ,GAAGrE,WAAW,CAACiE,EAAD,CAA5B;;AACA,gBAAA,MAAI,CAAC9B,IAAL,CAAUG,QAAV,CAAmB+B,QAAnB;;AACA,gBAAA,MAAI,CAACzD,KAAL,GAAayD,QAAQ,CAACC,YAAT;AAAA;AAAA,2CAAb;;AACA,gBAAA,MAAI,CAAC1D,KAAL,CAAWgC,IAAX,CAAgB,MAAI,CAACpC,KAAL,CAAW+D,UAA3B;AACH;AACJ,aAnBD,MAmBO;AACH,kBAAI,MAAI,CAAC3D,KAAT,EAAgB,MAAI,CAACA,KAAL,CAAWuB,IAAX,CAAgBQ,MAAhB,GAAyB,KAAzB;AAChB,cAAA,MAAI,CAACiB,IAAL,CAAUzB,IAAV,CAAee,OAAf,GAAyB,GAAzB;AACA;AAAA;AAAA,sCAAQgB,YAAR,CAAqBM,aAArB,CAAmC,MAAI,CAACZ,IAAxC,EAA8C,MAAI,CAACpD,KAAL,CAAW4D,KAAzD;AACA;AAAA;AAAA,sCAAQF,YAAR,CAAqBM,aAArB,CAAmC,MAAI,CAACrB,KAAxC,EAA+C,MAAI,CAAC3C,KAAL,CAAW4D,KAA1D;AACA,cAAA,MAAI,CAAC5C,QAAL,GAAgB5B,KAAK,GAAGiE,EAAR,CAAW,CAAX,EAAc;AAAEX,gBAAAA,OAAO,EAAE;AAAX,eAAd,EAAgCW,EAAhC,CAAmC,IAAI;AAAA;AAAA,4CAAWC,eAAlD,EAAmE;AAAEZ,gBAAAA,OAAO,EAAE;AAAX,eAAnE,CAAhB,CALG,CAOH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AACA,kBAAI,MAAI,CAAC/B,WAAT,EAAsB;AAClB,gBAAA,MAAI,CAACA,WAAL,CAAiBuC,OAAjB,GAA2B,KAA3B;AACH;;AAED,kBAAIxB,QAAQ,GAAG,MAAI,CAACC,IAAL,CAAUC,cAAV,CAAyB,MAAzB,CAAf;;AACA,kBAAI,CAACF,QAAL,EAAe;AACXA,gBAAAA,QAAQ,GAAG,IAAIvC,IAAJ,EAAX;;AACA,gBAAA,MAAI,CAACwC,IAAL,CAAUG,QAAV,CAAmBJ,QAAnB;;AACAA,gBAAAA,QAAQ,CAACK,IAAT,GAAgB,MAAhB;AACH;;AAED,kBAAMkC,SAAS,GAAG;AAAA;AAAA,4CAAWX,eAA7B;;AACA,mBAAK,IAAItB,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAG,MAAI,CAAChC,KAAL,CAAW+D,UAAX,CAAsBG,MAA1C,EAAkDlC,CAAC,EAAnD,EAAuD;AACnD,oBAAMM,KAAK,GAAG,MAAI,CAACtC,KAAL,CAAW+D,UAAX,CAAsB/B,CAAtB,CAAd;AACA,oBAAIC,QAAa,GAAG,MAAI,CAACvB,YAAL,CAAkBsB,CAAlB,CAApB;;AACA,oBAAI,CAACC,QAAL,EAAe;AACXA,kBAAAA,QAAQ,GAAG,IAAI9C,IAAJ,EAAX;AACAuC,kBAAAA,QAAQ,CAACI,QAAT,CAAkBG,QAAlB;AACAA,kBAAAA,QAAQ,CAACJ,YAAT,CAAsB5C,MAAtB;AACAgD,kBAAAA,QAAQ,CAAC6B,YAAT,CAAsBzE,WAAtB,EAAmC8E,OAAnC,GAA6C,CAA7C;;AACA,kBAAA,MAAI,CAACzD,YAAL,CAAkBwB,IAAlB,CAAuBD,QAAvB;AACH;;AACD3C,gBAAAA,KAAK,CAAC8E,eAAN,CAAsBnC,QAAtB;AACAA,gBAAAA,QAAQ,CAACE,MAAT,GAAkB,IAAlB;AACA;AAAA;AAAA,wCAAQuB,YAAR,CAAqBM,aAArB,CAAmC/B,QAAQ,CAAC6B,YAAT,CAAsB7E,MAAtB,CAAnC,EAAkE,SAASqD,KAAK,CAAC,CAAD,CAAhF;AACAL,gBAAAA,QAAQ,CAACoC,WAAT,CAAqB/B,KAAK,CAAC,CAAD,CAA1B,EAA+BA,KAAK,CAAC,CAAD,CAApC;AACAL,gBAAAA,QAAQ,CAAC6B,YAAT,CAAsBzE,WAAtB,EAAmCiF,cAAnC,CAAkDhC,KAAK,CAAC,CAAD,CAAvD,EAA4DA,KAAK,CAAC,CAAD,CAAjE;AACAL,gBAAAA,QAAQ,CAACsC,KAAT,GAAiBjC,KAAK,CAAC,CAAD,CAAL,IAAY,CAA7B;AAIAlD,gBAAAA,KAAK,CAAC6C,QAAD,CAAL,CACKoB,EADL,CACQY,SAAS,GAAG3B,KAAK,CAAC,CAAD,CADzB,EAC8B;AAAEkC,kBAAAA,KAAK,EAAEjF,EAAE,CAAC+C,KAAK,CAAC,CAAD,CAAN,EAAUA,KAAK,CAAC,CAAD,CAAf;AAAX,iBAD9B,EAEKe,EAFL,CAEQY,SAAS,GAAG3B,KAAK,CAAC,CAAD,CAFzB,EAE8B;AAAEkC,kBAAAA,KAAK,EAAEjF,EAAE,CAAC,CAAD,EAAG,CAAH;AAAX,iBAF9B,EAGKkF,aAHL,GAIKC,KAJL;AAKH;;AAED,mBAAK,IAAI1C,EAAC,GAAG,MAAI,CAAChC,KAAL,CAAW+D,UAAX,CAAsBG,MAAnC,EAA2ClC,EAAC,GAAG,MAAI,CAACtB,YAAL,CAAkBwD,MAAjE,EAAyElC,EAAC,EAA1E,EAA8E;AAC1E,oBAAMC,SAAQ,GAAG,MAAI,CAACvB,YAAL,CAAkBsB,EAAlB,CAAjB;AACA1C,gBAAAA,KAAK,CAAC8E,eAAN,CAAsBnC,SAAtB;AACAA,gBAAAA,SAAQ,CAACE,MAAT,GAAkB,KAAlB;AACH;AACJ;AAhF4B;AAiFhC;AAED;AACJ;AACA;AACA;AACA;;;AACIwC,QAAAA,gBAAgB,CAACC,SAAD,EAAYC,QAAZ,EAAsB;AAClC,eAAKzE,KAAL,IAAc,KAAKA,KAAL,CAAW0E,gBAAX,CAA4BF,SAA5B,EAAuCC,QAAvC,CAAd;AACH;AAED;AACJ;AACA;;;AACIE,QAAAA,WAAW,GAAG;AACV,eAAKpE,WAAL,IAAoB,KAAKA,WAAL,CAAiBqE,YAAjB,CAA8B,IAA9B,CAApB;AACH;AAED;AACJ;AACA;;;AACIC,QAAAA,UAAU,GAAG;AACT,eAAKtE,WAAL,IAAoB,KAAKA,WAAL,CAAiBqE,YAAjB,CAA8B,KAA9B,CAApB;AACH;AAED;AACJ;AACA;;;AACIE,QAAAA,SAAS,GAAG;AACR,cAAI,CAAC,KAAKnE,WAAV,EAAuB;AACnB,iBAAKA,WAAL,GAAmB,IAAnB;;AACA,gBAAI,CAAC,KAAKf,KAAL,CAAWwC,IAAhB,EAAsB;AAClB,mBAAKxB,QAAL,CAAcmE,KAAd,CAAoB,KAAKxC,KAAL,CAAWhB,IAA/B,EAAqC+C,KAArC;AACH;AACJ;AACJ;AAED;AACJ;AACA;AACA;AACA;AACA;;;AACIjC,QAAAA,QAAQ,CAAC2C,QAAD,EAAWP,QAAX,EAA4B;AAAA,cAAjBA,QAAiB;AAAjBA,YAAAA,QAAiB,GAAN,IAAM;AAAA;;AAChC,cAAI,CAAC,KAAK7E,KAAL,CAAWwC,IAAhB,EAAsB,OAAO,KAAP;;AACtB,cAAI,KAAKnC,QAAL,KAAkB+E,QAAtB,EAAgC;AAC5B;AAAA;AAAA,gCAAMC,GAAN,CAAU,WAAV,EAAuBD,QAAvB;AACA,mBAAO,IAAP;AACH;;AACD,cAAI,KAAKhF,KAAT,EAAgB;AACZ,iBAAKA,KAAL,CAAWqC,QAAX,CAAoB2C,QAApB,EAA8BP,QAA9B;;AACA,mBAAO,IAAP;AACH;;AACD;AAAA;AAAA,8BAAMQ,GAAN,CAAU,cAAV,EAA0BD,QAA1B;AACA,iBAAO,KAAP;AACH;AAED;AACJ;AACA;;;AACIE,QAAAA,SAAS,GAAG;AACR,eAAKlF,KAAL,IAAc,KAAKA,KAAL,CAAWkF,SAAX,EAAd;AACH;AAED;AACJ;AACA;;;AACIC,QAAAA,UAAU,GAAG;AACT,eAAKnF,KAAL,IAAc,KAAKA,KAAL,CAAWmF,UAAX,EAAd;AACH;AAED;AACJ;AACA;;;AACIzC,QAAAA,QAAQ,GAAG;AACP,eAAK1C,KAAL,IAAc,KAAKA,KAAL,CAAW0C,QAAX,EAAd;AACH;AAED;AACJ;AACA;;;AACItB,QAAAA,UAAU,GAAG,CACT;AACH;AAED;AACJ;AACA;;;AACUgE,QAAAA,cAAc,GAAG,CACnB;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAzBmB;AA0BtB;AAED;AACJ;AACA;AACA;;;AACIC,QAAAA,WAAW,CAAClB,KAAD,EAAQ;AACf,cAAI,KAAKnD,UAAL,IAAmB,KAAKA,UAAL,CAAgBO,IAAhB,CAAqBQ,MAA5C,EAAoD;AAChD,iBAAKf,UAAL,CAAgBO,IAAhB,CAAqB4C,KAArB,GAA6BA,KAA7B;AACH;AACJ;AAED;AACJ;AACA;;;AACImB,QAAAA,aAAa,GAAG;AACZ,cAAI,KAAKtE,UAAT,EAAqB;AACjB,iBAAKA,UAAL,CAAgBO,IAAhB,CAAqBQ,MAArB,GAA8B,IAA9B;AACA,iBAAKiB,IAAL,CAAUzB,IAAV,CAAee,OAAf,GAAyB,CAAzB;;AACA,gBAAI,KAAKtC,KAAT,EAAgB;AACZ,mBAAKA,KAAL,CAAWuB,IAAX,CAAgBe,OAAhB,GAA0B,CAA1B;AACH;;AACD,iBAAKtB,UAAL,CAAgB2B,KAAhB;AACH;AACJ;AAED;AACJ;AACA;;;AACI4C,QAAAA,iBAAiB,GAAG;AAAA;;AAChB,cAAI,KAAKvE,UAAT,EAAqB;AACjB,iBAAKA,UAAL,CAAgBO,IAAhB,CAAqBQ,MAArB,GAA8B,KAA9B;AACH;;AACD,cAAI,KAAKnC,KAAL,CAAWwC,IAAf,EAAqB;AACjB,gBAAI,KAAKpC,KAAT,EAAgB;AACZ,mBAAKA,KAAL,CAAWuB,IAAX,CAAgBe,OAAhB,GAA0B,GAA1B;AACH;AACJ,WAJD,MAIO;AACH,iBAAKU,IAAL,CAAUzB,IAAV,CAAee,OAAf,GAAyB,GAAzB;AACH;;AAED,cAAMuB,SAAS,GAAG;AAAA;AAAA,wCAAWX,eAA7B,CAZgB,CAahB;;AAbgB,uCAewC;AACpD,gBAAMsC,IAAI,GAAG,MAAI,CAAC1E,iBAAL,CAAuBc,CAAvB,CAAb;;AACA,YAAA,MAAI,CAAC6D,YAAL,CAAkB,MAAM;AACpBD,cAAAA,IAAI,CAACjE,IAAL,CAAUQ,MAAV,GAAmB,IAAnB;AACAyD,cAAAA,IAAI,CAACE,IAAL,CAAU,MAAV;AACH,aAHD,EAGG7B,SAAS,GAAG,MAAI,CAAC9C,kBAAL,CAAwBa,CAAxB,CAHf;AAIH,WArBe;;AAehB,eAAK,IAAIA,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAG,KAAKd,iBAAL,CAAuBgD,MAA3C,EAAmDlC,CAAC,EAApD;AAAA;AAAA;AAOH;AAED;AACJ;AACA;AACA;;;AACI+D,QAAAA,QAAQ,CAAClB,QAAD,EAAW,CACf;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACH;AAED;AACJ;AACA;;;AACImB,QAAAA,cAAc,GAAG,CACb;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AAEA;AACA;AACH;AAED;AACJ;AACA;AACA;;;AACIC,QAAAA,kBAAkB,CAACpB,QAAD,EAAkB,CAChC;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAtBgC,cAAjBA,QAAiB;AAAjBA,YAAAA,QAAiB,GAAN,IAAM;AAAA;AAuBnC;AAED;AACJ;AACA;AACA;;;AACIqB,QAAAA,kBAAkB,CAACrB,QAAD,EAAkB,CAChC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AA1BgC,cAAjBA,QAAiB;AAAjBA,YAAAA,QAAiB,GAAN,IAAM;AAAA;AA2BnC;AAED;AACJ;AACA;;;AACIsB,QAAAA,eAAe,GAAG;AACd,cAAI,CAAC,KAAKtF,SAAV,EAAqB;;AAErB,cAAI,CAAC,KAAKD,SAAV,EAAqB;AACjB,iBAAKA,SAAL,GAAiB,IAAIzB,IAAJ,EAAjB;;AACA,iBAAKyB,SAAL,CAAeiB,YAAf,CAA4BxC,WAA5B;;AACA,iBAAK+D,IAAL,CAAUzB,IAAV,CAAeG,QAAf,CAAwB,KAAKlB,SAA7B,EAAwC,CAAC,CAAzC;;AACA,iBAAKA,SAAL,CAAeiB,YAAf,CAA4B5C,MAA5B;;AACA,iBAAK2B,SAAL,CAAewF,CAAf,GAAmB,CAAC,EAApB;AACA,iBAAKxF,SAAL,CAAeuD,OAAf,GAAyB,CAAzB;AACH;;AAED;AAAA;AAAA,kCAAQT,YAAR,CAAqBM,aAArB,CAAmC,KAAKpD,SAAL,CAAekD,YAAf,CAA4B7E,MAA5B,CAAnC,EAAwE,UAAxE;AACA,eAAK2B,SAAL,CAAeuB,MAAf,GAAwB,IAAxB;AACA,eAAKvB,SAAL,CAAe8B,OAAf,GAAyB,GAAzB;AACA,eAAK9B,SAAL,CAAeyF,MAAf,GAAwB,CAAxB;AACA,eAAKzF,SAAL,CAAe0F,MAAf,GAAwB,IAAxB;AACA,eAAK1F,SAAL,CAAe2F,KAAf,GAAuB,EAAvB;;AACA,eAAK3F,SAAL,CAAe4F,cAAf;;AAEApH,UAAAA,KAAK,CAAC,KAAKwB,SAAN,CAAL,CACKyC,EADL,CACQ,GADR,EACa;AAAEgD,YAAAA,MAAM,EAAE;AAAV,WADb,EAEKI,KAFL,CAEW,GAFX,EAGKpD,EAHL,CAGQ,GAHR,EAGa;AAAEX,YAAAA,OAAO,EAAE;AAAX,WAHb,EAIKgE,IAJL,CAIU,MAAM;AACR,iBAAK9F,SAAL,CAAeuB,MAAf,GAAwB,KAAxB;AACH,WANL,EAOKuC,KAPL;AAQH;;AAvgBiD,O;;;;;iBAEvC,I;;;;;;;iBAGJ,I;;;;;;;iBAGC,I", "sourcesContent": ["import { _decorator, Component, Sprite, Vec2, Node, tween, v2, UITransform, Tween, v3, Prefab, instantiate} from \"cc\";\r\nimport GameConfig from \"../../../const/GameConfig\";\r\nimport { GameIns } from \"../../../GameIns\";\r\nimport { Tools } from \"../../../utils/Tools\";\r\nimport EnemyAnim from \"./EnemyAnim\";\r\n\r\nconst { ccclass, property } = _decorator;\r\n\r\n@ccclass\r\nexport default class EnemyPlaneRole extends Component {\r\n    @property(Sprite)\r\n    pedestal = null;\r\n\r\n    @property(Sprite)\r\n    role = null;\r\n\r\n    @property(Sprite)\r\n    white = null;\r\n\r\n    _data = null;\r\n    _param = \"\";\r\n    _target = null;\r\n    _curUId = -1;\r\n    _anim = null;\r\n    _curAnim = \"\";\r\n    _animWhiteArr = [];\r\n    _animWhitePool = [];\r\n    _animWhiteOffset = Vec2.ZERO;\r\n    _tailFireArr:Node[] = [];\r\n    _rotateComp = null;\r\n    _warnLine = null;\r\n    _warnAble = false;\r\n    _winkCount = 0;\r\n    _bWinkWhite = false;\r\n    _winkAct = null;\r\n    _initOver = false;\r\n    _sneakGoUpAnimArr = [];\r\n    _sneakGoUpDelayArr = [];\r\n    _sneakAnim = null;\r\n    _cloakeAnim = null;\r\n\r\n\r\n    /**\r\n     * 预加载 UI\r\n     * @param {Object} data 敌机数据\r\n     */\r\n    preLoadUI(data) {\r\n        if (this._data = data) {\r\n            this._initSneak();\r\n            this._initUI(true);\r\n        } else {\r\n            let fireNode = this.node.getChildByName(\"fire\");\r\n            if (!fireNode) {\r\n                fireNode = new Node();\r\n                fireNode.addComponent(UITransform);\r\n                this.node.addChild(fireNode);\r\n                fireNode.name = \"fire\";\r\n            }\r\n            for (let i = 0; i < 2; i++) {\r\n                const tailFire = new Node();\r\n                tailFire.addComponent(UITransform);\r\n                fireNode.addChild(tailFire);\r\n                tailFire.addComponent(Sprite);\r\n                // tailFire.anchorY = 0;\r\n                this._tailFireArr.push(tailFire);\r\n                tailFire.active = false;\r\n            }\r\n        }\r\n    }\r\n\r\n    /**\r\n     * 初始化敌机\r\n     * @param {Object} data 敌机数据\r\n     * @param {Object} target 目标对象\r\n     * @param {string} param 参数\r\n     */\r\n    async init(data, target, param = \"\") {\r\n        this._reset();\r\n        this._data = data;\r\n        this._param = param;\r\n        this._target = target;\r\n\r\n        if (!this._data.isAm) {\r\n            this._initSneak();\r\n            await this._initUI();\r\n        } else {\r\n            if (this._anim) {\r\n                this._initSneak();\r\n                this.playAnim(\"idle1\");\r\n                this._anim.node.opacity = 255;\r\n            } else {\r\n                this._initOver = true;\r\n            }\r\n        }\r\n    }\r\n\r\n    /**\r\n     * 重置敌机状态\r\n     */\r\n    _reset() {\r\n        this.white.node.opacity = 0;\r\n        this.pedestal.spriteFrame = null;\r\n        this._curAnim = \"\";\r\n        this._initOver = false;\r\n        this.stopAnim();\r\n        if (this._anim) this._anim.node.opacity = 0;\r\n        if (this._sneakAnim) this._sneakAnim.node.active = false;\r\n        if (this._rotateComp) this._rotateComp.reset();\r\n        this._warnAble = false;\r\n        if (this._warnLine) this._warnLine.active = false;\r\n    }\r\n\r\n    /**\r\n     * 更新游戏逻辑\r\n     * @param {number} dt 时间增量\r\n     */\r\n    updateGameLogic(dt) {\r\n        if (this._rotateComp && this._rotateComp.enabled) {\r\n            this._rotateComp.updateGameLogic(dt);\r\n        }\r\n        if (this._bWinkWhite) {\r\n            this._winkCount++;\r\n            if (this._winkCount > 8) {\r\n                this._winkCount = 0;\r\n                this._bWinkWhite = false;\r\n            }\r\n        }\r\n    }\r\n\r\n    /**\r\n     * 初始化 UI\r\n     * @param {boolean} isPreload 是否预加载\r\n     */\r\n    async _initUI(isPreload = false) {\r\n        if (this._data.isAm) {\r\n            this.role.spriteFrame = null;\r\n            this.white.spriteFrame = null;\r\n            this._winkAct = tween().to(0, { opacity: 204 }).to(3 * GameConfig.ActionFrameTime, { opacity: 0 });\r\n\r\n            if (this._curUId === this._data.id && this._anim) {\r\n                this._anim.node.opacity = 255;\r\n            } else {\r\n                this._curUId = this._data.id;\r\n                if (this._anim) {\r\n                    this._anim.node.destroy();\r\n                    this._anim = null;\r\n                }\r\n                let pf = await GameIns.enemyManager.getPlaneRole(this._data.image) as Prefab;\r\n                const animNode = instantiate(pf);\r\n                this.node.addChild(animNode);\r\n                this._anim = animNode.getComponent(EnemyAnim);\r\n                this._anim.init(this._data.extraParam);\r\n            }\r\n        } else {\r\n            if (this._anim) this._anim.node.active = false;\r\n            this.role.node.opacity = 255;\r\n            GameIns.enemyManager.setPlaneFrame(this.role, this._data.image);\r\n            GameIns.enemyManager.setPlaneFrame(this.white, this._data.image);\r\n            this._winkAct = tween().to(0, { opacity: 255 }).to(3 * GameConfig.ActionFrameTime, { opacity: 0 });\r\n\r\n            // if (this._data.id >= 50000 && this._data.id < 60000) {\r\n            //     GameIns.enemyManager.setPlaneFrame(this.pedestal, this._data.extraParam1);\r\n            //     if (!this._rotateComp) {\r\n            //         this._rotateComp = Tools.addScript(this.node, TurretRotateComp);\r\n            //         this._rotateComp.init(this.role.node);\r\n            //     }\r\n            //     this._rotateComp.setData(this._param);\r\n            //     this._rotateComp.enabled = true;\r\n            //     this._warnAble = true;\r\n            // } else \r\n            if (this._rotateComp) {\r\n                this._rotateComp.enabled = false;\r\n            }\r\n\r\n            let fireNode = this.node.getChildByName(\"fire\");\r\n            if (!fireNode) {\r\n                fireNode = new Node();\r\n                this.node.addChild(fireNode);\r\n                fireNode.name = \"fire\";\r\n            }\r\n\r\n            const frameTime = GameConfig.ActionFrameTime;\r\n            for (let i = 0; i < this._data.extraParam.length; i++) {\r\n                const param = this._data.extraParam[i];\r\n                let tailFire:Node = this._tailFireArr[i];\r\n                if (!tailFire) {\r\n                    tailFire = new Node();\r\n                    fireNode.addChild(tailFire);\r\n                    tailFire.addComponent(Sprite);\r\n                    tailFire.getComponent(UITransform).anchorY = 0;\r\n                    this._tailFireArr.push(tailFire);\r\n                }\r\n                Tween.stopAllByTarget(tailFire);\r\n                tailFire.active = true;\r\n                GameIns.enemyManager.setPlaneFrame(tailFire.getComponent(Sprite), \"fire\" + param[0]);\r\n                tailFire.setPosition(param[1], param[2]);\r\n                tailFire.getComponent(UITransform).setContentSize(param[3], param[4]);\r\n                tailFire.angle = param[7] || 0;\r\n\r\n\r\n            \r\n                tween(tailFire)\r\n                    .to(frameTime * param[5], { scale: v3(param[6],param[6]) })\r\n                    .to(frameTime * param[5], { scale: v3(1,1) })\r\n                    .repeatForever()\r\n                    .start();\r\n            }\r\n\r\n            for (let i = this._data.extraParam.length; i < this._tailFireArr.length; i++) {\r\n                const tailFire = this._tailFireArr[i];\r\n                Tween.stopAllByTarget(tailFire);\r\n                tailFire.active = false;\r\n            }\r\n        }\r\n    }\r\n\r\n    /**\r\n     * 设置动画事件回调\r\n     * @param {string} eventName 事件名称\r\n     * @param {Function} callback 回调函数\r\n     */\r\n    setEventCallback(eventName, callback) {\r\n        this._anim && this._anim.setAnimEventCall(eventName, callback);\r\n    }\r\n\r\n    /**\r\n     * 开始攻击\r\n     */\r\n    startAttack() {\r\n        this._rotateComp && this._rotateComp.setAngleAble(true);\r\n    }\r\n\r\n    /**\r\n     * 攻击结束\r\n     */\r\n    attackOver() {\r\n        this._rotateComp && this._rotateComp.setAngleAble(false);\r\n    }\r\n\r\n    /**\r\n     * 闪烁白色效果\r\n     */\r\n    winkWhite() {\r\n        if (!this._bWinkWhite) {\r\n            this._bWinkWhite = true;\r\n            if (!this._data.isAm) {\r\n                this._winkAct.clone(this.white.node).start();\r\n            }\r\n        }\r\n    }\r\n\r\n    /**\r\n     * 播放动画\r\n     * @param {string} animName 动画名称\r\n     * @param {Function} [callback] 动画结束回调\r\n     * @returns {boolean} 是否成功播放\r\n     */\r\n    playAnim(animName, callback = null) {\r\n        if (!this._data.isAm) return false;\r\n        if (this._curAnim === animName) {\r\n            Tools.log(\"save anim\", animName);\r\n            return true;\r\n        }\r\n        if (this._anim) {\r\n            this._anim.playAnim(animName, callback);\r\n            return true;\r\n        }\r\n        Tools.log(\"anim is null\", animName);\r\n        return false;\r\n    }\r\n\r\n    /**\r\n     * 暂停动画\r\n     */\r\n    pauseAnim() {\r\n        this._anim && this._anim.pauseAnim();\r\n    }\r\n\r\n    /**\r\n     * 恢复动画\r\n     */\r\n    resumeAnim() {\r\n        this._anim && this._anim.resumeAnim();\r\n    }\r\n\r\n    /**\r\n     * 停止动画\r\n     */\r\n    stopAnim() {\r\n        this._anim && this._anim.stopAnim();\r\n    }\r\n\r\n    /**\r\n     * 初始化潜行\r\n     */\r\n    _initSneak() {\r\n        // 潜行初始化逻辑\r\n    }\r\n\r\n    /**\r\n     * 初始化潜行动画\r\n     */\r\n    async _initSneakAnim() {\r\n        // try {\r\n        //     if (this._sneakGoUpAnimArr.length > 0) return;\r\n\r\n        //     for (let i = 0; i < this._data.sneakParam.length; i++) {\r\n        //         const param = this._data.sneakParam[i];\r\n        //         const animNode = await GameIns.enemyManager.getPlaneRole(\"water\" + param[0]);\r\n        //         if (!animNode) {\r\n        //             console.error(\"_initSneakAnim\", param);\r\n        //             continue;\r\n        //         }\r\n        //         this.node.addChild(animNode);\r\n        //         const anim = animNode.getComponent(Animation);\r\n        //         this._sneakGoUpAnimArr.push(anim);\r\n\r\n        //         animNode.position = v2(param[1], param[2]);\r\n        //         animNode.zIndex = param[3];\r\n        //         animNode.scaleX = param[4];\r\n        //         animNode.scaleY = param[5];\r\n        //         animNode.angle = param[6];\r\n        //         this._sneakGoUpDelayArr.push(param[7]);\r\n        //         animNode.active = false;\r\n        //     }\r\n        // } catch (error) {\r\n        //     Tools.log(\"_initSneakAnim error\", error);\r\n        // }\r\n    }\r\n\r\n    /**\r\n     * 设置潜行方向\r\n     * @param {number} angle 方向角度\r\n     */\r\n    setSneakDir(angle) {\r\n        if (this._sneakAnim && this._sneakAnim.node.active) {\r\n            this._sneakAnim.node.angle = angle;\r\n        }\r\n    }\r\n\r\n    /**\r\n     * 播放潜行动画\r\n     */\r\n    playSneakAnim() {\r\n        if (this._sneakAnim) {\r\n            this._sneakAnim.node.active = true;\r\n            this.role.node.opacity = 0;\r\n            if (this._anim) {\r\n                this._anim.node.opacity = 0;\r\n            }\r\n            this._sneakAnim.reset();\r\n        }\r\n    }\r\n\r\n    /**\r\n     * 播放潜行上浮动画\r\n     */\r\n    playSneakGoUpAnim() {\r\n        if (this._sneakAnim) {\r\n            this._sneakAnim.node.active = false;\r\n        }\r\n        if (this._data.isAm) {\r\n            if (this._anim) {\r\n                this._anim.node.opacity = 255;\r\n            }\r\n        } else {\r\n            this.role.node.opacity = 255;\r\n        }\r\n\r\n        const frameTime = GameConfig.ActionFrameTime;\r\n        // frameWork.audioManager.playEffect(\"sneakUp\");\r\n\r\n        for (let i = 0; i < this._sneakGoUpAnimArr.length; i++) {\r\n            const anim = this._sneakGoUpAnimArr[i];\r\n            this.scheduleOnce(() => {\r\n                anim.node.active = true;\r\n                anim.play(\"play\");\r\n            }, frameTime * this._sneakGoUpDelayArr[i]);\r\n        }\r\n    }\r\n\r\n    /**\r\n     * 播放蓝色特效\r\n     * @param {Function} callback 回调函数\r\n     */\r\n    blueShow(callback) {\r\n        // const frameTime = GameConfig.ActionFrameTime;\r\n        // const blueColor = color(19, 233, 139);\r\n        // const tween = tween()\r\n        //     .to(10 * frameTime, { color: blueColor, opacity: 204 })\r\n        //     .to(3 * frameTime, { color: Color.WHITE, opacity: 255 });\r\n\r\n        // const endTween = tween().call(() => {\r\n        //     if (callback) callback();\r\n        // });\r\n\r\n        // if (this._data.isAm && this._anim) {\r\n        //     for (let i = 0; i < this._anim.node.childrenCount; i++) {\r\n        //         const child = this._anim.node.children[i];\r\n        //         child.opacity = 0;\r\n        //         child.color = blueColor;\r\n        //         if (i === this._anim.node.childrenCount - 1) {\r\n        //             tween.clone(child).then(endTween).start();\r\n        //         } else {\r\n        //             tween.clone(child).start();\r\n        //         }\r\n        //     }\r\n        // } else {\r\n        //     this.role.node.opacity = 0;\r\n        //     this.role.node.color = blueColor;\r\n        //     tween.clone(this.role.node).then(endTween).start();\r\n        // }\r\n    }\r\n\r\n    /**\r\n     * 播放隐身动画\r\n     */\r\n    playCloakeAnim() {\r\n        // if (!this._cloakeAnim) {\r\n        //     const animNode = instantiate(GameConst.frameAnim);\r\n        //     this.node.addChild(animNode, 11);\r\n        //     this._cloakeAnim = animNode.getComponent(PfFrameAnim);\r\n        //     this._cloakeAnim.init(GameIns.enemyManager.enemyAtlas, \"a_\", 12, GameConfig.ActionFrameTime);\r\n        //     animNode.active = false;\r\n        // }\r\n\r\n        // if (this._data.sneakParam.length > 0 && this._data.sneakParam[0].length > 1) {\r\n        //     this._cloakeAnim.node.scaleX = this._data.sneakParam[0][0];\r\n        //     this._cloakeAnim.node.scaleY = this._data.sneakParam[0][1];\r\n        // }\r\n\r\n        // this._cloakeAnim.node.active = true;\r\n        // this._cloakeAnim.reset(1);\r\n    }\r\n\r\n    /**\r\n     * 播放隐身消失动画\r\n     * @param {Function} callback 回调函数\r\n     */\r\n    playCloakeHideAnim(callback = null) {\r\n        // frameWork.audioManager.playEffect(\"cloake\");\r\n        // this.playCloakeAnim();\r\n\r\n        // const frameTime = GameConfig.ActionFrameTime;\r\n        // tween(this.node)\r\n        //     .to(5 * frameTime, { opacity: 90 })\r\n        //     .to(2 * frameTime, { opacity: 0 })\r\n        //     .call(() => {\r\n        //         if (this._anim) {\r\n        //             this._anim.node.opacity = 0;\r\n        //         }\r\n        //         GameIns.enemyManager.setPlaneFrame(this.role, this._data.sneakAnim);\r\n        //         const fireNode = this.node.getChildByName(\"fire\");\r\n        //         if (fireNode) {\r\n        //             fireNode.opacity = 0;\r\n        //         }\r\n        //     })\r\n        //     .to(6 * frameTime, { opacity: 255 })\r\n        //     .call(() => {\r\n        //         if (callback) callback();\r\n        //     })\r\n        //     .start();\r\n    }\r\n\r\n    /**\r\n     * 播放隐身显现动画\r\n     * @param {Function} callback 回调函数\r\n     */\r\n    playCloakeShowAnim(callback = null) {\r\n        // const frameTime = GameConfig.ActionFrameTime;\r\n        // tween(this.node)\r\n        //     .to(4 * frameTime, { opacity: 102 })\r\n        //     .to(2 * frameTime, { opacity: 255 })\r\n        //     .to(4 * frameTime, { opacity: 102 })\r\n        //     .to(2 * frameTime, { opacity: 255 })\r\n        //     .to(3 * frameTime, { opacity: 102 })\r\n        //     .to(frameTime, { opacity: 0 })\r\n        //     .call(() => {\r\n        //         if (this._anim) {\r\n        //             this._anim.node.opacity = 255;\r\n        //             this.role.spriteFrame = null;\r\n        //         } else {\r\n        //             GameIns.enemyManager.setPlaneFrame(this.role, this._data.image);\r\n        //             const fireNode = this.node.getChildByName(\"fire\");\r\n        //             if (fireNode) {\r\n        //                 fireNode.opacity = 255;\r\n        //             }\r\n        //         }\r\n        //         this.playCloakeAnim();\r\n        //     })\r\n        //     .to(7 * frameTime, { opacity: 255 })\r\n        //     .call(() => {\r\n        //         if (callback) callback();\r\n        //     })\r\n        //     .start();\r\n    }\r\n\r\n    /**\r\n     * 播放攻击警告动画\r\n     */\r\n    playAtkWarnAnim() {\r\n        if (!this._warnAble) return;\r\n\r\n        if (!this._warnLine) {\r\n            this._warnLine = new Node();\r\n            this._warnLine.addComponent(UITransform);\r\n            this.role.node.addChild(this._warnLine, -1);\r\n            this._warnLine.addComponent(Sprite);\r\n            this._warnLine.y = -72;\r\n            this._warnLine.anchorY = 1;\r\n        }\r\n\r\n        GameIns.enemyManager.setPlaneFrame(this._warnLine.getComponent(Sprite), \"warnLine\");\r\n        this._warnLine.active = true;\r\n        this._warnLine.opacity = 255;\r\n        this._warnLine.scaleY = 0;\r\n        this._warnLine.height = 1800;\r\n        this._warnLine.width = 10;\r\n        this._warnLine.stopAllActions();\r\n\r\n        tween(this._warnLine)\r\n            .to(0.2, { scaleY: 1 })\r\n            .delay(0.5)\r\n            .to(0.2, { opacity: 0 })\r\n            .call(() => {\r\n                this._warnLine.active = false;\r\n            })\r\n            .start();\r\n    }\r\n}"]}