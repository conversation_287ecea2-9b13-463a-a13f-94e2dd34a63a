System.register(["__unresolved_0", "cc", "__unresolved_1"], function (_export, _context) {
  "use strict";

  var _reporterNs, _cclegacy, __checkObsolete__, __checkObsoleteInNamespace__, _decorator, Entity, _dec, _class, _crd, ccclass, property, EnemyEntity;

  function _reportPossibleCrUseOfEntity(extras) {
    _reporterNs.report("Entity", "../../base/Entity", _context.meta, extras);
  }

  return {
    setters: [function (_unresolved_) {
      _reporterNs = _unresolved_;
    }, function (_cc) {
      _cclegacy = _cc.cclegacy;
      __checkObsolete__ = _cc.__checkObsolete__;
      __checkObsoleteInNamespace__ = _cc.__checkObsoleteInNamespace__;
      _decorator = _cc._decorator;
    }, function (_unresolved_2) {
      Entity = _unresolved_2.default;
    }],
    execute: function () {
      _crd = true;

      _cclegacy._RF.push({}, "8d18fAA6hBK1LOC2BYMcdEA", "EnemyEntity", undefined);

      __checkObsolete__(['_decorator', 'instantiate']);

      ({
        ccclass,
        property
      } = _decorator);

      _export("default", EnemyEntity = (_dec = ccclass('EnemyEntity'), _dec(_class = class EnemyEntity extends (_crd && Entity === void 0 ? (_reportPossibleCrUseOfEntity({
        error: Error()
      }), Entity) : Entity) {
        constructor() {
          super(...arguments);
          this._type = 0;
          // 敌人类型
          this._isDead = false;
          // 是否死亡
          this._active = false;
          // 是否激活
          this._removeAble = false;
          // 是否可移除
          this._attack = 0;
          // 攻击力
          this._collideAtk = 0;
          // 碰撞攻击力
          this._sceneLayer = -1;
        }

        // 场景层

        /**
         * 重置敌人状态
         */
        reset() {
          this._isDead = false;
          this._active = false;
          this._removeAble = false;
          this._sceneLayer = -1;
        }
        /**
         * 获取敌人类型
         * @returns {number} 敌人类型
         */


        get type() {
          return this._type;
        }
        /**
         * 设置敌人类型
         * @param {number} value 敌人类型
         */


        set type(value) {
          this._type = value;
        }
        /**
         * 获取攻击力
         * @returns {number} 攻击力
         */


        get attack() {
          return this._attack;
        }
        /**
         * 设置攻击力
         * @param {number} value 攻击力
         */


        set attack(value) {
          this._attack = value;
        }
        /**
         * 设置碰撞攻击力
         * @param {number} value 碰撞攻击力
         */


        setCollideAtk(value) {
          this._collideAtk = value;
        }
        /**
         * 获取碰撞攻击力
         * @returns {number} 碰撞攻击力
         */


        getColliderAtk() {
          return this._collideAtk;
        }
        /**
         * 获取碰撞攻击力
         * @returns {number} 碰撞攻击力
         */


        get collideAtk() {
          return this._collideAtk;
        }
        /**
         * 设置碰撞攻击力
         * @param {number} value 碰撞攻击力
         */


        set collideAtk(value) {
          this._collideAtk = value;
        }
        /**
         * 获取是否死亡
         * @returns {boolean} 是否死亡
         */


        get isDead() {
          return this._isDead;
        }
        /**
         * 设置是否死亡
         * @param {boolean} value 是否死亡
         */


        set isDead(value) {
          this._isDead = value;
        }
        /**
         * 获取是否可移除
         * @returns {boolean} 是否可移除
         */


        get removeAble() {
          return this._removeAble;
        }
        /**
         * 设置是否可移除
         * @param {boolean} value 是否可移除
         */


        set removeAble(value) {
          this._removeAble = value;
        }
        /**
         * 获取是否激活
         * @returns {boolean} 是否激活
         */


        get active() {
          return this._active;
        }
        /**
         * 设置是否激活
         * @param {boolean} value 是否激活
         */


        set active(value) {
          this._active = value;
        }
        /**
         * 获取场景层
         * @returns {number} 场景层
         */


        get sceneLayer() {
          return this._sceneLayer;
        }
        /**
         * 设置场景层
         * @param {number} value 场景层
         */


        set sceneLayer(value) {
          this._sceneLayer = value;
        }
        /**
         * 处理敌人受到的伤害
         * @param {number} damage 伤害值
         */


        hurt(damage) {}
        /**
         * 敌人即将销毁时的回调
         */


        willDestroy() {}
        /**
         * 初始化剑气特效
         * @returns {Node} 剑气节点
         */


        initSword() {// let swordNode = this.node.getChildByName('sword');
          // if (!swordNode) {
          //     swordNode = instantiate(GameIns.loadManager.getRes('swordBlast', Prefab));
          //     swordNode.name = 'sword';
          //     this.node.addChild(swordNode, 1);
          //     swordNode.getComponent(SwordBlast).init();
          // }
          // return swordNode;
        }
        /**
         * 播放剑气的待机动画
         * @param {Node} swordNode 剑气节点
         * @param {boolean} randomize 是否随机化
         */


        playSwordIdleAnim(swordNode, randomize) {// swordNode = swordNode || this.node.getChildByName('sword') || this.initSword();
          // swordNode.angle = Tools.random_int(0, 359);
          // swordNode.scale = Tools.random_int(8, 11) / 10;
          // const swordComp = swordNode.getComponent(SwordBlast);
          // if (swordComp) {
          //     swordComp.playIdleAnim();
          // }

          if (randomize === void 0) {
            randomize = true;
          }
        }
        /**
         * 播放剑气的受伤动画
         * @param {number} delay 延迟时间
         */


        playSwordHurtAnim(delay) {// let swordNode = this.node.getChildByName('sword');
          // if (!swordNode) {
          //     swordNode = this.initSword();
          //     swordNode.angle = Tools.random_int(0, 359);
          //     swordNode.scale = Tools.random_int(8, 11) / 10;
          // }
          // const swordComp = swordNode.getComponent(SwordBlast);
          // this.scheduleOnce(() => {
          //     if (swordComp) {
          //         swordComp.playHurtAnim();
          //     }
          // }, delay);

          if (delay === void 0) {
            delay = 0;
          }
        }
        /**
         * 处理剑气伤害
         * @param {number} damage 伤害值
         */


        swordHurt(damage) {
          this.hurt(damage);
        }
        /**
         * 剑气结束时的回调
         */


        swordOver() {}

      }) || _class));

      _cclegacy._RF.pop();

      _crd = false;
    }
  };
});
//# sourceMappingURL=036ff455455a44c05d3317f77847ba34a2c3dba6.js.map