System.register(["__unresolved_0", "cc", "__unresolved_1", "__unresolved_2", "__unresolved_3", "__unresolved_4"], function (_export, _context) {
  "use strict";

  var _reporterNs, _cclegacy, __checkObsolete__, __checkObsoleteInNamespace__, _decorator, Component, Sprite, find, Collider2D, Contact2DType, ProgressBar, director, Global, GamePersistNode, Player, audioManager, _dec, _class, _crd, ccclass, property, EnemyBullet;

  function _reportPossibleCrUseOfGameFactory(extras) {
    _reporterNs.report("GameFactory", "./factroy/GameFactory", _context.meta, extras);
  }

  function _reportPossibleCrUseOfGlobal(extras) {
    _reporterNs.report("Global", "./Global", _context.meta, extras);
  }

  function _reportPossibleCrUseOfGamePersistNode(extras) {
    _reporterNs.report("GamePersistNode", "./GamePersistNode", _context.meta, extras);
  }

  function _reportPossibleCrUseOfPlayer(extras) {
    _reporterNs.report("Player", "./Player", _context.meta, extras);
  }

  function _reportPossibleCrUseOfaudioManager(extras) {
    _reporterNs.report("audioManager", "../ResUpdate/audioManager", _context.meta, extras);
  }

  return {
    setters: [function (_unresolved_) {
      _reporterNs = _unresolved_;
    }, function (_cc) {
      _cclegacy = _cc.cclegacy;
      __checkObsolete__ = _cc.__checkObsolete__;
      __checkObsoleteInNamespace__ = _cc.__checkObsoleteInNamespace__;
      _decorator = _cc._decorator;
      Component = _cc.Component;
      Sprite = _cc.Sprite;
      find = _cc.find;
      Collider2D = _cc.Collider2D;
      Contact2DType = _cc.Contact2DType;
      ProgressBar = _cc.ProgressBar;
      director = _cc.director;
    }, function (_unresolved_2) {
      Global = _unresolved_2.Global;
    }, function (_unresolved_3) {
      GamePersistNode = _unresolved_3.GamePersistNode;
    }, function (_unresolved_4) {
      Player = _unresolved_4.Player;
    }, function (_unresolved_5) {
      audioManager = _unresolved_5.audioManager;
    }],
    execute: function () {
      _crd = true;

      _cclegacy._RF.push({}, "c8821ERIhFJProTL+dq4/oc", "EnemyBullet", undefined);

      __checkObsolete__(['_decorator', 'Component', 'Node', 'SpriteFrame', 'Sprite', 'Vec3', 'find', 'Collider2D', 'Contact2DType', 'IPhysics2DContact', 'log', 'ProgressBar', 'director']);

      ({
        ccclass,
        property
      } = _decorator);

      _export("EnemyBullet", EnemyBullet = (_dec = ccclass('EnemyBullet'), _dec(_class = class EnemyBullet extends Component {
        constructor() {
          super(...arguments);
          this.enemyBulletType = null;
          this.curPos = null;
          this.enemyBullet1MoveSpeed = 0;
          //敌机子弹1的移动速度
          this.enemyBullet2MoveSpeed = 0;
          //敌机子弹2的移动速度
          this.enemyBullet1ReduceBlood = 0;
          //当被敌机子弹一打中后掉多少血
          this.enemyBullet2ReduceBlood = 0;
          //当被敌机子弹二打中后掉多少血
          this.enemyBulletFactory = null;
          this.persistNode = null;
          this.isLeft = true;
        }

        onLoad() {
          this.persistNode = find("GamePersistNode");
          this.enemyBulletFactory = this.persistNode.getComponent(_crd && GamePersistNode === void 0 ? (_reportPossibleCrUseOfGamePersistNode({
            error: Error()
          }), GamePersistNode) : GamePersistNode).enemyBulletFactory;
          this.enemyBullet1MoveSpeed = this.persistNode.getComponent(_crd && GamePersistNode === void 0 ? (_reportPossibleCrUseOfGamePersistNode({
            error: Error()
          }), GamePersistNode) : GamePersistNode).enemyBullet1MoveSpeed;
          this.enemyBullet2MoveSpeed = this.persistNode.getComponent(_crd && GamePersistNode === void 0 ? (_reportPossibleCrUseOfGamePersistNode({
            error: Error()
          }), GamePersistNode) : GamePersistNode).enemyBullet2MoveSpeed;
          this.enemyBullet1ReduceBlood = this.persistNode.getComponent(_crd && GamePersistNode === void 0 ? (_reportPossibleCrUseOfGamePersistNode({
            error: Error()
          }), GamePersistNode) : GamePersistNode).enemyBullet1ReduceBlood;
          this.enemyBullet2ReduceBlood = this.persistNode.getComponent(_crd && GamePersistNode === void 0 ? (_reportPossibleCrUseOfGamePersistNode({
            error: Error()
          }), GamePersistNode) : GamePersistNode).enemyBullet2ReduceBlood;
          var collider = this.node.getComponent(Collider2D);

          if (collider) {
            collider.on(Contact2DType.BEGIN_CONTACT, this.onBeginContact, this);
          }
        }

        onBeginContact(selfCollider, otherCollider, contact) {
          //判断被哪种敌机子弹击中
          if (this.enemyBulletType == (_crd && Global === void 0 ? (_reportPossibleCrUseOfGlobal({
            error: Error()
          }), Global) : Global).ENEMY_BULLET_1) {
            otherCollider.getComponent(_crd && Player === void 0 ? (_reportPossibleCrUseOfPlayer({
              error: Error()
            }), Player) : Player).planeBlood -= this.enemyBullet1ReduceBlood;
          } else {
            otherCollider.getComponent(_crd && Player === void 0 ? (_reportPossibleCrUseOfPlayer({
              error: Error()
            }), Player) : Player).planeBlood -= this.enemyBullet2ReduceBlood;
          } //更新血条


          otherCollider.node.getChildByName("Blood").getComponent(ProgressBar).progress = otherCollider.getComponent(_crd && Player === void 0 ? (_reportPossibleCrUseOfPlayer({
            error: Error()
          }), Player) : Player).planeBlood / otherCollider.getComponent(_crd && Player === void 0 ? (_reportPossibleCrUseOfPlayer({
            error: Error()
          }), Player) : Player).planeTotalBlood;
          this.enemyBulletFactory.recycleProduct(this.node);

          if (otherCollider.getComponent(_crd && Player === void 0 ? (_reportPossibleCrUseOfPlayer({
            error: Error()
          }), Player) : Player).planeBlood <= 0) {
            director.loadScene("Main");
            (_crd && audioManager === void 0 ? (_reportPossibleCrUseOfaudioManager({
              error: Error()
            }), audioManager) : audioManager).instance.playSound(this.persistNode.getComponent(_crd && GamePersistNode === void 0 ? (_reportPossibleCrUseOfGamePersistNode({
              error: Error()
            }), GamePersistNode) : GamePersistNode).gameOverAudioClip);
          }
        }
        /**
         * 初始化enemyBullet
         */


        init(enemyBulletType, spriteFrame) {
          this.enemyBulletType = enemyBulletType;
          this.node.getComponent(Sprite).spriteFrame = spriteFrame;
        }

        update(deltaTime) {
          if (this.enemyBulletType == (_crd && Global === void 0 ? (_reportPossibleCrUseOfGlobal({
            error: Error()
          }), Global) : Global).ENEMY_BULLET_1) {
            this.enemyBullet1Move(deltaTime);
          } else if (this.enemyBulletType == (_crd && Global === void 0 ? (_reportPossibleCrUseOfGlobal({
            error: Error()
          }), Global) : Global).ENEMY_BULLET_2) {
            this.enemyBullet2Move(deltaTime);
          }
        }
        /**
         * 敌机子弹1移动
         */


        enemyBullet1Move(deltaTime) {
          this.curPos = this.node.getPosition();
          this.curPos.y -= this.enemyBullet1MoveSpeed * deltaTime; //子弹一水平方向上移动

          if (this.isLeft) {
            this.curPos.x -= this.enemyBullet1MoveSpeed * deltaTime;

            if (this.curPos.x < -(_crd && Global === void 0 ? (_reportPossibleCrUseOfGlobal({
              error: Error()
            }), Global) : Global).WIDTH / 2) {
              this.isLeft = false;
            }
          } else {
            this.curPos.x += this.enemyBullet1MoveSpeed * deltaTime;

            if (this.curPos.x > (_crd && Global === void 0 ? (_reportPossibleCrUseOfGlobal({
              error: Error()
            }), Global) : Global).WIDTH / 2) {
              this.isLeft = true;
            }
          }

          this.node.setPosition(this.curPos);

          if (this.curPos.y < -(_crd && Global === void 0 ? (_reportPossibleCrUseOfGlobal({
            error: Error()
          }), Global) : Global).HEIGHT / 2) {
            this.enemyBulletFactory.recycleProduct(this.node);
          }
        }
        /**
         * 敌机子弹2移动
         */


        enemyBullet2Move(deltaTime) {
          this.curPos = this.node.getPosition();
          this.curPos.y -= this.enemyBullet2MoveSpeed * deltaTime;
          this.node.setPosition(this.curPos);

          if (this.curPos.y < -(_crd && Global === void 0 ? (_reportPossibleCrUseOfGlobal({
            error: Error()
          }), Global) : Global).HEIGHT / 2) {
            this.enemyBulletFactory.recycleProduct(this.node);
          }
        }

      }) || _class));

      _cclegacy._RF.pop();

      _crd = false;
    }
  };
});
//# sourceMappingURL=71926fd005df02a564dd5919242299e298b22b49.js.map