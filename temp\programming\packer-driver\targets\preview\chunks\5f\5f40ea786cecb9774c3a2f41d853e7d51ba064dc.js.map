{"version": 3, "sources": ["file:///E:/M2Game/Client/assets/scripts/Game/utils/Tools.ts"], "names": ["DYTools", "v2", "Vec2", "misc", "Size", "size", "sp", "resources", "SpriteFrame", "GameConst", "_btnAble", "char<PERSON><PERSON>", "getPhoneTimestamp", "date", "Date", "parse", "toString", "getCurTimestamp", "now", "isAfterDay", "t", "o", "date1", "year1", "getFullYear", "month1", "getMonth", "day1", "getDate", "date2", "year2", "month2", "day2", "isLaterDay", "timestamp", "currentDate", "targetDate", "isLaterDayRefresh", "refreshHour", "currentTimestamp", "diff", "hoursPassed", "Math", "floor", "lastDate", "getHours", "getDiffDays", "startTimestamp", "endTimestamp", "isNextDay", "nextDayStart", "log", "message", "args", "console", "error", "warn", "random_int", "min", "max", "random", "getRandomInArray", "array", "remove", "length", "index", "element", "splice", "getRandomCountInArray", "count", "result", "tempArray", "i", "push", "for<PERSON>ach", "item", "indexOf", "arrayInsert", "tail", "concat", "arrayInsertRandom", "getDict<PERSON>ength", "dict", "key", "getStringDivideForIndex", "str", "delimiter", "parts", "split", "stringToPoint", "Number", "ZERO", "stringToSize", "stringToNumber", "map", "part", "filter", "num", "isNaN", "getPosDis", "point1", "point2", "dx", "x", "dy", "y", "sqrt", "getDisForVec2", "vec1", "vec2", "subtract", "getDisFromPointToLine", "point", "lineStart", "lineEnd", "lineVec", "pointVec", "angle", "signAngle", "sin", "getPointToLineY", "slope", "intercept", "loadResSprite", "path", "sprite", "callback", "load", "err", "spriteFrame", "loadSkel", "skeleton", "onSuccess", "onError", "SkeletonData", "skeletonData", "premultipliedAlpha", "getLimitStr", "max<PERSON><PERSON><PERSON>", "getStringCharacterLength", "subStrByCharacter", "charCode", "charCodeAt", "numberDiffAbs", "num1", "num2", "abs", "numberDiff", "isNumberDiffRange", "range", "isVec2DiffRange", "copyArray", "numberToString3", "suffix", "numberToString2", "millions", "remainder", "thousands", "numberToString", "remainder1", "remainder2", "getTime", "seconds", "hours", "minutes", "secs", "getTimeSecondStr", "getTimestrDouble", "getTimeSecondStr2", "getTimeWord", "ceil", "remainingSeconds", "days", "getTimeWord1", "getTimeWord2", "getTimeWord3", "getTimeWord4", "getTimeStrForHMS1", "getRandomVec2", "xRange", "y<PERSON><PERSON><PERSON>", "arrContains", "arr<PERSON><PERSON><PERSON>", "arrC<PERSON>ain", "addScript", "node", "script", "getComponent", "addComponent", "removeChildByName", "parent", "name", "child", "getChildByName", "destroy", "isPlaneOutScreen", "position", "viewCenterX", "ViewCenter", "viewHeight", "ViewHeight", "getBezier", "p0", "p1", "p2", "p3", "pow", "getStraight", "start", "direction", "distance", "normalizedDir", "normalize", "add", "multiplyScalar", "getStraightForDir", "getAngle", "end", "asin", "radiansToDegrees", "getPositionByAngle", "radius", "radian", "atan2", "degreesToRadians", "cos", "clearArrayForNode", "clearMapForNode", "clear", "getRandomUserId", "userId", "randomIndex", "getRandomAdId", "adId", "version_compare", "version1", "version2", "v1Parts", "v2Parts", "<PERSON><PERSON><PERSON><PERSON>", "v1", "parseInt", "getLineDegrees", "x1", "y1", "x2", "y2", "vector", "reference", "getDir", "getDegreeForDir", "dir", "equals", "toDegrees", "radians", "PI", "getResetAngle", "getSingedResetAngle", "getSingedAngle", "arr<PERSON><PERSON>", "offset", "copy", "reverse", "temp", "clearArrayForComp", "comp", "clearMapForNodeArr", "nodeArray", "clearMapForComp", "clearMapForCompArr", "compA<PERSON>y", "clearNodePool", "pool", "get", "Tools"], "mappings": ";;;kJAGMA,O;;;;;;;;;;;;;AAHwBC,MAAAA,E,OAAAA,E;AAAUC,MAAAA,I,OAAAA,I;AAAMC,MAAAA,I,OAAAA,I;AAAMC,MAAAA,I,OAAAA,I;AAAMC,MAAAA,I,OAAAA,I;AAAcC,MAAAA,E,OAAAA,E;AAAIC,MAAAA,S,OAAAA,S;AAAWC,MAAAA,W,OAAAA,W;;AAC9EC,MAAAA,S,iBAAAA,S;;;;;;;;;AAEHT,MAAAA,O,GAAN,MAAMA,OAAN,CAAc;AAAA;AAAA,eACVU,QADU,GACU,IADV;AAAA,eAEVC,OAFU,GAEU,CAChB,GADgB,EACX,GADW,EACN,GADM,EACD,GADC,EACI,GADJ,EACS,GADT,EACc,GADd,EACmB,GADnB,EACwB,GADxB,EAC6B,GAD7B,EAEhB,GAFgB,EAEX,GAFW,EAEN,GAFM,EAED,GAFC,EAEI,GAFJ,EAES,GAFT,EAEc,GAFd,EAEmB,GAFnB,EAEwB,GAFxB,EAE6B,GAF7B,EAGhB,GAHgB,EAGX,GAHW,EAGN,GAHM,EAGD,GAHC,EAGI,GAHJ,EAGS,GAHT,EAGc,GAHd,EAGmB,GAHnB,EAGwB,GAHxB,EAG6B,GAH7B,EAIhB,GAJgB,EAIX,GAJW,EAIN,GAJM,EAID,GAJC,EAII,GAJJ,EAIS,GAJT,EAIc,GAJd,EAImB,GAJnB,EAIwB,GAJxB,EAI6B,GAJ7B,EAKhB,GALgB,EAKX,GALW,EAKN,GALM,EAKD,GALC,EAKI,GALJ,EAKS,GALT,EAKc,GALd,EAKmB,GALnB,EAKwB,GALxB,EAK6B,GAL7B,EAMhB,GANgB,EAMX,GANW,EAMN,GANM,EAMD,GANC,EAMI,GANJ,EAMS,GANT,EAMc,GANd,EAMmB,GANnB,EAMwB,GANxB,EAM6B,GAN7B,EAOhB,GAPgB,EAOX,GAPW,CAFV;AAAA;;AAYV;AACJ;AACA;AACIC,QAAAA,iBAAiB,GAAW;AACxB,cAAMC,IAAI,GAAG,IAAIC,IAAJ,EAAb;AACA,iBAAOA,IAAI,CAACC,KAAL,CAAWF,IAAI,CAACG,QAAL,EAAX,CAAP;AACH;AAED;AACJ;AACA;;;AACIC,QAAAA,eAAe,GAAW;AACtB;AACA,iBAAOH,IAAI,CAACI,GAAL,KAAa,IAApB;AACH;AAED;AACJ;AACA;;;AACIC,QAAAA,UAAU,CAACC,CAAD,EAAYC,CAAZ,EAAgC;AACtC,cAAMC,KAAK,GAAG,IAAIR,IAAJ,CAASM,CAAT,CAAd;AACA,cAAMG,KAAK,GAAGD,KAAK,CAACE,WAAN,EAAd;AACA,cAAMC,MAAM,GAAGH,KAAK,CAACI,QAAN,EAAf;AACA,cAAMC,IAAI,GAAGL,KAAK,CAACM,OAAN,EAAb;AAEA,cAAMC,KAAK,GAAG,IAAIf,IAAJ,CAASO,CAAT,CAAd;AACA,cAAMS,KAAK,GAAGD,KAAK,CAACL,WAAN,EAAd;AACA,cAAMO,MAAM,GAAGF,KAAK,CAACH,QAAN,EAAf;AACA,cAAMM,IAAI,GAAGH,KAAK,CAACD,OAAN,EAAb;AAEA,iBAAOL,KAAK,GAAGO,KAAR,IAAkBP,KAAK,KAAKO,KAAV,KAAoBL,MAAM,GAAGM,MAAT,IAAoBN,MAAM,KAAKM,MAAX,IAAqBJ,IAAI,GAAGK,IAApE,CAAzB;AACH;AAED;AACJ;AACA;;;AACIC,QAAAA,UAAU,CAACC,SAAD,EAA6B;AACnC,cAAMC,WAAW,GAAG,IAAIrB,IAAJ,CAAS,KAAKG,eAAL,EAAT,CAApB;AACA,cAAMmB,UAAU,GAAG,IAAItB,IAAJ,CAASoB,SAAT,CAAnB;AACA,iBACIE,UAAU,CAACZ,WAAX,KAA2BW,WAAW,CAACX,WAAZ,EAA3B,IACCY,UAAU,CAACZ,WAAX,OAA6BW,WAAW,CAACX,WAAZ,EAA7B,KACIY,UAAU,CAACV,QAAX,KAAwBS,WAAW,CAACT,QAAZ,EAAxB,IACIU,UAAU,CAACV,QAAX,OAA0BS,WAAW,CAACT,QAAZ,EAA1B,IACGU,UAAU,CAACR,OAAX,KAAuBO,WAAW,CAACP,OAAZ,EAHlC,CAFL;AAOH;AAED;AACJ;AACA;;;AACIS,QAAAA,iBAAiB,CAACH,SAAD,EAAoBI,WAApB,EAAkD;AAC/D,cAAMC,gBAAgB,GAAG,KAAKtB,eAAL,EAAzB;AACA,cAAMuB,IAAI,GAAGD,gBAAgB,GAAGL,SAAhC;;AAEA,cAAIM,IAAI,GAAG,CAAX,EAAc;AACV,gBAAMC,WAAW,GAAGC,IAAI,CAACC,KAAL,CAAWH,IAAI,GAAG,OAAlB,CAApB,CADU,CACsC;;AAChD,gBAAIC,WAAW,IAAI,EAAnB,EAAuB;AACnB,qBAAO,IAAP;AACH;;AAED,gBAAMG,QAAQ,GAAG,IAAI9B,IAAJ,CAASoB,SAAT,CAAjB;AACA,gBAAMC,WAAW,GAAG,IAAIrB,IAAJ,CAASyB,gBAAT,CAApB;;AAEA,gBAAIK,QAAQ,CAAChB,OAAT,KAAqBO,WAAW,CAACP,OAAZ,EAAzB,EAAgD;AAC5C,kBAAIU,WAAW,IAAIH,WAAW,CAACU,QAAZ,EAAnB,EAA2C;AACvC,uBAAO,IAAP;AACH;AACJ;AACJ;;AAED,iBAAO,KAAP;AACH;AAED;AACJ;AACA;;;AACIC,QAAAA,WAAW,CAACC,cAAD,EAAyBC,YAAzB,EAAuD;AAC9D,cAAIA,YAAY,GAAGD,cAAnB,EAAmC,OAAO,CAAC,CAAR;AACnC,cAAMP,IAAI,GAAGQ,YAAY,GAAGD,cAA5B;AACA,iBAAOL,IAAI,CAACC,KAAL,CAAWH,IAAI,IAAI,OAAO,EAAP,GAAY,EAAZ,GAAiB,EAArB,CAAf,CAAP;AACH;AAED;AACJ;AACA;;;AACIS,QAAAA,SAAS,CAACf,SAAD,EAA6B;AAClC,cAAMK,gBAAgB,GAAG,KAAKtB,eAAL,EAAzB;AACA,cAAMiC,YAAY,GAAGhB,SAAS,GAAG,QAAQ,IAAzC,CAFkC,CAEa;;AAC/C,iBAAOK,gBAAgB,IAAIW,YAA3B;AACH;AAED;AACJ;AACA;;;AACIC,QAAAA,GAAG,CAACC,OAAD,EAAkC;AAAA,4CAAbC,IAAa;AAAbA,YAAAA,IAAa;AAAA;;AACjCC,UAAAA,OAAO,CAACH,GAAR,CAAYC,OAAZ,EAAqB,GAAGC,IAAxB;AACH;AAED;AACJ;AACA;;;AACIE,QAAAA,KAAK,CAACH,OAAD,EAAkC;AAAA,6CAAbC,IAAa;AAAbA,YAAAA,IAAa;AAAA;;AACnCC,UAAAA,OAAO,CAACC,KAAR,CAAcH,OAAd,EAAuB,GAAGC,IAA1B;AACH;AAED;AACJ;AACA;;;AACIG,QAAAA,IAAI,CAACJ,OAAD,EAAkC;AAAA,6CAAbC,IAAa;AAAbA,YAAAA,IAAa;AAAA;;AAClCC,UAAAA,OAAO,CAACE,IAAR,CAAaJ,OAAb,EAAsB,GAAGC,IAAzB;AACH;AAED;AACJ;AACA;;;AACII,QAAAA,UAAU,CAACC,GAAD,EAAcC,GAAd,EAAmC;AACzC,cAAMC,MAAM,GAAGlB,IAAI,CAACC,KAAL,CAAWD,IAAI,CAACkB,MAAL,MAAiBD,GAAG,GAAGD,GAAN,GAAY,CAA7B,CAAX,IAA8CA,GAA7D;AACA,iBAAOhB,IAAI,CAACiB,GAAL,CAASD,GAAT,EAAchB,IAAI,CAACgB,GAAL,CAASC,GAAT,EAAcC,MAAd,CAAd,CAAP;AACH;AAED;AACJ;AACA;;;AACIC,QAAAA,gBAAgB,CAAIC,KAAJ,EAAgBC,MAAhB,EAAmD;AAAA,cAAnCA,MAAmC;AAAnCA,YAAAA,MAAmC,GAAjB,KAAiB;AAAA;;AAC/D,cAAMC,MAAM,GAAGF,KAAK,CAACE,MAArB;AACA,cAAIA,MAAM,KAAK,CAAf,EAAkB,OAAO,IAAP;AAElB,cAAMC,KAAK,GAAG,KAAKR,UAAL,CAAgB,CAAhB,EAAmBO,MAAM,GAAG,CAA5B,CAAd;AACA,cAAME,OAAO,GAAGJ,KAAK,CAACG,KAAD,CAArB;;AAEA,cAAIF,MAAJ,EAAY;AACRD,YAAAA,KAAK,CAACK,MAAN,CAAaF,KAAb,EAAoB,CAApB;AACH;;AAED,iBAAOC,OAAP;AACH;AAED;AACJ;AACA;;;AACIE,QAAAA,qBAAqB,CAAIN,KAAJ,EAAgBO,KAAhB,EAA+BN,MAA/B,EAA6D;AAAA,cAA9BA,MAA8B;AAA9BA,YAAAA,MAA8B,GAAZ,KAAY;AAAA;;AAC9E,cAAMO,MAAW,GAAG,EAApB;AACA,cAAMC,SAAS,GAAG,CAAC,GAAGT,KAAJ,CAAlB;;AAEA,eAAK,IAAIU,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAGH,KAApB,EAA2BG,CAAC,EAA5B,EAAgC;AAC5B,gBAAMN,OAAO,GAAG,KAAKL,gBAAL,CAAsBU,SAAtB,EAAiC,IAAjC,CAAhB;;AACA,gBAAIL,OAAO,KAAK,IAAhB,EAAsB;AAClBI,cAAAA,MAAM,CAACG,IAAP,CAAYP,OAAZ;AACH;AACJ;;AAED,cAAIH,MAAJ,EAAY;AACRO,YAAAA,MAAM,CAACI,OAAP,CAAgBC,IAAD,IAAU;AACrB,kBAAMV,KAAK,GAAGH,KAAK,CAACc,OAAN,CAAcD,IAAd,CAAd;;AACA,kBAAIV,KAAK,IAAI,CAAb,EAAgB;AACZH,gBAAAA,KAAK,CAACK,MAAN,CAAaF,KAAb,EAAoB,CAApB;AACH;AACJ,aALD;AAMH;;AAED,iBAAOK,MAAP;AACH;AAGD;AACJ;AACA;AACA;AACA;AACA;AACA;;;AACIO,QAAAA,WAAW,CAAIf,KAAJ,EAAgBG,KAAhB,EAA+BC,OAA/B,EAAgD;AACvD,cAAID,KAAK,GAAGH,KAAK,CAACE,MAAlB,EAA0B;AACtB,gBAAMc,IAAI,GAAGhB,KAAK,CAACK,MAAN,CAAaF,KAAb,EAAoBH,KAAK,CAACE,MAA1B,CAAb;AACAF,YAAAA,KAAK,CAACW,IAAN,CAAWP,OAAX;AACA,mBAAOJ,KAAK,CAACiB,MAAN,CAAaD,IAAb,CAAP;AACH,WAJD,MAIO;AACHhB,YAAAA,KAAK,CAACW,IAAN,CAAWP,OAAX;AACA,mBAAOJ,KAAP;AACH;AACJ;AAED;AACJ;AACA;AACA;AACA;AACA;;;AACIkB,QAAAA,iBAAiB,CAAIlB,KAAJ,EAAgBI,OAAhB,EAAiC;AAC9C,cAAMD,KAAK,GAAG,KAAKR,UAAL,CAAgB,CAAhB,EAAmBK,KAAK,CAACE,MAAzB,CAAd;AACA,cAAMc,IAAI,GAAGhB,KAAK,CAACK,MAAN,CAAaF,KAAb,EAAoBH,KAAK,CAACE,MAA1B,CAAb;AACAF,UAAAA,KAAK,CAACW,IAAN,CAAWP,OAAX;AACA,iBAAOJ,KAAK,CAACiB,MAAN,CAAaD,IAAb,CAAP;AACH;AAED;AACJ;AACA;AACA;AACA;;;AACIG,QAAAA,aAAa,CAACC,IAAD,EAAoC;AAC7C,cAAIb,KAAK,GAAG,CAAZ;;AACA,eAAK,IAAMc,GAAX,IAAkBD,IAAlB,EAAwB;AACpB,gBAAIA,IAAI,CAACC,GAAD,CAAJ,KAAc,IAAlB,EAAwB;AACpBd,cAAAA,KAAK;AACR;AACJ;;AACD,iBAAOA,KAAP;AACH;AAED;AACJ;AACA;AACA;AACA;AACA;AACA;;;AACIe,QAAAA,uBAAuB,CAACC,GAAD,EAAcC,SAAd,EAAiCrB,KAAjC,EAA+D;AAClF,cAAMsB,KAAK,GAAGF,GAAG,CAACG,KAAJ,CAAUF,SAAV,CAAd;AACA,iBAAOrB,KAAK,IAAI,CAAT,IAAcsB,KAAK,CAACvB,MAAN,GAAeC,KAA7B,GAAqCsB,KAAK,CAACtB,KAAD,CAA1C,GAAoD,IAA3D;AACH;AAED;AACJ;AACA;AACA;AACA;AACA;;;AACIwB,QAAAA,aAAa,CAACJ,GAAD,EAAcC,SAAd,EAAuC;AAChD,cAAMC,KAAK,GAAGF,GAAG,CAACG,KAAJ,CAAUF,SAAV,CAAd;;AACA,cAAIC,KAAK,CAACvB,MAAN,GAAe,CAAnB,EAAsB;AAClB,mBAAO/D,EAAE,CAACyF,MAAM,CAACH,KAAK,CAAC,CAAD,CAAN,CAAP,EAAmBG,MAAM,CAACH,KAAK,CAAC,CAAD,CAAN,CAAzB,CAAT;AACH;;AACD,iBAAOrF,IAAI,CAACyF,IAAZ;AACH;AAED;AACJ;AACA;AACA;AACA;AACA;;;AACIC,QAAAA,YAAY,CAACP,GAAD,EAAcC,SAAd,EAAuC;AAC/C,cAAMC,KAAK,GAAGF,GAAG,CAACG,KAAJ,CAAUF,SAAV,CAAd;;AACA,cAAIC,KAAK,CAACvB,MAAN,GAAe,CAAnB,EAAsB;AAClB,mBAAO3D,IAAI,CAACqF,MAAM,CAACH,KAAK,CAAC,CAAD,CAAN,CAAP,EAAmBG,MAAM,CAACH,KAAK,CAAC,CAAD,CAAN,CAAzB,CAAX;AACH;;AACD,iBAAOnF,IAAI,CAACuF,IAAZ;AACH;AAED;AACJ;AACA;AACA;AACA;AACA;;;AACIE,QAAAA,cAAc,CAACR,GAAD,EAAcC,SAAd,EAA2C;AACrD,cAAMC,KAAK,GAAGF,GAAG,CAACG,KAAJ,CAAUF,SAAV,CAAd;AACA,iBAAOC,KAAK,CAACO,GAAN,CAAWC,IAAD,IAAUL,MAAM,CAACK,IAAD,CAA1B,EAAkCC,MAAlC,CAA0CC,GAAD,IAAS,CAACC,KAAK,CAACD,GAAD,CAAxD,CAAP;AACH;AAED;AACJ;AACA;AACA;AACA;AACA;;;AACIE,QAAAA,SAAS,CAACC,MAAD,EAAeC,MAAf,EAAqC;AAC1C,cAAMC,EAAE,GAAGD,MAAM,CAACE,CAAP,GAAWH,MAAM,CAACG,CAA7B;AACA,cAAMC,EAAE,GAAGH,MAAM,CAACI,CAAP,GAAWL,MAAM,CAACK,CAA7B;AACA,iBAAO/D,IAAI,CAACgE,IAAL,CAAUJ,EAAE,GAAGA,EAAL,GAAUE,EAAE,GAAGA,EAAzB,CAAP;AACH;AAED;AACJ;AACA;AACA;AACA;AACA;;;AACIG,QAAAA,aAAa,CAACC,IAAD,EAAaC,IAAb,EAAiC;AAC1C,iBAAOD,IAAI,CAACE,QAAL,CAAcD,IAAd,EAAoB7C,MAApB,EAAP;AACH;AAED;AACJ;AACA;AACA;AACA;AACA;AACA;;;AACI+C,QAAAA,qBAAqB,CAACC,KAAD,EAAcC,SAAd,EAA+BC,OAA/B,EAAsD;AACvE,cAAMC,OAAO,GAAGD,OAAO,CAACJ,QAAR,CAAiBG,SAAjB,CAAhB;AACA,cAAMG,QAAQ,GAAGJ,KAAK,CAACF,QAAN,CAAeG,SAAf,CAAjB;AACA,cAAMI,KAAK,GAAGD,QAAQ,CAACE,SAAT,CAAmBH,OAAnB,CAAd;AACA,iBAAOC,QAAQ,CAACpD,MAAT,KAAoBtB,IAAI,CAAC6E,GAAL,CAASF,KAAT,CAA3B;AACH;AAED;AACJ;AACA;AACA;AACA;AACA;AACA;;;AACIG,QAAAA,eAAe,CAACR,KAAD,EAAcC,SAAd,EAA+BC,OAA/B,EAAsD;AACjE,cAAMO,KAAK,GAAG,CAACP,OAAO,CAACT,CAAR,GAAYQ,SAAS,CAACR,CAAvB,KAA6BS,OAAO,CAACX,CAAR,GAAYU,SAAS,CAACV,CAAnD,CAAd;AACA,cAAMmB,SAAS,GAAGT,SAAS,CAACR,CAAV,GAAcgB,KAAK,GAAGR,SAAS,CAACV,CAAlD;AACA,iBAAOkB,KAAK,GAAGT,KAAK,CAACT,CAAd,GAAkBmB,SAAlB,GAA8BV,KAAK,CAACP,CAA3C;AACH,SAjUS,CAmUV;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AAEA;AACA;;AAEA;AACJ;AACA;AACA;AACA;AACA;;;AACIkB,QAAAA,aAAa,CAACC,IAAD,EAAeC,MAAf,EAA+BC,QAA/B,EAA8E;AACvFvH,UAAAA,SAAS,CAACwH,IAAV,CAAeH,IAAI,GAAG,cAAtB,EAAsCpH,WAAtC,EAAmD,CAACwH,GAAD,EAAMC,WAAN,KAAsB;AACrE,gBAAID,GAAJ,EAAS;AACL1E,cAAAA,OAAO,CAACC,KAAR,CAAcyE,GAAd;AACH,aAFD,MAEO;AACH,kBAAIH,MAAJ,EAAY;AACRA,gBAAAA,MAAM,CAACI,WAAP,GAAqBA,WAArB;AACH;;AACD,kBAAIH,QAAJ,EAAc;AACVA,gBAAAA,QAAQ,CAACG,WAAD,CAAR;AACH;AACJ;AACJ,WAXD;AAYH;AAED;AACJ;AACA;AACA;AACA;AACA;AACA;;;AACIC,QAAAA,QAAQ,CAACN,IAAD,EAAeO,QAAf,EAAsCC,SAAtC,EAA2FC,OAA3F,EAAiH;AACrH9H,UAAAA,SAAS,CAACwH,IAAV,CAAeH,IAAf,EAAqBtH,EAAE,CAACgI,YAAxB,EAAsC,CAACN,GAAD,EAAMO,YAAN,KAAuB;AACzD,gBAAIP,GAAJ,EAAS;AACL1E,cAAAA,OAAO,CAACC,KAAR,CAAcyE,GAAd;;AACA,kBAAIK,OAAJ,EAAa;AACTA,gBAAAA,OAAO;AACV;AACJ,aALD,MAKO;AACH,kBAAIF,QAAJ,EAAc;AACVA,gBAAAA,QAAQ,CAACI,YAAT,GAAwBA,YAAxB;AACAJ,gBAAAA,QAAQ,CAACK,kBAAT,GAA8B,KAA9B;AACH;;AACD,kBAAIJ,SAAJ,EAAe;AACXA,gBAAAA,SAAS,CAACG,YAAD,CAAT;AACH;AACJ;AACJ,WAfD;AAgBH;AAED;AACJ;AACA;AACA;AACA;AACA;;;AACIE,QAAAA,WAAW,CAACpD,GAAD,EAAcqD,SAAd,EAAyC;AAChD,cAAI,KAAKC,wBAAL,CAA8BtD,GAA9B,IAAqCqD,SAAzC,EAAoD;AAChD,mBAAO,KAAKE,iBAAL,CAAuBvD,GAAvB,EAA4BqD,SAA5B,IAAyC,KAAhD;AACH;;AACD,iBAAOrD,GAAP;AACH;AAED;AACJ;AACA;AACA;AACA;;;AACIsD,QAAAA,wBAAwB,CAACtD,GAAD,EAAsB;AAC1C,cAAIrB,MAAM,GAAG,CAAb;;AACA,eAAK,IAAIQ,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAGa,GAAG,CAACrB,MAAxB,EAAgCQ,CAAC,EAAjC,EAAqC;AACjC,gBAAMqE,QAAQ,GAAGxD,GAAG,CAACyD,UAAJ,CAAetE,CAAf,CAAjB;AACAR,YAAAA,MAAM,IAAI6E,QAAQ,IAAI,CAAZ,IAAiBA,QAAQ,IAAI,GAA7B,GAAmC,CAAnC,GAAuC,CAAjD;AACH;;AACD,iBAAO7E,MAAP;AACH;AAED;AACJ;AACA;AACA;AACA;AACA;;;AACI4E,QAAAA,iBAAiB,CAACvD,GAAD,EAAcqD,SAAd,EAAyC;AACtD,cAAI1E,MAAM,GAAG,CAAb;AACA,cAAIM,MAAM,GAAG,EAAb;;AACA,eAAK,IAAIE,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAGa,GAAG,CAACrB,MAAxB,EAAgCQ,CAAC,EAAjC,EAAqC;AACjC,gBAAMqE,QAAQ,GAAGxD,GAAG,CAACyD,UAAJ,CAAetE,CAAf,CAAjB;AACAR,YAAAA,MAAM,IAAI6E,QAAQ,IAAI,CAAZ,IAAiBA,QAAQ,IAAI,GAA7B,GAAmC,CAAnC,GAAuC,CAAjD;;AACA,gBAAI7E,MAAM,GAAG0E,SAAb,EAAwB;AACpB;AACH;;AACDpE,YAAAA,MAAM,IAAIe,GAAG,CAACb,CAAD,CAAb;AACH;;AACD,iBAAOF,MAAP;AACH;AAED;AACJ;AACA;AACA;AACA;AACA;;;AACIyE,QAAAA,aAAa,CAACC,IAAD,EAAeC,IAAf,EAAqC;AAC9C,iBAAOvG,IAAI,CAACwG,GAAL,CAASF,IAAI,GAAGC,IAAhB,CAAP;AACH;AAED;AACJ;AACA;AACA;AACA;AACA;;;AACIE,QAAAA,UAAU,CAACH,IAAD,EAAeC,IAAf,EAAqC;AAC3C,iBAAOD,IAAI,GAAGC,IAAd;AACH;AAED;AACJ;AACA;AACA;AACA;AACA;AACA;;;AACIG,QAAAA,iBAAiB,CAACJ,IAAD,EAAeC,IAAf,EAA6BI,KAA7B,EAAqD;AAClE,iBAAO,KAAKN,aAAL,CAAmBC,IAAnB,EAAyBC,IAAzB,KAAkCI,KAAzC;AACH;AAED;AACJ;AACA;AACA;AACA;AACA;AACA;;;AACIC,QAAAA,eAAe,CAAC1C,IAAD,EAAaC,IAAb,EAAyBwC,KAAzB,EAAiD;AAC5D,cAAM7G,IAAI,GAAGoE,IAAI,CAACE,QAAL,CAAcD,IAAd,CAAb;AACA,iBAAOnE,IAAI,CAACwG,GAAL,CAAS1G,IAAI,CAAC+D,CAAd,KAAoB8C,KAApB,IAA6B3G,IAAI,CAACwG,GAAL,CAAS1G,IAAI,CAACiE,CAAd,KAAoB4C,KAAxD;AACH;AAED;AACJ;AACA;;;AACIE,QAAAA,SAAS,CAAIzF,KAAJ,EAAqB;AAC1B,iBAAO,CAAC,GAAGA,KAAJ,CAAP;AACH,SA3fS,CA6fV;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACJ;AACA;AACA;AACA;;;AACI0F,QAAAA,eAAe,CAACvD,GAAD,EAAsB;AACjC,cAAI3B,MAAM,GAAG,EAAb;AACA,cAAImF,MAAM,GAAG,EAAb;;AAEA,cAAIxD,GAAG,GAAG,IAAV,EAAgB;AACZ3B,YAAAA,MAAM,GAAG2B,GAAG,CAACjF,QAAJ,EAAT;AACH,WAFD,MAEO,IAAIiF,GAAG,IAAI,OAAX,EAAoB;AACvB3B,YAAAA,MAAM,GAAG5B,IAAI,CAACC,KAAL,CAAWsD,GAAG,GAAG,OAAjB,EAA0BjF,QAA1B,EAAT;AACAyI,YAAAA,MAAM,GAAG,GAAT;AACH,WAHM,MAGA,IAAIxD,GAAG,IAAI,IAAX,EAAiB;AACpB3B,YAAAA,MAAM,GAAG5B,IAAI,CAACC,KAAL,CAAWsD,GAAG,GAAG,IAAjB,EAAuBjF,QAAvB,EAAT;AACAyI,YAAAA,MAAM,GAAG,GAAT;AACH;;AAED,iBAAOnF,MAAM,GAAGmF,MAAhB;AACH;AAED;AACJ;AACA;AACA;AACA;;;AACIC,QAAAA,eAAe,CAACzD,GAAD,EAAsB;AACjC,cAAI3B,MAAM,GAAG,EAAb;AACA,cAAImF,MAAM,GAAG,EAAb;;AAEA,cAAIxD,GAAG,GAAG,IAAV,EAAgB;AACZ3B,YAAAA,MAAM,GAAG2B,GAAG,CAACjF,QAAJ,EAAT;AACH,WAFD,MAEO,IAAIiF,GAAG,IAAI,OAAX,EAAoB;AACvB,gBAAM0D,QAAQ,GAAGjH,IAAI,CAACC,KAAL,CAAWsD,GAAG,GAAG,OAAjB,CAAjB;AACA,gBAAM2D,SAAS,GAAGlH,IAAI,CAACC,KAAL,CAAYsD,GAAG,GAAG,OAAP,GAAkB,MAA7B,CAAlB;AACA3B,YAAAA,MAAM,GAAGqF,QAAQ,GAAG,GAAX,GAAiBC,SAA1B;AACAH,YAAAA,MAAM,GAAG,GAAT;AACH,WALM,MAKA,IAAIxD,GAAG,IAAI,IAAX,EAAiB;AACpB,gBAAM4D,SAAS,GAAGnH,IAAI,CAACC,KAAL,CAAWsD,GAAG,GAAG,IAAjB,CAAlB;;AACA,gBAAM2D,UAAS,GAAGlH,IAAI,CAACC,KAAL,CAAYsD,GAAG,GAAG,IAAP,GAAe,GAA1B,CAAlB;;AACA3B,YAAAA,MAAM,GAAGuF,SAAS,GAAG,GAAZ,GAAkBD,UAA3B;AACAH,YAAAA,MAAM,GAAG,GAAT;AACH;;AAED,iBAAOnF,MAAM,GAAGmF,MAAhB;AACH;AAED;AACJ;AACA;AACA;AACA;;;AACIK,QAAAA,cAAc,CAAC7D,GAAD,EAAsB;AAChC,cAAI3B,MAAM,GAAG,EAAb;AACA,cAAImF,MAAM,GAAG,EAAb;;AAEA,cAAIxD,GAAG,GAAG,IAAV,EAAgB;AACZ3B,YAAAA,MAAM,GAAG2B,GAAG,CAACjF,QAAJ,EAAT;AACH,WAFD,MAEO,IAAIiF,GAAG,IAAI,OAAX,EAAoB;AACvB,gBAAM0D,QAAQ,GAAGjH,IAAI,CAACC,KAAL,CAAWsD,GAAG,GAAG,OAAjB,CAAjB;AACA,gBAAM8D,UAAU,GAAGrH,IAAI,CAACC,KAAL,CAAYsD,GAAG,GAAG,OAAP,GAAkB,MAA7B,CAAnB;AACA,gBAAM+D,UAAU,GAAGtH,IAAI,CAACC,KAAL,CAAYsD,GAAG,GAAG,MAAP,GAAiB,KAA5B,CAAnB;AACA3B,YAAAA,MAAM,GAAGqF,QAAQ,GAAG,GAAX,GAAiBI,UAAjB,GAA8BC,UAAvC;AACAP,YAAAA,MAAM,GAAG,GAAT;AACH,WANM,MAMA,IAAIxD,GAAG,IAAI,IAAX,EAAiB;AACpB,gBAAM4D,SAAS,GAAGnH,IAAI,CAACC,KAAL,CAAWsD,GAAG,GAAG,IAAjB,CAAlB;;AACA,gBAAM8D,WAAU,GAAGrH,IAAI,CAACC,KAAL,CAAYsD,GAAG,GAAG,IAAP,GAAe,GAA1B,CAAnB;;AACA,gBAAM+D,WAAU,GAAGtH,IAAI,CAACC,KAAL,CAAYsD,GAAG,GAAG,GAAP,GAAc,EAAzB,CAAnB;;AACA3B,YAAAA,MAAM,GAAGuF,SAAS,GAAG,GAAZ,GAAkBE,WAAlB,GAA+BC,WAAxC;AACAP,YAAAA,MAAM,GAAG,GAAT;AACH;;AAED,iBAAOnF,MAAM,GAAGmF,MAAhB;AACH;AAED;AACJ;AACA;AACA;AACA;;;AACIQ,QAAAA,OAAO,CAACC,OAAD,EAA4B;AAC/B,cAAMC,KAAK,GAAGzH,IAAI,CAACC,KAAL,CAAWuH,OAAO,GAAG,IAArB,CAAd;AACA,cAAME,OAAO,GAAG1H,IAAI,CAACC,KAAL,CAAYuH,OAAO,GAAG,IAAX,GAAmB,EAA9B,CAAhB;AACA,cAAMG,IAAI,GAAG3H,IAAI,CAACC,KAAL,CAAWuH,OAAO,GAAG,EAArB,CAAb;AACA,iBAAO,CAACC,KAAD,EAAQC,OAAR,EAAiBC,IAAjB,CAAP;AACH;AAED;AACJ;AACA;AACA;AACA;;;AACIC,QAAAA,gBAAgB,CAACJ,OAAD,EAA0B;AACtC,cAAIA,OAAO,GAAG,EAAd,EAAkB;AACd,2BAAa,KAAKK,gBAAL,CAAsBL,OAAtB,CAAb;AACH,WAFD,MAEO,IAAIA,OAAO,GAAG,IAAd,EAAoB;AACvB,gBAAME,OAAO,GAAG1H,IAAI,CAACC,KAAL,CAAWuH,OAAO,GAAG,EAArB,CAAhB;AACA,gBAAMG,IAAI,GAAG3H,IAAI,CAACC,KAAL,CAAWuH,OAAO,GAAG,EAArB,CAAb;AACA,mBAAU,KAAKK,gBAAL,CAAsBH,OAAtB,CAAV,SAA4C,KAAKG,gBAAL,CAAsBF,IAAtB,CAA5C;AACH,WAJM,MAIA;AACH,gBAAMF,KAAK,GAAGzH,IAAI,CAACC,KAAL,CAAWuH,OAAO,GAAG,IAArB,CAAd;AACA,gBAAMN,SAAS,GAAGM,OAAO,GAAG,IAA5B;;AACA,gBAAME,QAAO,GAAG1H,IAAI,CAACC,KAAL,CAAWiH,SAAS,GAAG,EAAvB,CAAhB;;AACA,gBAAMS,KAAI,GAAG3H,IAAI,CAACC,KAAL,CAAWiH,SAAS,GAAG,EAAvB,CAAb;;AACA,mBAAU,KAAKW,gBAAL,CAAsBJ,KAAtB,CAAV,SAA0C,KAAKI,gBAAL,CAAsBH,QAAtB,CAA1C,SAA4E,KAAKG,gBAAL,CAAsBF,KAAtB,CAA5E;AACH;AACJ;AAED;AACJ;AACA;AACA;AACA;;;AACIG,QAAAA,iBAAiB,CAACN,OAAD,EAA0B;AACvC,cAAMC,KAAK,GAAGzH,IAAI,CAACC,KAAL,CAAWuH,OAAO,GAAG,IAArB,CAAd;AACA,cAAMN,SAAS,GAAGM,OAAO,GAAG,IAA5B;AACA,cAAME,OAAO,GAAG1H,IAAI,CAACC,KAAL,CAAWiH,SAAS,GAAG,EAAvB,CAAhB;AACA,cAAMS,IAAI,GAAG3H,IAAI,CAACC,KAAL,CAAWiH,SAAS,GAAG,EAAvB,CAAb;AACA,iBAAU,KAAKW,gBAAL,CAAsBJ,KAAtB,CAAV,SAA0C,KAAKI,gBAAL,CAAsBH,OAAtB,CAA1C,SAA4E,KAAKG,gBAAL,CAAsBF,IAAtB,CAA5E;AACH;AAED;AACJ;AACA;AACA;AACA;;;AACIE,QAAAA,gBAAgB,CAACtE,GAAD,EAAsB;AAClC,iBAAOA,GAAG,GAAG,EAAN,SAAeA,GAAf,QAA0BA,GAAjC;AACH;AAED;AACJ;AACA;AACA;AACA;;;AACIwE,QAAAA,WAAW,CAACP,OAAD,EAA0B;AACjC,cAAI5F,MAAM,GAAG,EAAb;AACA4F,UAAAA,OAAO,GAAGxH,IAAI,CAACgI,IAAL,CAAUR,OAAV,CAAV;;AAEA,cAAIA,OAAO,GAAG,EAAd,EAAkB;AACd5F,YAAAA,MAAM,GAAM4F,OAAN,MAAN;AACH,WAFD,MAEO,IAAIA,OAAO,GAAG,IAAd,EAAoB;AACvB,gBAAME,OAAO,GAAG1H,IAAI,CAACC,KAAL,CAAWuH,OAAO,GAAG,EAArB,CAAhB;AACA,gBAAMS,gBAAgB,GAAGjI,IAAI,CAACC,KAAL,CAAWuH,OAAO,GAAG,EAArB,CAAzB;AACA5F,YAAAA,MAAM,GAAM8F,OAAN,MAAN;;AACA,gBAAIO,gBAAgB,GAAG,CAAvB,EAA0B;AACtBrG,cAAAA,MAAM,IAAOqG,gBAAP,MAAN;AACH;AACJ,WAPM,MAOA,IAAIT,OAAO,GAAG,KAAd,EAAqB;AACxB,gBAAMC,KAAK,GAAGzH,IAAI,CAACC,KAAL,CAAWuH,OAAO,GAAG,IAArB,CAAd;;AACA,gBAAMS,iBAAgB,GAAGT,OAAO,GAAG,IAAnC;;AACA,gBAAME,SAAO,GAAG1H,IAAI,CAACC,KAAL,CAAWgI,iBAAgB,GAAG,EAA9B,CAAhB;;AACArG,YAAAA,MAAM,GAAM6F,KAAN,MAAN;;AACA,gBAAIC,SAAO,GAAG,CAAd,EAAiB;AACb9F,cAAAA,MAAM,IAAO8F,SAAP,MAAN;AACH;AACJ,WARM,MAQA;AACH,gBAAMQ,IAAI,GAAGlI,IAAI,CAACC,KAAL,CAAWuH,OAAO,GAAG,KAArB,CAAb;;AACA,gBAAMS,kBAAgB,GAAGT,OAAO,GAAG,KAAnC;;AACA,gBAAMC,MAAK,GAAGzH,IAAI,CAACC,KAAL,CAAWgI,kBAAgB,GAAG,IAA9B,CAAd;;AACArG,YAAAA,MAAM,GAAMsG,IAAN,MAAN;;AACA,gBAAIT,MAAK,GAAG,CAAZ,EAAe;AACX7F,cAAAA,MAAM,IAAO6F,MAAP,MAAN;AACH;AACJ;;AAED,iBAAO7F,MAAP;AACH;AAED;AACJ;AACA;AACA;AACA;;;AACIuG,QAAAA,YAAY,CAACX,OAAD,EAA0B;AAClC,cAAI5F,MAAM,GAAG,EAAb;AACA4F,UAAAA,OAAO,GAAGxH,IAAI,CAACgI,IAAL,CAAUR,OAAV,CAAV;;AAEA,cAAIA,OAAO,GAAG,EAAd,EAAkB;AACd5F,YAAAA,MAAM,GAAM4F,OAAN,WAAN;AACH,WAFD,MAEO,IAAIA,OAAO,GAAG,IAAd,EAAoB;AACvB,gBAAME,OAAO,GAAG1H,IAAI,CAACC,KAAL,CAAWuH,OAAO,GAAG,EAArB,CAAhB;AACA,gBAAMS,gBAAgB,GAAGjI,IAAI,CAACC,KAAL,CAAWuH,OAAO,GAAG,EAArB,CAAzB;AACA5F,YAAAA,MAAM,GAAM8F,OAAN,WAAN;;AACA,gBAAIO,gBAAgB,GAAG,CAAvB,EAA0B;AACtBrG,cAAAA,MAAM,IAAOqG,gBAAP,WAAN;AACH;AACJ,WAPM,MAOA,IAAIT,OAAO,GAAG,KAAd,EAAqB;AACxB,gBAAMC,KAAK,GAAGzH,IAAI,CAACC,KAAL,CAAWuH,OAAO,GAAG,IAArB,CAAd;;AACA,gBAAMS,kBAAgB,GAAGT,OAAO,GAAG,IAAnC;;AACA,gBAAME,SAAO,GAAG1H,IAAI,CAACC,KAAL,CAAWgI,kBAAgB,GAAG,EAA9B,CAAhB;;AACArG,YAAAA,MAAM,GAAM6F,KAAN,iBAAN;;AACA,gBAAIC,SAAO,GAAG,CAAd,EAAiB;AACb9F,cAAAA,MAAM,IAAO8F,SAAP,WAAN;AACH;AACJ,WARM,MAQA;AACH,gBAAMQ,IAAI,GAAGlI,IAAI,CAACC,KAAL,CAAWuH,OAAO,GAAG,KAArB,CAAb;;AACA,gBAAMS,kBAAgB,GAAGT,OAAO,GAAG,KAAnC;;AACA,gBAAMC,OAAK,GAAGzH,IAAI,CAACC,KAAL,CAAWgI,kBAAgB,GAAG,IAA9B,CAAd;;AACArG,YAAAA,MAAM,GAAMsG,IAAN,WAAN;;AACA,gBAAIT,OAAK,GAAG,CAAZ,EAAe;AACX7F,cAAAA,MAAM,IAAO6F,OAAP,iBAAN;AACH;AACJ;;AAED,iBAAO7F,MAAP;AACH;AAED;AACJ;AACA;AACA;AACA;;;AACIwG,QAAAA,YAAY,CAACZ,OAAD,EAA0B;AAClC,cAAI5F,MAAM,GAAG,EAAb;AACA4F,UAAAA,OAAO,GAAGxH,IAAI,CAACgI,IAAL,CAAUR,OAAV,CAAV;;AAEA,cAAIA,OAAO,GAAG,EAAd,EAAkB;AACd5F,YAAAA,MAAM,GAAM4F,OAAN,MAAN;AACH,WAFD,MAEO,IAAIA,OAAO,GAAG,IAAd,EAAoB;AACvB,gBAAME,OAAO,GAAG1H,IAAI,CAACC,KAAL,CAAWuH,OAAO,GAAG,EAArB,CAAhB;AACA5F,YAAAA,MAAM,GAAM8F,OAAN,MAAN;AACH,WAHM,MAGA,IAAIF,OAAO,GAAG,KAAd,EAAqB;AACxB,gBAAMC,KAAK,GAAGzH,IAAI,CAACC,KAAL,CAAWuH,OAAO,GAAG,IAArB,CAAd;AACA,gBAAMS,gBAAgB,GAAGT,OAAO,GAAG,IAAnC;;AACA,gBAAME,SAAO,GAAG1H,IAAI,CAACC,KAAL,CAAWgI,gBAAgB,GAAG,EAA9B,CAAhB;;AACArG,YAAAA,MAAM,GAAM6F,KAAN,MAAN;;AACA,gBAAIC,SAAO,GAAG,CAAd,EAAiB;AACb9F,cAAAA,MAAM,IAAO8F,SAAP,MAAN;AACH;AACJ,WARM,MAQA;AACH,gBAAMQ,IAAI,GAAGlI,IAAI,CAACC,KAAL,CAAWuH,OAAO,GAAG,KAArB,CAAb;;AACA,gBAAMS,kBAAgB,GAAGT,OAAO,GAAG,KAAnC;;AACA,gBAAMC,OAAK,GAAGzH,IAAI,CAACC,KAAL,CAAWgI,kBAAgB,GAAG,IAA9B,CAAd;;AACArG,YAAAA,MAAM,GAAMsG,IAAN,MAAN;;AACA,gBAAIT,OAAK,GAAG,CAAZ,EAAe;AACX7F,cAAAA,MAAM,IAAO6F,OAAP,MAAN;AACH;AACJ;;AAED,iBAAO7F,MAAP;AACH;AAED;AACJ;AACA;AACA;AACA;;;AACIyG,QAAAA,YAAY,CAACb,OAAD,EAA0B;AAClC,cAAI5F,MAAM,GAAG,EAAb;AACA4F,UAAAA,OAAO,GAAGxH,IAAI,CAACgI,IAAL,CAAUR,OAAV,CAAV;;AAEA,cAAIA,OAAO,GAAG,IAAd,EAAoB;AAChB,gBAAME,OAAO,GAAG1H,IAAI,CAACC,KAAL,CAAWuH,OAAO,GAAG,EAArB,CAAhB;AACA,gBAAMS,gBAAgB,GAAGjI,IAAI,CAACC,KAAL,CAAWuH,OAAO,GAAG,EAArB,CAAzB;AACA5F,YAAAA,MAAM,GAAM8F,OAAN,SAAiBO,gBAAjB,MAAN;AACH,WAJD,MAIO,IAAIT,OAAO,GAAG,KAAd,EAAqB;AACxB,gBAAMC,KAAK,GAAGzH,IAAI,CAACC,KAAL,CAAWuH,OAAO,GAAG,IAArB,CAAd;;AACA,gBAAMS,kBAAgB,GAAGT,OAAO,GAAG,IAAnC;;AACA,gBAAME,SAAO,GAAG1H,IAAI,CAACC,KAAL,CAAWgI,kBAAgB,GAAG,EAA9B,CAAhB;;AACArG,YAAAA,MAAM,GAAM6F,KAAN,SAAeC,SAAf,MAAN;AACH,WALM,MAKA;AACH,gBAAMQ,IAAI,GAAGlI,IAAI,CAACC,KAAL,CAAWuH,OAAO,GAAG,KAArB,CAAb;;AACA,gBAAMS,kBAAgB,GAAGT,OAAO,GAAG,KAAnC;;AACA,gBAAMC,OAAK,GAAGzH,IAAI,CAACgI,IAAL,CAAUC,kBAAgB,GAAG,IAA7B,CAAd;;AACArG,YAAAA,MAAM,GAAMsG,IAAN,SAAcT,OAAd,MAAN;AACH;;AAED,iBAAO7F,MAAP;AACH;AACD;AACJ;AACA;AACA;AACA;;;AACI0G,QAAAA,YAAY,CAACd,OAAD,EAA0B;AAClC,cAAI5F,MAAM,GAAG,EAAb;AACA4F,UAAAA,OAAO,GAAGxH,IAAI,CAACgI,IAAL,CAAUR,OAAV,CAAV;;AAEA,cAAIA,OAAO,GAAG,IAAd,EAAoB;AAChB,gBAAME,OAAO,GAAG1H,IAAI,CAACC,KAAL,CAAWuH,OAAO,GAAG,EAArB,CAAhB;AACA,gBAAMS,gBAAgB,GAAGjI,IAAI,CAACC,KAAL,CAAWuH,OAAO,GAAG,EAArB,CAAzB;AACA5F,YAAAA,MAAM,GAAM8F,OAAN,cAAiBO,gBAAjB,WAAN;AACH,WAJD,MAIO,IAAIT,OAAO,GAAG,KAAd,EAAqB;AACxB,gBAAMC,KAAK,GAAGzH,IAAI,CAACC,KAAL,CAAWuH,OAAO,GAAG,IAArB,CAAd;;AACA,gBAAMS,kBAAgB,GAAGT,OAAO,GAAG,IAAnC;;AACA,gBAAME,SAAO,GAAG1H,IAAI,CAACC,KAAL,CAAWgI,kBAAgB,GAAG,EAA9B,CAAhB;;AACArG,YAAAA,MAAM,GAAM6F,KAAN,oBAAgBC,SAAhB,WAAN;AACH,WALM,MAKA;AACH,gBAAMQ,IAAI,GAAGlI,IAAI,CAACC,KAAL,CAAWuH,OAAO,GAAG,KAArB,CAAb;;AACA,gBAAMS,kBAAgB,GAAGT,OAAO,GAAG,KAAnC;;AACA,gBAAMC,OAAK,GAAGzH,IAAI,CAACC,KAAL,CAAWgI,kBAAgB,GAAG,IAA9B,CAAd;;AACArG,YAAAA,MAAM,GAAMsG,IAAN,cAAcT,OAAd,iBAAN;AACH;;AAED,iBAAO7F,MAAP;AACH;AACD;AACJ;AACA;AACA;AACA;;;AACI2G,QAAAA,iBAAiB,CAACf,OAAD,EAA0B;AACvC,cAAIA,OAAO,GAAG,EAAd,EAAkB;AACd,mBAAUA,OAAV;AACH,WAFD,MAEO,IAAIA,OAAO,GAAG,IAAd,EAAoB;AACvB,gBAAME,OAAO,GAAG1H,IAAI,CAACC,KAAL,CAAWuH,OAAO,GAAG,EAArB,CAAhB;AACA,gBAAMS,gBAAgB,GAAGjI,IAAI,CAACC,KAAL,CAAWuH,OAAO,GAAG,EAArB,CAAzB;AACA,mBAAUE,OAAV,cAAqBO,gBAArB;AACH,WAJM,MAIA,IAAIT,OAAO,GAAG,KAAd,EAAqB;AACxB,gBAAMC,KAAK,GAAGzH,IAAI,CAACC,KAAL,CAAWuH,OAAO,GAAG,IAArB,CAAd;;AACA,gBAAMS,mBAAgB,GAAGT,OAAO,GAAG,IAAnC;;AACA,gBAAME,SAAO,GAAG1H,IAAI,CAACC,KAAL,CAAWgI,mBAAgB,GAAG,EAA9B,CAAhB;;AACA,mBAAUR,KAAV,oBAAoBC,SAApB,cAA+BO,mBAA/B;AACH,WALM,MAKA;AACH,gBAAMC,IAAI,GAAGlI,IAAI,CAACC,KAAL,CAAWuH,OAAO,GAAG,KAArB,CAAb;;AACA,gBAAMS,mBAAgB,GAAGT,OAAO,GAAG,KAAnC;;AACA,gBAAMC,OAAK,GAAGzH,IAAI,CAACC,KAAL,CAAWgI,mBAAgB,GAAG,IAA9B,CAAd;;AACA,gBAAMP,SAAO,GAAG1H,IAAI,CAACC,KAAL,CAAYgI,mBAAgB,GAAG,IAApB,GAA4B,EAAvC,CAAhB;;AACA,mBAAUC,IAAV,cAAkBT,OAAlB,oBAA4BC,SAA5B;AACH;AACJ;AAED;AACJ;AACA;AACA;AACA;AACA;;;AACIc,QAAAA,aAAa,CAACC,MAAD,EAAmCC,MAAnC,EAA2E;AACpF,cAAM7E,CAAC,GAAG,KAAK9C,UAAL,CAAgB0H,MAAM,CAAC5E,CAAvB,EAA0B4E,MAAM,CAAC1E,CAAjC,CAAV;AACA,cAAMA,CAAC,GAAG,KAAKhD,UAAL,CAAgB2H,MAAM,CAAC7E,CAAvB,EAA0B6E,MAAM,CAAC3E,CAAjC,CAAV;AACA,iBAAOxG,EAAE,CAACsG,CAAD,EAAIE,CAAJ,CAAT;AACH;AAED;AACJ;AACA;AACA;AACA;AACA;;;AACI4E,QAAAA,WAAW,CAAIvH,KAAJ,EAAgBI,OAAhB,EAAqC;AAC5C,iBAAOJ,KAAK,CAACc,OAAN,CAAcV,OAAd,KAA0B,CAAC,CAAlC;AACH;AAED;AACJ;AACA;AACA;AACA;AACA;;;AACIoH,QAAAA,SAAS,CAAIxH,KAAJ,EAAgBI,OAAhB,EAAqC;AAC1C,cAAMD,KAAK,GAAGH,KAAK,CAACc,OAAN,CAAcV,OAAd,CAAd;;AACA,cAAID,KAAK,IAAI,CAAb,EAAgB;AACZH,YAAAA,KAAK,CAACK,MAAN,CAAaF,KAAb,EAAoB,CAApB;AACA,mBAAO,IAAP;AACH;;AACD,iBAAO,KAAP;AACH;AAED;AACJ;AACA;AACA;AACA;AACA;;;AACIsH,QAAAA,UAAU,CAAIzH,KAAJ,EAAgBI,OAAhB,EAAqC;AAC3C,iBAAOJ,KAAK,CAACc,OAAN,CAAcV,OAAd,KAA0B,CAAC,CAAlC;AAAoC;AACvC;AAED;AACJ;AACA;AACA;AACA;AACA;;;AACIsH,QAAAA,SAAS,CAAsBC,IAAtB,EAAkCC,MAAlC,EAAkE;AACvE,cAAI,CAACD,IAAD,IAAS,CAACC,MAAd,EAAsB,OAAO,IAAP;AACtB,iBAAOD,IAAI,CAACE,YAAL,CAAkBD,MAAlB,KAA6BD,IAAI,CAACG,YAAL,CAAkBF,MAAlB,CAApC;AACH;AAED;AACJ;AACA;AACA;AACA;;;AACIG,QAAAA,iBAAiB,CAACC,MAAD,EAAeC,IAAf,EAA6B;AAC1C,cAAI,CAACD,MAAL,EAAa;AACb,cAAME,KAAK,GAAGF,MAAM,CAACG,cAAP,CAAsBF,IAAtB,CAAd;;AACA,cAAIC,KAAJ,EAAW;AACPA,YAAAA,KAAK,CAACE,OAAN;AACH;AACJ;AAED;AACJ;AACA;AACA;AACA;;;AACIC,QAAAA,gBAAgB,CAACC,QAAD,EAA0B;AACtC,cAAMC,WAAW,GAAG;AAAA;AAAA,sCAAUC,UAAV,CAAqB/F,CAArB,GAAyB,EAA7C;AACA,cAAMgG,UAAU,GAAG;AAAA;AAAA,sCAAUC,UAAV,GAAuB,EAA1C;AACA,iBAAOJ,QAAQ,CAAC7F,CAAT,GAAa,CAAC8F,WAAd,IAA6BD,QAAQ,CAAC7F,CAAT,GAAa8F,WAA1C,IAAyDD,QAAQ,CAAC3F,CAAT,GAAa,CAAC8F,UAAvE,IAAqFH,QAAQ,CAAC3F,CAAT,GAAa,EAAzG;AACH;AAED;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;AACIgG,QAAAA,SAAS,CAACC,EAAD,EAAaC,EAAb,EAAyBC,EAAzB,EAAqCC,EAArC,EAAiDzL,CAAjD,EAAoE;AACzE,iBACIsL,EAAE,GAAGhK,IAAI,CAACoK,GAAL,CAAS,IAAI1L,CAAb,EAAgB,CAAhB,CAAL,GACA,IAAIuL,EAAJ,GAASvL,CAAT,GAAasB,IAAI,CAACoK,GAAL,CAAS,IAAI1L,CAAb,EAAgB,CAAhB,CADb,GAEA,IAAIwL,EAAJ,GAASlK,IAAI,CAACoK,GAAL,CAAS1L,CAAT,EAAY,CAAZ,CAAT,IAA2B,IAAIA,CAA/B,CAFA,GAGAyL,EAAE,GAAGnK,IAAI,CAACoK,GAAL,CAAS1L,CAAT,EAAY,CAAZ,CAJT;AAMH;AAED;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;;;AACI2L,QAAAA,WAAW,CAACC,KAAD,EAAcC,SAAd,EAA+BC,QAA/B,EAAiD9L,CAAjD,EAAkE;AACzE,cAAM+L,aAAa,GAAGF,SAAS,CAACnG,QAAV,CAAmBkG,KAAnB,EAA0BI,SAA1B,EAAtB;AACA,iBAAOJ,KAAK,CAACK,GAAN,CAAUF,aAAa,CAACG,cAAd,CAA6BJ,QAAQ,GAAG9L,CAAxC,CAAV,CAAP;AACH;AAED;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;;;AACImM,QAAAA,iBAAiB,CAACP,KAAD,EAAcC,SAAd,EAA+BC,QAA/B,EAAiD9L,CAAjD,EAAkE;AAC/E,iBAAO4L,KAAK,CAACK,GAAN,CAAUJ,SAAS,CAACK,cAAV,CAAyBJ,QAAQ,GAAG9L,CAApC,CAAV,CAAP;AACH;AAED;AACJ;AACA;AACA;AACA;AACA;;;AACIoM,QAAAA,QAAQ,CAACR,KAAD,EAAcS,GAAd,EAAiC;AACrC,cAAMnH,EAAE,GAAGmH,GAAG,CAAClH,CAAJ,GAAQyG,KAAK,CAACzG,CAAzB;AACA,cAAMC,EAAE,GAAGiH,GAAG,CAAChH,CAAJ,GAAQuG,KAAK,CAACvG,CAAzB;AACA,cAAMyG,QAAQ,GAAGxK,IAAI,CAACgE,IAAL,CAAUJ,EAAE,GAAGA,EAAL,GAAUE,EAAE,GAAGA,EAAzB,CAAjB;AACA,cAAMa,KAAK,GAAG3E,IAAI,CAACgL,IAAL,CAAUpH,EAAE,GAAG4G,QAAf,CAAd;AACA,iBAAO1G,EAAE,GAAG,CAAL,GAAS,MAAMrG,IAAI,CAACwN,gBAAL,CAAsBtG,KAAtB,CAAf,GAA8ClH,IAAI,CAACwN,gBAAL,CAAsBtG,KAAtB,CAArD;AACH;AAED;AACJ;AACA;AACA;AACA;AACA;;;AACIuG,QAAAA,kBAAkB,CAAC5G,KAAD,EAAcK,KAAd,EAAmC;AACjD,cAAMwG,MAAM,GAAGnL,IAAI,CAACgE,IAAL,CAAUM,KAAK,CAACT,CAAN,GAAUS,KAAK,CAACT,CAAhB,GAAoBS,KAAK,CAACP,CAAN,GAAUO,KAAK,CAACP,CAA9C,CAAf;AACA,cAAMqH,MAAM,GAAIpL,IAAI,CAACqL,KAAL,CAAW/G,KAAK,CAACP,CAAjB,EAAoBO,KAAK,CAACT,CAA1B,IAA+BpG,IAAI,CAAC6N,gBAAL,CAAsB3G,KAAtB,CAA/C;AACA,iBAAOpH,EAAE,CAACyC,IAAI,CAACuL,GAAL,CAASH,MAAT,IAAmBD,MAApB,EAA4BnL,IAAI,CAAC6E,GAAL,CAASuG,MAAT,IAAmBD,MAA/C,CAAT;AACH;AAED;AACJ;AACA;AACA;;;AACIK,QAAAA,iBAAiB,CAACpK,KAAD,EAAgB;AAC7B,cAAI,CAACA,KAAL,EAAY;AACZA,UAAAA,KAAK,CAACY,OAAN,CAAe+G,IAAD,IAAU;AACpB,gBAAIA,IAAJ,EAAUA,IAAI,CAACS,OAAL;AACb,WAFD;AAGApI,UAAAA,KAAK,CAACE,MAAN,GAAe,CAAf;AACH;AAED;AACJ;AACA;AACA;;;AACImK,QAAAA,eAAe,CAACrI,GAAD,EAAsB;AACjC,cAAI,CAACA,GAAL,EAAU;AACVA,UAAAA,GAAG,CAACpB,OAAJ,CAAa+G,IAAD,IAAU;AAClB,gBAAIA,IAAJ,EAAUA,IAAI,CAACS,OAAL;AACb,WAFD;AAGApG,UAAAA,GAAG,CAACsI,KAAJ;AACH;AAED;AACJ;AACA;AACA;;;AACIC,QAAAA,eAAe,GAAW;AACtB,cAAM1N,OAAO,GAAG,gEAAhB;AACA,cAAI2N,MAAM,GAAG,EAAb;;AACA,eAAK,IAAI9J,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAG,EAApB,EAAwBA,CAAC,EAAzB,EAA6B;AACzB,gBAAM+J,WAAW,GAAG,KAAK9K,UAAL,CAAgB,CAAhB,EAAmB9C,OAAO,CAACqD,MAAR,GAAiB,CAApC,CAApB;AACAsK,YAAAA,MAAM,IAAI3N,OAAO,CAAC4N,WAAD,CAAjB;AACH;;AACD,iBAAOD,MAAP;AACH;AAED;AACJ;AACA;AACA;;;AACIE,QAAAA,aAAa,GAAW;AACpB,cAAM7N,OAAO,GAAG,gEAAhB;AACA,cAAI8N,IAAI,GAAG,EAAX;;AACA,eAAK,IAAIjK,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAG,EAApB,EAAwBA,CAAC,EAAzB,EAA6B;AACzB,gBAAM+J,WAAW,GAAG,KAAK9K,UAAL,CAAgB,CAAhB,EAAmB9C,OAAO,CAACqD,MAAR,GAAiB,CAApC,CAApB;AACAyK,YAAAA,IAAI,IAAI9N,OAAO,CAAC4N,WAAD,CAAf;AACH;;AACD,iBAAOE,IAAP;AACH;AAED;AACJ;AACA;AACA;AACA;AACA;;;AACIC,QAAAA,eAAe,CAACC,QAAD,EAAmBC,QAAnB,EAA6C;AACxD,cAAID,QAAQ,KAAKC,QAAjB,EAA2B,OAAO,CAAP;AAC3B,cAAMC,OAAO,GAAGF,QAAQ,CAACnJ,KAAT,CAAe,GAAf,CAAhB;AACA,cAAMsJ,OAAO,GAAGF,QAAQ,CAACpJ,KAAT,CAAe,GAAf,CAAhB;AACA,cAAMuJ,SAAS,GAAGrM,IAAI,CAACgB,GAAL,CAASmL,OAAO,CAAC7K,MAAjB,EAAyB8K,OAAO,CAAC9K,MAAjC,CAAlB;;AAEA,eAAK,IAAIQ,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAGuK,SAApB,EAA+BvK,CAAC,EAAhC,EAAoC;AAChC,gBAAMwK,EAAE,GAAGC,QAAQ,CAACJ,OAAO,CAACrK,CAAD,CAAR,EAAa,EAAb,CAAnB;;AACA,gBAAMvE,EAAE,GAAGgP,QAAQ,CAACH,OAAO,CAACtK,CAAD,CAAR,EAAa,EAAb,CAAnB;;AACA,gBAAIwK,EAAE,GAAG/O,EAAT,EAAa,OAAO,CAAP;AACb,gBAAI+O,EAAE,GAAG/O,EAAT,EAAa,OAAO,CAAC,CAAR;AAChB;;AAED,iBAAO4O,OAAO,CAAC7K,MAAR,GAAiB8K,OAAO,CAAC9K,MAAzB,GAAkC,CAAlC,GAAsC,CAAC,CAA9C;AACH;AAGD;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;;;AACIkL,QAAAA,cAAc,CAACC,EAAD,EAAaC,EAAb,EAAyBC,EAAzB,EAAqCC,EAArC,EAAyD;AACnE,cAAMC,MAAM,GAAGtP,EAAE,CAACoP,EAAD,EAAKC,EAAL,CAAF,CAAWxI,QAAX,CAAoB7G,EAAE,CAACkP,EAAD,EAAKC,EAAL,CAAtB,CAAf;AACA,cAAMI,SAAS,GAAGvP,EAAE,CAAC,CAAD,EAAI,CAAC,CAAL,CAApB,CAFmE,CAEtC;;AAC7B,cAAMoH,KAAK,GAAGkI,MAAM,CAACjI,SAAP,CAAiBkI,SAAjB,CAAd;AACA,iBAAOrP,IAAI,CAACwN,gBAAL,CAAsBtG,KAAtB,CAAP;AACH;AAED;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;;;AACIoI,QAAAA,MAAM,CAACN,EAAD,EAAaC,EAAb,EAAyBC,EAAzB,EAAqCC,EAArC,EAAuD;AACzD,iBAAOrP,EAAE,CAACoP,EAAD,EAAKC,EAAL,CAAF,CAAWxI,QAAX,CAAoB7G,EAAE,CAACkP,EAAD,EAAKC,EAAL,CAAtB,EAAgChC,SAAhC,EAAP;AACH;AAED;AACJ;AACA;AACA;AACA;;;AACIsC,QAAAA,eAAe,CAACC,GAAD,EAAoB;AAC/B,cAAMH,SAAS,GAAGvP,EAAE,CAAC,CAAD,EAAI,CAAC,CAAL,CAApB,CAD+B,CACF;;AAC7B,cAAI0P,GAAG,CAACC,MAAJ,CAAWJ,SAAX,KAAyBG,GAAG,CAACC,MAAJ,CAAW1P,IAAI,CAACyF,IAAhB,CAA7B,EAAoD;AAChD,mBAAO,CAAP;AACH;;AACD,cAAM0B,KAAK,GAAGsI,GAAG,CAACrI,SAAJ,CAAckI,SAAd,CAAd;AACA,iBAAOrP,IAAI,CAACwN,gBAAL,CAAsBtG,KAAtB,CAAP;AACH;AAED;AACJ;AACA;AACA;AACA;;;AACIwI,QAAAA,SAAS,CAACC,OAAD,EAA0B;AAC/B,iBAAQA,OAAO,GAAG,GAAX,GAAkBpN,IAAI,CAACqN,EAA9B;AACH;AAED;AACJ;AACA;AACA;AACA;;;AACIC,QAAAA,aAAa,CAAC3I,KAAD,EAAwB;AACjC,cAAIA,KAAK,GAAG,CAAZ,EAAe;AACXA,YAAAA,KAAK,IAAI,MAAM3E,IAAI,CAACC,KAAL,CAAW0E,KAAK,GAAG,GAAnB,CAAf;AACH,WAFD,MAEO,IAAIA,KAAK,GAAG,CAAZ,EAAe;AAClBA,YAAAA,KAAK,IAAI,MAAM3E,IAAI,CAACgI,IAAL,CAAUhI,IAAI,CAACwG,GAAL,CAAS7B,KAAK,GAAG,GAAjB,CAAV,CAAf;AACH;;AACD,iBAAOA,KAAP;AACH;AAED;AACJ;AACA;AACA;AACA;;;AACI4I,QAAAA,mBAAmB,CAAC5I,KAAD,EAAwB;AACvC,cAAIA,KAAK,GAAG,GAAZ,EAAiB;AACbA,YAAAA,KAAK,IAAI,MAAM3E,IAAI,CAACgI,IAAL,CAAUrD,KAAK,GAAG,GAAlB,CAAf;AACH,WAFD,MAEO,IAAIA,KAAK,GAAG,CAAC,GAAb,EAAkB;AACrBA,YAAAA,KAAK,IAAI,MAAM3E,IAAI,CAACgI,IAAL,CAAUhI,IAAI,CAACwG,GAAL,CAAS7B,KAAK,GAAG,GAAjB,CAAV,CAAf;AACH;;AACD,iBAAOA,KAAP;AACH;AAED;AACJ;AACA;AACA;AACA;;;AACI6I,QAAAA,cAAc,CAAC7I,KAAD,EAAwB;AAClC,cAAIA,KAAK,GAAG,GAAZ,EAAiB;AACbA,YAAAA,KAAK,IAAI,MAAM3E,IAAI,CAACgI,IAAL,CAAUrD,KAAK,GAAG,GAAlB,CAAf;;AACA,gBAAIA,KAAK,GAAG,CAAC,GAAb,EAAkB;AACdA,cAAAA,KAAK,IAAI,GAAT;AACH;AACJ,WALD,MAKO,IAAIA,KAAK,GAAG,CAAC,GAAb,EAAkB;AACrBA,YAAAA,KAAK,IAAI,MAAM3E,IAAI,CAACgI,IAAL,CAAUhI,IAAI,CAACwG,GAAL,CAAS7B,KAAK,GAAG,GAAjB,CAAV,CAAf;;AACA,gBAAIA,KAAK,GAAG,GAAZ,EAAiB;AACbA,cAAAA,KAAK,IAAI,GAAT;AACH;AACJ;;AACD,iBAAOA,KAAP;AACH;AAED;AACJ;AACA;AACA;AACA;AACA;AACA;;;AACI8I,QAAAA,OAAO,CAAIrM,KAAJ,EAAgBsM,MAAhB,EAAgCC,IAAhC,EAA4D;AAAA,cAA5BA,IAA4B;AAA5BA,YAAAA,IAA4B,GAAZ,KAAY;AAAA;;AAC/D,cAAMrM,MAAM,GAAGF,KAAK,CAACE,MAArB;;AACA,cAAIqM,IAAJ,EAAU;AACNvM,YAAAA,KAAK,GAAG,KAAKyF,SAAL,CAAezF,KAAf,CAAR;AACH;;AACDsM,UAAAA,MAAM,IAAIpM,MAAV;;AACA,cAAIoM,MAAM,GAAG,CAAb,EAAgB;AACZA,YAAAA,MAAM,GAAGpM,MAAM,GAAGtB,IAAI,CAACwG,GAAL,CAASkH,MAAT,CAAlB;AACH;;AACDtM,UAAAA,KAAK,GAAG,KAAKwM,OAAL,CAAaxM,KAAb,EAAoB,CAApB,EAAuBE,MAAM,GAAGoM,MAAT,GAAkB,CAAzC,CAAR;AACAtM,UAAAA,KAAK,GAAG,KAAKwM,OAAL,CAAaxM,KAAb,EAAoBE,MAAM,GAAGoM,MAA7B,EAAqCpM,MAAM,GAAG,CAA9C,CAAR;AACA,iBAAO,KAAKsM,OAAL,CAAaxM,KAAb,EAAoB,CAApB,EAAuBE,MAAM,GAAG,CAAhC,CAAP;AACH;AAED;AACJ;AACA;AACA;AACA;AACA;AACA;;;AACIsM,QAAAA,OAAO,CAAIxM,KAAJ,EAAgBkJ,KAAhB,EAA+BS,GAA/B,EAAiD;AACpD,iBAAOT,KAAK,GAAGS,GAAf,EAAoB;AAChB,gBAAM8C,IAAI,GAAGzM,KAAK,CAACkJ,KAAD,CAAlB;AACAlJ,YAAAA,KAAK,CAACkJ,KAAD,CAAL,GAAelJ,KAAK,CAAC2J,GAAD,CAApB;AACA3J,YAAAA,KAAK,CAAC2J,GAAD,CAAL,GAAa8C,IAAb;AACAvD,YAAAA,KAAK;AACLS,YAAAA,GAAG;AACN;;AACD,iBAAO3J,KAAP;AACH;AAED;AACJ;AACA;AACA;;;AACI0M,QAAAA,iBAAiB,CAAC1M,KAAD,EAAqB;AAClC,cAAI,CAACA,KAAL,EAAY;AACZA,UAAAA,KAAK,CAACY,OAAN,CAAe+L,IAAD,IAAU;AACpB,gBAAIA,IAAI,IAAIA,IAAI,CAAChF,IAAjB,EAAuB;AACnBgF,cAAAA,IAAI,CAAChF,IAAL,CAAUS,OAAV;AACH;AACJ,WAJD;AAKApI,UAAAA,KAAK,CAACK,MAAN,CAAa,CAAb;AACH;AAED;AACJ;AACA;AACA;;;AACIuM,QAAAA,kBAAkB,CAAC5K,GAAD,EAAwB;AACtC,cAAI,CAACA,GAAL,EAAU;AACVA,UAAAA,GAAG,CAACpB,OAAJ,CAAaiM,SAAD,IAAe;AACvBA,YAAAA,SAAS,CAACjM,OAAV,CAAmB+G,IAAD,IAAU;AACxB,kBAAIA,IAAJ,EAAUA,IAAI,CAACS,OAAL;AACb,aAFD;AAGH,WAJD;AAKApG,UAAAA,GAAG,CAACsI,KAAJ;AACH;AAED;AACJ;AACA;AACA;;;AACIwC,QAAAA,eAAe,CAAC9K,GAAD,EAA2B;AACtC,cAAI,CAACA,GAAL,EAAU;AACVA,UAAAA,GAAG,CAACpB,OAAJ,CAAa+L,IAAD,IAAU;AAClB,gBAAIA,IAAI,IAAIA,IAAI,CAAChF,IAAjB,EAAuB;AACnBgF,cAAAA,IAAI,CAAChF,IAAL,CAAUS,OAAV;AACH;AACJ,WAJD;AAKApG,UAAAA,GAAG,CAACsI,KAAJ;AACH;AAED;AACJ;AACA;AACA;;;AACIyC,QAAAA,kBAAkB,CAAC/K,GAAD,EAA6B;AAC3C,cAAI,CAACA,GAAL,EAAU;AACVA,UAAAA,GAAG,CAACpB,OAAJ,CAAaoM,SAAD,IAAe;AACvBA,YAAAA,SAAS,CAACpM,OAAV,CAAmB+L,IAAD,IAAU;AACxB,kBAAIA,IAAI,IAAIA,IAAI,CAAChF,IAAjB,EAAuB;AACnBgF,gBAAAA,IAAI,CAAChF,IAAL,CAAUS,OAAV;AACH;AACJ,aAJD;AAKH,WAND;AAOApG,UAAAA,GAAG,CAACsI,KAAJ;AACH;AAED;AACJ;AACA;AACA;;;AACI2C,QAAAA,aAAa,CAACC,IAAD,EAAiB;AAC1B,cAAI,CAACA,IAAL,EAAW;;AACX,iBAAOA,IAAI,CAAC3Q,IAAL,KAAc,CAArB,EAAwB;AACpB,gBAAMoL,IAAI,GAAGuF,IAAI,CAACC,GAAL,EAAb;AACA,gBAAIxF,IAAJ,EAAUA,IAAI,CAACS,OAAL;AACb;;AACD8E,UAAAA,IAAI,CAAC5C,KAAL;AACH;;AA5wCS,O;;uBA+wCD8C,K,GAAQ,IAAIlR,OAAJ,E", "sourcesContent": ["import { Component, NodePool, v2, Node, Vec2, misc, Size, size, Sprite, sp, resources, SpriteFrame } from 'cc';\r\nimport { GameConst } from '../const/GameConst';\r\n\r\nclass DYTools {\r\n    _btnAble: boolean = true;\r\n    charArr: string[] = [\r\n        \"0\", \"1\", \"2\", \"3\", \"4\", \"5\", \"6\", \"7\", \"8\", \"9\",\r\n        \"a\", \"b\", \"c\", \"d\", \"e\", \"f\", \"g\", \"h\", \"i\", \"j\",\r\n        \"k\", \"l\", \"m\", \"n\", \"o\", \"p\", \"q\", \"r\", \"s\", \"t\",\r\n        \"u\", \"v\", \"w\", \"x\", \"y\", \"z\", \"A\", \"B\", \"C\", \"D\",\r\n        \"E\", \"F\", \"G\", \"H\", \"I\", \"J\", \"K\", \"L\", \"M\", \"N\",\r\n        \"O\", \"P\", \"Q\", \"R\", \"S\", \"T\", \"U\", \"V\", \"W\", \"X\",\r\n        \"Y\", \"Z\"\r\n    ];\r\n\r\n    /**\r\n     * 获取当前时间戳（毫秒）\r\n     */\r\n    getPhoneTimestamp(): number {\r\n        const date = new Date();\r\n        return Date.parse(date.toString());\r\n    }\r\n\r\n    /**\r\n     * 获取当前游戏时间戳（秒）\r\n     */\r\n    getCurTimestamp(): number {\r\n        // return Math.floor(GameData.timestamp);\r\n        return Date.now() / 1000;\r\n    }\r\n\r\n    /**\r\n     * 检查日期 t 是否在日期 o 之后\r\n     */\r\n    isAfterDay(t: number, o: number): boolean {\r\n        const date1 = new Date(t);\r\n        const year1 = date1.getFullYear();\r\n        const month1 = date1.getMonth();\r\n        const day1 = date1.getDate();\r\n\r\n        const date2 = new Date(o);\r\n        const year2 = date2.getFullYear();\r\n        const month2 = date2.getMonth();\r\n        const day2 = date2.getDate();\r\n\r\n        return year1 < year2 || (year1 === year2 && (month1 < month2 || (month1 === month2 && day1 < day2)));\r\n    }\r\n\r\n    /**\r\n     * 检查是否是更晚的一天\r\n     */\r\n    isLaterDay(timestamp: number): boolean {\r\n        const currentDate = new Date(this.getCurTimestamp());\r\n        const targetDate = new Date(timestamp);\r\n        return (\r\n            targetDate.getFullYear() < currentDate.getFullYear() ||\r\n            (targetDate.getFullYear() === currentDate.getFullYear() &&\r\n                (targetDate.getMonth() < currentDate.getMonth() ||\r\n                    (targetDate.getMonth() === currentDate.getMonth() &&\r\n                        targetDate.getDate() < currentDate.getDate())))\r\n        );\r\n    }\r\n\r\n    /**\r\n     * 检查是否需要刷新到第二天\r\n     */\r\n    isLaterDayRefresh(timestamp: number, refreshHour: number): boolean {\r\n        const currentTimestamp = this.getCurTimestamp();\r\n        const diff = currentTimestamp - timestamp;\r\n\r\n        if (diff > 0) {\r\n            const hoursPassed = Math.floor(diff / 3600000); // 转换为小时\r\n            if (hoursPassed >= 24) {\r\n                return true;\r\n            }\r\n\r\n            const lastDate = new Date(timestamp);\r\n            const currentDate = new Date(currentTimestamp);\r\n\r\n            if (lastDate.getDate() < currentDate.getDate()) {\r\n                if (refreshHour <= currentDate.getHours()) {\r\n                    return true;\r\n                }\r\n            }\r\n        }\r\n\r\n        return false;\r\n    }\r\n\r\n    /**\r\n     * 获取两个时间戳之间的天数差\r\n     */\r\n    getDiffDays(startTimestamp: number, endTimestamp: number): number {\r\n        if (endTimestamp < startTimestamp) return -1;\r\n        const diff = endTimestamp - startTimestamp;\r\n        return Math.floor(diff / (1000 * 60 * 60 * 24));\r\n    }\r\n\r\n    /**\r\n     * 检查是否是第二天\r\n     */\r\n    isNextDay(timestamp: number): boolean {\r\n        const currentTimestamp = this.getCurTimestamp();\r\n        const nextDayStart = timestamp + 86400 * 1000; // 加一天的时间\r\n        return currentTimestamp >= nextDayStart;\r\n    }\r\n\r\n    /**\r\n     * 打印日志\r\n     */\r\n    log(message: string, ...args: any[]) {\r\n        console.log(message, ...args);\r\n    }\r\n\r\n    /**\r\n     * 打印错误日志\r\n     */\r\n    error(message: string, ...args: any[]) {\r\n        console.error(message, ...args);\r\n    }\r\n\r\n    /**\r\n     * 打印警告日志\r\n     */\r\n    warn(message: string, ...args: any[]) {\r\n        console.warn(message, ...args);\r\n    }\r\n\r\n    /**\r\n     * 生成随机整数\r\n     */\r\n    random_int(min: number, max: number): number {\r\n        const random = Math.floor(Math.random() * (max - min + 1)) + min;\r\n        return Math.max(min, Math.min(max, random));\r\n    }\r\n\r\n    /**\r\n     * 从数组中随机获取一个元素\r\n     */\r\n    getRandomInArray<T>(array: T[], remove: boolean = false): T | null {\r\n        const length = array.length;\r\n        if (length === 0) return null;\r\n\r\n        const index = this.random_int(0, length - 1);\r\n        const element = array[index];\r\n\r\n        if (remove) {\r\n            array.splice(index, 1);\r\n        }\r\n\r\n        return element;\r\n    }\r\n\r\n    /**\r\n     * 从数组中随机获取多个元素\r\n     */\r\n    getRandomCountInArray<T>(array: T[], count: number, remove: boolean = false): T[] {\r\n        const result: T[] = [];\r\n        const tempArray = [...array];\r\n\r\n        for (let i = 0; i < count; i++) {\r\n            const element = this.getRandomInArray(tempArray, true);\r\n            if (element !== null) {\r\n                result.push(element);\r\n            }\r\n        }\r\n\r\n        if (remove) {\r\n            result.forEach((item) => {\r\n                const index = array.indexOf(item);\r\n                if (index >= 0) {\r\n                    array.splice(index, 1);\r\n                }\r\n            });\r\n        }\r\n\r\n        return result;\r\n    }\r\n\r\n\r\n    /**\r\n     * 在数组的指定位置插入元素\r\n     * @param array 原数组\r\n     * @param index 插入位置\r\n     * @param element 插入的元素\r\n     * @returns 插入后的数组\r\n     */\r\n    arrayInsert<T>(array: T[], index: number, element: T): T[] {\r\n        if (index < array.length) {\r\n            const tail = array.splice(index, array.length);\r\n            array.push(element);\r\n            return array.concat(tail);\r\n        } else {\r\n            array.push(element);\r\n            return array;\r\n        }\r\n    }\r\n\r\n    /**\r\n     * 随机插入元素到数组\r\n     * @param array 原数组\r\n     * @param element 插入的元素\r\n     * @returns 插入后的数组\r\n     */\r\n    arrayInsertRandom<T>(array: T[], element: T): T[] {\r\n        const index = this.random_int(0, array.length);\r\n        const tail = array.splice(index, array.length);\r\n        array.push(element);\r\n        return array.concat(tail);\r\n    }\r\n\r\n    /**\r\n     * 获取字典的长度\r\n     * @param dict 字典对象\r\n     * @returns 字典的长度\r\n     */\r\n    getDictLength(dict: Record<string, any>): number {\r\n        let count = 0;\r\n        for (const key in dict) {\r\n            if (dict[key] !== null) {\r\n                count++;\r\n            }\r\n        }\r\n        return count;\r\n    }\r\n\r\n    /**\r\n     * 根据分隔符获取字符串的指定部分\r\n     * @param str 原字符串\r\n     * @param delimiter 分隔符\r\n     * @param index 索引\r\n     * @returns 指定部分的字符串\r\n     */\r\n    getStringDivideForIndex(str: string, delimiter: string, index: number): string | null {\r\n        const parts = str.split(delimiter);\r\n        return index >= 0 && parts.length > index ? parts[index] : null;\r\n    }\r\n\r\n    /**\r\n     * 将字符串转换为 Vec2\r\n     * @param str 原字符串\r\n     * @param delimiter 分隔符\r\n     * @returns Vec2 对象\r\n     */\r\n    stringToPoint(str: string, delimiter: string): Vec2 {\r\n        const parts = str.split(delimiter);\r\n        if (parts.length > 1) {\r\n            return v2(Number(parts[0]), Number(parts[1]));\r\n        }\r\n        return Vec2.ZERO;\r\n    }\r\n\r\n    /**\r\n     * 将字符串转换为 Size\r\n     * @param str 原字符串\r\n     * @param delimiter 分隔符\r\n     * @returns Size 对象\r\n     */\r\n    stringToSize(str: string, delimiter: string): Size {\r\n        const parts = str.split(delimiter);\r\n        if (parts.length > 1) {\r\n            return size(Number(parts[0]), Number(parts[1]));\r\n        }\r\n        return Size.ZERO;\r\n    }\r\n\r\n    /**\r\n     * 将字符串转换为数字数组\r\n     * @param str 原字符串\r\n     * @param delimiter 分隔符\r\n     * @returns 数字数组\r\n     */\r\n    stringToNumber(str: string, delimiter: string): number[] {\r\n        const parts = str.split(delimiter);\r\n        return parts.map((part) => Number(part)).filter((num) => !isNaN(num));\r\n    }\r\n\r\n    /**\r\n     * 获取两点之间的距离\r\n     * @param point1 点1\r\n     * @param point2 点2\r\n     * @returns 距离\r\n     */\r\n    getPosDis(point1: Vec2, point2: Vec2): number {\r\n        const dx = point2.x - point1.x;\r\n        const dy = point2.y - point1.y;\r\n        return Math.sqrt(dx * dx + dy * dy);\r\n    }\r\n\r\n    /**\r\n     * 获取两个 Vec2 的距离\r\n     * @param vec1 Vec2 对象1\r\n     * @param vec2 Vec2 对象2\r\n     * @returns 距离\r\n     */\r\n    getDisForVec2(vec1: Vec2, vec2: Vec2): number {\r\n        return vec1.subtract(vec2).length();\r\n    }\r\n\r\n    /**\r\n     * 获取点到直线的距离\r\n     * @param point 点\r\n     * @param lineStart 直线起点\r\n     * @param lineEnd 直线终点\r\n     * @returns 距离\r\n     */\r\n    getDisFromPointToLine(point: Vec2, lineStart: Vec2, lineEnd: Vec2): number {\r\n        const lineVec = lineEnd.subtract(lineStart);\r\n        const pointVec = point.subtract(lineStart);\r\n        const angle = pointVec.signAngle(lineVec);\r\n        return pointVec.length() * Math.sin(angle);\r\n    }\r\n\r\n    /**\r\n     * 获取点到直线的 Y 偏移\r\n     * @param point 点\r\n     * @param lineStart 直线起点\r\n     * @param lineEnd 直线终点\r\n     * @returns Y 偏移\r\n     */\r\n    getPointToLineY(point: Vec2, lineStart: Vec2, lineEnd: Vec2): number {\r\n        const slope = (lineEnd.y - lineStart.y) / (lineEnd.x - lineStart.x);\r\n        const intercept = lineStart.y - slope * lineStart.x;\r\n        return slope * point.x + intercept - point.y;\r\n    }\r\n\r\n    // /**\r\n    //  * 设置 URL 的 SpriteFrame\r\n    //  * @param url 图片 URL\r\n    //  * @param sprite 目标 Sprite\r\n    //  * @param callback 回调函数\r\n    //  */\r\n    // setUrlFrame(url: string, sprite: Sprite, callback?: () => void) {\r\n    //     if (!url || !sprite) return;\r\n    //     resources.load({ url, type: 'png' }, (err, texture) => {\r\n    //         if (err) {\r\n    //             this.error(err.message || err);\r\n    //             return;\r\n    //         }\r\n    //         const frame = new SpriteFrame(texture);\r\n    //         sprite.spriteFrame = frame;\r\n    //         if (callback) callback();\r\n    //     });\r\n    // }\r\n\r\n    // /**\r\n    //  * 加载网络上的 Spine 动画\r\n    //  * @param url 动画的 URL\r\n    //  * @param callback 加载完成后的回调函数\r\n    //  */\r\n    // loadNetSpine(url: string, callback: (data: any) => void) {\r\n    //     const xhr = loader.getXMLHttpRequest();\r\n    //     xhr.open(\"GET\", url, true);\r\n    //     xhr.timeout = 5000;\r\n\r\n    //     xhr.ontimeout = () => {\r\n    //         console.error(\"Request timeout\");\r\n    //     };\r\n\r\n    //     xhr.onreadystatechange = () => {\r\n    //         if (xhr.readyState === 4 && xhr.status === 200) {\r\n    //             callback(xhr.responseText);\r\n    //         }\r\n    //     };\r\n\r\n    //     xhr.send();\r\n    // }\r\n\r\n    /**\r\n     * 加载本地资源的 SpriteFrame\r\n     * @param path 资源路径\r\n     * @param sprite 目标 Sprite\r\n     * @param callback 加载完成后的回调函数\r\n     */\r\n    loadResSprite(path: string, sprite: Sprite, callback?: (spriteFrame: SpriteFrame) => void) {\r\n        resources.load(path + \"/spriteFrame\", SpriteFrame, (err, spriteFrame) => {\r\n            if (err) {\r\n                console.error(err);\r\n            } else {\r\n                if (sprite) {\r\n                    sprite.spriteFrame = spriteFrame;\r\n                }\r\n                if (callback) {\r\n                    callback(spriteFrame);\r\n                }\r\n            }\r\n        });\r\n    }\r\n\r\n    /**\r\n     * 加载本地资源的 Spine 动画\r\n     * @param path 资源路径\r\n     * @param skeleton 目标 Skeleton\r\n     * @param onSuccess 成功回调\r\n     * @param onError 错误回调\r\n     */\r\n    loadSkel(path: string, skeleton: sp.Skeleton, onSuccess?: (skeletonData: sp.SkeletonData) => void, onError?: () => void) {\r\n        resources.load(path, sp.SkeletonData, (err, skeletonData) => {\r\n            if (err) {\r\n                console.error(err);\r\n                if (onError) {\r\n                    onError();\r\n                }\r\n            } else {\r\n                if (skeleton) {\r\n                    skeleton.skeletonData = skeletonData;\r\n                    skeleton.premultipliedAlpha = false;\r\n                }\r\n                if (onSuccess) {\r\n                    onSuccess(skeletonData);\r\n                }\r\n            }\r\n        });\r\n    }\r\n\r\n    /**\r\n     * 获取限制长度的字符串\r\n     * @param str 原字符串\r\n     * @param maxLength 最大长度\r\n     * @returns 限制后的字符串\r\n     */\r\n    getLimitStr(str: string, maxLength: number): string {\r\n        if (this.getStringCharacterLength(str) > maxLength) {\r\n            return this.subStrByCharacter(str, maxLength) + \"...\";\r\n        }\r\n        return str;\r\n    }\r\n\r\n    /**\r\n     * 获取字符串的字符长度（区分中英文）\r\n     * @param str 原字符串\r\n     * @returns 字符长度\r\n     */\r\n    getStringCharacterLength(str: string): number {\r\n        let length = 0;\r\n        for (let i = 0; i < str.length; i++) {\r\n            const charCode = str.charCodeAt(i);\r\n            length += charCode >= 0 && charCode <= 128 ? 1 : 2;\r\n        }\r\n        return length;\r\n    }\r\n\r\n    /**\r\n     * 按字符长度截取字符串\r\n     * @param str 原字符串\r\n     * @param maxLength 最大长度\r\n     * @returns 截取后的字符串\r\n     */\r\n    subStrByCharacter(str: string, maxLength: number): string {\r\n        let length = 0;\r\n        let result = \"\";\r\n        for (let i = 0; i < str.length; i++) {\r\n            const charCode = str.charCodeAt(i);\r\n            length += charCode >= 0 && charCode <= 128 ? 1 : 2;\r\n            if (length > maxLength) {\r\n                break;\r\n            }\r\n            result += str[i];\r\n        }\r\n        return result;\r\n    }\r\n\r\n    /**\r\n     * 计算两个数字的绝对差值\r\n     * @param num1 数字1\r\n     * @param num2 数字2\r\n     * @returns 绝对差值\r\n     */\r\n    numberDiffAbs(num1: number, num2: number): number {\r\n        return Math.abs(num1 - num2);\r\n    }\r\n\r\n    /**\r\n     * 计算两个数字的差值\r\n     * @param num1 数字1\r\n     * @param num2 数字2\r\n     * @returns 差值\r\n     */\r\n    numberDiff(num1: number, num2: number): number {\r\n        return num1 - num2;\r\n    }\r\n\r\n    /**\r\n     * 判断两个数字的差值是否在范围内\r\n     * @param num1 数字1\r\n     * @param num2 数字2\r\n     * @param range 范围\r\n     * @returns 是否在范围内\r\n     */\r\n    isNumberDiffRange(num1: number, num2: number, range: number): boolean {\r\n        return this.numberDiffAbs(num1, num2) <= range;\r\n    }\r\n\r\n    /**\r\n     * 判断两个 Vec2 的差值是否在范围内\r\n     * @param vec1 Vec2 对象1\r\n     * @param vec2 Vec2 对象2\r\n     * @param range 范围\r\n     * @returns 是否在范围内\r\n     */\r\n    isVec2DiffRange(vec1: Vec2, vec2: Vec2, range: number): boolean {\r\n        const diff = vec1.subtract(vec2);\r\n        return Math.abs(diff.x) <= range && Math.abs(diff.y) <= range;\r\n    }\r\n\r\n    /**\r\n     * 复制数组\r\n     */\r\n    copyArray<T>(array: T[]): T[] {\r\n        return [...array];\r\n    }\r\n\r\n    // /**\r\n    //  * 加密字符串\r\n    //  */\r\n    // encode(data: string): string {\r\n    //     const key = CryptoJS.enc.Latin1.parse(\"mdy8155zs2619lfm\");\r\n    //     const iv = CryptoJS.enc.Latin1.parse(\"s9mnbdk1giu2e3wn\");\r\n    //     return CryptoJS.AES.encrypt(data, key, {\r\n    //         iv: iv,\r\n    //         mode: CryptoJS.mode.CBC,\r\n    //         padding: CryptoJS.pad.ZeroPadding\r\n    //     }).toString();\r\n    // }\r\n\r\n    // /**\r\n    //  * 解密字符串\r\n    //  */\r\n    // decode(data: string): string {\r\n    //     const key = CryptoJS.enc.Latin1.parse(\"mdy8155zs2619lfm\");\r\n    //     const iv = CryptoJS.enc.Latin1.parse(\"s9mnbdk1giu2e3wn\");\r\n    //     const decrypted = CryptoJS.AES.decrypt(data, key, {\r\n    //         iv: iv,\r\n    //         mode: CryptoJS.mode.CBC,\r\n    //         padding: CryptoJS.pad.ZeroPadding\r\n    //     });\r\n    //     return CryptoJS.enc.Utf8.stringify(decrypted);\r\n    // }\r\n\r\n    /**\r\n     * 将数字转换为字符串（简化为千位或百万单位）\r\n     * @param num 数字\r\n     * @returns 转换后的字符串\r\n     */\r\n    numberToString3(num: number): string {\r\n        let result = \"\";\r\n        let suffix = \"\";\r\n\r\n        if (num < 1000) {\r\n            result = num.toString();\r\n        } else if (num >= 1000000) {\r\n            result = Math.floor(num / 1000000).toString();\r\n            suffix = \"m\";\r\n        } else if (num >= 1000) {\r\n            result = Math.floor(num / 1000).toString();\r\n            suffix = \"k\";\r\n        }\r\n\r\n        return result + suffix;\r\n    }\r\n\r\n    /**\r\n     * 将数字转换为字符串（保留一位小数，简化为千位或百万单位）\r\n     * @param num 数字\r\n     * @returns 转换后的字符串\r\n     */\r\n    numberToString2(num: number): string {\r\n        let result = \"\";\r\n        let suffix = \"\";\r\n\r\n        if (num < 1000) {\r\n            result = num.toString();\r\n        } else if (num >= 1000000) {\r\n            const millions = Math.floor(num / 1000000);\r\n            const remainder = Math.floor((num % 1000000) / 100000);\r\n            result = millions + \".\" + remainder;\r\n            suffix = \"m\";\r\n        } else if (num >= 1000) {\r\n            const thousands = Math.floor(num / 1000);\r\n            const remainder = Math.floor((num % 1000) / 100);\r\n            result = thousands + \".\" + remainder;\r\n            suffix = \"k\";\r\n        }\r\n\r\n        return result + suffix;\r\n    }\r\n\r\n    /**\r\n     * 将数字转换为字符串（保留两位小数，简化为千位或百万单位）\r\n     * @param num 数字\r\n     * @returns 转换后的字符串\r\n     */\r\n    numberToString(num: number): string {\r\n        let result = \"\";\r\n        let suffix = \"\";\r\n\r\n        if (num < 1000) {\r\n            result = num.toString();\r\n        } else if (num >= 1000000) {\r\n            const millions = Math.floor(num / 1000000);\r\n            const remainder1 = Math.floor((num % 1000000) / 100000);\r\n            const remainder2 = Math.floor((num % 100000) / 10000);\r\n            result = millions + \".\" + remainder1 + remainder2;\r\n            suffix = \"m\";\r\n        } else if (num >= 1000) {\r\n            const thousands = Math.floor(num / 1000);\r\n            const remainder1 = Math.floor((num % 1000) / 100);\r\n            const remainder2 = Math.floor((num % 100) / 10);\r\n            result = thousands + \".\" + remainder1 + remainder2;\r\n            suffix = \"k\";\r\n        }\r\n\r\n        return result + suffix;\r\n    }\r\n\r\n    /**\r\n     * 将秒数转换为 [小时, 分钟, 秒] 的数组\r\n     * @param seconds 秒数\r\n     * @returns [小时, 分钟, 秒]\r\n     */\r\n    getTime(seconds: number): number[] {\r\n        const hours = Math.floor(seconds / 3600);\r\n        const minutes = Math.floor((seconds % 3600) / 60);\r\n        const secs = Math.floor(seconds % 60);\r\n        return [hours, minutes, secs];\r\n    }\r\n\r\n    /**\r\n     * 将秒数转换为时间字符串（格式：HH:MM:SS 或 MM:SS）\r\n     * @param seconds 秒数\r\n     * @returns 时间字符串\r\n     */\r\n    getTimeSecondStr(seconds: number): string {\r\n        if (seconds < 60) {\r\n            return `00:${this.getTimestrDouble(seconds)}`;\r\n        } else if (seconds < 3600) {\r\n            const minutes = Math.floor(seconds / 60);\r\n            const secs = Math.floor(seconds % 60);\r\n            return `${this.getTimestrDouble(minutes)}:${this.getTimestrDouble(secs)}`;\r\n        } else {\r\n            const hours = Math.floor(seconds / 3600);\r\n            const remainder = seconds % 3600;\r\n            const minutes = Math.floor(remainder / 60);\r\n            const secs = Math.floor(remainder % 60);\r\n            return `${this.getTimestrDouble(hours)}:${this.getTimestrDouble(minutes)}:${this.getTimestrDouble(secs)}`;\r\n        }\r\n    }\r\n\r\n    /**\r\n     * 将秒数转换为时间字符串（格式：HH:MM:SS，始终包含小时）\r\n     * @param seconds 秒数\r\n     * @returns 时间字符串\r\n     */\r\n    getTimeSecondStr2(seconds: number): string {\r\n        const hours = Math.floor(seconds / 3600);\r\n        const remainder = seconds % 3600;\r\n        const minutes = Math.floor(remainder / 60);\r\n        const secs = Math.floor(remainder % 60);\r\n        return `${this.getTimestrDouble(hours)}:${this.getTimestrDouble(minutes)}:${this.getTimestrDouble(secs)}`;\r\n    }\r\n\r\n    /**\r\n     * 将数字转换为两位字符串（不足两位补 0）\r\n     * @param num 数字\r\n     * @returns 两位字符串\r\n     */\r\n    getTimestrDouble(num: number): string {\r\n        return num < 10 ? `0${num}` : `${num}`;\r\n    }\r\n\r\n    /**\r\n     * 将秒数转换为简短的时间字符串（如 \"1h30m\" 或 \"30m\"）\r\n     * @param seconds 秒数\r\n     * @returns 时间字符串\r\n     */\r\n    getTimeWord(seconds: number): string {\r\n        let result = \"\";\r\n        seconds = Math.ceil(seconds);\r\n\r\n        if (seconds < 60) {\r\n            result = `${seconds}s`;\r\n        } else if (seconds < 3600) {\r\n            const minutes = Math.floor(seconds / 60);\r\n            const remainingSeconds = Math.floor(seconds % 60);\r\n            result = `${minutes}m`;\r\n            if (remainingSeconds > 0) {\r\n                result += `${remainingSeconds}s`;\r\n            }\r\n        } else if (seconds < 86400) {\r\n            const hours = Math.floor(seconds / 3600);\r\n            const remainingSeconds = seconds % 3600;\r\n            const minutes = Math.floor(remainingSeconds / 60);\r\n            result = `${hours}h`;\r\n            if (minutes > 0) {\r\n                result += `${minutes}m`;\r\n            }\r\n        } else {\r\n            const days = Math.floor(seconds / 86400);\r\n            const remainingSeconds = seconds % 86400;\r\n            const hours = Math.floor(remainingSeconds / 3600);\r\n            result = `${days}d`;\r\n            if (hours > 0) {\r\n                result += `${hours}h`;\r\n            }\r\n        }\r\n\r\n        return result;\r\n    }\r\n\r\n    /**\r\n     * 将秒数转换为简短的时间字符串（如 \"1小时30分\" 或 \"30秒\"）\r\n     * @param seconds 秒数\r\n     * @returns 时间字符串\r\n     */\r\n    getTimeWord1(seconds: number): string {\r\n        let result = \"\";\r\n        seconds = Math.ceil(seconds);\r\n\r\n        if (seconds < 60) {\r\n            result = `${seconds}秒`;\r\n        } else if (seconds < 3600) {\r\n            const minutes = Math.floor(seconds / 60);\r\n            const remainingSeconds = Math.floor(seconds % 60);\r\n            result = `${minutes}分`;\r\n            if (remainingSeconds > 0) {\r\n                result += `${remainingSeconds}秒`;\r\n            }\r\n        } else if (seconds < 86400) {\r\n            const hours = Math.floor(seconds / 3600);\r\n            const remainingSeconds = seconds % 3600;\r\n            const minutes = Math.floor(remainingSeconds / 60);\r\n            result = `${hours}小时`;\r\n            if (minutes > 0) {\r\n                result += `${minutes}分`;\r\n            }\r\n        } else {\r\n            const days = Math.floor(seconds / 86400);\r\n            const remainingSeconds = seconds % 86400;\r\n            const hours = Math.floor(remainingSeconds / 3600);\r\n            result = `${days}天`;\r\n            if (hours > 0) {\r\n                result += `${hours}小时`;\r\n            }\r\n        }\r\n\r\n        return result;\r\n    }\r\n\r\n    /**\r\n     * 将秒数转换为简短的时间字符串（如 \"1h30m\" 或 \"30m\"）\r\n     * @param seconds 秒数\r\n     * @returns 时间字符串\r\n     */\r\n    getTimeWord2(seconds: number): string {\r\n        let result = \"\";\r\n        seconds = Math.ceil(seconds);\r\n\r\n        if (seconds < 60) {\r\n            result = `${seconds}s`;\r\n        } else if (seconds < 3600) {\r\n            const minutes = Math.floor(seconds / 60);\r\n            result = `${minutes}m`;\r\n        } else if (seconds < 86400) {\r\n            const hours = Math.floor(seconds / 3600);\r\n            const remainingSeconds = seconds % 3600;\r\n            const minutes = Math.floor(remainingSeconds / 60);\r\n            result = `${hours}h`;\r\n            if (minutes > 0) {\r\n                result += `${minutes}m`;\r\n            }\r\n        } else {\r\n            const days = Math.floor(seconds / 86400);\r\n            const remainingSeconds = seconds % 86400;\r\n            const hours = Math.floor(remainingSeconds / 3600);\r\n            result = `${days}d`;\r\n            if (hours > 0) {\r\n                result += `${hours}h`;\r\n            }\r\n        }\r\n\r\n        return result;\r\n    }\r\n\r\n    /**\r\n     * 将秒数转换为简短的时间字符串（如 \"1天2小时30分\" 或 \"30秒\"）\r\n     * @param seconds 秒数\r\n     * @returns 时间字符串\r\n     */\r\n    getTimeWord3(seconds: number): string {\r\n        let result = \"\";\r\n        seconds = Math.ceil(seconds);\r\n\r\n        if (seconds < 3600) {\r\n            const minutes = Math.floor(seconds / 60);\r\n            const remainingSeconds = Math.floor(seconds % 60);\r\n            result = `${minutes}m${remainingSeconds}s`;\r\n        } else if (seconds < 86400) {\r\n            const hours = Math.floor(seconds / 3600);\r\n            const remainingSeconds = seconds % 3600;\r\n            const minutes = Math.floor(remainingSeconds / 60);\r\n            result = `${hours}h${minutes}m`;\r\n        } else {\r\n            const days = Math.floor(seconds / 86400);\r\n            const remainingSeconds = seconds % 86400;\r\n            const hours = Math.ceil(remainingSeconds / 3600);\r\n            result = `${days}d${hours}h`;\r\n        }\r\n\r\n        return result;\r\n    }\r\n    /**\r\n* 将秒数转换为简短的时间字符串（如 \"1小时30分\" 或 \"30秒\"）\r\n* @param seconds 秒数\r\n* @returns 时间字符串\r\n*/\r\n    getTimeWord4(seconds: number): string {\r\n        let result = \"\";\r\n        seconds = Math.ceil(seconds);\r\n\r\n        if (seconds < 3600) {\r\n            const minutes = Math.floor(seconds / 60);\r\n            const remainingSeconds = Math.floor(seconds % 60);\r\n            result = `${minutes}分${remainingSeconds}秒`;\r\n        } else if (seconds < 86400) {\r\n            const hours = Math.floor(seconds / 3600);\r\n            const remainingSeconds = seconds % 3600;\r\n            const minutes = Math.floor(remainingSeconds / 60);\r\n            result = `${hours}小时${minutes}分`;\r\n        } else {\r\n            const days = Math.floor(seconds / 86400);\r\n            const remainingSeconds = seconds % 86400;\r\n            const hours = Math.floor(remainingSeconds / 3600);\r\n            result = `${days}天${hours}小时`;\r\n        }\r\n\r\n        return result;\r\n    }\r\n    /**\r\n * 将秒数转换为时间字符串（如 \"1天2小时30分\" 或 \"30秒\"）\r\n * @param seconds 秒数\r\n * @returns 时间字符串\r\n */\r\n    getTimeStrForHMS1(seconds: number): string {\r\n        if (seconds < 60) {\r\n            return `${seconds}秒`;\r\n        } else if (seconds < 3600) {\r\n            const minutes = Math.floor(seconds / 60);\r\n            const remainingSeconds = Math.floor(seconds % 60);\r\n            return `${minutes}分${remainingSeconds}秒`;\r\n        } else if (seconds < 86400) {\r\n            const hours = Math.floor(seconds / 3600);\r\n            const remainingSeconds = seconds % 3600;\r\n            const minutes = Math.floor(remainingSeconds / 60);\r\n            return `${hours}小时${minutes}分${remainingSeconds}秒`;\r\n        } else {\r\n            const days = Math.floor(seconds / 86400);\r\n            const remainingSeconds = seconds % 86400;\r\n            const hours = Math.floor(remainingSeconds / 3600);\r\n            const minutes = Math.floor((remainingSeconds % 3600) / 60);\r\n            return `${days}天${hours}小时${minutes}分`;\r\n        }\r\n    }\r\n\r\n    /**\r\n     * 获取随机 Vec2 坐标\r\n     * @param xRange x 坐标范围\r\n     * @param yRange y 坐标范围\r\n     * @returns 随机 Vec2 坐标\r\n     */\r\n    getRandomVec2(xRange: { x: number; y: number }, yRange: { x: number; y: number }): Vec2 {\r\n        const x = this.random_int(xRange.x, xRange.y);\r\n        const y = this.random_int(yRange.x, yRange.y);\r\n        return v2(x, y);\r\n    }\r\n\r\n    /**\r\n     * 检查数组是否包含某个元素\r\n     * @param array 数组\r\n     * @param element 元素\r\n     * @returns 是否包含\r\n     */\r\n    arrContains<T>(array: T[], element: T): boolean {\r\n        return array.indexOf(element) != -1;\r\n    }\r\n\r\n    /**\r\n     * 从数组中移除某个元素\r\n     * @param array 数组\r\n     * @param element 元素\r\n     * @returns 是否成功移除\r\n     */\r\n    arrRemove<T>(array: T[], element: T): boolean {\r\n        const index = array.indexOf(element);\r\n        if (index >= 0) {\r\n            array.splice(index, 1);\r\n            return true;\r\n        }\r\n        return false;\r\n    }\r\n\r\n    /**\r\n     * 检查数组是否包含某个元素（与 arrContains 类似）\r\n     * @param array 数组\r\n     * @param element 元素\r\n     * @returns 是否包含\r\n     */\r\n    arrContain<T>(array: T[], element: T): boolean {\r\n        return array.indexOf(element) != -1;;\r\n    }\r\n\r\n    /**\r\n     * 添加脚本组件到节点\r\n     * @param node 节点\r\n     * @param script 脚本类\r\n     * @returns 添加的脚本组件\r\n     */\r\n    addScript<T extends Component>(node: Node, script: { new(): T }): T | null {\r\n        if (!node || !script) return null;\r\n        return node.getComponent(script) || node.addComponent(script);\r\n    }\r\n\r\n    /**\r\n     * 根据名称移除子节点\r\n     * @param parent 父节点\r\n     * @param name 子节点名称\r\n     */\r\n    removeChildByName(parent: Node, name: string) {\r\n        if (!parent) return;\r\n        const child = parent.getChildByName(name);\r\n        if (child) {\r\n            child.destroy();\r\n        }\r\n    }\r\n\r\n    /**\r\n     * 检查飞机是否超出屏幕范围\r\n     * @param position 飞机位置\r\n     * @returns 是否超出屏幕\r\n     */\r\n    isPlaneOutScreen(position: Vec2): boolean {\r\n        const viewCenterX = GameConst.ViewCenter.x + 50;\r\n        const viewHeight = GameConst.ViewHeight + 50;\r\n        return position.x < -viewCenterX || position.x > viewCenterX || position.y < -viewHeight || position.y > 50;\r\n    }\r\n\r\n    /**\r\n     * 获取贝塞尔曲线上的点\r\n     * @param p0 起点\r\n     * @param p1 控制点1\r\n     * @param p2 控制点2\r\n     * @param p3 终点\r\n     * @param t 参数 t（0 到 1）\r\n     * @returns 贝塞尔曲线上的点\r\n     */\r\n    getBezier(p0: number, p1: number, p2: number, p3: number, t: number): number {\r\n        return (\r\n            p0 * Math.pow(1 - t, 3) +\r\n            3 * p1 * t * Math.pow(1 - t, 2) +\r\n            3 * p2 * Math.pow(t, 2) * (1 - t) +\r\n            p3 * Math.pow(t, 3)\r\n        );\r\n    }\r\n\r\n    /**\r\n     * 获取直线上的点\r\n     * @param start 起点\r\n     * @param direction 方向向量\r\n     * @param distance 距离\r\n     * @param t 参数 t（比例）\r\n     * @returns 直线上的点\r\n     */\r\n    getStraight(start: Vec2, direction: Vec2, distance: number, t: number): Vec2 {\r\n        const normalizedDir = direction.subtract(start).normalize();\r\n        return start.add(normalizedDir.multiplyScalar(distance * t));\r\n    }\r\n\r\n    /**\r\n     * 获取方向向量上的点\r\n     * @param start 起点\r\n     * @param direction 方向向量\r\n     * @param distance 距离\r\n     * @param t 参数 t（比例）\r\n     * @returns 方向向量上的点\r\n     */\r\n    getStraightForDir(start: Vec2, direction: Vec2, distance: number, t: number): Vec2 {\r\n        return start.add(direction.multiplyScalar(distance * t));\r\n    }\r\n\r\n    /**\r\n     * 获取两点之间的角度\r\n     * @param start 起点\r\n     * @param end 终点\r\n     * @returns 角度\r\n     */\r\n    getAngle(start: Vec2, end: Vec2): number {\r\n        const dx = end.x - start.x;\r\n        const dy = end.y - start.y;\r\n        const distance = Math.sqrt(dx * dx + dy * dy);\r\n        const angle = Math.asin(dx / distance);\r\n        return dy < 0 ? 180 - misc.radiansToDegrees(angle) : misc.radiansToDegrees(angle);\r\n    }\r\n\r\n    /**\r\n     * 根据角度获取点的位置\r\n     * @param point 点\r\n     * @param angle 角度\r\n     * @returns 新位置\r\n     */\r\n    getPositionByAngle(point: Vec2, angle: number): Vec2 {\r\n        const radius = Math.sqrt(point.x * point.x + point.y * point.y);\r\n        const radian = (Math.atan2(point.y, point.x) + misc.degreesToRadians(angle));\r\n        return v2(Math.cos(radian) * radius, Math.sin(radian) * radius);\r\n    }\r\n\r\n    /**\r\n     * 清空数组中的节点\r\n     * @param array 节点数组\r\n     */\r\n    clearArrayForNode(array: Node[]) {\r\n        if (!array) return;\r\n        array.forEach((node) => {\r\n            if (node) node.destroy();\r\n        });\r\n        array.length = 0;\r\n    }\r\n\r\n    /**\r\n     * 清空 Map 中的节点\r\n     * @param map 节点 Map\r\n     */\r\n    clearMapForNode(map: Map<any, Node>) {\r\n        if (!map) return;\r\n        map.forEach((node) => {\r\n            if (node) node.destroy();\r\n        });\r\n        map.clear();\r\n    }\r\n\r\n    /**\r\n     * 获取随机用户 ID\r\n     * @returns 随机用户 ID\r\n     */\r\n    getRandomUserId(): string {\r\n        const charArr = \"0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ\";\r\n        let userId = \"\";\r\n        for (let i = 0; i < 32; i++) {\r\n            const randomIndex = this.random_int(0, charArr.length - 1);\r\n            userId += charArr[randomIndex];\r\n        }\r\n        return userId;\r\n    }\r\n\r\n    /**\r\n     * 获取随机广告 ID\r\n     * @returns 随机广告 ID\r\n     */\r\n    getRandomAdId(): string {\r\n        const charArr = \"0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ\";\r\n        let adId = \"\";\r\n        for (let i = 0; i < 64; i++) {\r\n            const randomIndex = this.random_int(0, charArr.length - 1);\r\n            adId += charArr[randomIndex];\r\n        }\r\n        return adId;\r\n    }\r\n\r\n    /**\r\n     * 比较两个版本号\r\n     * @param version1 版本号1\r\n     * @param version2 版本号2\r\n     * @returns 比较结果（-1：version1 < version2，0：version1 == version2，1：version1 > version2）\r\n     */\r\n    version_compare(version1: string, version2: string): number {\r\n        if (version1 === version2) return 0;\r\n        const v1Parts = version1.split(\".\");\r\n        const v2Parts = version2.split(\".\");\r\n        const minLength = Math.min(v1Parts.length, v2Parts.length);\r\n\r\n        for (let i = 0; i < minLength; i++) {\r\n            const v1 = parseInt(v1Parts[i], 10);\r\n            const v2 = parseInt(v2Parts[i], 10);\r\n            if (v1 > v2) return 1;\r\n            if (v1 < v2) return -1;\r\n        }\r\n\r\n        return v1Parts.length > v2Parts.length ? 1 : -1;\r\n    }\r\n\r\n\r\n    /**\r\n     * 获取两点之间的角度（以度为单位）\r\n     * @param x1 起点 x 坐标\r\n     * @param y1 起点 y 坐标\r\n     * @param x2 终点 x 坐标\r\n     * @param y2 终点 y 坐标\r\n     * @returns 角度\r\n     */\r\n    getLineDegrees(x1: number, y1: number, x2: number, y2: number): number {\r\n        const vector = v2(x2, y2).subtract(v2(x1, y1));\r\n        const reference = v2(0, -1); // 参考向量\r\n        const angle = vector.signAngle(reference);\r\n        return misc.radiansToDegrees(angle);\r\n    }\r\n\r\n    /**\r\n     * 获取方向向量\r\n     * @param x1 起点 x 坐标\r\n     * @param y1 起点 y 坐标\r\n     * @param x2 终点 x 坐标\r\n     * @param y2 终点 y 坐标\r\n     * @returns 方向向量\r\n     */\r\n    getDir(x1: number, y1: number, x2: number, y2: number): Vec2 {\r\n        return v2(x2, y2).subtract(v2(x1, y1)).normalize();\r\n    }\r\n\r\n    /**\r\n     * 获取方向向量的角度（以度为单位）\r\n     * @param dir 方向向量\r\n     * @returns 角度\r\n     */\r\n    getDegreeForDir(dir: Vec2): number {\r\n        const reference = v2(0, -1); // 参考向量\r\n        if (dir.equals(reference) || dir.equals(Vec2.ZERO)) {\r\n            return 0;\r\n        }\r\n        const angle = dir.signAngle(reference);\r\n        return misc.radiansToDegrees(angle);\r\n    }\r\n\r\n    /**\r\n     * 将弧度转换为角度\r\n     * @param radians 弧度\r\n     * @returns 角度\r\n     */\r\n    toDegrees(radians: number): number {\r\n        return (radians * 180) / Math.PI;\r\n    }\r\n\r\n    /**\r\n     * 将角度重置到 [0, 360) 范围内\r\n     * @param angle 角度\r\n     * @returns 重置后的角度\r\n     */\r\n    getResetAngle(angle: number): number {\r\n        if (angle > 0) {\r\n            angle -= 360 * Math.floor(angle / 360);\r\n        } else if (angle < 0) {\r\n            angle += 360 * Math.ceil(Math.abs(angle / 360));\r\n        }\r\n        return angle;\r\n    }\r\n\r\n    /**\r\n     * 将角度重置到 [-180, 180) 范围内\r\n     * @param angle 角度\r\n     * @returns 重置后的角度\r\n     */\r\n    getSingedResetAngle(angle: number): number {\r\n        if (angle > 180) {\r\n            angle -= 360 * Math.ceil(angle / 360);\r\n        } else if (angle < -180) {\r\n            angle += 360 * Math.ceil(Math.abs(angle / 360));\r\n        }\r\n        return angle;\r\n    }\r\n\r\n    /**\r\n     * 将角度限制在 [-180, 180) 范围内\r\n     * @param angle 角度\r\n     * @returns 限制后的角度\r\n     */\r\n    getSingedAngle(angle: number): number {\r\n        if (angle > 180) {\r\n            angle -= 360 * Math.ceil(angle / 360);\r\n            if (angle < -180) {\r\n                angle += 360;\r\n            }\r\n        } else if (angle < -180) {\r\n            angle += 360 * Math.ceil(Math.abs(angle / 360));\r\n            if (angle > 180) {\r\n                angle -= 360;\r\n            }\r\n        }\r\n        return angle;\r\n    }\r\n\r\n    /**\r\n     * 将数组中的元素移动指定位置\r\n     * @param array 原数组\r\n     * @param offset 偏移量\r\n     * @param copy 是否复制数组\r\n     * @returns 移动后的数组\r\n     */\r\n    arrMove<T>(array: T[], offset: number, copy: boolean = false): T[] {\r\n        const length = array.length;\r\n        if (copy) {\r\n            array = this.copyArray(array);\r\n        }\r\n        offset %= length;\r\n        if (offset < 0) {\r\n            offset = length - Math.abs(offset);\r\n        }\r\n        array = this.reverse(array, 0, length - offset - 1);\r\n        array = this.reverse(array, length - offset, length - 1);\r\n        return this.reverse(array, 0, length - 1);\r\n    }\r\n\r\n    /**\r\n     * 反转数组中指定范围的元素\r\n     * @param array 原数组\r\n     * @param start 起始索引\r\n     * @param end 结束索引\r\n     * @returns 反转后的数组\r\n     */\r\n    reverse<T>(array: T[], start: number, end: number): T[] {\r\n        while (start < end) {\r\n            const temp = array[start];\r\n            array[start] = array[end];\r\n            array[end] = temp;\r\n            start++;\r\n            end--;\r\n        }\r\n        return array;\r\n    }\r\n\r\n    /**\r\n     * 清空数组中的组件\r\n     * @param array 组件数组\r\n     */\r\n    clearArrayForComp(array: Component[]) {\r\n        if (!array) return;\r\n        array.forEach((comp) => {\r\n            if (comp && comp.node) {\r\n                comp.node.destroy();\r\n            }\r\n        });\r\n        array.splice(0);\r\n    }\r\n\r\n    /**\r\n     * 清空 Map 中的节点数组\r\n     * @param map 节点数组的 Map\r\n     */\r\n    clearMapForNodeArr(map: Map<any, Node[]>) {\r\n        if (!map) return;\r\n        map.forEach((nodeArray) => {\r\n            nodeArray.forEach((node) => {\r\n                if (node) node.destroy();\r\n            });\r\n        });\r\n        map.clear();\r\n    }\r\n\r\n    /**\r\n     * 清空 Map 中的组件\r\n     * @param map 组件的 Map\r\n     */\r\n    clearMapForComp(map: Map<any, Component>) {\r\n        if (!map) return;\r\n        map.forEach((comp) => {\r\n            if (comp && comp.node) {\r\n                comp.node.destroy();\r\n            }\r\n        });\r\n        map.clear();\r\n    }\r\n\r\n    /**\r\n     * 清空 Map 中的组件数组\r\n     * @param map 组件数组的 Map\r\n     */\r\n    clearMapForCompArr(map: Map<any, Component[]>) {\r\n        if (!map) return;\r\n        map.forEach((compArray) => {\r\n            compArray.forEach((comp) => {\r\n                if (comp && comp.node) {\r\n                    comp.node.destroy();\r\n                }\r\n            });\r\n        });\r\n        map.clear();\r\n    }\r\n\r\n    /**\r\n     * 清空节点池\r\n     * @param pool 节点池\r\n     */\r\n    clearNodePool(pool: NodePool) {\r\n        if (!pool) return;\r\n        while (pool.size() > 0) {\r\n            const node = pool.get();\r\n            if (node) node.destroy();\r\n        }\r\n        pool.clear();\r\n    }\r\n}\r\n\r\nexport const Tools = new DYTools();"]}