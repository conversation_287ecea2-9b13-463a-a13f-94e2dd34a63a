{"version": 3, "sources": ["file:///E:/M2Game/Client/assets/scripts/Game/ui/map/loadingUI.ts"], "names": ["_decorator", "Component", "Label", "ProgressBar", "ccclass", "property", "loadingUI", "start", "update", "deltaTime", "setProgress", "percent", "progress", "labProgress", "string", "Math", "floor"], "mappings": ";;;;;;;;;;;;;;;;AAASA,MAAAA,U,OAAAA,U;AAAYC,MAAAA,S,OAAAA,S;AAAWC,MAAAA,K,OAAAA,K;AAAaC,MAAAA,W,OAAAA,W;;;;;;;;;OACvC;AAAEC,QAAAA,OAAF;AAAWC,QAAAA;AAAX,O,GAAwBL,U;;2BAGjBM,S,WADZF,OAAO,CAAC,WAAD,C,UAGHC,QAAQ,CAACF,WAAD,C,UAERE,QAAQ,CAACH,KAAD,C,2BALb,MACaI,SADb,SAC+BL,SAD/B,CACyC;AAAA;AAAA;;AAAA;;AAAA;AAAA;;AAOrCM,QAAAA,KAAK,GAAG,CAEP;;AAEDC,QAAAA,MAAM,CAACC,SAAD,EAAoB,CAEzB;;AAEDC,QAAAA,WAAW,CAACC,OAAD,EAAkB;AACzB,eAAKR,WAAL,CAAiBS,QAAjB,GAA4BD,OAAO,GAAG,GAAtC;AACA,eAAKE,WAAL,CAAiBC,MAAjB,GAA6BC,IAAI,CAACC,KAAL,CAAWL,OAAX,CAA7B;AACH;;AAlBoC,O", "sourcesContent": ["import { _decorator, Component, Label, Node, ProgressBar } from 'cc';\r\nconst { ccclass, property } = _decorator;\r\n\r\n@ccclass('loadingUI')\r\nexport class loadingUI extends Component {\r\n\r\n    @property(ProgressBar)\r\n    ProgressBar:ProgressBar;\r\n    @property(Label)\r\n    labProgress:Label;\r\n\r\n    start() {\r\n\r\n    }\r\n\r\n    update(deltaTime: number) {\r\n        \r\n    }\r\n\r\n    setProgress(percent: number) {\r\n        this.ProgressBar.progress = percent / 100;\r\n        this.labProgress.string = `${Math.floor(percent)}%`;\r\n    }\r\n}\r\n\r\n\r\n"]}