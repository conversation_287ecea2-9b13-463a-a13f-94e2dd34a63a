System.register(["__unresolved_0", "cc", "__unresolved_1"], function (_export, _context) {
  "use strict";

  var _reporterNs, _cclegacy, __checkObsolete__, __checkObsoleteInNamespace__, _decorator, sys, logDebug, _dec, _class, _class2, _crd, ccclass, DevLoginData;

  function _reportPossibleCrUseOflogDebug(extras) {
    _reporterNs.report("logDebug", "../Utils/Logger", _context.meta, extras);
  }

  return {
    setters: [function (_unresolved_) {
      _reporterNs = _unresolved_;
    }, function (_cc) {
      _cclegacy = _cc.cclegacy;
      __checkObsolete__ = _cc.__checkObsolete__;
      __checkObsoleteInNamespace__ = _cc.__checkObsoleteInNamespace__;
      _decorator = _cc._decorator;
      sys = _cc.sys;
    }, function (_unresolved_2) {
      logDebug = _unresolved_2.logDebug;
    }],
    execute: function () {
      _crd = true;

      _cclegacy._RF.push({}, "2a33akGBvVPu4qkCzg4kvWW", "DevLoginData", undefined);

      __checkObsolete__(['_decorator', 'sys']);

      ({
        ccclass
      } = _decorator);

      _export("DevLoginData", DevLoginData = (_dec = ccclass("DevLoginData"), _dec(_class = (_class2 = class DevLoginData {
        constructor() {
          this.data = void 0;
        }

        static get instance() {
          if (!this._instance) {
            var loginData = new DevLoginData();
            var strData = sys.localStorage.getItem("devLoginData");
            (_crd && logDebug === void 0 ? (_reportPossibleCrUseOflogDebug({
              error: Error()
            }), logDebug) : logDebug)("DevLoginData", "localStorage devLoginData:" + strData);
            loginData.data = JSON.parse(strData);

            if (!loginData.data) {
              loginData.data = {};
              loginData.saveLoginData();
            }

            if (!this.serverList.has(loginData.data.servername)) {
              loginData.data.servername = this.serverList.keys().next().value;
              loginData.saveLoginData();
            }

            this._instance = loginData;
          }

          return this._instance;
        }

        saveLoginData() {
          sys.localStorage.setItem("devLoginData", JSON.stringify(this.data));
        }

        getServerAddr() {
          return DevLoginData.serverList.get(this.data.servername);
        }

        get servername() {
          return this.data.servername;
        }

        set servername(value) {
          if (this.data.servername != value) {
            this.data.servername = value;
            this.saveLoginData();
          }
        }

        get user() {
          return this.data.username || "";
        }

        set user(value) {
          if (this.data.username != value) {
            this.data.username = value;
            this.saveLoginData();
          }
        }

        get password() {
          return this.data.password || "";
        }

        set password(value) {
          if (this.data.password != value) {
            this.data.password = value;
            this.saveLoginData();
          }
        }

      }, _class2.serverList = new Map([["jerry", "ws://175.178.238.98:9011"]]), _class2._instance = void 0, _class2)) || _class));

      _cclegacy._RF.pop();

      _crd = false;
    }
  };
});
//# sourceMappingURL=d45960b1fcc38c0f2c70ea3e04e648f13c0129b9.js.map