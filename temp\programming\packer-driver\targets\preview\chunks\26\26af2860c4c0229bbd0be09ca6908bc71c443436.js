System.register(["__unresolved_0", "cc", "__unresolved_1"], function (_export, _context) {
  "use strict";

  var _reporterNs, _cclegacy, __checkObsolete__, __checkObsoleteInNamespace__, _decorator, Component, Sprite, Node, Vec3, Global, _dec, _dec2, _dec3, _class, _class2, _descriptor, _descriptor2, _crd, ccclass, property, BackgroundLayer;

  function _initializerDefineProperty(target, property, descriptor, context) { if (!descriptor) return; Object.defineProperty(target, property, { enumerable: descriptor.enumerable, configurable: descriptor.configurable, writable: descriptor.writable, value: descriptor.initializer ? descriptor.initializer.call(context) : void 0 }); }

  function _applyDecoratedDescriptor(target, property, decorators, descriptor, context) { var desc = {}; Object.keys(descriptor).forEach(function (key) { desc[key] = descriptor[key]; }); desc.enumerable = !!desc.enumerable; desc.configurable = !!desc.configurable; if ('value' in desc || desc.initializer) { desc.writable = true; } desc = decorators.slice().reverse().reduce(function (desc, decorator) { return decorator(target, property, desc) || desc; }, desc); if (context && desc.initializer !== void 0) { desc.value = desc.initializer ? desc.initializer.call(context) : void 0; desc.initializer = undefined; } if (desc.initializer === void 0) { Object.defineProperty(target, property, desc); desc = null; } return desc; }

  function _initializerWarningHelper(descriptor, context) { throw new Error('Decorating class property failed. Please ensure that ' + 'transform-class-properties is enabled and runs after the decorators transform.'); }

  function _reportPossibleCrUseOfGlobal(extras) {
    _reporterNs.report("Global", "../../../Global", _context.meta, extras);
  }

  function _reportPossibleCrUseOfBackgroundLayerData(extras) {
    _reporterNs.report("BackgroundLayerData", "./Background", _context.meta, extras);
  }

  return {
    setters: [function (_unresolved_) {
      _reporterNs = _unresolved_;
    }, function (_cc) {
      _cclegacy = _cc.cclegacy;
      __checkObsolete__ = _cc.__checkObsolete__;
      __checkObsoleteInNamespace__ = _cc.__checkObsoleteInNamespace__;
      _decorator = _cc._decorator;
      Component = _cc.Component;
      Sprite = _cc.Sprite;
      Node = _cc.Node;
      Vec3 = _cc.Vec3;
    }, function (_unresolved_2) {
      Global = _unresolved_2.Global;
    }],
    execute: function () {
      _crd = true;

      _cclegacy._RF.push({}, "e901cF2PUtE/p490KSf5HxT", "BackgroundLayer", undefined);

      __checkObsolete__(['_decorator', 'Component', 'Sprite', 'SpriteFrame', 'Node', 'Enum', 'Vec3']);

      ({
        ccclass,
        property
      } = _decorator);

      _export("BackgroundLayer", BackgroundLayer = (_dec = ccclass('BackgroundLayer'), _dec2 = property({
        type: Node,
        displayName: "Node A (Visible)",
        tooltip: "First node that starts visible"
      }), _dec3 = property({
        type: Node,
        displayName: "Node B (Top)",
        tooltip: "Second node that starts at the top"
      }), _dec(_class = (_class2 = class BackgroundLayer extends Component {
        constructor() {
          super(...arguments);

          // we use 2 nodes for scrolling, each with a sprite component
          _initializerDefineProperty(this, "nodeA", _descriptor, this);

          _initializerDefineProperty(this, "nodeB", _descriptor2, this);

          this.currentData = null;
          this.currentSpriteIndex = 0;
          this.scrollProgress = 0;
          // Progress from 0 to 1 for current sprite
          this.positionA = new Vec3();
          this.positionB = new Vec3();
        }

        get spriteFrames() {
          return this.currentData && this.currentData.spriteFrames;
        }

        onLoad() {
          this.initializeNodes();
        }

        initializeNodes() {
          if (!this.nodeA || !this.nodeB) {
            console.warn("BackgroundLayer: nodeA and nodeB must be assigned!");
            return;
          } // NodeA starts at the visible area (y = 0)


          this.positionA.set(0, 0, 0);
          this.nodeA.setPosition(this.positionA); // NodeB starts at the top (y = Global.HEIGHT)

          this.positionB.set(0, (_crd && Global === void 0 ? (_reportPossibleCrUseOfGlobal({
            error: Error()
          }), Global) : Global).HEIGHT, 0);
          this.nodeB.setPosition(this.positionB);
        }

        initialize(data) {
          this.currentData = data;
          this.initializeSprites();
        }

        tick(deltaTime) {
          if (!this.currentData || this.currentData.spriteFrames.length === 0 || !this.nodeA || !this.nodeB) {
            return;
          } // Calculate scroll progress based on scrollDuration
          // scrollDuration is the time to complete one full scroll cycle


          var scrollSpeed = 1.0 / this.currentData.scrollDuration; // Progress per second

          this.scrollProgress += scrollSpeed * deltaTime; // Handle looping within spriteFrames array and swap nodes

          if (this.scrollProgress >= 1.0) {
            this.scrollProgress = 0.0;
            this.currentSpriteIndex = (this.currentSpriteIndex + 1) % this.spriteFrames.length;
            this.swapNodes();
            this.updateCurrentSprite();
          } // Calculate positions based on scroll progress
          // NodeA moves from 0 to -Global.HEIGHT
          // NodeB moves from Global.HEIGHT to 0


          var offsetY = (_crd && Global === void 0 ? (_reportPossibleCrUseOfGlobal({
            error: Error()
          }), Global) : Global).HEIGHT * this.scrollProgress;
          this.positionA.set(0, -offsetY, 0);
          this.nodeA.setPosition(this.positionA);
          this.positionB.set(0, (_crd && Global === void 0 ? (_reportPossibleCrUseOfGlobal({
            error: Error()
          }), Global) : Global).HEIGHT - offsetY, 0);
          this.nodeB.setPosition(this.positionB);
        }

        swapNodes() {
          // Swap the nodes so the one that was at the top becomes the visible one
          var tempNode = this.nodeA;
          this.nodeA = this.nodeB;
          this.nodeB = tempNode; // Reset positions after swap

          this.positionA.set(0, 0, 0);
          this.nodeA.setPosition(this.positionA);
          this.positionB.set(0, (_crd && Global === void 0 ? (_reportPossibleCrUseOfGlobal({
            error: Error()
          }), Global) : Global).HEIGHT, 0);
          this.nodeB.setPosition(this.positionB);
        }

        initializeSprites() {
          if (this.spriteFrames && this.spriteFrames.length > 0) {
            this.currentSpriteIndex = 0;
            this.updateCurrentSprite();
          }
        }

        updateCurrentSprite() {
          if (!this.spriteFrames || this.spriteFrames.length === 0 || !this.nodeA || !this.nodeB) {
            return;
          } // Update nodeA with current sprite


          var spriteComponentA = this.nodeA.getComponent(Sprite);

          if (spriteComponentA && this.spriteFrames[this.currentSpriteIndex]) {
            spriteComponentA.spriteFrame = this.spriteFrames[this.currentSpriteIndex];
          } // Update nodeB with next sprite (for seamless transition)


          var nextSpriteIndex = (this.currentSpriteIndex + 1) % this.spriteFrames.length;
          var spriteComponentB = this.nodeB.getComponent(Sprite);

          if (spriteComponentB && this.spriteFrames[nextSpriteIndex]) {
            spriteComponentB.spriteFrame = this.spriteFrames[nextSpriteIndex];
          }
        }

        changeBackground(data) {
          this.currentData = data;
          this.currentSpriteIndex = 0;
          this.scrollProgress = 0.0;
          this.updateCurrentSprite();
        }
        /**
         * Reset the layer to its initial state
         */


        resetPosition() {
          this.currentSpriteIndex = 0;
          this.scrollProgress = 0.0;
          this.initializeNodes();
          this.updateCurrentSprite();
        }
        /**
         * Get the current sprite index
         */


        getCurrentSpriteIndex() {
          return this.currentSpriteIndex;
        }
        /**
         * Get the current scroll progress (0-1)
         */


        getScrollProgress() {
          return this.scrollProgress;
        }

      }, (_descriptor = _applyDecoratedDescriptor(_class2.prototype, "nodeA", [_dec2], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function initializer() {
          return null;
        }
      }), _descriptor2 = _applyDecoratedDescriptor(_class2.prototype, "nodeB", [_dec3], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function initializer() {
          return null;
        }
      })), _class2)) || _class));

      _cclegacy._RF.pop();

      _crd = false;
    }
  };
});
//# sourceMappingURL=26af2860c4c0229bbd0be09ca6908bc71c443436.js.map