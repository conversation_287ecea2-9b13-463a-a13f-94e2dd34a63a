System.register(["cc"], function (_export, _context) {
  "use strict";

  var _cclegacy, _crd, TabStatus, BagSortType;

  return {
    setters: [function (_cc) {
      _cclegacy = _cc.cclegacy;
    }],
    execute: function () {
      _crd = true;

      _cclegacy._RF.push({}, "d71bazAMe1IL6WKavjuPdtF", "PlaneTypes", undefined);

      _export("TabStatus", TabStatus = /*#__PURE__*/function (TabStatus) {
        TabStatus["Bag"] = "Bag";
        TabStatus["Merge"] = "Merge";
        return TabStatus;
      }({}));

      _export("BagSortType", BagSortType = /*#__PURE__*/function (BagSortType) {
        BagSortType["Quality"] = "Quality";
        BagSortType["Part"] = "Part";
        BagSortType["Merge"] = "Merge";
        return BagSortType;
      }({}));

      _cclegacy._RF.pop();

      _crd = false;
    }
  };
});
//# sourceMappingURL=10c53abf0c2bd81c04a29e50903c11892b846dea.js.map