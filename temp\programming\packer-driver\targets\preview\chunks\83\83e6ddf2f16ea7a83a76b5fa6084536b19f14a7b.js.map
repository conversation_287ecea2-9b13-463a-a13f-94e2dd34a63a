{"version": 3, "sources": ["file:///E:/M2Game/Client/assets/scripts/GameOld/world/level/Level.ts"], "names": ["_decorator", "Component", "LevelEventGroup", "World", "LevelSystem", "ccclass", "property", "Level", "_levelElapsedTime", "levelEvents", "levelElapsedTime", "onLoad", "levelSys", "getInstance", "getSystem", "setCurrentLevel", "getComponentsInChildren", "for<PERSON>ach", "event", "init", "tick", "deltaTime", "tryExecute"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;AAASA,MAAAA,U,OAAAA,U;AAAYC,MAAAA,S,OAAAA,S;;AACZC,MAAAA,e,iBAAAA,e;;AACAC,MAAAA,K,iBAAAA,K;;AACAC,MAAAA,W,iBAAAA,W;;;;;;;;;OACH;AAAEC,QAAAA,OAAF;AAAWC,QAAAA;AAAX,O,GAAwBN,U;;uBAGjBO,K,WADZF,OAAO,CAAC,OAAD,C,gBAAR,MACaE,KADb,SAC2BN,SAD3B,CACqC;AAAA;AAAA;AAAA,eAEjCO,iBAFiC,GAEL,CAFK;AAOjC;AAPiC,eAQvBC,WARuB,GAQW,EARX;AAAA;;AAGN,YAAhBC,gBAAgB,GAAW;AAClC,iBAAO,KAAKF,iBAAZ;AACH;;AAKSG,QAAAA,MAAM,GAAU;AACtB;AACA,cAAIC,QAAQ,GAAG;AAAA;AAAA,8BAAMC,WAAN,GAAoBC,SAApB;AAAA;AAAA,yCAAf;AACAF,UAAAA,QAAQ,CAACG,eAAT,CAAyB,IAAzB,EAHsB,CAKtB;;AACA,eAAKN,WAAL,GAAmB,KAAKO,uBAAL;AAAA;AAAA,iDAAnB;AACA,eAAKP,WAAL,CAAiBQ,OAAjB,CAAyBC,KAAK,IAAI;AAC9BA,YAAAA,KAAK,CAACC,IAAN,CAAW,IAAX;AACH,WAFD;AAGA,eAAKX,iBAAL,GAAyB,CAAzB;AACH;;AAEMY,QAAAA,IAAI,CAACC,SAAD,EAA2B;AAClC,eAAKb,iBAAL,IAA0Ba,SAA1B;AACA,eAAKZ,WAAL,CAAiBQ,OAAjB,CAAyBC,KAAK,IAAI;AAC9BA,YAAAA,KAAK,CAACI,UAAN;AACH,WAFD;AAGH;;AA5BgC,O", "sourcesContent": ["import { _decorator, Component, Node } from 'cc';\r\nimport { LevelEventGroup } from './LevelEventGroup'\r\nimport { World } from '../base/World';\r\nimport { LevelSystem } from './LevelSystem';\r\nconst { ccclass, property } = _decorator;\r\n\r\n@ccclass('Level')\r\nexport class Level extends Component {\r\n\r\n    _levelElapsedTime: number = 0;\r\n    public get levelElapsedTime(): number {\r\n        return this._levelElapsedTime;\r\n    }\r\n\r\n    //@property({type:[LevelEventGroup]})\r\n    protected levelEvents : LevelEventGroup[] = [];\r\n\r\n    protected onLoad() : void {\r\n        // 非正式代码\r\n        let levelSys = World.getInstance().getSystem(LevelSystem);\r\n        levelSys.setCurrentLevel(this);\r\n\r\n        // get all LevelEventGroup from children\r\n        this.levelEvents = this.getComponentsInChildren(LevelEventGroup);\r\n        this.levelEvents.forEach(event => {\r\n            event.init(this);\r\n        });\r\n        this._levelElapsedTime = 0;\r\n    }\r\n\r\n    public tick(deltaTime: number) : void {\r\n        this._levelElapsedTime += deltaTime;\r\n        this.levelEvents.forEach(event => {\r\n            event.tryExecute();\r\n        });\r\n    }\r\n}"]}