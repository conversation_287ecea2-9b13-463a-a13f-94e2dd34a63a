{"version": 3, "sources": ["file:///E:/M2Game/Client/assets/scripts/Game/ui/plane/enemy/EnemyComponent.ts"], "names": ["_decorator", "Component", "ccclass", "property", "EnemyComponent", "_active", "active", "value"], "mappings": ";;;;;;;;;;;;;;;;AAASA,MAAAA,U,OAAAA,U;AAAYC,MAAAA,S,OAAAA,S;;;;;;;;;OAEf;AAAEC,QAAAA,OAAF;AAAWC,QAAAA;AAAX,O,GAAwBH,U;;yBAGTI,c,WADpBF,OAAO,CAAC,gBAAD,C,2BAAR,MACqBE,cADrB,SAC4CH,SAD5C,CACsD;AAAA;AAAA;;AAAA;;AAAA,eAIlDI,OAJkD,GAI/B,IAJ+B;AAAA;;AAMlD;AACJ;AACA;AACA;AACc,YAANC,MAAM,GAAG;AACT,iBAAO,KAAKD,OAAZ;AACH;AAED;AACJ;AACA;AACA;;;AACc,YAANC,MAAM,CAACC,KAAD,EAAiB;AACvB,eAAKF,OAAL,GAAeE,KAAf;AACH;;AApBiD,O,yEACjDJ,Q;;;;;iBACa,I", "sourcesContent": ["import { _decorator, Component } from 'cc';\r\n\r\nconst { ccclass, property } = _decorator;\r\n\r\n@ccclass('EnemyComponent')\r\nexport default class EnemyComponent extends Component {\r\n    @property\r\n    target: any = null;\r\n\r\n    _active: boolean = true;\r\n\r\n    /**\r\n     * 获取组件是否激活\r\n     * @returns {boolean} 是否激活\r\n     */\r\n    get active() {\r\n        return this._active;\r\n    }\r\n\r\n    /**\r\n     * 设置组件是否激活\r\n     * @param {boolean} value 是否激活\r\n     */\r\n    set active(value: boolean) {\r\n        this._active = value;\r\n    }\r\n}"]}