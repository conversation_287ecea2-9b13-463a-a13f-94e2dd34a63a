{"version": 3, "sources": ["file:///E:/M2Game/Client/assets/scripts/ui/main/plane/components/display/EquipDisplay.ts"], "names": ["_decorator", "Component", "ccclass", "property", "EquipDisplay"], "mappings": ";;;;;;;;;;AAASA,MAAAA,U,OAAAA,U;AAAYC,MAAAA,S,OAAAA,S;;;;;;;;;OACf;AAAEC,QAAAA,OAAF;AAAWC,QAAAA;AAAX,O,GAAwBH,U;;8BAGjBI,Y,WADZF,OAAO,CAAC,gBAAD,C,gBAAR,MACaE,YADb,SACkCH,SADlC,CAC4C,E", "sourcesContent": ["import { _decorator, Component } from 'cc';\nconst { ccclass, property } = _decorator;\n\n@ccclass('EquipDisPlayUI')\nexport class EquipDisplay extends Component {\n}\n"]}