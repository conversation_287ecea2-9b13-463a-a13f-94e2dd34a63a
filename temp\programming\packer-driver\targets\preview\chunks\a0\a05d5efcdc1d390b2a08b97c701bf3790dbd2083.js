System.register(["cc"], function (_export, _context) {
  "use strict";

  var _cclegacy, __checkObsolete__, __checkObsoleteInNamespace__, _decorator, Component, Sprite, tween, isValid, _dec, _dec2, _class, _class2, _descriptor, _crd, ccclass, property, ImageSequence;

  function asyncGeneratorStep(gen, resolve, reject, _next, _throw, key, arg) { try { var info = gen[key](arg); var value = info.value; } catch (error) { reject(error); return; } if (info.done) { resolve(value); } else { Promise.resolve(value).then(_next, _throw); } }

  function _asyncToGenerator(fn) { return function () { var self = this, args = arguments; return new Promise(function (resolve, reject) { var gen = fn.apply(self, args); function _next(value) { asyncGeneratorStep(gen, resolve, reject, _next, _throw, "next", value); } function _throw(err) { asyncGeneratorStep(gen, resolve, reject, _next, _throw, "throw", err); } _next(undefined); }); }; }

  function _initializerDefineProperty(target, property, descriptor, context) { if (!descriptor) return; Object.defineProperty(target, property, { enumerable: descriptor.enumerable, configurable: descriptor.configurable, writable: descriptor.writable, value: descriptor.initializer ? descriptor.initializer.call(context) : void 0 }); }

  function _applyDecoratedDescriptor(target, property, decorators, descriptor, context) { var desc = {}; Object.keys(descriptor).forEach(function (key) { desc[key] = descriptor[key]; }); desc.enumerable = !!desc.enumerable; desc.configurable = !!desc.configurable; if ('value' in desc || desc.initializer) { desc.writable = true; } desc = decorators.slice().reverse().reduce(function (desc, decorator) { return decorator(target, property, desc) || desc; }, desc); if (context && desc.initializer !== void 0) { desc.value = desc.initializer ? desc.initializer.call(context) : void 0; desc.initializer = undefined; } if (desc.initializer === void 0) { Object.defineProperty(target, property, desc); desc = null; } return desc; }

  function _initializerWarningHelper(descriptor, context) { throw new Error('Decorating class property failed. Please ensure that ' + 'transform-class-properties is enabled and runs after the decorators transform.'); }

  return {
    setters: [function (_cc) {
      _cclegacy = _cc.cclegacy;
      __checkObsolete__ = _cc.__checkObsolete__;
      __checkObsoleteInNamespace__ = _cc.__checkObsoleteInNamespace__;
      _decorator = _cc._decorator;
      Component = _cc.Component;
      Sprite = _cc.Sprite;
      tween = _cc.tween;
      isValid = _cc.isValid;
    }],
    execute: function () {
      _crd = true;

      _cclegacy._RF.push({}, "215d5bASPJNx6KeFqWgAZs9", "ImageSequence", undefined);

      __checkObsolete__(['_decorator', 'Component', 'Sprite', 'tween', 'isValid']);

      ({
        ccclass,
        property
      } = _decorator);

      _export("default", ImageSequence = (_dec = ccclass('ImageSequence'), _dec2 = property(Sprite), _dec(_class = (_class2 = class ImageSequence extends Component {
        constructor() {
          super(...arguments);

          _initializerDefineProperty(this, "img", _descriptor, this);

          // 用于显示序列帧的 Sprite 组件
          this.m_frameTime = 0;
          // 每帧的时间间隔（毫秒）
          this.m_imgNum = 0;
          // 图片数量
          this.m_textures = [];
          // 图片纹理数组
          this.m_playTime = 0;
          // 播放时间累计
          this.m_times = 0;
          // 播放次数
          this.m_isPlay = false;
          // 是否正在播放
          this.m_finishCallback = null;
          // 播放完成回调
          this._finishCall = null;
        }

        // 自定义完成回调

        /**
         * 加载时初始化 Sprite 组件
         */
        onLoad() {
          if (this.img == null) {
            var sprite = this.getComponent(Sprite);

            if (sprite == null) {
              sprite = this.addComponent(Sprite);
            }

            this.img = sprite;
          }
        }
        /**
         * 获取是否正在播放
         */


        get playing() {
          return this.m_isPlay;
        }
        /**
         * 设置序列帧数据
         * @param {Array} textures 图片纹理数组
         * @param {number} speed 播放速度（帧率）
         */


        setData(textures, speed) {
          if (speed === void 0) {
            speed = 30;
          }

          this.setSpeed(speed);

          if (textures) {
            this.m_imgNum = textures.length;
            this.m_textures = textures;
          }
        }
        /**
         * 设置播放速度
         * @param {number} speed 播放速度（帧率）
         */


        setSpeed(speed) {
          this.m_frameTime = Math.floor(1000 / speed);
        }
        /**
         * 播放序列帧动画
         * @param {number} times 播放次数
         * @param {Function} finishCallback 播放完成回调
         * @returns {Promise<void>}
         */


        play(times, finishCallback) {
          if (finishCallback === void 0) {
            finishCallback = null;
          }

          if (!isValid(this) || !isValid(this.node)) {
            return;
          }

          this.m_times = times;
          this.m_playTime = 0;
          this.m_isPlay = true;

          if (this.img) {
            this.img.spriteFrame = this.m_textures[0];
          } // this.node.opacity = 255;


          this.m_finishCallback = null;
          this._finishCall = finishCallback;
          return new Promise(resolve => {
            this.m_finishCallback = () => {
              resolve(true);
            };
          });
        }
        /**
         * 延迟播放序列帧动画
         * @param {number} delay 延迟时间（帧数）
         * @param {number} times 播放次数
         */


        delayPlay(delay, times) {
          var _this = this;

          tween(this).delay(delay / 30).call( /*#__PURE__*/_asyncToGenerator(function* () {
            yield _this.play(times);

            _this.setStart();
          })).start();
        }
        /**
         * 停止播放
         */


        stop() {
          this.m_isPlay = false; // this.node.opacity = 0;
        }
        /**
         * 设置为第一帧
         */


        setStart() {
          if (this.img) {
            this.img.spriteFrame = this.m_textures[0];
          }
        }
        /**
         * 清除序列帧数据
         */


        clear() {
          this.stop();

          if (this.img) {
            this.img.spriteFrame = null;
          }

          this.m_textures = [];
        }
        /**
         * 播放完成时的处理
         */


        playFinish() {
          this.stop();

          if (this.m_finishCallback) {
            this.m_finishCallback();
            this.m_finishCallback = null;
          }

          if (this._finishCall) {
            this._finishCall();

            this._finishCall = null;
          }
        }
        /**
         * 更新序列帧动画
         * @param {number} deltaTime 帧间隔时间
         */


        update(deltaTime) {
          if (this.m_isPlay && this.m_imgNum !== 0) {
            if (deltaTime > 0.2) {
              deltaTime = 0.016666666666667; // 限制最大帧间隔
            }

            this.m_playTime += Math.floor(deltaTime * 1000);
            var frameIndex = Math.floor(this.m_playTime / this.m_frameTime) % this.m_imgNum;

            if (this.img) {
              this.img.spriteFrame = this.m_textures[frameIndex];
            }

            if (this.m_times > 0 && Math.floor(this.m_playTime / this.m_frameTime) + 1 >= this.m_times * this.m_imgNum) {
              this.playFinish();
            }
          }
        }

      }, (_descriptor = _applyDecoratedDescriptor(_class2.prototype, "img", [_dec2], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function initializer() {
          return null;
        }
      })), _class2)) || _class));

      _cclegacy._RF.pop();

      _crd = false;
    }
  };
});
//# sourceMappingURL=a05d5efcdc1d390b2a08b97c701bf3790dbd2083.js.map