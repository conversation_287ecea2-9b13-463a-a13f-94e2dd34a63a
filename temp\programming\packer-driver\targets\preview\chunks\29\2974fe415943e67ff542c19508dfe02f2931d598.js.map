{"version": 3, "sources": ["file:///E:/M2Game/Client/assets/scripts/ui/main/SkyIslandUI.ts"], "names": ["_decorator", "BaseUI", "<PERSON><PERSON><PERSON><PERSON>", "ccclass", "property", "SkyIslandUI", "getUrl", "<PERSON><PERSON><PERSON><PERSON>", "Background", "onLoad", "onShow", "onHide", "onClose", "update", "dt"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;AAASA,MAAAA,U,OAAAA,U;;AAEAC,MAAAA,M,iBAAAA,M;AAAQC,MAAAA,O,iBAAAA,O;;;;;;;;;OAEX;AAAEC,QAAAA,OAAF;AAAWC,QAAAA;AAAX,O,GAAwBJ,U;;6BAGjBK,W,WADZF,OAAO,CAAC,aAAD,C,gBAAR,MACaE,WADb;AAAA;AAAA,4BACwC;AAChB,eAANC,MAAM,GAAW;AAAE,iBAAO,qBAAP;AAA+B;;AAC1C,eAARC,QAAQ,GAAY;AAAE,iBAAO;AAAA;AAAA,kCAAQC,UAAf;AAA2B;;AAGrDC,QAAAA,MAAM,GAAS,CAExB;;AAEKC,QAAAA,MAAM,GAAgC;AAAA;AAC3C;;AACKC,QAAAA,MAAM,GAAgC;AAAA;AAC3C;;AACKC,QAAAA,OAAO,GAAgC;AAAA;AAC5C;;AACSC,QAAAA,MAAM,CAACC,EAAD,EAAmB,CAClC;;AAhBmC,O", "sourcesContent": ["import { _decorator } from 'cc';\n\nimport { BaseUI, UILayer } from '../UIMgr';\n\nconst { ccclass, property } = _decorator;\n\n@ccclass('SkyIslandUI')\nexport class SkyIslandUI extends BaseUI {\n    public static getUrl(): string { return \"ui/main/SkyIslandUI\"; }\n    public static getLayer(): UILayer { return UILayer.Background }\n\n\n    protected onLoad(): void {\n\n    }\n\n    async onShow(...args: any[]): Promise<void> {\n    }\n    async onHide(...args: any[]): Promise<void> {\n    }\n    async onClose(...args: any[]): Promise<void> {\n    }\n    protected update(dt: number): void {\n    }\n\n}\n\n"]}