{"version": 3, "sources": ["file:///E:/M2Game/Client/assets/scripts/Game/ui/plane/boss/BossHurt.ts"], "names": ["_decorator", "EnemyEntity", "ccclass", "property", "BossHurt", "getHpPercent"], "mappings": ";;;;;;;;;;;;;;;;AAASA,MAAAA,U,OAAAA,U;;AACFC,MAAAA,W;;;;;;;;;OAED;AAAEC,QAAAA,OAAF;AAAWC,QAAAA;AAAX,O,GAAwBH,U;;yBAGTI,Q,WADpBF,OAAO,CAAC,UAAD,C,gBAAR,MACqBE,QADrB;AAAA;AAAA,sCACkD;AAC9CC,QAAAA,YAAY,GAAW;AACnB,iBAAO,CAAP;AACH;;AAH6C,O", "sourcesContent": ["import { _decorator } from 'cc';\r\nimport EnemyEntity from '../enemy/EnemyEntity';\r\n\r\nconst { ccclass, property } = _decorator;\r\n\r\n@ccclass('BossHurt')\r\nexport default class BossHurt extends EnemyEntity {\r\n    getHpPercent(): number {\r\n        return 1;\r\n    }\r\n}"]}