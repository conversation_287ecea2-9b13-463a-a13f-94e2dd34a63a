System.register(["__unresolved_0", "cc", "__unresolved_1", "__unresolved_2"], function (_export, _context) {
  "use strict";

  var _reporterNs, _cclegacy, __checkObsolete__, __checkObsoleteInNamespace__, _decorator, Component, NodeEventType, GameIns, GameEnum, _dec, _class, _class2, _crd, ccclass, property, Controller;

  function _reportPossibleCrUseOfGameIns(extras) {
    _reporterNs.report("GameIns", "../../GameIns", _context.meta, extras);
  }

  function _reportPossibleCrUseOfGameEnum(extras) {
    _reporterNs.report("GameEnum", "../../const/GameEnum", _context.meta, extras);
  }

  return {
    setters: [function (_unresolved_) {
      _reporterNs = _unresolved_;
    }, function (_cc) {
      _cclegacy = _cc.cclegacy;
      __checkObsolete__ = _cc.__checkObsolete__;
      __checkObsoleteInNamespace__ = _cc.__checkObsoleteInNamespace__;
      _decorator = _cc._decorator;
      Component = _cc.Component;
      NodeEventType = _cc.NodeEventType;
    }, function (_unresolved_2) {
      GameIns = _unresolved_2.GameIns;
    }, function (_unresolved_3) {
      GameEnum = _unresolved_3.default;
    }],
    execute: function () {
      _crd = true;

      _cclegacy._RF.push({}, "572fcBk7RlLfpdl/RebWJUY", "Controller", undefined);

      __checkObsolete__(['_decorator', 'Component', 'Animation', 'NodeEventType']);

      ({
        ccclass,
        property
      } = _decorator);

      _export("Controller", Controller = (_dec = ccclass('Controller'), _dec(_class = (_class2 = class Controller extends Component {
        constructor() {
          super(...arguments);
          this.target = null;
        }

        // 单例实例

        /**
         * 加载时初始化
         */
        onLoad() {
          Controller.instance = this; // 单例模式

          this.target = (_crd && GameIns === void 0 ? (_reportPossibleCrUseOfGameIns({
            error: Error()
          }), GameIns) : GameIns).planeManager.mainPlane; // 获取主飞机
        }
        /**
         * 开始时绑定触摸事件
         */


        start() {
          this.node.on(NodeEventType.TOUCH_START, this.onTouchStart, this);
          this.node.on(NodeEventType.TOUCH_END, this.onTouchEnd, this);
          this.node.on(NodeEventType.TOUCH_MOVE, this.onTouchMove, this);
        }
        /**
         * 触摸开始事件
         * @param {Event.EventTouch} event 触摸事件
         */


        onTouchStart(event) {
          if (this.target != null) {// const handGuiderDialog = GameIns.uiManager.getDialogByName("handGuider");
            // if (handGuiderDialog) {
            //     handGuiderDialog.OnRemove(); // 移除引导手势
            // }
            // const bossEnterDialog = GameIns.uiManager.getDialogByName("BossEnterDialog");
            // if (bossEnterDialog) {
            //     bossEnterDialog.onCloseBtnTap(); // 关闭 Boss 进入对话框
            // }
          }
        }
        /**
         * 触摸移动事件
         * @param {Event.EventTouch} event 触摸事件
         */


        onTouchMove(event) {
          if (this.target != null) {
            if ((_crd && GameIns === void 0 ? (_reportPossibleCrUseOfGameIns({
              error: Error()
            }), GameIns) : GameIns).gameRuleManager.gameState < (_crd && GameEnum === void 0 ? (_reportPossibleCrUseOfGameEnum({
              error: Error()
            }), GameEnum) : GameEnum).GameState.Battle) {
              return; // 游戏未进入战斗状态时不处理
            }

            var deltaX = event.getDeltaX();
            var deltaY = event.getDeltaY();
            this.target.onControl(deltaX, deltaY); // 控制主飞机移动
          }
        }
        /**
         * 触摸结束事件
         * @param {Event.EventTouch} event 触摸事件
         */


        onTouchEnd(event) {
          // 当前实现中，触摸结束事件未处理任何逻辑
          if (this.target) {// 可扩展逻辑
          }
        }

      }, _class2.instance = null, _class2)) || _class));

      _cclegacy._RF.pop();

      _crd = false;
    }
  };
});
//# sourceMappingURL=95de4628444ebe6da9d7706c4fc30745b6f7f662.js.map