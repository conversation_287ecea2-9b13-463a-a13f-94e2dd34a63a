{"version": 3, "sources": ["file:///E:/M2Game/Client/assets/scripts/ui/components/common/SelectList/uiSelectItem.ts"], "names": ["_decorator", "Component", "Label", "Node", "ccclass", "property", "uiSelectItem", "_select", "itemData", "updateValue", "labelValue", "string", "onLoad", "node", "on", "EventType", "TOUCH_END", "onChoose", "onDestroy", "off", "onChooseItem"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;AAASA,MAAAA,U,OAAAA,U;AAAYC,MAAAA,S,OAAAA,S;AAAWC,MAAAA,K,OAAAA,K;AAAOC,MAAAA,I,OAAAA,I;;;;;;;;;OAEjC;AAAEC,QAAAA,OAAF;AAAWC,QAAAA;AAAX,O,GAAwBL,U;;8BAGjBM,Y,WADZF,OAAO,CAAC,cAAD,C,UAIHC,QAAQ,CAACH,KAAD,C,2BAJb,MACaI,YADb,SACkCL,SADlC,CAC4C;AAAA;AAAA;;AAExC;AAFwC;;AAMxC;AANwC,eAOhCM,OAPgC;AASxC;AATwC,eAUjCC,QAViC;AAAA;;AAYxC;AACJ;AACA;AACA;AACWC,QAAAA,WAAW,CAACF,OAAD,EAAoBC,QAApB,EAA4C;AAC1D,eAAKD,OAAL,GAAeA,OAAf;AACA,eAAKC,QAAL,GAAgBA,QAAhB;AACA,eAAKE,UAAL,CAAgBC,MAAhB,GAAyB,KAAKH,QAA9B;AACH;;AAESI,QAAAA,MAAM,GAAS;AACrB;AACA,eAAKC,IAAL,CAAUC,EAAV,CAAaX,IAAI,CAACY,SAAL,CAAeC,SAA5B,EAAuC,KAAKC,QAA5C,EAAsD,IAAtD;AACH;;AACSC,QAAAA,SAAS,GAAS;AACxB;AACA,eAAKL,IAAL,CAAUM,GAAV,CAAchB,IAAI,CAACY,SAAL,CAAeC,SAA7B,EAAwC,KAAKC,QAA7C,EAAuD,IAAvD;AACH,SA7BuC,CA+BxC;;;AACQA,QAAAA,QAAQ,GAAS;AACrB,eAAKV,OAAL,CAAaa,YAAb,CAA0B,KAAKZ,QAA/B;AACH;;AAlCuC,O", "sourcesContent": ["import { _decorator, Component, Label, Node } from 'cc';\r\nimport { uiSelect } from './uiSelect';\r\nconst { ccclass, property } = _decorator;\r\n\r\n@ccclass('uiSelectItem')\r\nexport class uiSelectItem extends Component {\r\n\r\n    // 绑定的文本组件\r\n    @property(Label)\r\n    labelValue!: Label;\r\n\r\n    // uiSelect组件\r\n    private _select!: uiSelect;\r\n\r\n    // 项目数据\r\n    public itemData!: string;\r\n\r\n    /**\r\n     * 更新项目数据\r\n     * @param itemData 项目数据\r\n     */\r\n    public updateValue(_select: uiSelect, itemData: string): void {\r\n        this._select = _select;\r\n        this.itemData = itemData;\r\n        this.labelValue.string = this.itemData;\r\n    }\r\n\r\n    protected onLoad(): void {\r\n        // 注册点击事件\r\n        this.node.on(Node.EventType.TOUCH_END, this.onChoose, this);\r\n    }\r\n    protected onDestroy(): void {\r\n        // 注销点击事件\r\n        this.node.off(Node.EventType.TOUCH_END, this.onChoose, this);\r\n    }\r\n\r\n    // 选择当前项目\r\n    private onChoose(): void {\r\n        this._select.onChooseItem(this.itemData);\r\n    }\r\n\r\n}\r\n\r\n\r\n"]}