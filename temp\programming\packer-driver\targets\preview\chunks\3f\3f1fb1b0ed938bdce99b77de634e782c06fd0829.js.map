{"version": 3, "sources": ["file:///E:/M2Game/Client/assets/scripts/Game/ui/plane/mainPlane/MainPlane.ts"], "names": ["_decorator", "Node", "Sprite", "Animation", "Label", "Vec2", "tween", "UIOpacity", "instantiate", "sp", "UITransform", "Tween", "Color", "view", "v3", "Plane", "ColliderComp", "GameConst", "GameIns", "MainSkillBase", "GameEnum", "GameConfig", "BattleLayer", "GameMapRun", "GameFunc", "FireShells", "Bullet", "EnemyEntity", "BossUnit", "GameEvent", "ccclass", "property", "AnimState", "MainPlane", "Skeleton", "parkourTop", "parkourMid", "parkourBot", "below", "challengeAnim", "relifeComp", "hpbarBack", "hpbarMid", "hpbarFont", "hpfont", "hpBar", "hpMidActin", "challengeAnimName", "m_spines", "m_fireAnim", "m_screenDatas", "m_moveEnable", "m_config", "m_collideComp", "bShootingSound", "m_fires", "m_skillFires", "Map", "m_shieldKnifeFire", "_hurtAct", "_hurtActTime", "_hurtActDuration", "m_moveX", "m_moveY", "m_fireState", "m_audioName", "_mapTween", "_collideHurtTime", "mainTransLevel", "m_fireHand", "m_fireLight", "isPlayFireAnim", "initPos", "downSuitCall", "mechaCall1", "mechaCall2", "roguelikeInCall", "roguelikeOutCall", "m_skill", "skillNode", "m_skinShield", "m_skinCircle", "pfb", "mecha<PERSON><PERSON><PERSON>_<PERSON><PERSON>", "unitPrefab", "anim<PERSON>rr", "mechaUnitOverLight", "initPosSuitBot", "initPosSuitTop", "initPosMechaAnim", "initPosSkinAnim", "skillBatDis", "mechaScale", "skillTargetNdoes", "skillBatPoint", "ZERO", "_loadFinish", "_loadTotal", "_loadCount", "mapH", "_ligatureAnim", "_goldAnimArr", "m_data", "m_animState", "idle", "loadFinish", "checkLoadFinish", "battleManager", "addLoadingPercent", "onLoad", "mainPlaneManager", "mainRecord", "data", "start", "self", "eventManager", "on", "MainHpChange", "UpdateHp", "MainRelife", "onRelife", "enemy", "die", "gameDataManager", "battlePlaneActive", "addComp", "enabled", "m_comps", "for<PERSON>ach", "comp", "init", "initPlane", "setFireEnable", "update", "dt", "GameAble", "gameRuleManager", "gameState", "GameState", "Battle", "fireEnable", "type", "checkLockSkill", "zjshoot", "Over", "Pause", "posX", "node", "position", "x", "posY", "y", "Math", "min", "max", "ViewHeight", "setPosition", "setDirection", "addFireAnim", "i", "zjdmtxzb", "length", "fireNode", "addComponent", "parent", "skin", "push", "getComponent", "opacity", "skeletonData", "loadManager", "loadSpine", "skeleton", "setAnimation", "addStreak", "stop", "fill<PERSON><PERSON><PERSON>", "hp", "maxhp", "duration", "abs", "to", "call", "string", "toFixed", "beginBattle", "initMainSkill", "pieceTogetherMainPlane", "battleQuit", "fires", "fire", "destroy", "clear", "suitEffect", "active", "hideSkillNode", "quitGameSetPic", "quiteBattle", "endBattle", "skinAnim", "mechaAnimNode", "suitAnimBot", "suitAnimTop", "streak", "spriteFrame", "shadow", "dg", "loadAtlas", "setData", "body", "playPointAnim", "getFireState", "changeScreenLv", "screenLv", "initPic", "bl", "air", "config", "removeComp", "instantiateNode", "getChildByName", "scale", "getScale", "setScale", "hpScale", "prefabName", "componentType", "siblingName", "parentNode", "callback", "loadPrefab1", "prefab", "component", "siblingIndex", "childrenCount", "siblingNode", "getSiblingIndex", "<PERSON><PERSON><PERSON><PERSON>", "color", "clearData", "reviveCount", "relife", "reviveType", "revive", "scheduleOnce", "setMainPlaneHp", "emit", "playRelifeAim", "stopAllByTarget", "setCompleteListener", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "bind", "union", "repeatF<PERSON><PERSON>", "level", "attack<PERSON>ey", "isConditionOver", "attackData", "screenData", "slice", "mainPlaneFireCounts", "index", "createAttackPoint", "changeScreen", "getType", "baseAttack", "initAtk1", "attack1", "attack2", "atkChallenge", "atkAddRatio", "attack", "attackLv", "hpAttackLv", "speedLv", "cirtLv", "catapultLv", "fireIntensify", "intensifyAtk", "getAttack", "moveX", "deltaTime", "cutHp", "damage", "hurtTotal", "newHp", "to<PERSON><PERSON>", "addHp", "heal", "onCollide", "collision", "invincible", "entity", "getColliderAtk", "onControl", "moveY", "_playDieAnim", "blast", "_dieAnimEnd", "lifeNum", "relifeNum", "battleFail", "battleDie", "enable", "anim", "point", "setMoveAble", "setColAble", "beginFire", "stopFire", "will<PERSON>egine", "isContinue", "getMainPlaneHp", "begine", "startBattle", "planeIn", "frameTime", "ActionFrameTime", "me", "selfPlane<PERSON><PERSON><PERSON>", "getVisibleSize", "height", "getRatio", "instance", "fromTo", "animSpeed", "changeMapSpeedRatio", "changeSkySpeedRatio", "delay", "targetY", "targetX", "attspe", "attspeAnim", "getImage", "transSrc", "transExt", "split", "Number", "scrAnim", "children", "child", "scr", "pos", "auxAnim", "aux", "playChallengeAnim", "animationName", "animation", "PrefabName"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAASA,MAAAA,U,OAAAA,U;AAAuBC,MAAAA,I,OAAAA,I;AAAMC,MAAAA,M,OAAAA,M;AAAQC,MAAAA,S,OAAAA,S;AAAWC,MAAAA,K,OAAAA,K;AAAOC,MAAAA,I,OAAAA,I;AAAMC,MAAAA,K,OAAAA,K;AAAOC,MAAAA,S,OAAAA,S;AAAWC,MAAAA,W,OAAAA,W;AAAaC,MAAAA,E,OAAAA,E;AAAIC,MAAAA,W,OAAAA,W;AAAaC,MAAAA,K,OAAAA,K;AAAOC,MAAAA,K,OAAAA,K;AAAOC,MAAAA,I,OAAAA,I;AAAMC,MAAAA,E,OAAAA,E;;AACnIC,MAAAA,K;;AACEC,MAAAA,Y,iBAAAA,Y;;AACAC,MAAAA,S,iBAAAA,S;;AACAC,MAAAA,O,iBAAAA,O;;AAGFC,MAAAA,a;;AAEAC,MAAAA,Q;;AACAC,MAAAA,U;;AACAC,MAAAA,W;;AACAC,MAAAA,U;;AACEC,MAAAA,Q,kBAAAA,Q;;AACFC,MAAAA,U;;AACAC,MAAAA,M;;AACAC,MAAAA,W;;AACAC,MAAAA,Q;;AACAC,MAAAA,S;;;;;;;;;OAED;AAAEC,QAAAA,OAAF;AAAWC,QAAAA;AAAX,O,GAAwB/B,U;AAG9B;AACA;AACA;;AACKgC,MAAAA,S,0BAAAA,S;AAAAA,QAAAA,S,CAAAA,S;AAAAA,QAAAA,S,CAAAA,S;AAAAA,QAAAA,S,CAAAA,S;eAAAA,S;QAAAA,S;;2BAOQC,S,WADZH,OAAO,CAAC,WAAD,C,UAKHC,QAAQ,CAAC7B,MAAD,C,UAGR6B,QAAQ,CAACtB,EAAE,CAACyB,QAAJ,C,UAGRH,QAAQ,CAAC7B,MAAD,C,UAGR6B,QAAQ,CAAC9B,IAAD,C,UAGR8B,QAAQ,CAAC9B,IAAD,C,UAGR8B,QAAQ,CAAC9B,IAAD,C,UAGR8B,QAAQ,CAACtB,EAAE,CAACyB,QAAJ,C,UAGRH,QAAQ,CAAC9B,IAAD,C,WAGR8B,QAAQ,CAAC9B,IAAD,C,WAGR8B,QAAQ,CAAC7B,MAAD,C,WAGR6B,QAAQ,CAAC7B,MAAD,C,WAGR6B,QAAQ,CAAC9B,IAAD,C,WAGR8B,QAAQ,CAAC9B,IAAD,C,WAGR8B,QAAQ,CAAC5B,SAAD,C,WAGR4B,QAAQ,CAAC5B,SAAD,C,WAGR4B,QAAQ,CAAC5B,SAAD,C,WAGR4B,QAAQ,CAAC9B,IAAD,C,WAGR8B,QAAQ,CAAC9B,IAAD,C,WAGR8B,QAAQ,CAAC9B,IAAD,C,WAGR8B,QAAQ,CAAC9B,IAAD,C,WAGR8B,QAAQ,CAAC9B,IAAD,C,sCAjEb,MACagC,SADb;AAAA;AAAA,0BACqC;AAAA;AAAA;;AAAA;;AAAA;;AAAA;;AAAA;;AAAA;;AAAA;;AAAA;;AAAA;;AAAA;;AAAA;;AAAA;;AAAA;;AAAA;;AAAA;;AAAA;;AAAA;;AAAA;;AAAA;;AAAA;;AAAA;;AAAA;;AAqEjC;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AA5FiC,eA8FjCE,UA9FiC,GA8FpB,IA9FoB;AA8Fd;AA9Fc,eA+FjCC,UA/FiC,GA+FpB,IA/FoB;AA+Fd;AA/Fc,eAgGjCC,UAhGiC,GAgGpB,IAhGoB;AAgGd;AAhGc,eAiGjCC,KAjGiC,GAiGzB,IAjGyB;AAiGnB;AAjGmB,eAkGjCC,aAlGiC,GAkGjB,IAlGiB;AAkGX;AAlGW,eAmGjCC,UAnGiC,GAmGpB,IAnGoB;AAmGd;AAEnB;AArGiC,eAsGjCC,SAtGiC,GAsGrB,IAtGqB;AAsGf;AAtGe,eAuGjCC,QAvGiC,GAuGtB,IAvGsB;AAuGhB;AAvGgB,eAwGjCC,SAxGiC,GAwGrB,IAxGqB;AAwGf;AAxGe,eAyGjCC,MAzGiC,GAyGxB,IAzGwB;AAyGlB;AAzGkB,eA0GjCC,KA1GiC,GA0GzB,IA1GyB;AA0GnB;AA1GmB,eA2GjCC,UA3GiC,GA2GpB,IA3GoB;AA2Gd;AAEnB;AA7GiC,eA8GjCC,iBA9GiC,GA8Gb,IA9Ga;AA8GP;AAC1B;AACA;AACA;AACA;AAlHiC,eAmHjCC,QAnHiC,GAmHtB,CAAC,IAAD,EAAO,IAAP,EAAa,IAAb,EAAmB,IAAnB,CAnHsB;AAmHI;AAnHJ,eAoHjCC,UApHiC,GAoHpB,EApHoB;AAoHhB;AAEjB;AAtHiC,eAuHjCC,aAvHiC,GAuHjB,EAvHiB;AAuHb;AAvHa,eAwHjCC,YAxHiC,GAwHlB,IAxHkB;AAwHZ;AAxHY,eAyHjCC,QAzHiC,GAyHtB,IAzHsB;AAyHhB;AAzHgB,eA0HjCC,aA1HiC,GA0HjB,IA1HiB;AA0HX;AA1HW,eA2HjCC,cA3HiC,GA2HhB,KA3HgB;AA2HT;AA3HS,eA4HjCC,OA5HiC,GA4HvB,EA5HuB;AA4HnB;AA5HmB,eA6HjCC,YA7HiC,GA6HlB,IAAIC,GAAJ,EA7HkB;AA6HP;AA7HO,eA8HjCC,iBA9HiC,GA8Hb,IA9Ha;AA8HP;AA9HO,eA+HjCC,QA/HiC,GA+HtB,IA/HsB;AA+HhB;AA/HgB,eAgIjCC,YAhIiC,GAgIlB,CAhIkB;AAgIf;AAhIe,eAiIjCC,gBAjIiC,GAiId,GAjIc;AAiIT;AAExB;AAnIiC,eAoIjCC,OApIiC,GAoIvB,CApIuB;AAoIpB;AApIoB,eAqIjCC,OArIiC,GAqIvB,CArIuB;AAqIpB;AAEb;AAvIiC,eAwIjCC,WAxIiC,GAwInB,IAxImB;AAwIb;AAxIa,eAyIjCC,WAzIiC,GAyInB,EAzImB;AAyIf;AAzIe,eA0IjCC,SA1IiC,GA0IrB,IA1IqB;AA0If;AA1Ie,eA2IjCC,gBA3IiC,GA2Id,CA3Ic;AA2IX;AAEtB;AA7IiC,eA8IjCC,cA9IiC,GA8IhB,IAAIX,GAAJ,EA9IgB;AA8IL;AA9IK,eA+IjCY,UA/IiC,GA+IpB,IA/IoB;AA+Id;AA/Ic,eAgJjCC,WAhJiC,GAgJnB,IAhJmB;AAgJb;AAhJa,eAiJjCC,cAjJiC,GAiJhB,KAjJgB;AAiJT;AAExB;AAnJiC,eAoJjCC,OApJiC,GAoJvB,EApJuB;AAoJnB;AApJmB,eAqJjCC,YArJiC,GAqJlB,IArJkB;AAqJZ;AArJY,eAsJjCC,UAtJiC,GAsJpB,IAtJoB;AAsJd;AAtJc,eAuJjCC,UAvJiC,GAuJpB,IAvJoB;AAuJd;AAvJc,eAwJjCC,eAxJiC,GAwJf,IAxJe;AAwJT;AAxJS,eAyJjCC,gBAzJiC,GAyJd,IAzJc;AAyJR;AAEzB;AA3JiC,eA4JjCC,OA5JiC,GA4JvB,IA5JuB;AA4JjB;AA5JiB,eA6JjCC,SA7JiC,GA6JrB,IA7JqB;AA6Jf;AA7Je,eA8JjCC,YA9JiC,GA8JlB,IA9JkB;AA8JZ;AA9JY,eA+JjCC,YA/JiC,GA+JlB,IA/JkB;AA+JZ;AA/JY,eAgKjCC,GAhKiC,GAgK3B,IAhK2B;AAgKrB;AAhKqB,eAiKjCC,eAjKiC,GAiKf,IAjKe;AAiKT;AAjKS,eAkKjCC,UAlKiC,GAkKpB,EAlKoB;AAkKhB;AAlKgB,eAmKjCC,OAnKiC,GAmKvB,EAnKuB;AAmKnB;AAnKmB,eAoKjCC,kBApKiC,GAoKZ,IApKY;AAoKN;AAE3B;AAtKiC,eAuKjCC,cAvKiC,GAuKhB,CAvKgB;AAuKb;AAvKa,eAwKjCC,cAxKiC,GAwKhB,CAxKgB;AAwKb;AAxKa,eAyKjCC,gBAzKiC,GAyKd,CAzKc;AAyKX;AAzKW,eA0KjCC,eA1KiC,GA0Kf,CA1Ke;AA0KZ;AAErB;AA5KiC,eA6KjCC,WA7KiC,GA6KnB,EA7KmB;AA6Kf;AA7Ke,eA8KjCC,UA9KiC,GA8KpB,CAAC,GAAD,EAAM,IAAN,EAAY,CAAZ,EAAe,CAAf,CA9KoB;AA8KD;AA9KC,eA+KjCC,gBA/KiC,GA+Kd,IA/Kc;AA+KR;AA/KQ,eAgLjCC,aAhLiC,GAgLjBzF,IAAI,CAAC0F,IAhLY;AAgLN;AAE3B;AAlLiC,eAmLjCC,WAnLiC,GAmLnB,KAnLmB;AAmLZ;AAnLY,eAoLjCC,UApLiC,GAoLpB,CApLoB;AAoLjB;AApLiB,eAqLjCC,UArLiC,GAqLpB,CArLoB;AAqLjB;AAEhB;AAvLiC,eAwLjCC,IAxLiC,GAwL1B;AACH,iBAAK,CADF;AAEH,iBAAK,CAAC,EAFH;AAGH,iBAAK,CAAC,EAHH;AAIH,iBAAK,CAJF;AAKH,iBAAK;AALF,WAxL0B;AAgMjC;AAhMiC,eAiMjCC,aAjMiC,GAiMjB,IAjMiB;AAiMX;AAjMW,eAkMjCC,YAlMiC,GAkMlB,EAlMkB;AAkMd;AAlMc,eAmMjCC,MAnMiC;AAAA,eAoMjCC,WApMiC,GAoMRvE,SAAS,CAACwE,IApMF;AAAA;;AAoMQ;AAG3B,YAAVC,UAAU,GAAY;AACtB,iBAAO,KAAKT,WAAZ;AACH;;AAEDU,QAAAA,eAAe,GAAG;AACd,eAAKR,UAAL;;AACA,cAAI,KAAKA,UAAL,IAAmB,KAAKD,UAA5B,EAAwC;AACpC,iBAAKD,WAAL,GAAmB,IAAnB;AACH;;AACD;AAAA;AAAA,kCAAQW,aAAR,CAAsBC,iBAAtB,CAAwC,IAAI,KAAKX,UAAjD;AACH,SAjNgC,CAmNjC;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AACA;AACJ;AACA;AACA;;;AACIY,QAAAA,MAAM,GAAG;AACL,eAAKzD,QAAL,GAAgB;AAAA;AAAA,kCAAQ0D,gBAAR,CAAyBC,UAAzC,CADK,CACgD;;AACrD,eAAKT,MAAL,GAAc;AAAA;AAAA,kCAAQQ,gBAAR,CAAyBE,IAAvC,CAFK,CAEwC;AAChD;AAED;AACJ;AACA;AACA;;;AACIC,QAAAA,KAAK,GAAG;AACJ,cAAMC,IAAI,GAAG,IAAb,CADI,CAGJ;;AACA;AAAA;AAAA,kCAAQC,YAAR,CAAqBC,EAArB,CAAwB;AAAA;AAAA,sCAAUC,YAAlC,EAAgD,KAAKC,QAArD,EAA+D,IAA/D,EAJI,CAKJ;;AACA;AAAA;AAAA,kCAAQH,YAAR,CAAqBC,EAArB,CAAwB;AAAA;AAAA,sCAAUG,UAAlC,EAA8C,KAAKC,QAAnD,EAA6D,IAA7D,EANI,CAQJ;;AACA,eAAKpE,QAAL,GAAgB;AAAA;AAAA,kCAAQ0D,gBAAR,CAAyBC,UAAzC;AACA,eAAKU,KAAL,GAAa,KAAb;AACA,eAAKnB,MAAL,CAAYoB,GAAZ,GAAkB,KAAlB;AACA;AAAA;AAAA,kCAAQC,eAAR,CAAwBC,iBAAxB,GAA4C,IAA5C,CAZI,CAcJ;;AACA,cAAI,CAAC,KAAKvE,aAAV,EAAyB;AACrB,iBAAKA,aAAL,GAAqB,KAAKwE,OAAL;AAAA;AAAA,8CAA2B;AAAA;AAAA,+CAA3B,CAArB;AACH;;AACD,eAAKxE,aAAL,CAAmByE,OAAnB,GAA6B,IAA7B,CAlBI,CAqBJ;;AACA,eAAKvB,WAAL,GAAmBvE,SAAS,CAACwE,IAA7B,CAtBI,CAwBJ;;AACA,eAAKuB,OAAL,CAAaC,OAAb,CAAsBC,IAAD,IAAU;AAC3BA,YAAAA,IAAI,CAACC,IAAL,CAAUhB,IAAV;AACH,WAFD,EAzBI,CA6BJ;;AACA,eAAKiB,SAAL,GA9BI,CAgCJ;;AACA,eAAKC,aAAL,CAAmB,KAAnB,EAjCI,CAmCJ;AACA;AACH;AAED;AACJ;AACA;AACA;AACA;;;AACIC,QAAAA,MAAM,CAACC,EAAD,EAAK;AACP,cAAI,CAAC;AAAA;AAAA,sCAAUC,QAAf,EAAyB,OADlB,CAGP;;AACA,cAAID,EAAE,GAAG,GAAT,EAAcA,EAAE,GAAG,iBAAL,CAJP,CAMP;;AACA,cACI;AAAA;AAAA,kCAAQE,eAAR,CAAwBC,SAAxB,KAAsC;AAAA;AAAA,oCAASC,SAAT,CAAmBC,MAAzD,IACA;AAAA;AAAA,kCAAQ7B,gBAAR,CAAyB8B,UAF7B,EAGE;AACE,gBAAI,CAAC,KAAKtF,cAAV,EAA0B;AACtB,mBAAKA,cAAL,GAAsB,IAAtB;;AACA,kBAAI,KAAKW,WAAL,KAAqB,EAAzB,EAA6B;AACzB,oBACI,KAAKb,QAAL,CAAcyF,IAAd,KAAuB,GAAvB,IACA;AAAA;AAAA,wCAAQ/B,gBAAR,CAAyBgC,cAAzB,EAFJ,EAGE;AACE,uBAAK7E,WAAL,GAAmB,aAAnB;AACH,iBALD,MAKO,IACH,KAAKb,QAAL,CAAcyF,IAAd,KAAuB,GAAvB,IACA;AAAA;AAAA,wCAAQ/B,gBAAR,CAAyBgC,cAAzB,EAFG,EAGL;AACE,uBAAK7E,WAAL,GAAmB,EAAnB;AACH,iBALM,MAKA;AACH,uBAAKA,WAAL,GAAmB,KAAKb,QAAL,CAAc2F,OAAjC;AACH,iBAbwB,CAczB;AACA;AACA;;AACH;AACJ,aArBH,CAuBE;;;AACA,iBAAKhB,OAAL,CAAaC,OAAb,CAAsBC,IAAD,IAAU;AAC3BA,cAAAA,IAAI,CAACI,MAAL,CAAYC,EAAZ;AACH,aAFD;AAGH,WA9BD,MA8BO,IACH;AAAA;AAAA,kCAAQE,eAAR,CAAwBC,SAAxB,KAAsC;AAAA;AAAA,oCAASC,SAAT,CAAmBM,IAAzD,IACA;AAAA;AAAA,kCAAQR,eAAR,CAAwBC,SAAxB,KAAsC;AAAA;AAAA,oCAASC,SAAT,CAAmBO,KADzD,IAEA,CAAC;AAAA;AAAA,kCAAQnC,gBAAR,CAAyB8B,UAHvB,EAIL;AACE,gBAAI,KAAKtF,cAAT,EAAyB;AACrB,mBAAKA,cAAL,GAAsB,KAAtB;;AACA,kBAAI,KAAKW,WAAL,KAAqB,EAAzB,EAA6B,CACzB;AACH;AACJ;AACJ,WAhDM,CAkDP;;;AACA,cAAI,KAAKH,OAAL,KAAiB,CAAjB,IAAsB,KAAKC,OAAL,KAAiB,CAA3C,EAA8C;AAC1C,gBAAImF,IAAI,GAAG,KAAKC,IAAL,CAAUC,QAAV,CAAmBC,CAAnB,GAAuB,KAAKvF,OAAvC;AACA,gBAAIwF,IAAI,GAAG,KAAKH,IAAL,CAAUC,QAAV,CAAmBG,CAAnB,GAAuB,KAAKxF,OAAvC,CAF0C,CAI1C;;AACAmF,YAAAA,IAAI,GAAGM,IAAI,CAACC,GAAL,CAAS,GAAT,EAAcP,IAAd,CAAP;AACAA,YAAAA,IAAI,GAAGM,IAAI,CAACE,GAAL,CAAS,CAAC,GAAV,EAAeR,IAAf,CAAP;AACAI,YAAAA,IAAI,GAAGE,IAAI,CAACC,GAAL,CAAS,CAAT,EAAYH,IAAZ,CAAP;AACAA,YAAAA,IAAI,GAAGE,IAAI,CAACE,GAAL,CAAS,CAAC;AAAA;AAAA,wCAAUC,UAApB,EAAgCL,IAAhC,CAAP;AACA,iBAAKH,IAAL,CAAUS,WAAV,CAAsBV,IAAtB,EAA4BI,IAA5B;AAEA,iBAAKO,YAAL,CAAkB,KAAK/F,OAAvB,EAAgCwE,EAAhC;AACH,WA/DM,CAiEP;;;AACA,eAAKxE,OAAL,GAAe,CAAf;AACA,eAAKC,OAAL,GAAe,CAAf,CAnEO,CAqEP;;AACA,eAAKH,YAAL,IAAqB0E,EAArB,CAtEO,CAwEP;;AACA,cAAI,KAAKnE,gBAAL,GAAwB,CAA5B,EAA+B;AAC3B,iBAAKA,gBAAL,IAAyBmE,EAAzB;AACH;AACJ;AACD;AACJ;AACA;;;AACUwB,QAAAA,WAAW,GAAG;AAAA;;AAAA;AAChB,iBAAK,IAAIC,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAG,KAAI,CAAC3G,QAAL,CAAc4G,QAAd,CAAuBC,MAA3C,EAAmDF,CAAC,IAAI,CAAxD,EAA2D;AACvD,kBAAMV,CAAC,GAAG,KAAI,CAACjG,QAAL,CAAc4G,QAAd,CAAuBD,CAAvB,CAAV;AACA,kBAAMR,CAAC,GAAG,KAAI,CAACnG,QAAL,CAAc4G,QAAd,CAAuBD,CAAC,GAAG,CAA3B,CAAV;AAEA,kBAAMG,QAAQ,GAAG,IAAIjK,IAAJ,EAAjB;AACAiK,cAAAA,QAAQ,CAACC,YAAT,CAAsBzJ,WAAtB;AACAwJ,cAAAA,QAAQ,CAACC,YAAT,CAAsB5J,SAAtB;AACA2J,cAAAA,QAAQ,CAACN,WAAT,CAAqBP,CAArB,EAAwBE,CAAxB;AACAW,cAAAA,QAAQ,CAACE,MAAT,GAAkB,KAAI,CAACC,IAAvB;;AACA,cAAA,KAAI,CAACpH,UAAL,CAAgBqH,IAAhB,CAAqBJ,QAArB;;AACAA,cAAAA,QAAQ,CAACK,YAAT,CAAsBhK,SAAtB,EAAiCiK,OAAjC,GAA2C,CAA3C;AAEA,kBAAMC,YAAY,SAAS;AAAA;AAAA,sCAAQC,WAAR,CAAoBC,SAApB,CAA8B,mCAA9B,CAA3B;AACA,kBAAMC,QAAQ,GAAGV,QAAQ,CAACC,YAAT,CAAsB1J,EAAE,CAACyB,QAAzB,CAAjB;AACA0I,cAAAA,QAAQ,CAACH,YAAT,GAAwBA,YAAxB;AACAG,cAAAA,QAAQ,CAACC,YAAT,CAAsB,CAAtB,EAAyB,MAAzB,EAAiC,IAAjC;AACH;AAjBe;AAkBnB,SAhbgC,CAkbjC;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACJ;AACA;;;AACIC,QAAAA,SAAS,GAAG,CACR;AACA;AACA;AACA;AACA;AAEA;AACH,SArcgC,CAucjC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AAEA;AACA;AAEA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AACA;AACJ;AACA;;;AACIxD,QAAAA,QAAQ,GAAG;AACP,cAAI,KAAKzE,KAAL,IAAc,KAAKF,SAAnB,IAAgC,KAAKD,QAAzC,EAAmD;AAC/C;AACA,gBAAI,KAAKI,UAAT,EAAqB;AACjB,mBAAKA,UAAL,CAAgBiI,IAAhB;AACA,mBAAKjI,UAAL,GAAkB,IAAlB;AACH,aAL8C,CAO/C;;;AACA,iBAAKH,SAAL,CAAeqI,SAAf,GAA2B;AAAA;AAAA,oCAAQlE,gBAAR,CAAyBE,IAAzB,CAA8BiE,EAA9B,GAAmC;AAAA;AAAA,oCAAQnE,gBAAR,CAAyBE,IAAzB,CAA8BkE,KAA5F,CAR+C,CAU/C;;AACA,gBAAMC,QAAQ,GAAG3B,IAAI,CAAC4B,GAAL,CAAS,KAAK1I,QAAL,CAAcsI,SAAd,GAA0B,KAAKrI,SAAL,CAAeqI,SAAlD,CAAjB,CAX+C,CAa/C;;AACA,iBAAKlI,UAAL,GAAkBxC,KAAK,CAAC,KAAKoC,QAAN,CAAL,CACb2I,EADa,CACVF,QADU,EACA;AAAEH,cAAAA,SAAS,EAAE,KAAKrI,SAAL,CAAeqI;AAA5B,aADA,EAEbM,IAFa,CAER,MAAM;AACR,mBAAKxI,UAAL,GAAkB,IAAlB;AACH,aAJa,EAKbmE,KALa,EAAlB,CAd+C,CAqB/C;;AACA,iBAAKrE,MAAL,CAAY2I,MAAZ,GAAqB;AAAA;AAAA,oCAAQzE,gBAAR,CAAyBE,IAAzB,CAA8BiE,EAA9B,CAAiCO,OAAjC,CAAyC,CAAzC,CAArB;AACH;AACJ;AAED;AACJ;AACA;;;AACIC,QAAAA,WAAW,GAAG;AACV;AACA,eAAKC,aAAL,CAAmB,KAAKtI,QAAxB,EAFU,CAIV;;AACA,cAAI,KAAK0B,OAAT,EAAkB;AACd,iBAAKA,OAAL,CAAa2G,WAAb;AACH,WAPS,CASV;;;AACA,eAAKE,sBAAL;AACH;AAED;AACJ;AACA;;;AACIC,QAAAA,UAAU,GAAG;AACT,eAAKtF,MAAL,CAAYoB,GAAZ,GAAkB,KAAlB,CADS,CAET;AAEA;;AACA,eAAKlE,YAAL,CAAkBwE,OAAlB,CAA2B6D,KAAD,IAAW;AACjCA,YAAAA,KAAK,CAAC7D,OAAN,CAAe8D,IAAD,IAAU;AACpBA,cAAAA,IAAI,CAAC3C,IAAL,CAAUiB,MAAV,GAAmB,IAAnB;AACA0B,cAAAA,IAAI,CAAC3C,IAAL,CAAU4C,OAAV;AACH,aAHD;AAIH,WALD;AAMA,eAAKvI,YAAL,CAAkBwI,KAAlB,GAXS,CAaT;;AACA,eAAK3B,IAAL,CAAUE,YAAV,CAAuBhK,SAAvB,EAAkCiK,OAAlC,GAA4C,GAA5C;AACA,eAAKyB,UAAL,CAAgBC,MAAhB,GAAyB,KAAzB;AACA,cAAI,KAAKzH,YAAT,EAAuB,KAAKA,YAAL;AACvB;AAAA;AAAA,kCAAQqC,gBAAR,CAAyBqF,aAAzB;AACA,eAAKC,cAAL,GAlBS,CAmBT;AAEA;;AACA,cAAI,KAAKtH,OAAT,EAAkB;AACd,iBAAKA,OAAL,CAAauH,WAAb;AACA,iBAAKvH,OAAL,CAAawH,SAAb;AACH,WAzBQ,CA2BT;;;AACA,eAAKC,QAAL,CAAcpD,IAAd,CAAmBI,CAAnB,GAAuB,KAAK7D,eAA5B;AACA,eAAK8G,aAAL,CAAmBjD,CAAnB,GAAuB,KAAK9D,gBAA5B;AACA,eAAKgH,WAAL,CAAiBlD,CAAjB,GAAqB,KAAKhE,cAA1B;AACA,eAAKmH,WAAL,CAAiBnD,CAAjB,GAAqB,KAAK/D,cAA1B,CA/BS,CAiCT;;AACA,cAAI,KAAKpC,QAAL,CAAcyF,IAAd,KAAuB,GAA3B,EAAgC;AAC5B,gBAAI,KAAKzF,QAAL,CAAcyF,IAAd,KAAuB,GAA3B,EAAgC;AAC5B,mBAAK0D,QAAL,CAAcpD,IAAd,CAAmBI,CAAnB,IAAwB,CAAxB;AACA,mBAAKiD,aAAL,CAAmBjD,CAAnB,IAAwB,CAAxB;AACH,aAHD,MAGO;AACH,mBAAKgD,QAAL,CAAcpD,IAAd,CAAmBI,CAAnB,IAAwB,GAAxB;AACA,mBAAKiD,aAAL,CAAmBjD,CAAnB,IAAwB,GAAxB;AACH;AACJ;AACJ;AAED;AACJ;AACA;;;AACUpB,QAAAA,SAAS,GAAG;AAAA;;AAAA;AACd;AACA,YAAA,MAAI,CAACwE,MAAL,CAAYC,WAAZ,GAA0B,IAA1B;AACA,YAAA,MAAI,CAACC,MAAL,CAAYD,WAAZ,GAA0B,IAA1B;AACA,YAAA,MAAI,CAACE,EAAL,CAAQF,WAAR,GAAsB,IAAtB,CAJc,CAMd;;AACA,YAAA,MAAI,CAACxJ,QAAL,GAAgB;AAAA;AAAA,oCAAQ0D,gBAAR,CAAyBC,UAAzC,CAPc,CAQd;;AACA,kBAAM;AAAA;AAAA,oCAAQ2D,WAAR,CAAoBqC,SAApB,wCAAmE,MAAI,CAAC3J,QAAL,CAAcyF,IAAjF,CAAN,CATc,CAWd;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;;AACA,kBAAM,MAAI,CAACiB,WAAL,EAAN,CA3Bc,CA6Bd;;AACA,gBAAI,MAAI,CAACzG,aAAT,EAAwB;AACpB,cAAA,MAAI,CAACA,aAAL,CAAmB2J,OAAnB,CAA2B,MAAI,CAAC5J,QAAL,CAAc6J,IAAzC;AACH,aAhCa,CAkCd;;;AACA,YAAA,MAAI,CAACC,aAAL,GAnCc,CAqCd;;;AACA,YAAA,MAAI,CAACC,YAAL,GAtCc,CAwCd;AACA;AAEA;;;AACA,YAAA,MAAI,CAACC,cAAL,CAAoB,MAAI,CAAC9G,MAAL,CAAY+G,QAAhC,EA5Cc,CA8Cd;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;;;AACA,YAAA,MAAI,CAACC,OAAL,GA1Dc,CA4Dd;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;AACA,YAAA,MAAI,CAACR,EAAL,CAAQ3D,IAAR,CAAa+C,MAAb,GAAsB,KAAtB;AACA,YAAA,MAAI,CAACqB,EAAL,CAAQrB,MAAR,GAAiB,KAAjB;AACA,YAAA,MAAI,CAACsB,GAAL,CAAStB,MAAT,GAAkB,KAAlB,CAvEc,CAyEd;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAnFc;AAoFjB,SArsBgC,CAssBjC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACJ;AACA;AACA;;;AACIR,QAAAA,aAAa,CAAC+B,MAAD,EAAS;AAClB,eAAKzH,WAAL,GAAmB,IAAnB;AACA,eAAKC,UAAL,GAAkB,CAAlB;AACA,eAAKC,UAAL,GAAkB,CAAlB;;AAEA,cAAI,KAAKpB,OAAT,EAAkB;AACd,iBAAKA,OAAL,CAAauH,WAAb;AACA,iBAAKqB,UAAL;AAAA;AAAA;AACA,iBAAK5I,OAAL,GAAe,IAAf;AACH;;AAED,eAAKO,OAAL,GAAe,EAAf,CAXkB,CAalB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACH,SA52BgC,CA62BjC;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AAEA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AACA;AACJ;AACA;;;AACIsG,QAAAA,sBAAsB,GAAG;AACrB,cAAMzE,IAAI,GAAG,IAAb,CADqB,CAGrB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;;AACA,cAAI,CAAC,KAAKrE,KAAV,EAAiB;AACb,iBAAK8K,eAAL,CAAqB,OAArB,EAA8B1N,IAA9B,EAAoC,eAApC,EAAqD,KAAKkJ,IAA1D,EAAiEA,IAAD,IAAU;AACtEjC,cAAAA,IAAI,CAACrE,KAAL,GAAasG,IAAb,CADsE,CAEtE;AACA;AACA;;AACAjC,cAAAA,IAAI,CAACrE,KAAL,CAAW0G,CAAX,GAAe,EAAf,CALsE,CAMtE;;AAEArC,cAAAA,IAAI,CAACzE,SAAL,GAAiByE,IAAI,CAACrE,KAAL,CAAW+K,cAAX,CAA0B,WAA1B,EAAuCrD,YAAvC,CAAoDrK,MAApD,CAAjB;AACAgH,cAAAA,IAAI,CAACxE,QAAL,GAAgBwE,IAAI,CAACrE,KAAL,CAAW+K,cAAX,CAA0B,UAA1B,EAAsCrD,YAAtC,CAAmDrK,MAAnD,CAAhB;AACAgH,cAAAA,IAAI,CAACvE,SAAL,GAAiBuE,IAAI,CAACrE,KAAL,CAAW+K,cAAX,CAA0B,WAA1B,EAAuCrD,YAAvC,CAAoDrK,MAApD,CAAjB;AACAgH,cAAAA,IAAI,CAACtE,MAAL,GAAcsE,IAAI,CAACrE,KAAL,CAAW+K,cAAX,CAA0B,QAA1B,EAAoCrD,YAApC,CAAiDnK,KAAjD,CAAd,CAXsE,CAatE;AACA;AACA;;AAEA,kBAAMyN,KAAK,GAAG,IAAI3G,IAAI,CAACiC,IAAL,CAAU2E,QAAV,GAAqBzE,CAAzB,GAA6B,IAA3C;AACAnC,cAAAA,IAAI,CAACzE,SAAL,CAAe0G,IAAf,CAAoB4E,QAApB,CAA6BF,KAA7B,EAAoC,MAAM,CAACA,KAA3C;AACA3G,cAAAA,IAAI,CAACxE,QAAL,CAAcyG,IAAd,CAAmB4E,QAAnB,CAA4BF,KAA5B,EAAmC,MAAMA,KAAzC;AACA3G,cAAAA,IAAI,CAACvE,SAAL,CAAewG,IAAf,CAAoB4E,QAApB,CAA6BF,KAA7B,EAAoC,MAAM,CAACA,KAA3C;AAEA,kBAAIG,OAAO,GAAG,MAAM9G,IAAI,CAACiC,IAAL,CAAU0E,KAAV,CAAgBxE,CAAtB,GAA0B,IAAxC;AACAnC,cAAAA,IAAI,CAACtE,MAAL,CAAYuG,IAAZ,CAAiB4E,QAAjB,CAA0BC,OAA1B,EAAmCA,OAAnC;AACH,aAxBD;AAyBH;AACJ;AACD;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;;;AACIL,QAAAA,eAAe,CAACM,UAAD,EAAaC,aAAb,EAA4BC,WAA5B,EAAyCC,UAAzC,EAAqDC,QAArD,EAA+D;AAC1E,cAAMnH,IAAI,GAAG,IAAb,CAD0E,CAG1E;;AACA,eAAKjB,UAAL;AACA,eAAKD,WAAL,GAAmB,KAAnB,CAL0E,CAO1E;;AACA;AAAA;AAAA,kCAAQ0E,WAAR,CAAoB4D,WAApB,CAAgCL,UAAhC,EAA6CM,MAAD,IAAY;AACpD,gBAAIA,MAAJ,EAAY;AACR,kBAAMpF,IAAI,GAAG3I,WAAW,CAAC+N,MAAD,CAAxB;AACA,kBAAMC,SAAS,GAAGN,aAAa,KAAKjO,IAAlB,GAAyBkJ,IAAzB,GAAgCA,IAAI,CAACoB,YAAL,CAAkB2D,aAAlB,CAAlD;AAEA/E,cAAAA,IAAI,CAACiB,MAAL,GAAc,IAAd,CAJQ,CAMR;;AACA,kBAAIqE,YAAY,GAAGL,UAAU,CAACM,aAA9B;;AACA,kBAAIP,WAAW,IAAIA,WAAW,KAAK,EAAnC,EAAuC;AACnC,oBAAMQ,WAAW,GAAGP,UAAU,CAACR,cAAX,CAA0BO,WAA1B,CAApB;;AACA,oBAAIQ,WAAJ,EAAiB;AACbF,kBAAAA,YAAY,GAAGE,WAAW,CAACC,eAAZ,EAAf;AACH;AACJ;;AACDR,cAAAA,UAAU,CAACS,WAAX,CAAuB1F,IAAvB,EAA6BsF,YAA7B,EAdQ,CAgBR;;AACA,kBAAIJ,QAAJ,EAAc;AACVA,gBAAAA,QAAQ,CAACG,SAAD,CAAR;AACH,eAnBO,CAqBR;;;AACAtH,cAAAA,IAAI,CAACR,eAAL;AACH;AACJ,WAzBD;AA0BH,SA5hCgC,CA8hCjC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACJ;AACA;;;AACIwB,QAAAA,IAAI,GAAG;AACH,eAAKiB,IAAL,CAAU+C,MAAV,GAAmB,IAAnB;AACA,eAAKW,MAAL,CAAY1D,IAAZ,CAAiBoB,YAAjB,CAA8BhK,SAA9B,EAAyCiK,OAAzC,GAAmD,CAAnD,CAFG,CAIH;;AACA,eAAK2C,YAAL,GALG,CAOH;;AACA,cAAI,CAAC,KAAK9J,aAAV,EAAyB;AACrB,iBAAKA,aAAL,GAAqB,KAAKwE,OAAL;AAAA;AAAA,8CAA2B;AAAA;AAAA,+CAA3B,CAArB;AACH;;AACD,eAAKxE,aAAL,CAAmByE,OAAnB,GAA6B,KAA7B,CAXG,CAaH;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;;AACA,cAAI,CAAC,KAAKnE,QAAV,EAAoB;AAChB,iBAAKA,QAAL,GAAgBrD,KAAK,GAChB+K,EADW,CACR,CADQ,EACL;AAAEyD,cAAAA,KAAK,EAAE,IAAIlO,KAAJ,CAAU,GAAV,EAAe,CAAf,EAAkB,CAAlB;AAAT,aADK,EAC4B;AAD5B,aAEXyK,EAFW,CAER,IAFQ,EAEF;AAAEyD,cAAAA,KAAK,EAAE,IAAIlO,KAAJ,CAAU,GAAV,EAAe,GAAf,EAAoB,GAApB;AAAT,aAFE,CAAhB,CADgB,CAGoC;AACpD;AACH,WAjCE,CAmCH;;;AACA,eAAK4C,YAAL,CAAkBwE,OAAlB,CAA2B6D,KAAD,IAAW;AACjCA,YAAAA,KAAK,CAAC7D,OAAN,CAAe8D,IAAD,IAAU;AACpBA,cAAAA,IAAI,CAACiD,SAAL;AACH,aAFD;AAGH,WAJD,EApCG,CA0CH;;AACA,eAAK3B,cAAL,CAAoB,KAAK9G,MAAL,CAAY+G,QAAhC,EA3CG,CA6CH;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AAEA;AACA;AACH;AACD;AACJ;AACA;;;AACI7F,QAAAA,QAAQ,GAAG;AACP;AAAA;AAAA,kCAAQG,eAAR,CAAwBqH,WAAxB,IAAuC,CAAvC,CADO,CACmC;;AAC1C,eAAKC,MAAL,CAAY,CAAZ,EAFO,CAES;AACnB;AAED;AACJ;AACA;AACA;;;AACIA,QAAAA,MAAM,CAACC,UAAD,EAAa;AACf;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA,eAAK5I,MAAL,CAAYoB,GAAZ,GAAkB,KAAlB,CAZe,CAYU;;AACzB,eAAKpB,MAAL,CAAY6I,MAAZ,GAAqB,IAArB,CAbe,CAaY;;AAC3B,eAAKC,YAAL,CAAkB,MAAM;AACpB,iBAAK9I,MAAL,CAAY6I,MAAZ,GAAqB,KAArB;AACH,WAFD,EAEG,GAFH;AAIA;AAAA;AAAA,kCAAQxH,eAAR,CAAwBC,iBAAxB,GAA4C,IAA5C,CAlBe,CAkBmC;;AAClD;AAAA;AAAA,kCAAQd,gBAAR,CAAyBE,IAAzB,CAA8BiE,EAA9B,GAAmC;AAAA;AAAA,kCAAQnE,gBAAR,CAAyBE,IAAzB,CAA8BkE,KAAjE,CAnBe,CAmByD;;AACxE;AAAA;AAAA,kCAAQvD,eAAR,CAAwB0H,cAAxB,CAAuC;AAAA;AAAA,kCAAQvI,gBAAR,CAAyBE,IAAzB,CAA8BiE,EAArE,EApBe,CAoB2D;;AAC1E;AAAA;AAAA,kCAAQ9D,YAAR,CAAqBmI,IAArB,CAA0B;AAAA;AAAA,sCAAUjI,YAApC,EArBe,CAqBoC;AAEnD;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACH;AAED;AACJ;AACA;;;AACIkI,QAAAA,aAAa,GAAG;AACZ,eAAKlM,aAAL,CAAmByE,OAAnB,GAA6B,KAA7B,CADY,CACwB;;AACpC,eAAKsH,YAAL,CAAkB,MAAM;AACpBzO,YAAAA,KAAK,CAAC6O,eAAN,CAAsB,KAAKnF,IAAL,CAAUE,YAAV,CAAuBhK,SAAvB,CAAtB;AACA,iBAAK8J,IAAL,CAAUE,YAAV,CAAuBhK,SAAvB,EAAkCiK,OAAlC,GAA4C,GAA5C;AACA,iBAAKnH,aAAL,CAAmByE,OAAnB,GAA6B,IAA7B,CAHoB,CAGe;AACtC,WAJD,EAIG,CAJH;AAMA,eAAKuC,IAAL,CAAUE,YAAV,CAAuBhK,SAAvB,EAAkCiK,OAAlC,GAA4C,GAA5C;;AAEA,cAAI,KAAKhI,UAAT,EAAqB;AACjB,iBAAKA,UAAL,CAAgB2G,IAAhB,CAAqB+C,MAArB,GAA8B,IAA9B;AACA,iBAAK1J,UAAL,CAAgBiN,mBAAhB,CAAoC,KAAKC,aAAL,CAAmBC,IAAnB,CAAwB,IAAxB,CAApC;AACH;;AAEDrP,UAAAA,KAAK,CAAC,KAAK+J,IAAL,CAAUE,YAAV,CAAuBhK,SAAvB,CAAD,CAAL,CACK8K,EADL,CACQ,IADR,EACc;AAAEb,YAAAA,OAAO,EAAE;AAAX,WADd,EAEKa,EAFL,CAEQ,IAFR,EAEc;AAAEb,YAAAA,OAAO,EAAE;AAAX,WAFd,EAGKoF,KAHL,GAIKC,aAJL,GAKK5I,KALL;AAMH;AAED;AACJ;AACA;;;AACIyI,QAAAA,aAAa,GAAG;AACZ,cAAI,KAAKlN,UAAT,EAAqB;AACjB,iBAAKA,UAAL,CAAgB2G,IAAhB,CAAqB+C,MAArB,GAA8B,KAA9B;AACH;AACJ,SAvvCgC,CAyvCjC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AACA;AACJ;AACA;AACA;;;AACIkB,QAAAA,cAAc,CAAC0C,KAAD,EAAQ;AAClB,cAAIA,KAAK,KAAK,CAAd,EAAiB;AAEjB,eAAK5M,aAAL,GAAqB,EAArB;AACA,cAAM6M,SAAS,GAAG;AAAA;AAAA,kCAAQpI,eAAR,CAAwBqI,eAAxB,GAA0C,UAA1C,GAAuD,aAAzE;AACA,cAAMC,UAAU,GAAG,KAAK7M,QAAL,MAAiB2M,SAAjB,GAA6BD,KAA7B,CAAnB;;AAEA,eAAK,IAAI/F,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAGkG,UAAU,CAAChG,MAA/B,EAAuCF,CAAC,IAAI,CAA5C,EAA+C;AAC3C,gBAAMmG,UAAU,GAAGD,UAAU,CAACE,KAAX,CAAiBpG,CAAjB,EAAoBA,CAAC,GAAG,CAAxB,CAAnB;AACA,iBAAK7G,aAAL,CAAmBoH,IAAnB,CAAwB4F,UAAxB;AACH;;AAED,eAAKxM,iBAAL,GAAyB,IAAzB;AACA;AAAA;AAAA,kCAAQoD,gBAAR,CAAyBsJ,mBAAzB,GAA+C,KAAKlN,aAAL,CAAmB+G,MAAlE;AAEA,eAAK/G,aAAL,CAAmB8E,OAAnB,CAA2B,CAAChB,IAAD,EAAOqJ,KAAP,KAAiB;AACxC,gBAAI,KAAK9M,OAAL,CAAa8M,KAAb,KAAuB,IAA3B,EAAiC;AAC7B,mBAAKC,iBAAL,CAAuBtJ,IAAvB;AACH,aAFD,MAEO;AACH,mBAAKuJ,YAAL,CAAkBF,KAAlB,EAAyBrJ,IAAzB;AACH;;AAED,gBAAI,KAAK5D,QAAL,CAAcyF,IAAd,KAAuB,GAAvB,IAA8B,KAAKtF,OAAL,CAAa8M,KAAb,EAAoBG,OAApB,OAAkC,EAApE,EAAwE;AACpE,mBAAK9M,iBAAL,GAAyB,KAAKH,OAAL,CAAa8M,KAAb,CAAzB;AACH;AACJ,WAVD;;AAYA,eAAK,IAAItG,EAAC,GAAG,KAAK7G,aAAL,CAAmB+G,MAAhC,EAAwCF,EAAC,GAAG,KAAKxG,OAAL,CAAa0G,MAAzD,EAAiEF,EAAC,EAAlE,EAAsE;AAClE,iBAAKwG,YAAL,CAAkBxG,EAAlB,EAAqB,IAArB;AACH;AACJ,SAryCgC,CAuyCjC;AACA;AACA;AACA;AACA;AACA;AAEA;AAEA;AACA;AACA;AAEA;AACA;AACA;AAEA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AAEA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACJ;AACA;;;AACIoD,QAAAA,YAAY,GAAG;AACX;AACA;AACA;AACA,cAAMsD,UAAU,GAAG,KAAKnK,MAAL,CAAYoK,QAAZ,GAAuB,CAAvB,GAA2B,KAAKpK,MAAL,CAAYqK,OAAvC,GAAiD,KAAKrK,MAAL,CAAYsK,OAAhF,CAJW,CAMX;AACA;AACA;AACA;AACA;AACA;AAEA;;AACA,cAAMC,YAAY,GAAG,KAAKvK,MAAL,CAAYwK,WAAjC;AAEA,eAAK9M,WAAL,GAAmB;AACf+M,YAAAA,MAAM,EAAEN,UADO;AAEfO,YAAAA,QAAQ,EAAE,CAFK;AAGfC,YAAAA,UAAU,EAAE,CAHG;AAIfC,YAAAA,OAAO,EAAE,CAJM;AAKfC,YAAAA,MAAM,EAAE,CALO;AAKL;AACVC,YAAAA,UAAU,EAAE,CANG;AAMD;AACdP,YAAAA,YAAY,EAAEA,YAPC;AAQfQ,YAAAA,aAAa,EAAE,KAAK/K,MAAL,CAAYgL;AARZ,WAAnB;AAUH;AAED;AACJ;AACA;AACA;;;AACIC,QAAAA,SAAS,GAAG;AACR,iBAAO,KAAKvN,WAAL,CAAiB+M,MAAxB;AACH,SAp5CgC,CAs5CjC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AACA;AACJ;AACA;AACA;AACA;;;AACIR,QAAAA,YAAY,CAACF,KAAD,EAAQrJ,IAAR,EAAc;AACtB,cAAIA,IAAI,IAAI,IAAZ,EAAkB;AACd,gBAAIqJ,KAAK,GAAG,KAAK9M,OAAL,CAAa0G,MAAzB,EAAiC;AAC7B,kBAAM6B,IAAI,GAAG,KAAKvI,OAAL,CAAa8M,KAAb,CAAb;AACAvE,cAAAA,IAAI,CAACkB,OAAL,CAAa,IAAb,EAAmB,KAAKhJ,WAAxB,EAAqC,KAArC,EAA4C,IAA5C;AACA8H,cAAAA,IAAI,CAAC3C,IAAL,CAAU+C,MAAV,GAAmB,KAAnB;AACH;AACJ,WAND,MAMO;AACH,gBAAImE,KAAK,GAAG,KAAK9M,OAAL,CAAa0G,MAAzB,EAAiC;AAC7B,kBAAM6B,KAAI,GAAG,KAAKvI,OAAL,CAAa8M,KAAb,CAAb;AACAvE,cAAAA,KAAI,CAAC3C,IAAL,CAAU+C,MAAV,GAAmB,IAAnB;;AACAJ,cAAAA,KAAI,CAACkB,OAAL,CAAahG,IAAb,EAAmB,KAAKhD,WAAxB,EAAqC,KAArC,EAA4C,IAA5C;AACH,aAJD,MAIO;AACH,mBAAKsM,iBAAL,CAAuBtJ,IAAvB;AACH;AACJ;AACJ,SAn+CgC,CAq+CjC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACJ;AACA;AACA;AACA;;;AACIsJ,QAAAA,iBAAiB,CAACtJ,IAAD,EAAO;AACpB,cAAMkD,QAAQ,GAAG,IAAIjK,IAAJ,CAAS,MAAT,CAAjB;AACAiK,UAAAA,QAAQ,CAACE,MAAT,GAAkB,KAAKjB,IAAvB;AAEA,cAAM2C,IAAI,GAAG5B,QAAQ,CAACC,YAAT;AAAA;AAAA,uCAAb;AACA2B,UAAAA,IAAI,CAACkB,OAAL,CAAahG,IAAb,EAAmB,KAAKhD,WAAxB,EAAqC,KAArC,EAA4C,IAA5C;AAEA,eAAKT,OAAL,CAAa+G,IAAb,CAAkBwB,IAAlB;AACA,iBAAOA,IAAP;AACH,SA5/CgC,CA8/CjC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AAEA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AAEA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AAEA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACJ;AACA;;;AACIoB,QAAAA,aAAa,GAAG,CACZ;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AAEA;AACH;AACD;AACJ;AACA;AACA;AACA;;;AACIrD,QAAAA,YAAY,CAAC2H,KAAD,EAAQC,SAAR,EAAmB,CAC3B;AACA;AACA;AACA;AACA;AACH;AAED;AACJ;AACA;AACA;;;AACIC,QAAAA,KAAK,CAACC,MAAD,EAAS;AACV;AAAA;AAAA,kCAAQ7K,gBAAR,CAAyB8K,SAAzB,IAAsCD,MAAtC;AACA,cAAME,KAAK,GAAG;AAAA;AAAA,kCAAQ/K,gBAAR,CAAyBE,IAAzB,CAA8BiE,EAA9B,GAAmC0G,MAAjD;AACA;AAAA;AAAA,kCAAQ7K,gBAAR,CAAyBE,IAAzB,CAA8BiE,EAA9B,GAAmCzB,IAAI,CAACE,GAAL,CAAS,CAAT,EAAYmI,KAAZ,CAAnC;;AAEA,cAAIA,KAAK,GAAG,CAAZ,EAAe;AACX;AAAA;AAAA,oCAAQ/K,gBAAR,CAAyB8K,SAAzB,IAAsCC,KAAtC;AACH;;AAED;AAAA;AAAA,kCAAQ1K,YAAR,CAAqBmI,IAArB,CAA0B;AAAA;AAAA,sCAAUjI,YAApC;;AAEA,cAAI;AAAA;AAAA,kCAAQP,gBAAR,CAAyBE,IAAzB,CAA8BiE,EAA9B,IAAoC,CAApC,IAAyC,CAAC,KAAK3E,MAAL,CAAYoB,GAA1D,EAA+D;AAC3D,iBAAKoK,KAAL;AACH;AACJ;AAED;AACJ;AACA;AACA;;;AACIC,QAAAA,KAAK,CAACC,IAAD,EAAO;AACR;AAAA;AAAA,kCAAQlL,gBAAR,CAAyBE,IAAzB,CAA8BiE,EAA9B,GAAmCzB,IAAI,CAACC,GAAL,CAC/B;AAAA;AAAA,kCAAQ3C,gBAAR,CAAyBE,IAAzB,CAA8BkE,KADC,EAE/B;AAAA;AAAA,kCAAQpE,gBAAR,CAAyBE,IAAzB,CAA8BiE,EAA9B,GAAmC+G,IAFJ,CAAnC;AAIA;AAAA;AAAA,kCAAQ7K,YAAR,CAAqBmI,IAArB,CAA0B;AAAA;AAAA,sCAAUjI,YAApC;AACH;AAED;AACJ;AACA;AACA;;;AACI4K,QAAAA,SAAS,CAACC,SAAD,EAAY;AACjB,cAAI,KAAKpN,OAAL,IAAgB,KAAKA,OAAL,CAAaqN,UAAjC,EAA6C;AAE7C,cAAIR,MAAM,GAAG,CAAb;;AACA,cAAIO,SAAS,CAACE,MAAV;AAAA;AAAA,+BAAJ,EAAwC;AACpCT,YAAAA,MAAM,GAAGO,SAAS,CAACE,MAAV,CAAiBb,SAAjB,CAA2B,IAA3B,CAAT;AACH,WAFD,MAEO,IACHW,SAAS,CAACE,MAAV;AAAA;AAAA,6CACAF,SAAS,CAACE,MAAV;AAAA;AAAA,mCAFG,EAGL;AACET,YAAAA,MAAM,GAAGO,SAAS,CAACE,MAAV,CAAiBC,cAAjB,EAAT;AACH;;AAED,cAAIV,MAAM,GAAG,CAAb,EAAgB;AACZ,iBAAKD,KAAL,CAAWC,MAAX;;AACA,gBAAI;AAAA;AAAA,oCAAQ7K,gBAAR,CAAyBE,IAAzB,CAA8BiE,EAA9B,IAAoC,CAApC,IAAyC,CAAC,KAAK3E,MAAL,CAAYoB,GAA1D,EAA+D;AAC3D,mBAAKoK,KAAL;AACH;AACJ;AACJ;AAED;AACJ;AACA;AACA;AACA;;;AACIQ,QAAAA,SAAS,CAACd,KAAD,EAAQe,KAAR,EAAe;AACpB,cAAI,CAAC,KAAKjM,MAAL,CAAYoB,GAAb,IAAoB,KAAKvE,YAA7B,EAA2C;AACvC,iBAAKW,OAAL,IAAgB0N,KAAhB;AACA,iBAAKzN,OAAL,IAAgBwO,KAAhB;AACH;AACJ,SAtrDgC,CAwrDjC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACJ;AACA;;;AACIT,QAAAA,KAAK,GAAG;AACJ,eAAKxL,MAAL,CAAYoB,GAAZ,GAAkB,IAAlB,CADI,CACoB;;AACxB,eAAKrE,aAAL,CAAmByE,OAAnB,GAA6B,KAA7B,CAFI,CAEgC;;AAEpC,cAAI,KAAKpF,QAAT,EAAmB;AACfpC,YAAAA,KAAK,CAAC,KAAKoC,QAAN,CAAL,CAAqBqI,IAArB;AACA,iBAAKrI,QAAL,CAAcsI,SAAd,GAA0B,CAA1B,CAFe,CAEc;AAChC;;AAED,eAAKwH,YAAL,GATI,CASiB;AACrB;;;AACA;AAAA;AAAA,kCAAQ7K,eAAR,CAAwBC,iBAAxB,GAA4C,KAA5C;AACH;AAED;AACJ;AACA;;;AACI4K,QAAAA,YAAY,GAAG;AACX,eAAKC,KAAL,CAAWtJ,IAAX,CAAgBoB,YAAhB,CAA6BhK,SAA7B,EAAwCiK,OAAxC,GAAkD,GAAlD,CADW,CAC4C;;AACvD,eAAKiI,KAAL,CAAWhD,mBAAX,CAA+B,KAAKiD,WAAL,CAAiB/C,IAAjB,CAAsB,IAAtB,CAA/B,EAFW,CAEkD;;AAC7D,eAAK8C,KAAL,CAAW5H,YAAX,CAAwB,CAAxB,EAA2B,MAA3B,EAAmC,KAAnC,EAHW,CAGgC;AAC9C;AAED;AACJ;AACA;;;AACI6H,QAAAA,WAAW,GAAS;AAChB;AACA,eAAKD,KAAL,CAAWtJ,IAAX,CAAgBoB,YAAhB,CAA6BhK,SAA7B,EAAwCiK,OAAxC,GAAkD,CAAlD,CAFgB,CAIhB;;AACA,cAAI,KAAKlE,MAAL,CAAYqM,OAAZ,GAAsB,CAA1B,EAA6B;AACzB;AACA,iBAAKrM,MAAL,CAAYqM,OAAZ,GAFyB,CAIzB;AACA;AAEA;;AACA,iBAAK1D,MAAL,CAAY,CAAZ;AACH,WATD,MASO;AACH;AACA,gBAAMD,WAAW,GAAG;AAAA;AAAA,oCAAQrH,eAAR,CAAwBqH,WAA5C;;AACA,gBAAI,KAAK1I,MAAL,CAAYsM,SAAZ,GAAwB5D,WAAxB,IAAuC,CAA3C,EAA8C;AAC1C;AACA;AACA;AACA;AACA;AACI;AAAA;AAAA,sCAAQrI,aAAR,CAAsBkM,UAAtB,GANsC,CAO1C;AACH,aARD,MAQO;AACH;AACA;AAAA;AAAA,sCAAQlM,aAAR,CAAsBmM,SAAtB;AACH;AACJ;AACJ;AAED;AACJ;AACA;AACA;;;AACI1K,QAAAA,aAAa,CAAC2K,MAAD,EAAS;AAClB,cAAI,KAAK3P,QAAL,IAAiB,KAAKA,QAAL,CAAcyF,IAAd,KAAuB,GAA5C,EAAiD;;AAEjD,cAAIkK,MAAJ,EAAY;AACR,iBAAK9P,UAAL,CAAgB+E,OAAhB,CAAyBgL,IAAD,IAAU;AAC9BA,cAAAA,IAAI,CAACzI,YAAL,CAAkBhK,SAAlB,EAA6BiK,OAA7B,GAAuC,GAAvC,CAD8B,CACc;AAC/C,aAFD;AAGA,iBAAKyI,KAAL,CAAW7I,MAAX,CAAkBG,YAAlB,CAA+BhK,SAA/B,EAA0CiK,OAA1C,GAAoD,GAApD;AACH,WALD,MAKO;AACH,iBAAKvH,UAAL,CAAgB+E,OAAhB,CAAyBgL,IAAD,IAAU;AAC9BA,cAAAA,IAAI,CAACzI,YAAL,CAAkBhK,SAAlB,EAA6BiK,OAA7B,GAAuC,CAAvC,CAD8B,CACY;AAC7C,aAFD;AAGA,iBAAKyI,KAAL,CAAW7I,MAAX,CAAkBG,YAAlB,CAA+BhK,SAA/B,EAA0CiK,OAA1C,GAAoD,CAApD;AACH;AACJ;AAED;AACJ;AACA;AACA;;;AACI0I,QAAAA,WAAW,CAACH,MAAD,EAAS;AAChB,eAAK5P,YAAL,GAAoB4P,MAApB;AACH;AAED;AACJ;AACA;AACA;;;AACII,QAAAA,UAAU,CAACJ,MAAD,EAAS;AACf,eAAK1P,aAAL,CAAmByE,OAAnB,GAA6BiL,MAA7B;AACH;AAED;AACJ;AACA;;;AACIK,QAAAA,SAAS,GAAG;AACR;AAAA;AAAA,kCAAQtM,gBAAR,CAAyB8B,UAAzB,GAAsC,IAAtC;AACH;AAED;AACJ;AACA;;;AACIyK,QAAAA,QAAQ,GAAG;AACP;AAAA;AAAA,kCAAQvM,gBAAR,CAAyB8B,UAAzB,GAAsC,KAAtC;AACH,SA/yDgC,CAizDjC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACJ;AACA;;;AACI0K,QAAAA,UAAU,GAAG;AACT,cAAI;AAAA;AAAA,kCAAQ3M,aAAR,CAAsB4M,UAA1B,EAAsC;AAClC,gBAAMtI,EAAE,GAAG;AAAA;AAAA,oCAAQtD,eAAR,CAAwB6L,cAAxB,EAAX;AACA;AAAA;AAAA,oCAAQ1M,gBAAR,CAAyBE,IAAzB,CAA8BiE,EAA9B,GAAmCA,EAAnC,CAFkC,CAEK;;AACvC,iBAAK3D,QAAL,GAHkC,CAGjB;AACpB;AACJ;AAED;AACJ;AACA;AACA;;;AACImM,QAAAA,MAAM,CAACF,UAAD,EAAqB;AAAA,cAApBA,UAAoB;AAApBA,YAAAA,UAAoB,GAAP,KAAO;AAAA;;AACvB,cAAIA,UAAJ,EAAgB;AACZ,iBAAKH,SAAL;AACA,iBAAKjQ,YAAL,GAAoB,IAApB,CAFY,CAGZ;;AACA,gBAAI,KAAKE,aAAT,EAAwB;AACpB,mBAAKA,aAAL,CAAmByE,OAAnB,GAA6B,IAA7B;AACH;;AACD,gBAAI,KAAKhD,OAAT,EAAkB;AACd,mBAAKA,OAAL,CAAa4O,WAAb;AACH;AACJ,WAVD,MAUO;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA,oCAAQ/M,aAAR,CAAsB+M,WAAtB,GAZG,CAaH;AACA;AACH;;AAED;AAAA;AAAA,kCAAQ/L,eAAR,CAAwB0H,cAAxB,CAAuC;AAAA;AAAA,kCAAQvI,gBAAR,CAAyBE,IAAzB,CAA8BiE,EAArE,EA5BuB,CA8BvB;AACA;AACA;AACA;AACA;AACA;AACH,SAt3DgC,CAu3DjC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACJ;AACA;;;AACI0I,QAAAA,OAAO,GAAS;AACZ,cAAMzM,IAAI,GAAG,IAAb,CADY,CAGZ;;AACA,eAAKoM,UAAL;AACA,cAAMM,SAAS,GAAG;AAAA;AAAA,wCAAWC,eAA7B,CALY,CAOZ;AACA;AAEA;;AACA,eAAK1K,IAAL,CAAUoB,YAAV,CAAuBhK,SAAvB,EAAkCiK,OAAlC,GAA4C,GAA5C;AACA,eAAKrB,IAAL,CAAUiB,MAAV,GAAmB;AAAA;AAAA,0CAAY0J,EAAZ,CAAeC,cAAlC;AACA,eAAK1J,IAAL,CAAUE,YAAV,CAAuBhK,SAAvB,EAAkCiK,OAAlC,GAA4C,GAA5C;AACA,eAAKH,IAAL,CAAU0D,QAAV,CAAmB,CAAnB,EAAsB,CAAtB;AACA,cAAIzE,IAAI,GAAG,CAACzI,IAAI,CAACmT,cAAL,GAAsBC,MAAvB,IAAiC,KAAK7Q,QAAL,CAAcyF,IAAd,KAAuB,GAAvB,GAA6B,IAA7B,GAAoC,EAArE,CAAX;AACA,eAAKM,IAAL,CAAUS,WAAV,CAAsB,CAAtB,EAAyBN,IAAzB;AACA,eAAKH,IAAL,CAAU4E,QAAV,CAAmB;AAAA;AAAA,kCAAQpH,aAAR,CAAsBuN,QAAtB,EAAnB,EAAqD;AAAA;AAAA,kCAAQvN,aAAR,CAAsBuN,QAAtB,EAArD;AACA,eAAKb,QAAL;AACA,eAAKlQ,YAAL,GAAoB,KAApB;AACAxC,UAAAA,KAAK,CAAC6O,eAAN,CAAsB,KAAKrG,IAA3B,EApBY,CAsBZ;;AACA,eAAKjF,SAAL,GAAiB5D,KAAK,CAAC;AAAA;AAAA,wCAAW6T,QAAZ,CAAL,CACZ9I,EADY,CACT;AAAA;AAAA,oCAAS+I,MAAT,CAAgB,CAAhB,EAAmB,EAAnB,IAAyB;AAAA;AAAA,kCAAQzN,aAAR,CAAsB0N,SADtC,EACiD;AAC1DC,YAAAA,mBAAmB,EAAE,IADqC;AAE1DC,YAAAA,mBAAmB,EAAE;AAFqC,WADjD,EAKZC,KALY,CAKN;AAAA;AAAA,oCAASJ,MAAT,CAAgB,EAAhB,EAAoB,EAApB,IAA0B;AAAA;AAAA,kCAAQzN,aAAR,CAAsB0N,SAL1C,EAMZhJ,EANY,CAMT;AAAA;AAAA,oCAAS+I,MAAT,CAAgB,EAAhB,EAAoB,EAApB,IAA0B;AAAA;AAAA,kCAAQzN,aAAR,CAAsB0N,SANvC,EAMkD;AAC3DC,YAAAA,mBAAmB,EAAE,IADsC;AAE3DC,YAAAA,mBAAmB,EAAE;AAFsC,WANlD,EAUZlJ,EAVY,CAUT;AAAA;AAAA,oCAAS+I,MAAT,CAAgB,EAAhB,EAAoB,EAApB,IAA0B;AAAA;AAAA,kCAAQzN,aAAR,CAAsB0N,SAVvC,EAUkD;AAC3DC,YAAAA,mBAAmB,EAAE,IADsC;AAE3DC,YAAAA,mBAAmB,EAAE;AAFsC,WAVlD,EAcZlJ,EAdY,CAcT;AAAA;AAAA,oCAAS+I,MAAT,CAAgB,EAAhB,EAAoB,GAApB,IAA2B;AAAA;AAAA,kCAAQzN,aAAR,CAAsB0N,SAdxC,EAcmD;AAC5DC,YAAAA,mBAAmB,EAAE,GADuC;AAE5DC,YAAAA,mBAAmB,EAAE;AAFuC,WAdnD,EAkBZlJ,EAlBY,CAkBT;AAAA;AAAA,oCAAS+I,MAAT,CAAgB,GAAhB,EAAqB,GAArB,IAA4B;AAAA;AAAA,kCAAQzN,aAAR,CAAsB0N,SAlBzC,EAkBoD;AAC7DC,YAAAA,mBAAmB,EAAE,GADwC;AAE7DC,YAAAA,mBAAmB,EAAE;AAFwC,WAlBpD,EAsBZlJ,EAtBY,CAsBT;AAAA;AAAA,oCAAS+I,MAAT,CAAgB,GAAhB,EAAqB,GAArB,IAA4B;AAAA;AAAA,kCAAQzN,aAAR,CAAsB0N,SAtBzC,EAsBoD;AAC7DC,YAAAA,mBAAmB,EAAE,CADwC;AAE7DC,YAAAA,mBAAmB,EAAE;AAFwC,WAtBpD,EA0BZjJ,IA1BY,CA0BP,MAAM;AACRpE,YAAAA,IAAI,CAAChD,SAAL,GAAiB,IAAjB;AACH,WA5BY,EA6BZ+C,KA7BY,EAAjB,CAvBY,CAsDZ;;AACA,eAAKmI,YAAL,CAAkB,MAAM;AACpB,gBAAMqF,OAAO,GAAG,CAAC5T,IAAI,CAACmT,cAAL,GAAsBC,MAAvB,GAAgC,GAAhD;AACA,gBAAMS,OAAO,GAAG,KAAKvL,IAAL,CAAUC,QAAV,CAAmBC,CAAnC;AACA/I,YAAAA,KAAK,CAAC,KAAK6I,IAAN,CAAL,CACKkC,EADL,CACQ,KAAKuI,SAAL,GAAiB;AAAA;AAAA,oCAAQjN,aAAR,CAAsB0N,SAD/C,EAC0D;AAAEjL,cAAAA,QAAQ,EAAEtI,EAAE,CAAC4T,OAAD,EAAUD,OAAO,GAAG,EAApB;AAAd,aAD1D,EAEKpJ,EAFL,CAEQ,KAAKuI,SAAL,GAAiB;AAAA;AAAA,oCAAQjN,aAAR,CAAsB0N,SAF/C,EAE0D;AAAEjL,cAAAA,QAAQ,EAAEtI,EAAE,CAAC4T,OAAD,EAAUD,OAAO,GAAG,EAApB;AAAd,aAF1D,EAGKpJ,EAHL,CAGQ,KAAKuI,SAAL,GAAiB;AAAA;AAAA,oCAAQjN,aAAR,CAAsB0N,SAH/C,EAG0D;AAAEjL,cAAAA,QAAQ,EAAEtI,EAAE,CAAC4T,OAAD,EAAUD,OAAO,GAAG,EAApB;AAAd,aAH1D,EAIKpJ,EAJL,CAIQ,KAAKuI,SAAL,GAAiB;AAAA;AAAA,oCAAQjN,aAAR,CAAsB0N,SAJ/C,EAI0D;AAAEjL,cAAAA,QAAQ,EAAEtI,EAAE,CAAC4T,OAAD,EAAUD,OAAV;AAAd,aAJ1D,EAKKnJ,IALL,CAKU,MAAM;AACRpE,cAAAA,IAAI,CAACuM,MAAL;AACH,aAPL,EAQKxM,KARL;AAUA3G,YAAAA,KAAK,CAAC,KAAK+J,IAAN,CAAL,CACKgB,EADL,CACQ,KAAKuI,SAAL,GAAiB;AAAA;AAAA,oCAAQjN,aAAR,CAAsB0N,SAD/C,EAC0D;AAAExG,cAAAA,KAAK,EAAE/M,EAAE,CAAC,GAAD,EAAM,GAAN;AAAX,aAD1D,EAEKuK,EAFL,CAEQ,KAAKuI,SAAL,GAAiB;AAAA;AAAA,oCAAQjN,aAAR,CAAsB0N,SAF/C,EAE0D;AAAExG,cAAAA,KAAK,EAAE/M,EAAE,CAAC,GAAD,EAAM,GAAN;AAAX,aAF1D,EAGKuK,EAHL,CAGQ,KAAKuI,SAAL,GAAiB;AAAA;AAAA,oCAAQjN,aAAR,CAAsB0N,SAH/C,EAG0D;AAAExG,cAAAA,KAAK,EAAE/M,EAAE,CAAC,CAAD,EAAI,CAAJ;AAAX,aAH1D,EAIKuK,EAJL,CAIQ,KAAKuI,SAAL,GAAiB;AAAA;AAAA,oCAAQjN,aAAR,CAAsB0N,SAJ/C,EAI0D;AAAExG,cAAAA,KAAK,EAAE/M,EAAE,CAAC,CAAD,EAAI,CAAJ;AAAX,aAJ1D,EAKKmG,KALL,GAboB,CAoBpB;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA,iBAAKmI,YAAL,CAAkB,MAAM;AACpB9O,cAAAA,KAAK,CAAC,KAAKqM,MAAL,CAAYxD,IAAb,CAAL,CACKkC,EADL,CACQ,IAAIuI,SAAJ,GAAgB;AAAA;AAAA,sCAAQjN,aAAR,CAAsB0N,SAD9C,EACyD;AAAExG,gBAAAA,KAAK,EAAE/M,EAAE,CAAC,CAAD,EAAI,CAAJ;AAAX,eADzD,EAEKuK,EAFL,CAEQ,IAAIuI,SAAJ,GAAgB;AAAA;AAAA,sCAAQjN,aAAR,CAAsB0N,SAF9C,EAEyD;AAAExG,gBAAAA,KAAK,EAAE/M,EAAE,CAAC,CAAD,EAAI,CAAJ;AAAX,eAFzD,EAGKuK,EAHL,CAGQ,IAAIuI,SAAJ,GAAgB;AAAA;AAAA,sCAAQjN,aAAR,CAAsB0N,SAH9C,EAGyD;AAAExG,gBAAAA,KAAK,EAAE/M,EAAE,CAAC,CAAD,EAAI,CAAJ;AAAX,eAHzD,EAIKwK,IAJL,CAIU,MAAM;AACRpE,gBAAAA,IAAI,CAAC4D,SAAL;AACH,eANL,EAOK7D,KAPL;AAQH,aATD,EASG,IAAI2M,SAAJ,GAAgB;AAAA;AAAA,oCAAQjN,aAAR,CAAsB0N,SATzC;AAUH,WAtCD,EAsCG,IAAIT,SAAJ,GAAgB;AAAA;AAAA,kCAAQjN,aAAR,CAAsB0N,SAtCzC,EAvDY,CA+FZ;AACA;AACA;AACA;AACA;AACA;AACH,SAv/DgC,CAy/DjC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AAEA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AAEA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AAEA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AAEA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACJ;AACA;;;AACI/G,QAAAA,OAAO,GAAG;AACN,eAAKlJ,cAAL,GAAsB,IAAIX,GAAJ,EAAtB;AAEA,cAAMkR,MAAM,GAAG,KAAKC,UAAL,CAAgBzL,IAAhB,CAAqByE,cAArB,CAAoC,QAApC,CAAf;AACA+G,UAAAA,MAAM,CAACpK,YAAP,CAAoBrK,MAApB,EAA4B0M,WAA5B,GAA2C;AAAA;AAAA,kCAAQlC,WAAR,CAAoBmK,QAApB,CACpC,KAAKzR,QAAL,CAAc0R,QAAd,CAAuB,CAAvB,CADoC,+CAEF,KAAK1R,QAAL,CAAcyF,IAFZ,CAA3C;AAKA,cAAMO,QAAQ,GAAG,KAAKhG,QAAL,CAAc2R,QAAd,CAAuB,CAAvB,EAA0BC,KAA1B,CAAgC,GAAhC,CAAjB;AACAL,UAAAA,MAAM,CAAC/K,WAAP,CAAmBqL,MAAM,CAAC7L,QAAQ,CAAC,CAAD,CAAT,CAAzB,EAAwC6L,MAAM,CAAC7L,QAAQ,CAAC,CAAD,CAAT,CAA9C;AAEA,eAAK8L,OAAL,CAAa/L,IAAb,CAAkBgM,QAAlB,CAA2BnN,OAA3B,CAAoCoN,KAAD,IAAW;AAC1C,gBAAMC,GAAG,GAAGD,KAAK,CAACxH,cAAN,CAAqB,KAArB,CAAZ;AACAyH,YAAAA,GAAG,CAAC9K,YAAJ,CAAiBrK,MAAjB,EAAyB0M,WAAzB,GAAwC;AAAA;AAAA,oCAAQlC,WAAR,CAAoBmK,QAApB,CACjC,KAAKzR,QAAL,CAAc0R,QAAd,CAAuB,CAAvB,CADiC,+CAEC,KAAK1R,QAAL,CAAcyF,IAFf,CAAxC;AAGkB;AAClB,gBAAMyM,GAAG,GAAG,KAAKlS,QAAL,CAAc2R,QAAd,CAAuB,CAAvB,EAA0BC,KAA1B,CAAgC,GAAhC,CAAZ;AACAI,YAAAA,KAAK,CAACxL,WAAN,CAAkBqL,MAAM,CAACK,GAAG,CAAC,CAAD,CAAJ,CAAxB,EAAkCL,MAAM,CAACK,GAAG,CAAC,CAAD,CAAJ,CAAxC;AACH,WARD;AAUA,eAAKC,OAAL,CAAapM,IAAb,CAAkBgM,QAAlB,CAA2BnN,OAA3B,CAAoCoN,KAAD,IAAW;AAC1C,gBAAMI,GAAG,GAAGJ,KAAK,CAACxH,cAAN,CAAqB,KAArB,CAAZ;AACA4H,YAAAA,GAAG,CAACjL,YAAJ,CAAiBrK,MAAjB,EAAyB0M,WAAzB,GAAwC;AAAA;AAAA,oCAAQlC,WAAR,CAAoBmK,QAApB,CACjC,KAAKzR,QAAL,CAAc0R,QAAd,CAAuB,CAAvB,CADiC,+CAEC,KAAK1R,QAAL,CAAcyF,IAFf,CAAxC;AAGkB;AAClB,gBAAMyM,GAAG,GAAG,KAAKlS,QAAL,CAAc2R,QAAd,CAAuB,CAAvB,EAA0BC,KAA1B,CAAgC,GAAhC,CAAZ;AACAI,YAAAA,KAAK,CAACxL,WAAN,CAAkBqL,MAAM,CAACK,GAAG,CAAC,CAAD,CAAJ,CAAxB,EAAkCL,MAAM,CAACK,GAAG,CAAC,CAAD,CAAJ,CAAxC;AACH,WARD;AASH,SAx2EgC,CAy2EjC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACJ;AACA;AACA;;;AACIG,QAAAA,iBAAiB,CAACC,aAAD,EAAgB;AAC7B,cAAI,KAAKnT,aAAT,EAAwB;AACpB,iBAAKA,aAAL,CAAmB4G,IAAnB,CAAwB+C,MAAxB,GAAiC,IAAjC;AACA,iBAAK3J,aAAL,CAAmBoT,SAAnB,GAA+BD,aAA/B;AAEA,iBAAKnT,aAAL,CAAmBkN,mBAAnB,CAAuC,MAAM;AACzC,mBAAKlN,aAAL,CAAmB4G,IAAnB,CAAwB+C,MAAxB,GAAiC,KAAjC;AACA,mBAAK3J,aAAL,CAAmBkN,mBAAnB,CAAuC,IAAvC;AACH,aAHD;AAKA,iBAAK1M,iBAAL,GAAyB,IAAzB;AACH,WAVD,MAUO;AACH,iBAAKA,iBAAL,GAAyB2S,aAAzB;AACH;AACJ,SA38EgC,CA68EjC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AAEA;AACA;AACA;AAEA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACJ;AACA;;;AACItJ,QAAAA,cAAc,GAAG,CACb;AACA;AACA;AACH,SAryFgC,CAuyFjC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AAEA;AACA;AACA;AAEA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;AAn/FiC,O,UAE1BwJ,U,GAAa,W;;;;;iBAGF,I;;;;;;;iBAGM,I;;;;;;;iBAGP,I;;;;;;;iBAGC,I;;;;;;;iBAGJ,I;;;;;;;iBAGD,I;;;;;;;iBAGQ,I;;;;;;;iBAGH,I;;;;;;;iBAGD,I;;;;;;;iBAGA,I;;;;;;;iBAGJ,I;;;;;;;iBAGF,I;;;;;;;iBAGC,I;;;;;;;iBAGY,I;;;;;;;iBAGH,I;;;;;;;iBAGA,I;;;;;;;iBAGF,I;;;;;;;iBAGC,I;;;;;;;iBAGA,I;;;;;;;iBAGD,I;;;;;;;iBAGG,I", "sourcesContent": ["import { _decorator, Component, Node, Sprite, Animation, Label, Vec2, tween, UIOpacity, instantiate, sp, UITransform, Tween, Color, view, v3, v2, SpriteFrame } from \"cc\";\r\nimport Plane from \"../Plane\";\r\nimport { ColliderComp } from \"../../base/ColliderComp\";\r\nimport { GameConst } from \"../../../const/GameConst\";\r\nimport { GameIns } from \"../../../GameIns\";\r\nimport MainRelifeComp from \"./MainRelifeComp\";\r\nimport HDSprite from \"../../base/HDSprite\";\r\nimport MainSkillBase from \"./MainSkillBase\";\r\nimport { MainData } from \"../../../data/MainData\";\r\nimport GameEnum from \"../../../const/GameEnum\";\r\nimport GameConfig from \"../../../const/GameConfig\";\r\nimport BattleLayer from \"../../layer/BattleLayer\";\r\nimport GameMapRun from \"../../map/GameMapRun\";\r\nimport { GameFunc } from \"../../../GameFunc\";\r\nimport FireShells from \"./FireShells\";\r\nimport Bullet from \"../../bullet/Bullet\";\r\nimport EnemyEntity from \"../enemy/EnemyEntity\";\r\nimport BossUnit from \"../boss/BossUnit\";\r\nimport GameEvent from \"../../../event/GameEvent\";\r\n\r\nconst { ccclass, property } = _decorator;\r\n\r\n\r\n/**\r\n * 动画状态枚举\r\n */\r\nenum AnimState {\r\n    idle = 0,             // 空闲状态\r\n    crazy = 1,            // 疯狂状态\r\n    willCancelCrazy = 2   // 即将取消疯狂状态\r\n}\r\n\r\n@ccclass(\"MainPlane\")\r\nexport class MainPlane extends Plane {\r\n\r\n    static PrefabName = \"MainPlane\";\r\n\r\n    @property(Sprite)\r\n    skinImg: Sprite = null;\r\n\r\n    @property(sp.Skeleton)\r\n    skinAnim: sp.Skeleton = null;\r\n\r\n    @property(Sprite)\r\n    streak: Sprite = null;\r\n\r\n    @property(Node)\r\n    planeNode: Node = null;\r\n\r\n    @property(Node)\r\n    point: Node = null;\r\n\r\n    @property(Node)\r\n    skin: Node = null;\r\n\r\n    @property(sp.Skeleton)\r\n    blast: sp.Skeleton = null;\r\n\r\n    @property(Node)\r\n    frontNode: Node = null;\r\n\r\n    @property(Node)\r\n    backNode: Node = null;\r\n\r\n    @property(Sprite)\r\n    shadow: Sprite = null;\r\n\r\n    @property(Sprite)\r\n    dg: Sprite = null;\r\n\r\n    @property(Node)\r\n    bl: Node = null;\r\n\r\n    @property(Node)\r\n    air: Node = null;\r\n\r\n    @property(Animation)\r\n    attspeAnim: Animation = null;\r\n\r\n    @property(Animation)\r\n    scrAnim: Animation = null;\r\n\r\n    @property(Animation)\r\n    auxAnim: Animation = null;\r\n\r\n    @property(Node)\r\n    avatarNode: Node = null;\r\n\r\n    @property(Node)\r\n    suitAnimBot: Node = null;\r\n\r\n    @property(Node)\r\n    suitAnimTop: Node = null;\r\n\r\n    @property(Node)\r\n    suitEffect: Node = null;\r\n\r\n    @property(Node)\r\n    mechaAnimNode: Node = null;\r\n\r\n\r\n\r\n    // // 飞机皮肤相关\r\n    // skinImg = null; // 飞机皮肤图片\r\n    // skinAnim = null; // 飞机皮肤动画\r\n    // streak = null; // 飞机拖尾效果\r\n\r\n    // // 飞机节点\r\n    // planeNode = null; // 飞机主节点\r\n    // point = null; // 飞机中心点\r\n    // skin = null; // 飞机皮肤节点\r\n    // blast = null; // 爆炸效果节点\r\n    // frontNode = null; // 前置节点\r\n    // backNode = null; // 后置节点\r\n    // shadow = null; // 飞机阴影\r\n\r\n    // // 飞机状态相关\r\n    // dg = null; // 防御技能节点\r\n    // bl = null; // 蓝色特效节点\r\n    // air = null; // 空气特效节点\r\n    // attspeAnim = null; // 攻击特效动画\r\n    // scrAnim = null; // 屏幕特效动画\r\n    // auxAnim = null; // 辅助特效动画\r\n\r\n    // // 飞机附加节点\r\n    // avatarNode = null; // 飞机头像节点\r\n\r\n    parkourTop = null; // 跑酷顶部节点\r\n    parkourMid = null; // 跑酷中部节点\r\n    parkourBot = null; // 跑酷底部节点\r\n    below = null; // 飞机下方特效节点\r\n    challengeAnim = null; // 挑战动画节点\r\n    relifeComp = null; // 复活组件\r\n\r\n    // 血条相关\r\n    hpbarBack = null; // 血条背景\r\n    hpbarMid = null; // 血条中间部分\r\n    hpbarFont = null; // 血条前景\r\n    hpfont = null; // 血量文字\r\n    hpBar = null; // 血条节点\r\n    hpMidActin = null; // 血条动画\r\n\r\n    // 动画相关\r\n    challengeAnimName = null; // 挑战动画名称\r\n    // suitAnimBot = null; // 套装底部动画\r\n    // suitAnimTop = null; // 套装顶部动画\r\n    // suitEffect = null; // 套装特效\r\n    // mechaAnimNode = null; // 机甲动画节点\r\n    m_spines = [null, null, null, null]; // 骨骼动画数组\r\n    m_fireAnim = []; // 射击动画数组\r\n\r\n    // 飞机状态\r\n    m_screenDatas = []; // 屏幕数据\r\n    m_moveEnable = true; // 是否允许移动\r\n    m_config = null; // 飞机配置\r\n    m_collideComp = null; // 碰撞组件\r\n    bShootingSound = false; // 是否播放射击音效\r\n    m_fires = []; // 射击点数组\r\n    m_skillFires = new Map(); // 技能射击点\r\n    m_shieldKnifeFire = null; // 护盾刀火力\r\n    _hurtAct = null; // 受伤动画\r\n    _hurtActTime = 0; // 受伤动画时间\r\n    _hurtActDuration = 0.5; // 受伤动画持续时间\r\n\r\n    // 飞机移动\r\n    m_moveX = 0; // X 轴移动\r\n    m_moveY = 0; // Y 轴移动\r\n\r\n    // 飞机状态\r\n    m_fireState = null; // 射击状态\r\n    m_audioName = \"\"; // 音效名称\r\n    _mapTween = null; // 地图动画\r\n    _collideHurtTime = 0; // 碰撞伤害时间\r\n\r\n    // 飞机变形相关\r\n    mainTransLevel = new Map(); // 主变形等级\r\n    m_fireHand = null; // 火焰手节点\r\n    m_fireLight = null; // 火焰光节点\r\n    isPlayFireAnim = false; // 是否播放火焰动画\r\n\r\n    // 飞机初始位置\r\n    initPos = 50; // 初始位置\r\n    downSuitCall = null; // 套装下降回调\r\n    mechaCall1 = null; // 机甲回调 1\r\n    mechaCall2 = null; // 机甲回调 2\r\n    roguelikeInCall = null; // Roguelike 进入回调\r\n    roguelikeOutCall = null; // Roguelike 退出回调\r\n\r\n    // 技能相关\r\n    m_skill = null; // 技能\r\n    skillNode = null; // 技能节点\r\n    m_skinShield = null; // 皮肤护盾\r\n    m_skinCircle = null; // 皮肤圆圈\r\n    pfb = null; // 预制体\r\n    mechaAtlas_Anim = null; // 机甲图集动画\r\n    unitPrefab = []; // 单位预制体\r\n    animArr = []; // 动画数组\r\n    mechaUnitOverLight = null; // 机甲单位结束光效\r\n\r\n    // 飞机初始位置\r\n    initPosSuitBot = 0; // 套装底部初始位置\r\n    initPosSuitTop = 0; // 套装顶部初始位置\r\n    initPosMechaAnim = 0; // 机甲动画初始位置\r\n    initPosSkinAnim = 0; // 皮肤动画初始位置\r\n\r\n    // 技能相关\r\n    skillBatDis = []; // 技能蝙蝠距离\r\n    mechaScale = [0.8, 0.75, 1, 1]; // 机甲缩放\r\n    skillTargetNdoes = null; // 技能目标节点\r\n    skillBatPoint = Vec2.ZERO; // 技能蝙蝠点\r\n\r\n    // 加载状态\r\n    _loadFinish = false; // 是否加载完成\r\n    _loadTotal = 0; // 加载总数\r\n    _loadCount = 0; // 已加载数量\r\n\r\n    // 地图高度\r\n    mapH = {\r\n        701: 0,\r\n        702: -23,\r\n        703: -30,\r\n        705: 0,\r\n        706: 1\r\n    };\r\n\r\n    // 动画相关\r\n    _ligatureAnim = null; // 连线动画\r\n    _goldAnimArr = []; // 金币动画数组\r\n    m_data: MainData;\r\n    m_animState: AnimState = AnimState.idle; // 动画状态\r\n\r\n\r\n    get loadFinish(): boolean {\r\n        return this._loadFinish;\r\n    }\r\n\r\n    checkLoadFinish() {\r\n        this._loadCount++;\r\n        if (this._loadCount >= this._loadTotal) {\r\n            this._loadFinish = true;\r\n        }\r\n        GameIns.battleManager.addLoadingPercent(5 / this._loadTotal);\r\n    }\r\n\r\n    //     /**\r\n    //  * 隐藏套装 Buff 动画\r\n    //  */\r\n    //     hideSuitBuff() {\r\n    //         this.suitAnimBot.active = false;\r\n    //         this.suitAnimTop.active = false;\r\n    //     }\r\n\r\n    //     /**\r\n    //      * 显示套装 Buff 动画\r\n    //      */\r\n    //     showSuitBuff() {\r\n    //         this.suitAnimBot.active = true;\r\n    //         this.suitAnimTop.active = true;\r\n    //     }\r\n\r\n    //     /**\r\n    //      * 播放火焰手动画\r\n    //      */\r\n    //     playFireHandAnim() {\r\n    //         if (this.m_fireHand == null) {\r\n    //             this.m_fireHand = new Node();\r\n    //             const sprite = this.m_fireHand.addComponent(Sprite);\r\n    //             m.default.loadManager.setImage(sprite, \"zj_8_zd_1b\", \"mainBullet\");\r\n    //         }\r\n\r\n    //         if (this.m_fireLight == null) {\r\n    //             this.m_fireLight = new Node();\r\n    //             const sprite = this.m_fireLight.addComponent(Sprite);\r\n    //             m.default.loadManager.setImage(sprite, \"zj_8_zd_1b\", \"mainBullet\");\r\n    //         }\r\n\r\n    //         if (!this.isPlayFireAnim) {\r\n    //             this.m_fireHand.position = v2(0, this.initPos);\r\n    //             this.m_fireLight.position = v2(0, this.initPos);\r\n    //             this.m_fireHand.active = true;\r\n    //             this.m_fireHand.scale = 0.3;\r\n    //             this.m_fireHand.getComponent(UIOpacity).opacity = 255;\r\n\r\n    //             tween(this.m_fireHand)\r\n    //                 .to(_.default.fromTo(0, 8), { scale: 0.5, opacity: 255 })\r\n    //                 .to(_.default.fromTo(8, 15), { scale: 30, opacity: 0 })\r\n    //                 .call(() => {\r\n    //                     this.m_fireHand.active = false;\r\n    //                 })\r\n    //                 .start();\r\n\r\n    //             tween(this.m_fireLight)\r\n    //                 .delay(8 / 30)\r\n    //                 .call(() => {\r\n    //                     this.m_fireLight.scaleX = 0.08;\r\n    //                     this.m_fireLight.scaleY = 1;\r\n    //                     this.m_fireLight.getComponent(UIOpacity).opacity = 255;\r\n    //                     this.m_fireLight.active = true;\r\n    //                 })\r\n    //                 .to(_.default.fromTo(0, 6), {\r\n    //                     scaleX: 0.38,\r\n    //                     scaleY: 1.1,\r\n    //                     y: this.m_fireLight.y + 20,\r\n    //                 })\r\n    //                 .call(() => {\r\n    //                     this.m_fireLight.active = false;\r\n    //                 })\r\n    //                 .start();\r\n    //         }\r\n    //     }\r\n    /**\r\n     * 生命周期方法：onLoad\r\n     * 初始化主飞机的配置和数据\r\n     */\r\n    onLoad() {\r\n        this.m_config = GameIns.mainPlaneManager.mainRecord; // 获取主飞机的配置记录\r\n        this.m_data = GameIns.mainPlaneManager.data; // 获取主飞机的数据信息\r\n    }\r\n\r\n    /**\r\n     * 生命周期方法：start\r\n     * 初始化主飞机的组件和状态\r\n     */\r\n    start() {\r\n        const self = this;\r\n\r\n        // 监听主飞机血量变化事件\r\n        GameIns.eventManager.on(GameEvent.MainHpChange, this.UpdateHp, this);\r\n        // 监听复活事件\r\n        GameIns.eventManager.on(GameEvent.MainRelife, this.onRelife, this);\r\n\r\n        // 初始化主飞机配置和状态\r\n        this.m_config = GameIns.mainPlaneManager.mainRecord;\r\n        this.enemy = false;\r\n        this.m_data.die = false;\r\n        GameIns.gameDataManager.battlePlaneActive = true;\r\n\r\n        // 初始化碰撞组件\r\n        if (!this.m_collideComp) {\r\n            this.m_collideComp = this.addComp(ColliderComp, new ColliderComp());\r\n        }\r\n        this.m_collideComp.enabled = true;\r\n\r\n\r\n        // 初始化动画状态\r\n        this.m_animState = AnimState.idle;\r\n\r\n        // 初始化所有组件\r\n        this.m_comps.forEach((comp) => {\r\n            comp.init(self);\r\n        });\r\n\r\n        // 初始化飞机\r\n        this.initPlane();\r\n\r\n        // 禁用射击\r\n        this.setFireEnable(false);\r\n\r\n        // // 获取护甲伤害比例\r\n        // Number(ConfigDataManager.getGlobalData(GameEnum.GBKey.AmorDemageRatio));\r\n    }\r\n\r\n    /**\r\n     * 生命周期方法：update\r\n     * 更新主飞机的逻辑\r\n     * @param {number} dt 时间增量\r\n     */\r\n    update(dt) {\r\n        if (!GameConst.GameAble) return;\r\n\r\n        // 限制帧率\r\n        if (dt > 0.2) dt = 0.016666666666667;\r\n\r\n        // 游戏状态为战斗时更新逻辑\r\n        if (\r\n            GameIns.gameRuleManager.gameState === GameEnum.GameState.Battle &&\r\n            GameIns.mainPlaneManager.fireEnable\r\n        ) {\r\n            if (!this.bShootingSound) {\r\n                this.bShootingSound = true;\r\n                if (this.m_audioName !== \"\") {\r\n                    if (\r\n                        this.m_config.type === 708 &&\r\n                        GameIns.mainPlaneManager.checkLockSkill()\r\n                    ) {\r\n                        this.m_audioName = \"dragon_fire\";\r\n                    } else if (\r\n                        this.m_config.type === 711 &&\r\n                        GameIns.mainPlaneManager.checkLockSkill()\r\n                    ) {\r\n                        this.m_audioName = \"\";\r\n                    } else {\r\n                        this.m_audioName = this.m_config.zjshoot;\r\n                    }\r\n                    // if (this.m_audioName !== \"\") {\r\n                    //     GameIns.audioManager.playEffect(this.m_audioName, true);\r\n                    // }\r\n                }\r\n            }\r\n\r\n            // 更新所有组件\r\n            this.m_comps.forEach((comp) => {\r\n                comp.update(dt);\r\n            });\r\n        } else if (\r\n            GameIns.gameRuleManager.gameState !== GameEnum.GameState.Over &&\r\n            GameIns.gameRuleManager.gameState !== GameEnum.GameState.Pause &&\r\n            !GameIns.mainPlaneManager.fireEnable\r\n        ) {\r\n            if (this.bShootingSound) {\r\n                this.bShootingSound = false;\r\n                if (this.m_audioName !== \"\") {\r\n                    // GameIns.audioManager.stop(this.m_audioName);\r\n                }\r\n            }\r\n        }\r\n\r\n        // 更新飞机移动\r\n        if (this.m_moveX !== 0 || this.m_moveY !== 0) {\r\n            let posX = this.node.position.x + this.m_moveX;\r\n            let posY = this.node.position.y + this.m_moveY;\r\n\r\n            // 限制飞机移动范围\r\n            posX = Math.min(360, posX);\r\n            posX = Math.max(-360, posX);\r\n            posY = Math.min(0, posY);\r\n            posY = Math.max(-GameConst.ViewHeight, posY);\r\n            this.node.setPosition(posX, posY);\r\n\r\n            this.setDirection(this.m_moveX, dt);\r\n        }\r\n\r\n        // 重置移动值\r\n        this.m_moveX = 0;\r\n        this.m_moveY = 0;\r\n\r\n        // 更新受伤动画时间\r\n        this._hurtActTime += dt;\r\n\r\n        // 更新碰撞伤害时间\r\n        if (this._collideHurtTime > 0) {\r\n            this._collideHurtTime -= dt;\r\n        }\r\n    }\r\n    /**\r\n     * 添加火焰动画\r\n     */\r\n    async addFireAnim() {\r\n        for (let i = 0; i < this.m_config.zjdmtxzb.length; i += 2) {\r\n            const x = this.m_config.zjdmtxzb[i];\r\n            const y = this.m_config.zjdmtxzb[i + 1];\r\n\r\n            const fireNode = new Node();\r\n            fireNode.addComponent(UITransform);\r\n            fireNode.addComponent(UIOpacity)\r\n            fireNode.setPosition(x, y);\r\n            fireNode.parent = this.skin;\r\n            this.m_fireAnim.push(fireNode);\r\n            fireNode.getComponent(UIOpacity).opacity = 0;\r\n\r\n            const skeletonData = await GameIns.loadManager.loadSpine(\"mainPlane/firePoint/skel_mainfire\") as sp.SkeletonData;\r\n            const skeleton = fireNode.addComponent(sp.Skeleton);\r\n            skeleton.skeletonData = skeletonData;\r\n            skeleton.setAnimation(0, \"play\", true);\r\n        }\r\n    }\r\n\r\n    //     /**\r\n    //      * 设置拖尾效果是否可见\r\n    //      * @param {boolean} visible 是否可见\r\n    //      */\r\n    //     setStreakVisible(visible) {\r\n    //         this.streak.node.active = visible;\r\n    //     }\r\n\r\n    /**\r\n     * 添加拖尾效果\r\n     */\r\n    addStreak() {\r\n        // this.streak.node.stopAllActions();\r\n        // const tween = tween(this.streak.node)\r\n        //     .to(1, { opacity: 255, scale: 1 })\r\n        //     .to(3, { opacity: 204, scale: 0.88 })\r\n        //     .to(6, { opacity: 255, scale: 1 });\r\n\r\n        // tween(this.streak.node).repeatForever(tween).start();\r\n    }\r\n\r\n    //     /**\r\n    //      * 获取防御技能\r\n    //      */\r\n    //     getaddDefenseSkill() {\r\n    //         this.dg.node.active = true;\r\n    //         this.dg.node.y = this.mapH[GameIns.mainPlaneManager.idToType(this.m_config.id)];\r\n    //         this.bl.active = true;\r\n    //         this.air.active = true;\r\n\r\n    //         this.bl.stopAllActions();\r\n    //         this.air.stopAllActions();\r\n\r\n    //         tween(this.bl)\r\n    //             .repeatForever(\r\n    //                 tween(this.bl)\r\n    //                     .to(1, { scale: 1 })\r\n    //                     .to(8, { scale: 0.9 })\r\n    //                     .to(22, { scale: 1 })\r\n    //             )\r\n    //             .start();\r\n\r\n    //         tween(this.air)\r\n    //             .repeatForever(\r\n    //                 tween(this.air)\r\n    //                     .to(1, { opacity: 255, scale: 1 })\r\n    //                     .to(4, { opacity: 102, scale: 1.07 })\r\n    //                     .to(11, { opacity: 255, scale: 1 })\r\n    //             )\r\n    //             .start();\r\n    //     }\r\n\r\n    //     /**\r\n    //      * 取消防御技能\r\n    //      */\r\n    //     cancelDefenseSkill() {\r\n    //         this.dg.node.active = false;\r\n    //         this.bl.active = false;\r\n    //         this.air.active = false;\r\n\r\n    //         this.bl.stopAllActions();\r\n    //         this.air.stopAllActions();\r\n    //     }\r\n\r\n    //     /**\r\n    //      * 初始化狂暴动画\r\n    //      */\r\n    //     async initCrazyAnim() {\r\n    //         const skeletonData = await GameIns.loadManager.loadSpine(this.m_config.rampagedh);\r\n    //         this.skinAnim.skeletonData = skeletonData;\r\n\r\n    //         if (this.m_config.type === 712) {\r\n    //             const shieldNode = new Node(\"shield\");\r\n    //             this.m_skinShield = shieldNode.addComponent(sp.Skeleton);\r\n    //             shieldNode.parent = this.skinAnim.node;\r\n    //             this.m_skinShield.premultipliedAlpha = false;\r\n\r\n    //             const shieldSkeletonData = await GameIns.loadManager.loadSpine(this.m_config.rampagedh + \"_shield\");\r\n    //             this.m_skinShield.skeletonData = shieldSkeletonData;\r\n\r\n    //             const circleNode = new Node(\"circle\");\r\n    //             this.m_skinCircle = circleNode.addComponent(sp.Skeleton);\r\n    //             circleNode.parent = this.skinAnim.node;\r\n    //             this.m_skinCircle.premultipliedAlpha = false;\r\n\r\n    //             const circleSkeletonData = await GameIns.loadManager.loadSpine(this.m_config.rampagedh + \"_circle\");\r\n    //             this.m_skinCircle.skeletonData = circleSkeletonData;\r\n    //         } else {\r\n    //             Tools.removeChildByName(this.skinAnim.node, \"shield\");\r\n    //             Tools.removeChildByName(this.skinAnim.node, \"circle\");\r\n    //             this.m_skinCircle = null;\r\n    //             this.m_skinShield = null;\r\n    //         }\r\n    //     }\r\n    /**\r\n     * 更新血量显示\r\n     */\r\n    UpdateHp() {\r\n        if (this.hpBar && this.hpbarFont && this.hpbarMid) {\r\n            // 停止当前血条动画\r\n            if (this.hpMidActin) {\r\n                this.hpMidActin.stop();\r\n                this.hpMidActin = null;\r\n            }\r\n\r\n            // 更新血条前景的填充范围\r\n            this.hpbarFont.fillRange = GameIns.mainPlaneManager.data.hp / GameIns.mainPlaneManager.data.maxhp;\r\n\r\n            // 计算血条动画时间\r\n            const duration = Math.abs(this.hpbarMid.fillRange - this.hpbarFont.fillRange);\r\n\r\n            // 血条中间部分的动画\r\n            this.hpMidActin = tween(this.hpbarMid)\r\n                .to(duration, { fillRange: this.hpbarFont.fillRange })\r\n                .call(() => {\r\n                    this.hpMidActin = null;\r\n                })\r\n                .start();\r\n\r\n            // 更新血量文字\r\n            this.hpfont.string = GameIns.mainPlaneManager.data.hp.toFixed(0);\r\n        }\r\n    }\r\n\r\n    /**\r\n     * 开始战斗\r\n     */\r\n    beginBattle() {\r\n        // 初始化主飞机技能\r\n        this.initMainSkill(this.m_config);\r\n\r\n        // 开始技能战斗\r\n        if (this.m_skill) {\r\n            this.m_skill.beginBattle();\r\n        }\r\n\r\n        // 组装主飞机\r\n        this.pieceTogetherMainPlane();\r\n    }\r\n\r\n    /**\r\n     * 退出战斗\r\n     */\r\n    battleQuit() {\r\n        this.m_data.die = false;\r\n        // GameIns.gameDataManager.battlePlaneActive = true;\r\n\r\n        // 清理技能火力点\r\n        this.m_skillFires.forEach((fires) => {\r\n            fires.forEach((fire) => {\r\n                fire.node.parent = null;\r\n                fire.node.destroy();\r\n            });\r\n        });\r\n        this.m_skillFires.clear();\r\n\r\n        // 重置飞机状态\r\n        this.skin.getComponent(UIOpacity).opacity = 255;\r\n        this.suitEffect.active = false;\r\n        if (this.downSuitCall) this.downSuitCall();\r\n        GameIns.mainPlaneManager.hideSkillNode();\r\n        this.quitGameSetPic();\r\n        // this.clearMechaOverPic(true);\r\n\r\n        // 结束技能战斗\r\n        if (this.m_skill) {\r\n            this.m_skill.quiteBattle();\r\n            this.m_skill.endBattle();\r\n        }\r\n\r\n        // 重置动画节点位置\r\n        this.skinAnim.node.y = this.initPosSkinAnim;\r\n        this.mechaAnimNode.y = this.initPosMechaAnim;\r\n        this.suitAnimBot.y = this.initPosSuitBot;\r\n        this.suitAnimTop.y = this.initPosSuitTop;\r\n\r\n        // 特殊处理某些飞机类型\r\n        if (this.m_config.type !== 710) {\r\n            if (this.m_config.type === 711) {\r\n                this.skinAnim.node.y -= 2;\r\n                this.mechaAnimNode.y -= 2;\r\n            } else {\r\n                this.skinAnim.node.y -= 100;\r\n                this.mechaAnimNode.y -= 100;\r\n            }\r\n        }\r\n    }\r\n\r\n    /**\r\n     * 初始化主飞机\r\n     */\r\n    async initPlane() {\r\n        // 清空拖尾、阴影和护盾的图像\r\n        this.streak.spriteFrame = null;\r\n        this.shadow.spriteFrame = null;\r\n        this.dg.spriteFrame = null;\r\n\r\n        // 获取主飞机的配置数据\r\n        this.m_config = GameIns.mainPlaneManager.mainRecord;\r\n        // 加载飞机资源\r\n        await GameIns.loadManager.loadAtlas(`mainPlane/package_mainPlane_trans_${this.m_config.type}`);\r\n\r\n        // 设置飞机音效\r\n        // this.m_audioName = this.m_config.zjshoot;\r\n\r\n        // // 设置飞机缩放\r\n        // if (this.m_config.type === 710) {\r\n        //     this.skinAnim.node.scale = 1;\r\n        //     this.mechaAnimNode.scale = 1;\r\n        // } else {\r\n        // this.skinAnim.node.scale = 1.25;\r\n        // this.mechaAnimNode.scale = 1.25;\r\n        //     if (this.m_config.type === 713) {\r\n        //         this.mechaAnimNode.scale = 1;\r\n        //     }\r\n        // }\r\n\r\n        // 添加火焰动画\r\n        await this.addFireAnim();\r\n\r\n        // 初始化碰撞组件\r\n        if (this.m_collideComp) {\r\n            this.m_collideComp.setData(this.m_config.body);\r\n        }\r\n\r\n        // 播放攻击点动画\r\n        this.playPointAnim();\r\n\r\n        // 获取火力状态\r\n        this.getFireState();\r\n\r\n        // 初始化狂暴动画\r\n        // await this.initCrazyAnim();\r\n\r\n        // 根据屏幕等级更改屏幕数据\r\n        this.changeScreenLv(this.m_data.screenLv);\r\n\r\n        // // 刷新拖尾效果\r\n        // this.refreshStreak();\r\n\r\n        // // 设置阴影\r\n        // const isMechaOver = GameIns.gameDataManager.isMechaOver || GameIns.mainPlaneManager.checkPlayMechaAnim();\r\n        // this.shadow.spriteFrame = GameIns.loadManager.getImage(\r\n        //     isMechaOver ? `${this.m_config.yz}2` : this.m_config.yz,\r\n        //     `package_mainPlane_trans_${this.m_config.type}`\r\n        // );\r\n        // this.shadow.node.getComponent(UIOpacity).opacity = 255;\r\n\r\n        // 初始化图片\r\n        this.initPic();\r\n\r\n        // // 设置防御技能图片\r\n        // this.dg.spriteFrame = GameIns.loadManager.getImage(\r\n        //     `${this.m_config.zjjdimage[0]}_dg`,\r\n        //     `package_mainPlane_trans_${this.m_config.type}`\r\n        // );\r\n        // if (this.dg.spriteFrame) {\r\n        //     this.dg.node.width = this.dg.spriteFrame.getOriginalSize().width;\r\n        //     this.dg.node.height = this.dg.spriteFrame.getOriginalSize().height;\r\n        // }\r\n        this.dg.node.active = false;\r\n        this.bl.active = false;\r\n        this.air.active = false;\r\n\r\n        // // 清理机甲图片\r\n        // this.clearMechaOverPic();\r\n\r\n        // // 设置套装动画初始位置\r\n        // if ([712, 714, 715].includes(this.m_config.type)) {\r\n        //     this.initPosSuitBot = 0;\r\n        //     this.initPosSuitTop = 0;\r\n        // } else {\r\n        //     this.initPosSuitBot = this.suitAnimBot.y;\r\n        //     this.initPosSuitTop = this.suitAnimTop.y;\r\n        // }\r\n    }\r\n    //     /**\r\n    //      * 刷新拖尾效果\r\n    //      */\r\n    //     refreshStreak() {\r\n    //         switch (this.m_config.type) {\r\n    //             case 713:\r\n    //                 if (GameIns.gameDataManager.isMechaOver) {\r\n    //                     this.streak.spriteFrame = GameIns.loadManager.getImage(\r\n    //                         this.m_config.wy + \"2\",\r\n    //                         \"package_mainPlane_trans_\" + this.m_config.type\r\n    //                     );\r\n    //                     this.streak.node.y = -288;\r\n    //                 } else {\r\n    //                     this.streak.spriteFrame = GameIns.loadManager.getImage(\r\n    //                         this.m_config.wy,\r\n    //                         \"package_mainPlane_trans_\" + this.m_config.type\r\n    //                     );\r\n    //                     this.streak.node.y = -37;\r\n    //                 }\r\n    //                 break;\r\n\r\n    //             case 714:\r\n    //             case 715:\r\n    //                 if (GameIns.gameDataManager.isMechaOver) {\r\n    //                     this.streak.spriteFrame = GameIns.loadManager.getImage(\r\n    //                         this.m_config.wy + \"2\",\r\n    //                         \"package_mainPlane_trans_\" + this.m_config.type\r\n    //                     );\r\n    //                     this.streak.node.y = -80;\r\n    //                 } else {\r\n    //                     this.streak.spriteFrame = GameIns.loadManager.getImage(\r\n    //                         this.m_config.wy,\r\n    //                         \"package_mainPlane_trans_\" + this.m_config.type\r\n    //                     );\r\n    //                     this.streak.node.y = -37;\r\n    //                 }\r\n    //                 break;\r\n\r\n    //             default:\r\n    //                 this.streak.spriteFrame = GameIns.loadManager.getImage(\r\n    //                     this.m_config.wy,\r\n    //                     \"package_mainPlane_trans_\" + this.m_config.type\r\n    //                 );\r\n    //                 this.streak.node.y = this.m_config.type === 712 ? -50 : -77;\r\n    //         }\r\n    //     }\r\n\r\n    /**\r\n     * 初始化主飞机的主要技能\r\n     * @param {Object} config 飞机配置\r\n     */\r\n    initMainSkill(config) {\r\n        this._loadFinish = true;\r\n        this._loadTotal = 0;\r\n        this._loadCount = 0;\r\n\r\n        if (this.m_skill) {\r\n            this.m_skill.quiteBattle();\r\n            this.removeComp(MainSkillBase);\r\n            this.m_skill = null;\r\n        }\r\n\r\n        this.animArr = [];\r\n\r\n        // switch (config.type) {\r\n        //     case 708:\r\n        //         this._loadFinish = false;\r\n        //         const dragonAssets = [\"dragonAnim1\", \"dragonAnim2\", \"dragonAnim3\"];\r\n        //         this.loadMechaRes(\"dragon\", dragonAssets);\r\n        //         this.m_skill = new MainSkillDragon();\r\n        //         this.addComp(MainSkillDragon, this.m_skill);\r\n        //         this.m_skill.setData(config);\r\n        //         this.initPosMechaAnim = 87.857;\r\n        //         this.initPosSkinAnim = -13.646;\r\n        //         break;\r\n\r\n        //     case 709:\r\n        //         this._loadFinish = false;\r\n        //         const batAssets = [\"batAnim1\", \"batAnim2\", \"batAnim3\", \"batAnim4\"];\r\n        //         this.animArr = [\r\n        //             \"animation/MainPlane/mecha/batAnim1\",\r\n        //             \"animation/MainPlane/mecha/batAnim2\",\r\n        //             \"animation/MainPlane/mecha/batAnim3\",\r\n        //             \"animation/MainPlane/mecha/batAnim4\",\r\n        //         ];\r\n        //         this.loadMechaRes(\"bat\", batAssets);\r\n        //         this.m_skill = new MainSkillBat();\r\n        //         this.addComp(MainSkillBat, this.m_skill);\r\n        //         this.m_skill.setData(config);\r\n        //         this.initPosMechaAnim = 84.506;\r\n        //         this.initPosSkinAnim = 42.646;\r\n        //         break;\r\n\r\n        //     case 710:\r\n        //         this._loadFinish = false;\r\n        //         const diaochanAssets = [\"diaochanAnim1\", \"diaochanAnim2\", \"diaochanAnim3\", \"diaochanAnim4\"];\r\n        //         this.animArr = [\r\n        //             \"animation/MainPlane/mecha/diaochanAnim1\",\r\n        //             \"animation/MainPlane/mecha/diaochanAnim2\",\r\n        //             \"animation/MainPlane/mecha/diaochanAnim4\",\r\n        //         ];\r\n        //         this.loadMechaRes(\"diaochan\", diaochanAssets);\r\n        //         this.initPosMechaAnim = -97;\r\n        //         this.initPosSkinAnim = -33;\r\n        //         break;\r\n\r\n        //     case 711:\r\n        //         this._loadFinish = false;\r\n        //         const swordAssets = [\"swordAnim1\"];\r\n        //         this.animArr = [\"animation/MainPlane/mecha/sword/swordAnim1\"];\r\n        //         this.loadMechaRes(\"sword\", swordAssets);\r\n        //         this.m_skill = new MainSkillSword();\r\n        //         this.addComp(MainSkillSword, this.m_skill);\r\n        //         this.m_skill.setData(config);\r\n        //         this.initPosMechaAnim = -15;\r\n        //         this.initPosSkinAnim = -2;\r\n        //         break;\r\n\r\n        //     case 712:\r\n        //         this._loadFinish = false;\r\n        //         const shieldAssets = [\"shieldAnim\"];\r\n        //         this.animArr = [\"animation/MainPlane/mecha/shield/shieldAnim\"];\r\n        //         this.loadMechaRes(\"shield\", shieldAssets);\r\n        //         this.m_skill = new MainSkillShield();\r\n        //         this.addComp(MainSkillShield, this.m_skill);\r\n        //         this.m_skill.setData(config);\r\n        //         this.initPosMechaAnim = 100;\r\n        //         this.initPosSkinAnim = 100;\r\n        //         break;\r\n\r\n        //     case 713:\r\n        //         this._loadFinish = false;\r\n        //         const deerAssets = [\"deerAnim\"];\r\n        //         this.animArr = [\"animation/MainPlane/mecha/deer/deerAnim\"];\r\n        //         this.loadMechaRes(\"deer\", deerAssets);\r\n        //         this.m_skill = new MainSkillDeer();\r\n        //         this.addComp(MainSkillDeer, this.m_skill);\r\n        //         this.m_skill.setData(config);\r\n        //         this.initPosMechaAnim = 100;\r\n        //         this.initPosSkinAnim = 100;\r\n        //         break;\r\n\r\n        //     case 714:\r\n        //         this._loadFinish = false;\r\n        //         const clothAssets = [\"clothAnim\"];\r\n        //         this.animArr = [\"animation/MainPlane/mecha/cloth/clothAnim\"];\r\n        //         this.loadMechaRes(\"cloth\", clothAssets);\r\n        //         this.m_skill = new MainSkillCloth();\r\n        //         this.addComp(MainSkillCloth, this.m_skill);\r\n        //         this.m_skill.setData(config);\r\n        //         this.initPosMechaAnim = 100;\r\n        //         this.initPosSkinAnim = 100;\r\n        //         break;\r\n\r\n        //     case 715:\r\n        //         this._loadFinish = false;\r\n        //         const legclothAssets = [\"legclothAnim\"];\r\n        //         this.animArr = [\"animation/MainPlane/mecha/legcloth/legclothAnim\"];\r\n        //         this.loadMechaRes(\"legcloth\", legclothAssets);\r\n        //         this.m_skill = new MainSkillLegCloth();\r\n        //         this.addComp(MainSkillLegCloth, this.m_skill);\r\n        //         this.m_skill.setData(config);\r\n        //         this.initPosMechaAnim = 100;\r\n        //         this.initPosSkinAnim = 100;\r\n        //         break;\r\n        // }\r\n    }\r\n    //     /**\r\n    //      * 加载机甲资源\r\n    //      * @param {string} type 机甲类型\r\n    //      * @param {Array<string>} assets 资源名称数组\r\n    //      */\r\n    //     loadMechaRes(type, assets) {\r\n    //         const self = this;\r\n\r\n    //         // 增加加载总数\r\n    //         this._loadTotal++;\r\n\r\n    //         // 加载机甲单位预制体\r\n    //         GameIns.loadManager.loadPrefab1(\"mechaUnit\", (prefab) => {\r\n    //             self.pfb = prefab;\r\n    //             self.checkLoadFinish();\r\n    //         });\r\n\r\n    //         // 加载机甲动画图集\r\n    //         this._loadTotal++;\r\n    //         GameIns.loadManager.loadAtlas1(type, (atlas) => {\r\n    //             self.mechaAtlas_Anim = atlas;\r\n    //             self.checkLoadFinish();\r\n    //         });\r\n\r\n    //         // 加载机甲结束光效预制体\r\n    //         this._loadTotal++;\r\n    //         GameIns.loadManager.loadPrefab1(\"unitOverLight\", (prefab) => {\r\n    //             self.mechaUnitOverLight = prefab;\r\n    //             self.checkLoadFinish();\r\n    //         });\r\n\r\n    //         // 加载其他资源\r\n    //         this.unitPrefab = [];\r\n    //         assets.forEach((asset) => {\r\n    //             this._loadTotal++;\r\n    //             GameIns.loadManager.loadPrefab1(asset, (prefab) => {\r\n    //                 self.unitPrefab.push(prefab);\r\n    //                 self.checkLoadFinish();\r\n    //             });\r\n    //         });\r\n    // }\r\n    /**\r\n     * 组装主飞机的节点\r\n     */\r\n    pieceTogetherMainPlane() {\r\n        const self = this;\r\n\r\n        // // 加载跑酷底部节点\r\n        // if (!this.parkourBot) {\r\n        //     this.instantiateNode(\"paoku_1\", Animation, \"suitAnimTop\", this.planeNode, (node) => {\r\n        //         self.parkourBot = node;\r\n        //         self.parkourBot.node.scale = 1.8;\r\n        //         self.parkourBot.node.getComponent(UIOpacity).opacity = 0;\r\n        //         self.parkourBot.node.active = false;\r\n        //     });\r\n        // }\r\n\r\n        // // 加载跑酷中部节点\r\n        // if (!this.parkourMid) {\r\n        //     this.instantiateNode(\"paoku_2\", Animation, \"paoku_1\", this.planeNode, (node) => {\r\n        //         self.parkourMid = node;\r\n        //         self.parkourMid.node.scale = 1.8;\r\n        //         self.parkourMid.node.getComponent(UIOpacity).opacity = 0;\r\n        //         self.parkourMid.node.active = false;\r\n        //     });\r\n        // }\r\n\r\n        // // 加载跑酷顶部节点\r\n        // if (!this.parkourTop) {\r\n        //     this.instantiateNode(\"paoku_3\", Animation, \"skin\", this.planeNode, (node) => {\r\n        //         self.parkourTop = node;\r\n        //         self.parkourTop.node.scale = 1.8;\r\n        //         self.parkourTop.node.getComponent(UIOpacity).opacity = 0;\r\n        //         self.parkourTop.node.active = false;\r\n        //     });\r\n        // }\r\n\r\n        // // 加载飞机下方特效节点\r\n        // if (!this.below) {\r\n        //     this.instantiateNode(\"below\", sp.Skeleton, \"bglight\", this.node, (node) => {\r\n        //         self.below = node;\r\n        //         self.below.node.active = false;\r\n        //     });\r\n        // }\r\n\r\n        // // 加载复活组件\r\n        // if (!this.relifeComp) {\r\n        //     this.instantiateNode(\"relief\", MainRelifeComp, \"laserPoint\", this.node, (node) => {\r\n        //         self.relifeComp = node;\r\n        //         self.relifeComp.node.active = false;\r\n        //     });\r\n        // }\r\n\r\n        // // 加载挑战动画节点\r\n        // if (!this.challengeAnim) {\r\n        //     this.instantiateNode(\"challengeAnim\", sp.Skeleton, \"\", this.node, (node) => {\r\n        //         self.challengeAnim = node;\r\n        //         self.challengeAnim.node.active = false;\r\n        //         if (self.challengeAnimName) {\r\n        //             self.playChallengeAnim(self.challengeAnimName);\r\n        //         }\r\n        //     });\r\n        // }\r\n\r\n        // 加载血条节点\r\n        if (!this.hpBar) {\r\n            this.instantiateNode(\"hpBar\", Node, \"challengeAnim\", this.node, (node) => {\r\n                self.hpBar = node;\r\n                // if (self.m_config.type === 712) {\r\n                //     self.hpBar.y = GameIns.gameDataManager.isMechaOver ? 90 : 30;\r\n                // } else {\r\n                self.hpBar.y = 19;\r\n                // }\r\n\r\n                self.hpbarBack = self.hpBar.getChildByName(\"hpbarback\").getComponent(Sprite);\r\n                self.hpbarMid = self.hpBar.getChildByName(\"hpbarmid\").getComponent(Sprite);\r\n                self.hpbarFont = self.hpBar.getChildByName(\"hpbarfont\").getComponent(Sprite);\r\n                self.hpfont = self.hpBar.getChildByName(\"hpfont\").getComponent(Label);\r\n\r\n                // if (!GameIns.gameDataManager.guideStage) {\r\n                //     self.node.scale = 0.4;\r\n                // }\r\n\r\n                const scale = 1 / self.node.getScale().x * 0.45;\r\n                self.hpbarBack.node.setScale(scale, 1.2 * -scale);\r\n                self.hpbarMid.node.setScale(scale, 1.2 * scale);\r\n                self.hpbarFont.node.setScale(scale, 1.2 * -scale);\r\n\r\n                let hpScale = 0.6 / self.node.scale.x * 0.66;\r\n                self.hpfont.node.setScale(hpScale, hpScale);\r\n            });\r\n        }\r\n    }\r\n    /**\r\n     * 动态实例化节点\r\n     * @param {string} prefabName 预制体名称\r\n     * @param {Function} componentType 组件类型\r\n     * @param {string} siblingName 同级节点名称\r\n     * @param {Node} parentNode 父节点\r\n     * @param {Function} callback 回调函数\r\n     */\r\n    instantiateNode(prefabName, componentType, siblingName, parentNode, callback) {\r\n        const self = this;\r\n\r\n        // 增加加载总数\r\n        this._loadTotal++;\r\n        this._loadFinish = false;\r\n\r\n        // 加载预制体\r\n        GameIns.loadManager.loadPrefab1(prefabName, (prefab) => {\r\n            if (prefab) {\r\n                const node = instantiate(prefab);\r\n                const component = componentType === Node ? node : node.getComponent(componentType);\r\n\r\n                node.parent = null;\r\n\r\n                // 设置节点插入位置\r\n                let siblingIndex = parentNode.childrenCount;\r\n                if (siblingName && siblingName !== \"\") {\r\n                    const siblingNode = parentNode.getChildByName(siblingName);\r\n                    if (siblingNode) {\r\n                        siblingIndex = siblingNode.getSiblingIndex();\r\n                    }\r\n                }\r\n                parentNode.insertChild(node, siblingIndex);\r\n\r\n                // 执行回调\r\n                if (callback) {\r\n                    callback(component);\r\n                }\r\n\r\n                // 检查加载完成\r\n                self.checkLoadFinish();\r\n            }\r\n        });\r\n    }\r\n\r\n    //     /**\r\n    //      * 拆解主飞机的节点\r\n    //      */\r\n    //     BreakUpMainPlane() {\r\n    //         // 清理金币动画\r\n    //         if (this._goldAnimArr.length > 0) {\r\n    //             this._goldAnimArr.splice(0);\r\n    //             this.skin.getChildByName(\"goldAnim\").destroyAllChildren();\r\n    //         }\r\n\r\n    //         // 清理连线动画\r\n    //         if (this._ligatureAnim) {\r\n    //             this._ligatureAnim.node.destroy();\r\n    //             this._ligatureAnim = null;\r\n    //         }\r\n\r\n    //         // 清理跑酷节点\r\n    //         if (this.parkourBot) {\r\n    //             this.parkourBot.node.destroy();\r\n    //             this.parkourBot = null;\r\n    //         }\r\n    //         if (this.parkourMid) {\r\n    //             this.parkourMid.node.destroy();\r\n    //             this.parkourMid = null;\r\n    //         }\r\n    //         if (this.parkourTop) {\r\n    //             this.parkourTop.node.destroy();\r\n    //             this.parkourTop = null;\r\n    //         }\r\n\r\n    //         // 清理其他节点\r\n    //         if (this.below) {\r\n    //             this.below.node.destroy();\r\n    //             this.below = null;\r\n    //         }\r\n    //         if (this.relifeComp) {\r\n    //             this.relifeComp.node.destroy();\r\n    //             this.relifeComp = null;\r\n    //         }\r\n    //         if (this.challengeAnim) {\r\n    //             this.challengeAnim.node.destroy();\r\n    //             this.challengeAnim = null;\r\n    //         }\r\n\r\n    //         // 停止血条动画并清理\r\n    //         if (this.hpMidActin) {\r\n    //             this.hpMidActin.stop();\r\n    //             this.hpMidActin = null;\r\n    //         }\r\n    //         if (this.hpBar) {\r\n    //             this.hpBar.destroy();\r\n    //             this.hpBar = null;\r\n    //             this.hpbarBack = null;\r\n    //             this.hpbarMid = null;\r\n    //             this.hpbarFont = null;\r\n    //             this.hpfont = null;\r\n    //         }\r\n\r\n    //         // 延迟释放资源\r\n    //         setTimeout(() => {\r\n    //             GameIns.loadManager.releaseAsset(loader.getRes(\"animation/MainPlane/paoku_1\", AnimationClip), false);\r\n    //             GameIns.loadManager.releaseAsset(loader.getRes(\"animation/MainPlane/paoku_2\", AnimationClip), false);\r\n    //             GameIns.loadManager.releaseAsset(loader.getRes(\"animation/MainPlane/paoku_3\", AnimationClip), false);\r\n    //             GameIns.loadManager.releaseRes(\"package_mainPlane\", SpriteAtlas);\r\n    //             GameIns.loadManager.releaseResArr(\r\n    //                 [\"below\", \"relief\", \"challengeAnim\", \"paoku_1\", \"paoku_2\", \"paoku_3\", \"hpBar\"],\r\n    //                 Prefab,\r\n    //                 false\r\n    //             );\r\n    //             GameIns.loadManager.releaseRes(\"skel_mainblow\", sp.SkeletonData);\r\n    //             GameIns.loadManager.releaseRes(\"skel_relief\", sp.SkeletonData);\r\n    //             GameIns.loadManager.releaseRes(\"skel_challengeAnim\", sp.SkeletonData);\r\n    //             Tools.warn(\"release BreakUpMainPlane\");\r\n    //         }, 20);\r\n    //     }\r\n\r\n    /**\r\n     * 初始化主飞机\r\n     */\r\n    init() {\r\n        this.node.active = true;\r\n        this.shadow.node.getComponent(UIOpacity).opacity = 0;\r\n\r\n        // 获取火力状态\r\n        this.getFireState();\r\n\r\n        // 初始化碰撞组件\r\n        if (!this.m_collideComp) {\r\n            this.m_collideComp = this.addComp(ColliderComp, new ColliderComp());\r\n        }\r\n        this.m_collideComp.enabled = false;\r\n\r\n        // // 设置阴影节点是否可见\r\n        // const shadowVisible = BattleManager.shadowAble(BattleManager.mainStage);\r\n        // this.shadow.node.active = shadowVisible;\r\n\r\n        // // 特殊处理某些飞机类型\r\n        // if (this.m_config.type === 710) {\r\n        //     GameIns.gameDataManager.isMechaOver = true;\r\n        //     this.setMechaOverPic(true);\r\n        //     this.skinAnim.node.y = this.initPosSkinAnim;\r\n        //     this.mechaAnimNode.y = this.initPosMechaAnim;\r\n        //     this.suitAnimBot.y = this.initPosSuitBot;\r\n        //     this.suitAnimTop.y = this.initPosSuitTop;\r\n        // }\r\n\r\n        // 初始化动画状态\r\n        if (!this._hurtAct) {\r\n            this._hurtAct = tween()\r\n                .to(0, { color: new Color(255, 0, 0) }) // 变为红色\r\n                .to(0.13, { color: new Color(255, 255, 255) }); // 恢复为正常颜色\r\n            // .repeatForever()\r\n        }\r\n\r\n        // 初始化火力点\r\n        this.m_skillFires.forEach((fires) => {\r\n            fires.forEach((fire) => {\r\n                fire.clearData();\r\n            });\r\n        });\r\n\r\n        // 更新屏幕等级\r\n        this.changeScreenLv(this.m_data.screenLv);\r\n\r\n        // // 初始化影子飞机\r\n        // if (PlaneManager.shadowPlane) {\r\n        //     PlaneManager.shadowPlane.init();\r\n        // }\r\n\r\n        // // 设置机甲状态\r\n        // if (GameIns.gameDataManager.isConditionOver && BattleManager.isContinue && GameIns.gameDataManager.isMechaOver) {\r\n        //     this.mechaAnimNode.destroyAllChildren();\r\n        //     this.setMechaOverPic();\r\n        // }\r\n\r\n        // // 刷新拖尾效果\r\n        // this.refreshStreak();\r\n    }\r\n    /**\r\n     * 处理复活逻辑\r\n     */\r\n    onRelife() {\r\n        GameIns.gameDataManager.reviveCount += 1; // 增加复活次数\r\n        this.relife(1); // 调用复活方法\r\n    }\r\n\r\n    /**\r\n     * 执行复活\r\n     * @param {number} reviveType 复活类型\r\n     */\r\n    relife(reviveType) {\r\n        // GameIns.gameDataManager.addBattleReviveStage(\r\n        //     100 * BattleManager.mainStage + BattleManager.subStage,\r\n        //     reviveType + 1\r\n        // );\r\n        // ReportManager.sendReportRevive(\r\n        //     BattleManager.mainStage,\r\n        //     BattleManager.subStage,\r\n        //     reviveType\r\n        // );\r\n\r\n        // this.playRelifeAim(); // 播放复活动画\r\n        this.m_data.die = false; // 设置飞机为非死亡状态\r\n        this.m_data.revive = true; // 设置复活状态\r\n        this.scheduleOnce(() => {\r\n            this.m_data.revive = false;\r\n        }, 0.5);\r\n\r\n        GameIns.gameDataManager.battlePlaneActive = true; // 激活主飞机\r\n        GameIns.mainPlaneManager.data.hp = GameIns.mainPlaneManager.data.maxhp; // 恢复满血\r\n        GameIns.gameDataManager.setMainPlaneHp(GameIns.mainPlaneManager.data.hp); // 更新血量显示\r\n        GameIns.eventManager.emit(GameEvent.MainHpChange); // 触发血量更新事件\r\n\r\n        // this.showSuitBuff(); // 显示套装 Buff\r\n        // if (this.m_skill) {\r\n        //     this.m_skill.startBattle(true); // 开始技能战斗\r\n        // }\r\n\r\n        // const mechaUI = GameIns.uiManager.getDialog(MechaUI);\r\n        // if (mechaUI) {\r\n        //     mechaUI.node.active = true; // 显示机甲 UI\r\n        // }\r\n    }\r\n\r\n    /**\r\n     * 播放复活动画\r\n     */\r\n    playRelifeAim() {\r\n        this.m_collideComp.enabled = false; // 禁用碰撞组件\r\n        this.scheduleOnce(() => {\r\n            Tween.stopAllByTarget(this.skin.getComponent(UIOpacity));\r\n            this.skin.getComponent(UIOpacity).opacity = 255;\r\n            this.m_collideComp.enabled = true; // 恢复碰撞组件\r\n        }, 2);\r\n\r\n        this.skin.getComponent(UIOpacity).opacity = 255;\r\n\r\n        if (this.relifeComp) {\r\n            this.relifeComp.node.active = true;\r\n            this.relifeComp.setCompleteListener(this.hideRelifeAim.bind(this));\r\n        }\r\n\r\n        tween(this.skin.getComponent(UIOpacity))\r\n            .to(0.08, { opacity: 10 })\r\n            .to(0.08, { opacity: 255 })\r\n            .union()\r\n            .repeatForever()\r\n            .start();\r\n    }\r\n\r\n    /**\r\n     * 隐藏复活动画\r\n     */\r\n    hideRelifeAim() {\r\n        if (this.relifeComp) {\r\n            this.relifeComp.node.active = false;\r\n        }\r\n    }\r\n\r\n    //     /**\r\n    //      * 获取复活碰撞器\r\n    //      * @returns {Collider} 复活碰撞器\r\n    //      */\r\n    //     getRelifeCollider() {\r\n    //         if (this.relifeComp && this.relifeComp.node && this.relifeComp.node.active) {\r\n    //             return this.relifeComp.collider;\r\n    //         }\r\n    //         return null;\r\n    //     }\r\n    /**\r\n     * 改变屏幕等级\r\n     * @param {number} level 屏幕等级\r\n     */\r\n    changeScreenLv(level) {\r\n        if (level === 0) return;\r\n\r\n        this.m_screenDatas = [];\r\n        const attackKey = GameIns.gameDataManager.isConditionOver ? \"transatk\" : \"shiftingatk\";\r\n        const attackData = this.m_config[`${attackKey}${level}`];\r\n\r\n        for (let i = 0; i < attackData.length; i += 8) {\r\n            const screenData = attackData.slice(i, i + 8);\r\n            this.m_screenDatas.push(screenData);\r\n        }\r\n\r\n        this.m_shieldKnifeFire = null;\r\n        GameIns.mainPlaneManager.mainPlaneFireCounts = this.m_screenDatas.length;\r\n\r\n        this.m_screenDatas.forEach((data, index) => {\r\n            if (this.m_fires[index] == null) {\r\n                this.createAttackPoint(data);\r\n            } else {\r\n                this.changeScreen(index, data);\r\n            }\r\n\r\n            if (this.m_config.type === 712 && this.m_fires[index].getType() === 48) {\r\n                this.m_shieldKnifeFire = this.m_fires[index];\r\n            }\r\n        });\r\n\r\n        for (let i = this.m_screenDatas.length; i < this.m_fires.length; i++) {\r\n            this.changeScreen(i, null);\r\n        }\r\n    }\r\n\r\n    //     /**\r\n    //      * 进入狂暴状态\r\n    //      */\r\n    //     toCrazyState() {\r\n    //         this.skinAnim.node.active = true;\r\n    //         this.skinImg.node.active = false;\r\n\r\n    //         this.removeAllFire();\r\n\r\n    //         GameIns.mainPlaneManager.crazyScreens.forEach((data, index) => {\r\n    //             this.changeScreen(index, data);\r\n    //         });\r\n\r\n    //         if (this.m_animState !== AnimState.crazy) {\r\n    //             GameIns.audioManager.playEffect(\"goBallistic\");\r\n    //             this.m_animState = AnimState.crazy;\r\n\r\n    //             this.skinAnim.setAnimation(0, \"tocrazy\", false);\r\n    //             if (this.m_skinShield) this.m_skinShield.setAnimation(0, \"tocrazy\", false);\r\n    //             if (this.m_skinCircle) this.m_skinCircle.setAnimation(0, \"tocrazy\", false);\r\n\r\n    //             this.skinAnim.setCompleteListener(() => {\r\n    //                 this.skinAnim.setCompleteListener(null);\r\n    //                 this.skinAnim.setAnimation(0, \"crazyidle\", true);\r\n    //                 if (this.m_skinShield) this.m_skinShield.setAnimation(0, \"crazyidle\", true);\r\n    //                 if (this.m_skinCircle) this.m_skinCircle.setAnimation(0, \"crazyidle\", true);\r\n    //             });\r\n    //         }\r\n    //     }\r\n\r\n    //     /**\r\n    //      * 取消狂暴状态\r\n    //      */\r\n    //     cancelCrazyState() {\r\n    //         this.removeAllFire();\r\n    //         this.changeScreenLv(this.m_data.screenLv);\r\n\r\n    //         if (this.m_animState === AnimState.crazy) {\r\n    //             this.m_animState = AnimState.willCancelCrazy;\r\n\r\n    //             this.skinAnim.setAnimation(0, \"toidle\", false);\r\n    //             if (this.m_skinShield) this.m_skinShield.setAnimation(0, \"toidle\", false);\r\n    //             if (this.m_skinCircle) this.m_skinCircle.setAnimation(0, \"toidle\", false);\r\n\r\n    //             this.skinAnim.setCompleteListener(() => {\r\n    //                 this.skinAnim.setCompleteListener(null);\r\n    //                 this.skinAnim.setAnimation(0, \"idle\", true);\r\n    //                 if (this.m_skinShield) this.m_skinShield.setAnimation(0, \"idle\", true);\r\n    //                 if (this.m_skinCircle) this.m_skinCircle.setAnimation(0, \"idle\", true);\r\n\r\n    //                 this.m_animState = AnimState.idle;\r\n    //                 this.skinAnim.node.active = false;\r\n    //                 this.skinImg.node.active = true;\r\n    //             });\r\n    //         }\r\n    //     }\r\n    //     /**\r\n    //      * 强化屏幕效果\r\n    //      */\r\n    //     intensifyScreen() {\r\n    //         this.m_fireState.fireIntensify = true;\r\n    //         this.refreshFireState();\r\n    //     }\r\n\r\n    //     /**\r\n    //      * 取消强化屏幕效果\r\n    //      */\r\n    //     cancelIntensify() {\r\n    //         this.m_fireState.fireIntensify = false;\r\n    //         this.refreshFireState();\r\n    //     }\r\n\r\n    /**\r\n     * 获取火力状态\r\n     */\r\n    getFireState() {\r\n        // const attackLevel = SkillManager.me.getLv(SkillType.mainAttack);\r\n        // const hpAttackLevel = SkillManager.me.getLv(SkillType.mainHpAttack);\r\n        // const speedLevel = SkillManager.me.getLv(SkillType.mainSpeed);\r\n        const baseAttack = this.m_data.initAtk1 > 0 ? this.m_data.attack1 : this.m_data.attack2;\r\n\r\n        // if (\r\n        //     GameIns.mainPlaneManager.idToType(this.m_data.id) === 711 &&\r\n        //     (GameIns.gameDataManager.isMechaOver || GameIns.mainPlaneManager.checkPlayMechaAnim())\r\n        // ) {\r\n        //     baseAttack = this.m_data.attack2;\r\n        // }\r\n\r\n        // const cirtLevel = SkillManager.me.getLv(SkillType.addCirt);\r\n        const atkChallenge = this.m_data.atkAddRatio;\r\n\r\n        this.m_fireState = {\r\n            attack: baseAttack,\r\n            attackLv: 0,\r\n            hpAttackLv: 0,\r\n            speedLv: 0,\r\n            cirtLv: 0,//cirtLevel,\r\n            catapultLv: 0,//SkillManager.me.getLv(SkillType.catapult),\r\n            atkChallenge: atkChallenge,\r\n            fireIntensify: this.m_data.intensifyAtk,\r\n        };\r\n    }\r\n\r\n    /**\r\n     * 获取攻击力\r\n     * @returns {number} 当前攻击力\r\n     */\r\n    getAttack() {\r\n        return this.m_fireState.attack;\r\n    }\r\n\r\n    //     /**\r\n    //      * 挑战模式下增加攻击力\r\n    //      */\r\n    //     atkAddByChallenge() {\r\n    //         const attackLevel = SkillManager.me.getLv(SkillType.mainAttack);\r\n    //         const fireState = {\r\n    //             attack: this.m_data.initAtk1 > 0 ? this.m_data.attack1 : this.m_data.attack2,\r\n    //             attackLv: attackLevel,\r\n    //             hpAttackLv: SkillManager.me.getLv(SkillType.mainHpAttack),\r\n    //             atkChallenge: this.m_data.atkAddRatio,\r\n    //         };\r\n\r\n    //         this.m_fires.forEach((fire) => {\r\n    //             fire.setState(fireState, this);\r\n    //         });\r\n    //     }\r\n\r\n    //     /**\r\n    //      * 设置冷却时间\r\n    //      * @param {number} time 冷却时间\r\n    //      */\r\n    //     setCoolTime(time) {\r\n    //         const skillFires = this.m_skillFires.get(SkillType.feibiao);\r\n    //         if (skillFires) {\r\n    //             skillFires.forEach((fire) => {\r\n    //                 fire.setCoolTime(time);\r\n    //             });\r\n    //         }\r\n    //     }\r\n\r\n    //     /**\r\n    //      * 刷新火力状态\r\n    //      */\r\n    //     refreshFireState() {\r\n    //         this.getFireState();\r\n    //         this.m_fires.forEach((fire) => {\r\n    //             fire.setState(this.m_fireState, this);\r\n    //         });\r\n\r\n    //         const skillFires = this.m_skillFires.get(28);\r\n    //         if (skillFires) {\r\n    //             skillFires.forEach((fire) => {\r\n    //                 fire.setState(this.m_fireState, this, true);\r\n    //             });\r\n    //         }\r\n    //     }\r\n\r\n    //     /**\r\n    //      * 增加暴击\r\n    //      * @param {number} value 暴击值\r\n    //      */\r\n    //     addCirt(value) {\r\n    //         this.m_fires.forEach((fire) => {\r\n    //             fire.addCirt(value);\r\n    //         });\r\n    //     }\r\n    /**\r\n     * 改变屏幕上的火力点\r\n     * @param {number} index 火力点索引\r\n     * @param {Array|null} data 火力点数据\r\n     */\r\n    changeScreen(index, data) {\r\n        if (data == null) {\r\n            if (index < this.m_fires.length) {\r\n                const fire = this.m_fires[index];\r\n                fire.setData(null, this.m_fireState, false, this);\r\n                fire.node.active = false;\r\n            }\r\n        } else {\r\n            if (index < this.m_fires.length) {\r\n                const fire = this.m_fires[index];\r\n                fire.node.active = true;\r\n                fire.setData(data, this.m_fireState, false, this);\r\n            } else {\r\n                this.createAttackPoint(data);\r\n            }\r\n        }\r\n    }\r\n\r\n    //     /**\r\n    //      * 移除所有火力点\r\n    //      */\r\n    //     removeAllFire() {\r\n    //         this.m_fires.forEach((fire) => {\r\n    //             fire.setData(null, this.m_fireState, false, this);\r\n    //         });\r\n    //     }\r\n\r\n    /**\r\n     * 创建攻击点\r\n     * @param {Array} data 攻击点数据\r\n     * @returns {FireShells} 创建的攻击点\r\n     */\r\n    createAttackPoint(data) {\r\n        const fireNode = new Node(\"fire\");\r\n        fireNode.parent = this.node;\r\n\r\n        const fire = fireNode.addComponent(FireShells);\r\n        fire.setData(data, this.m_fireState, false, this);\r\n\r\n        this.m_fires.push(fire);\r\n        return fire;\r\n    }\r\n\r\n    //     /**\r\n    //      * 根据技能添加攻击点\r\n    //      * @param {number} skillType 技能类型\r\n    //      * @param {Array} data 攻击点数据\r\n    //      * @param {Array} extra 额外数据\r\n    //      */\r\n    //     addAttackPointBySkill(skillType, data, extra = []) {\r\n    //         const skillFires = this.m_skillFires.get(skillType);\r\n    //         this.getFireState();\r\n    //         this.m_fireState.extra = extra;\r\n\r\n    //         if (!skillFires || skillFires.length === 0) {\r\n    //             data.forEach((pointData) => {\r\n    //                 const fireNode = new Node(\"fire-skill\");\r\n    //                 fireNode.parent = this.node;\r\n\r\n    //                 const fire = fireNode.addComponent(FireShells);\r\n    //                 fire.setData(pointData, this.m_fireState, false, this);\r\n\r\n    //                 const fires = this.m_skillFires.get(skillType) || [];\r\n    //                 fires.push(fire);\r\n    //                 this.m_skillFires.set(skillType, fires);\r\n    //             });\r\n    //         } else {\r\n    //             const maxLength = Math.max(skillFires.length, data.length);\r\n    //             const updatedFires = [];\r\n\r\n    //             for (let i = 0; i < maxLength; i++) {\r\n    //                 if (i < data.length && i < skillFires.length) {\r\n    //                     skillFires[i].setData(data[i], this.m_fireState, false, this);\r\n    //                     updatedFires.push(skillFires[i]);\r\n    //                 } else if (i < data.length) {\r\n    //                     const fireNode = new Node(\"fire-skill\");\r\n    //                     fireNode.parent = this.node;\r\n\r\n    //                     const fire = fireNode.addComponent(FireShells);\r\n    //                     fire.setData(data[i], this.m_fireState, false, this);\r\n    //                     updatedFires.push(fire);\r\n    //                 } else if (i < skillFires.length) {\r\n    //                     skillFires[i].node.destroy();\r\n    //                 }\r\n    //             }\r\n\r\n    //             this.m_skillFires.set(skillType, updatedFires);\r\n    //         }\r\n    //     }\r\n\r\n    //     /**\r\n    //      * 添加普通攻击点\r\n    //      * @param {FireShells} fire 火力点对象\r\n    //      * @param {boolean} isTrueAttack 是否为真实攻击\r\n    //      */\r\n    //     addAttackPoint(fire, isTrueAttack = false) {\r\n    //         fire.node.parent = this.node;\r\n\r\n    //         const attack = isTrueAttack\r\n    //             ? WinePlaneManager.me.lvRecord.atk_true\r\n    //             : this.m_data.initAtk1 > 0\r\n    //                 ? this.m_data.attack1\r\n    //                 : this.m_data.attack2;\r\n\r\n    //         fire.setState({ ...this.m_fireState, attack }, this, false, isTrueAttack);\r\n    //     }\r\n\r\n    //     /**\r\n    //      * 根据机甲添加攻击点\r\n    //      * @param {FireShells} fire 火力点对象\r\n    //      * @param {Node|null} parent 父节点\r\n    //      */\r\n    //     addAttackPointByMecha(fire, parent = null) {\r\n    //         if (parent) {\r\n    //             fire.node.parent = parent;\r\n    //         }\r\n\r\n    //         const attack =\r\n    //             this.m_data.initAtk1 > 0 ? this.m_data.attack1 : this.m_data.attack2;\r\n\r\n    //         fire.setState({ ...this.m_fireState, attack }, null, false);\r\n    //     }\r\n\r\n    //     /**\r\n    //      * 移除攻击点\r\n    //      * @param {FireShells} fire 火力点对象\r\n    //      */\r\n    //     removeAttackPoint(fire) {\r\n    //         fire.node.parent = null;\r\n    //     }\r\n\r\n    /**\r\n     * 播放攻击点动画\r\n     */\r\n    playPointAnim() {\r\n        // this.point.setScale(1,1);\r\n        // this.point.parent.getComponent(UIOpacity).opacity = 255;\r\n        // this.point.getComponent(UIOpacity).opacity = 255;\r\n        // this.point.parent.y = this.m_config.body[2];\r\n        // Tween.stopAllByTarget(this.point)\r\n\r\n        // const ani = tween(this.point)\r\n        //     .to(2 / 30, { scale: 0.85, opacity: 125 })\r\n        //     .to(2 / 30, { scale: 1, opacity: 255 });\r\n\r\n        // ani.repeatForever().start();\r\n    }\r\n    /**\r\n     * 设置飞机的方向\r\n     * @param {number} moveX 水平方向的移动量\r\n     * @param {number} deltaTime 时间增量\r\n     */\r\n    setDirection(moveX, deltaTime) {\r\n        // if (moveX > 0) {\r\n        //     this.skin.scaleX = 1; // 向右\r\n        // } else if (moveX < 0) {\r\n        //     this.skin.scaleX = -1; // 向左\r\n        // }\r\n    }\r\n\r\n    /**\r\n     * 减少血量\r\n     * @param {number} damage 受到的伤害值\r\n     */\r\n    cutHp(damage) {\r\n        GameIns.mainPlaneManager.hurtTotal += damage;\r\n        const newHp = GameIns.mainPlaneManager.data.hp - damage;\r\n        GameIns.mainPlaneManager.data.hp = Math.max(0, newHp);\r\n\r\n        if (newHp < 0) {\r\n            GameIns.mainPlaneManager.hurtTotal += newHp;\r\n        }\r\n\r\n        GameIns.eventManager.emit(GameEvent.MainHpChange);\r\n\r\n        if (GameIns.mainPlaneManager.data.hp <= 0 && !this.m_data.die) {\r\n            this.toDie();\r\n        }\r\n    }\r\n\r\n    /**\r\n     * 增加血量\r\n     * @param {number} heal 恢复的血量值\r\n     */\r\n    addHp(heal) {\r\n        GameIns.mainPlaneManager.data.hp = Math.min(\r\n            GameIns.mainPlaneManager.data.maxhp,\r\n            GameIns.mainPlaneManager.data.hp + heal\r\n        );\r\n        GameIns.eventManager.emit(GameEvent.MainHpChange);\r\n    }\r\n\r\n    /**\r\n     * 碰撞处理\r\n     * @param {Object} collision 碰撞对象\r\n     */\r\n    onCollide(collision) {\r\n        if (this.m_skill && this.m_skill.invincible) return;\r\n\r\n        let damage = 0;\r\n        if (collision.entity instanceof Bullet) {\r\n            damage = collision.entity.getAttack(this);\r\n        } else if (\r\n            collision.entity instanceof EnemyEntity ||\r\n            collision.entity instanceof BossUnit\r\n        ) {\r\n            damage = collision.entity.getColliderAtk();\r\n        }\r\n\r\n        if (damage > 0) {\r\n            this.cutHp(damage);\r\n            if (GameIns.mainPlaneManager.data.hp <= 0 && !this.m_data.die) {\r\n                this.toDie();\r\n            }\r\n        }\r\n    }\r\n\r\n    /**\r\n     * 控制飞机移动\r\n     * @param {number} moveX 水平方向的移动量\r\n     * @param {number} moveY 垂直方向的移动量\r\n     */\r\n    onControl(moveX, moveY) {\r\n        if (!this.m_data.die && this.m_moveEnable) {\r\n            this.m_moveX += moveX;\r\n            this.m_moveY += moveY;\r\n        }\r\n    }\r\n\r\n    //     /**\r\n    //      * 战斗胜利处理\r\n    //      */\r\n    //     onSucc() {\r\n    //         this.m_collideComp.enabled = false; // 禁用碰撞组件\r\n    //         if (this.m_skill) {\r\n    //             this.m_skill.quiteBattle(); // 停止技能战斗\r\n    //         }\r\n    //     }\r\n\r\n    /**\r\n     * 设置飞机死亡状态\r\n     */\r\n    toDie() {\r\n        this.m_data.die = true; // 标记为死亡状态\r\n        this.m_collideComp.enabled = false; // 禁用碰撞组件\r\n\r\n        if (this.hpbarMid) {\r\n            tween(this.hpbarMid).stop();\r\n            this.hpbarMid.fillRange = 0; // 设置血条为0\r\n        }\r\n\r\n        this._playDieAnim(); // 播放死亡动画\r\n        // GameIns.eventManager.emit(GameEvent.MainPlaneDie); // 触发飞机死亡事件\r\n        GameIns.gameDataManager.battlePlaneActive = false;\r\n    }\r\n\r\n    /**\r\n     * 播放死亡动画\r\n     */\r\n    _playDieAnim() {\r\n        this.blast.node.getComponent(UIOpacity).opacity = 255; // 显示爆炸效果\r\n        this.blast.setCompleteListener(this._dieAnimEnd.bind(this)); // 设置动画完成回调\r\n        this.blast.setAnimation(0, \"play\", false); // 播放爆炸动画\r\n    }\r\n\r\n    /**\r\n     * 主飞机死亡动画结束后的处理逻辑\r\n     */\r\n    _dieAnimEnd(): void {\r\n        // 隐藏爆炸动画节点\r\n        this.blast.node.getComponent(UIOpacity).opacity = 0;\r\n\r\n        // 如果主飞机还有剩余生命次数\r\n        if (this.m_data.lifeNum > 0) {\r\n            // 减少生命次数\r\n            this.m_data.lifeNum--;\r\n\r\n            // 更新剩余生命次数到全局数据\r\n            // GameIns.gameDataManager.setLifeNum(this.m_data.lifeNum);\r\n\r\n            // 触发复活逻辑\r\n            this.relife(0);\r\n        } else {\r\n            // 如果没有剩余生命次数，检查是否可以复活\r\n            const reviveCount = GameIns.gameDataManager.reviveCount;\r\n            if (this.m_data.relifeNum - reviveCount <= 0) {\r\n                // // 如果是远征模式，结束当前章节\r\n                // if (ExpeditionManager.isExpedition) {\r\n                //     ExpeditionManager.sectionOver(false);\r\n                // } else {\r\n                //     // 否则触发战斗失败逻辑\r\n                    GameIns.battleManager.battleFail();\r\n                // }\r\n            } else {\r\n                // 如果可以复活，触发战斗死亡逻辑\r\n                GameIns.battleManager.battleDie();\r\n            }\r\n        }\r\n    }\r\n\r\n    /**\r\n     * 启用或禁用火力\r\n     * @param {boolean} enable 是否启用火力\r\n     */\r\n    setFireEnable(enable) {\r\n        if (this.m_config && this.m_config.type === 710) return;\r\n\r\n        if (enable) {\r\n            this.m_fireAnim.forEach((anim) => {\r\n                anim.getComponent(UIOpacity).opacity = 255; // 显示火力动画\r\n            });\r\n            this.point.parent.getComponent(UIOpacity).opacity = 255;\r\n        } else {\r\n            this.m_fireAnim.forEach((anim) => {\r\n                anim.getComponent(UIOpacity).opacity = 0; // 隐藏火力动画\r\n            });\r\n            this.point.parent.getComponent(UIOpacity).opacity = 0;\r\n        }\r\n    }\r\n\r\n    /**\r\n     * 设置飞机是否可移动\r\n     * @param {boolean} enable 是否可移动\r\n     */\r\n    setMoveAble(enable) {\r\n        this.m_moveEnable = enable;\r\n    }\r\n\r\n    /**\r\n     * 设置碰撞是否可用\r\n     * @param {boolean} enable 是否启用碰撞\r\n     */\r\n    setColAble(enable) {\r\n        this.m_collideComp.enabled = enable;\r\n    }\r\n\r\n    /**\r\n     * 开始射击\r\n     */\r\n    beginFire() {\r\n        GameIns.mainPlaneManager.fireEnable = true;\r\n    }\r\n\r\n    /**\r\n     * 停止射击\r\n     */\r\n    stopFire() {\r\n        GameIns.mainPlaneManager.fireEnable = false;\r\n    }\r\n\r\n    //     /**\r\n    //      * 飞机准备阶段\r\n    //      */\r\n    //     planeHold() {\r\n    //         this.willBegine(); // 准备开始\r\n    //         this.node.parent = BattleLayer.me.selfPlaneLayer; // 设置父节点\r\n    //         this.skin.getComponent(UIOpacity).opacity = 255;\r\n    //         this.skin.scale = 1;\r\n    //         this.node.scale = GameIns.battleManager.getRatio();\r\n\r\n    //         tween(this.node)\r\n    //             .delay(1)\r\n    //             .call(() => {\r\n    //                 this.begine(); // 开始战斗\r\n    //             })\r\n    //             .start();\r\n    //     }\r\n\r\n    /**\r\n     * 准备开始战斗\r\n     */\r\n    willBegine() {\r\n        if (GameIns.battleManager.isContinue) {\r\n            const hp = GameIns.gameDataManager.getMainPlaneHp();\r\n            GameIns.mainPlaneManager.data.hp = hp; // 恢复血量\r\n            this.UpdateHp(); // 更新血量显示\r\n        }\r\n    }\r\n\r\n    /**\r\n     * 开始战斗\r\n     * @param {boolean} isContinue 是否继续战斗\r\n     */\r\n    begine(isContinue = false) {\r\n        if (isContinue) {\r\n            this.beginFire();\r\n            this.m_moveEnable = true;\r\n            // WinePlaneManager.me.beginBattle();\r\n            if (this.m_collideComp) {\r\n                this.m_collideComp.enabled = true;\r\n            }\r\n            if (this.m_skill) {\r\n                this.m_skill.startBattle();\r\n            }\r\n        } else {\r\n            // switch (GameIns.battleManager.gameType) {\r\n            // case GameEnum.GameType.Gold:\r\n            //     GoldBattleManager.onStartBattle();\r\n            //     break;\r\n            // case GameType.Boss:\r\n            //     BossBattleManager.startBattle();\r\n            //     break;\r\n            // default:\r\n            //     if (ExpeditionManager.isExpedition) {\r\n            //         ExpeditionManager.startBattle();\r\n            //     } else {\r\n            GameIns.battleManager.startBattle();\r\n            // }\r\n            // }\r\n        }\r\n\r\n        GameIns.gameDataManager.setMainPlaneHp(GameIns.mainPlaneManager.data.hp);\r\n\r\n        // const isMechaOver = GameIns.gameDataManager.isMechaOver || GameIns.mainPlaneManager.checkPlayMechaAnim();\r\n        // this.shadow.spriteFrame = GameIns.loadManager.getImage(\r\n        //     isMechaOver ? this.m_config.yz + \"2\" : this.m_config.yz,\r\n        //     \"package_mainPlane_trans_\" + this.m_config.type\r\n        // );\r\n        // this.shadow.node.getComponent(UIOpacity).opacity = 255;\r\n    }\r\n    //     /**\r\n    //      * 隐藏主飞机的特效\r\n    //      * @param {Function} callback 回调函数\r\n    //      */\r\n    //     hideMainEffect(callback) {\r\n    //         if (this.parkourBot) {\r\n    //             tween(this.parkourBot.node)\r\n    //                 .to(0.3, { opacity: 0 })\r\n    //                 .start();\r\n    //             tween(this.parkourTop.node)\r\n    //                 .to(0.3, { opacity: 0 })\r\n    //                 .start();\r\n    //             tween(this.parkourMid.node)\r\n    //                 .delay(8 / 30)\r\n    //                 .to(0.3, { opacity: 0 })\r\n    //                 .call(() => {\r\n    //                     if (callback) callback();\r\n    //                 })\r\n    //                 .start();\r\n    //         } else if (callback) {\r\n    //             callback();\r\n    //         }\r\n    //     }\r\n\r\n    /**\r\n     * 主飞机入场动画\r\n     */\r\n    planeIn(): void {\r\n        const self = this;\r\n\r\n        // 初始化飞机状态\r\n        this.willBegine();\r\n        const frameTime = GameConfig.ActionFrameTime;\r\n\r\n        // 播放入场音效\r\n        // frameWork.audioManager.playEffect(\"planeFristIn\");\r\n\r\n        // 设置初始位置和状态\r\n        this.node.getComponent(UIOpacity).opacity = 255;\r\n        this.node.parent = BattleLayer.me.selfPlaneLayer;\r\n        this.skin.getComponent(UIOpacity).opacity = 255;\r\n        this.skin.setScale(1, 1)\r\n        let posY = -view.getVisibleSize().height - (this.m_config.type === 711 ? 1000 : 80);\r\n        this.node.setPosition(0, posY)\r\n        this.node.setScale(GameIns.battleManager.getRatio(), GameIns.battleManager.getRatio());\r\n        this.stopFire();\r\n        this.m_moveEnable = false;\r\n        Tween.stopAllByTarget(this.node)\r\n\r\n        // 设置地图速度变化动画\r\n        this._mapTween = tween(GameMapRun.instance)\r\n            .to(GameFunc.fromTo(0, 10) / GameIns.battleManager.animSpeed, {\r\n                changeMapSpeedRatio: 2100,\r\n                changeSkySpeedRatio: 2100,\r\n            })\r\n            .delay(GameFunc.fromTo(10, 40) / GameIns.battleManager.animSpeed)\r\n            .to(GameFunc.fromTo(40, 60) / GameIns.battleManager.animSpeed, {\r\n                changeMapSpeedRatio: 1400,\r\n                changeSkySpeedRatio: 1400,\r\n            })\r\n            .to(GameFunc.fromTo(60, 80) / GameIns.battleManager.animSpeed, {\r\n                changeMapSpeedRatio: 1050,\r\n                changeSkySpeedRatio: 1050,\r\n            })\r\n            .to(GameFunc.fromTo(80, 110) / GameIns.battleManager.animSpeed, {\r\n                changeMapSpeedRatio: 700,\r\n                changeSkySpeedRatio: 700,\r\n            })\r\n            .to(GameFunc.fromTo(110, 150) / GameIns.battleManager.animSpeed, {\r\n                changeMapSpeedRatio: 350,\r\n                changeSkySpeedRatio: 350,\r\n            })\r\n            .to(GameFunc.fromTo(150, 200) / GameIns.battleManager.animSpeed, {\r\n                changeMapSpeedRatio: 0,\r\n                changeSkySpeedRatio: 0,\r\n            })\r\n            .call(() => {\r\n                self._mapTween = null;\r\n            })\r\n            .start();\r\n\r\n        // 飞机入场动画\r\n        this.scheduleOnce(() => {\r\n            const targetY = -view.getVisibleSize().height * 0.7;\r\n            const targetX = this.node.position.x;\r\n            tween(this.node)\r\n                .to(20 * frameTime / GameIns.battleManager.animSpeed, { position: v3(targetX, targetY - 17) })\r\n                .to(11 * frameTime / GameIns.battleManager.animSpeed, { position: v3(targetX, targetY + 57) })\r\n                .to(10 * frameTime / GameIns.battleManager.animSpeed, { position: v3(targetX, targetY + 76) })\r\n                .to(27 * frameTime / GameIns.battleManager.animSpeed, { position: v3(targetX, targetY) })\r\n                .call(() => {\r\n                    self.begine();\r\n                })\r\n                .start();\r\n\r\n            tween(this.skin)\r\n                .to(20 * frameTime / GameIns.battleManager.animSpeed, { scale: v3(1.9, 1.9) })\r\n                .to(11 * frameTime / GameIns.battleManager.animSpeed, { scale: v3(1.4, 1.4) })\r\n                .to(10 * frameTime / GameIns.battleManager.animSpeed, { scale: v3(1, 1) })\r\n                .to(27 * frameTime / GameIns.battleManager.animSpeed, { scale: v3(1, 1) })\r\n                .start();\r\n\r\n            // if (this.hpBar) {\r\n            //     tween(this.hpBar)\r\n            //         .to(0, { opacity: 0 })\r\n            //         .delay(31 * frameTime / GameIns.battleManager.animSpeed)\r\n            //         .to(10 * frameTime / GameIns.battleManager.animSpeed, { opacity: 255 })\r\n            //         .start();\r\n            // }\r\n\r\n            this.scheduleOnce(() => {\r\n                tween(this.streak.node)\r\n                    .to(9 * frameTime / GameIns.battleManager.animSpeed, { scale: v3(1, 4) })\r\n                    .to(7 * frameTime / GameIns.battleManager.animSpeed, { scale: v3(1, 2) })\r\n                    .to(5 * frameTime / GameIns.battleManager.animSpeed, { scale: v3(1, 1) })\r\n                    .call(() => {\r\n                        self.addStreak();\r\n                    })\r\n                    .start();\r\n            }, 2 * frameTime / GameIns.battleManager.animSpeed);\r\n        }, 7 * frameTime / GameIns.battleManager.animSpeed);\r\n\r\n        // // 更新影子动画\r\n        // tween(this.shadow.node)\r\n        //     .to(GameFunc.fromTo(0, 24) / GameIns.battleManager.animSpeed, { y: 0, x: 0, scale: 1.184 })\r\n        //     .to(GameFunc.fromTo(34, 56) / GameIns.battleManager.animSpeed, { y: -100, x: 40, scale: 0.86 })\r\n        //     .to(GameFunc.fromTo(56, 86) / GameIns.battleManager.animSpeed, { y: -180, x: 108, scale: 0.62, opacity: 255 })\r\n        //     .start();\r\n    }\r\n\r\n    //     /**\r\n    //      * 飞机出场动画\r\n    //      */\r\n    //     planeOut() {\r\n    //         tween(this.node)\r\n    //             .to(0.5, { y: winSize.height })\r\n    //             .call(() => {\r\n    //                 this.battleOver();\r\n    //             })\r\n    //             .start();\r\n    //     }\r\n\r\n    //     /**\r\n    //      * 播放下方瞄准特效\r\n    //      */\r\n    //     playBelowAim() {\r\n    //         this.below.node.active = true;\r\n    //         this.below.node.scale = 2.2 * this.node.scale;\r\n    //         this.below.animation = \"play\";\r\n    //         this.below.setCompleteListener(() => {\r\n    //             this.below.node.active = false;\r\n    //         });\r\n    //     }\r\n\r\n    //     /**\r\n    //      * 飞机降落动画\r\n    //      */\r\n    //     planeLand() {\r\n    //         PlaneManager.me.shadowPlane?.hideSelf();\r\n    //         const targetNode = GameIns.battleManager.rogueui.node.getChildByName(\"back\").getChildByName(\"smain\");\r\n    //         this.node.parent = null;\r\n    //         targetNode.insertChild(this.node, 1);\r\n    //         this.node.position = GameIns.battleManager.rogueui.mainLandTarget.position;\r\n\r\n    //         CopyPlaneManager.me.copyPlanes.forEach((plane) => {\r\n    //             plane.node.parent = null;\r\n    //             targetNode.insertChild(plane.node, 1);\r\n    //             plane.node.y = GameIns.battleManager.rogueui.mainLandTarget.y - 100;\r\n    //         });\r\n\r\n    //         this.streak.node.active = false;\r\n    //     }\r\n\r\n    //     /**\r\n    //      * 飞机进入战斗准备状态\r\n    //      */\r\n    //     mainPlaneInBoot() {\r\n    //         const targetY = GameIns.battleManager.rogueui.mainLandTarget.y;\r\n    //         this.node.parent = null;\r\n    //         const backNode = GameIns.battleManager.rogueui.node.getChildByName(\"back\");\r\n    //         const siblingIndex = backNode.getChildByName(\"fc\").getSiblingIndex();\r\n\r\n    //         backNode.insertChild(this.node, siblingIndex + 1);\r\n    //         PlaneManager.me.shadowPlane?.node.setSiblingIndex(siblingIndex + 1);\r\n\r\n    //         CopyPlaneManager.me.copyPlanes.forEach((plane) => {\r\n    //             plane.node.parent = null;\r\n    //             backNode.insertChild(plane.node, siblingIndex + 1);\r\n    //             plane.node.y = -100;\r\n    //         });\r\n\r\n    //         this.node.y = this.node.y - GameIns.battleManager.rogueui.node.y + backNode.y + winSize.height / 2;\r\n    //         this.node.scale = GameIns.battleManager.getRatio();\r\n    //         this.streak.node.stopAllActions();\r\n    //         this.shadow.node.y = -180;\r\n    //         this.shadow.node.x = 108;\r\n\r\n    //         tween(this.node)\r\n    //             .to(1.5, { y: targetY + 117 + 350, x: 0 })\r\n    //             .call(() => {\r\n    //                 PlaneManager.me.shadowPlane?.mechaShow(false);\r\n    //                 this.begine();\r\n    //             })\r\n    //             .to(1, { y: targetY })\r\n    //             .call(this.playBelowAim.bind(this))\r\n    //             .start();\r\n    //     }\r\n\r\n    //     /**\r\n    //      * 战斗结束处理\r\n    //      */\r\n    //     battleOver() {\r\n    //         BattleManager.isGameType(GameType.Gold) || GameIns.battleManager.battleOver();\r\n    //     }\r\n    //     /**\r\n    //      * 播放连线动画\r\n    //      */\r\n    //     playLigatureAnim() {\r\n    //         if (!this._ligatureAnim) {\r\n    //             const animNode = instantiate(GameConst.frameAnim);\r\n    //             this.skin.addChild(animNode);\r\n    //             this._ligatureAnim = animNode.getComponent(PfFrameAnim);\r\n    //             this._ligatureAnim.init(\r\n    //                 GameIns.loadManager.getRes(\"package_mainPlane\", SpriteAtlas),\r\n    //                 \"all_\",\r\n    //                 8,\r\n    //                 2 * GameConfig.ActionFrameTime,\r\n    //                 () => {\r\n    //                     this._ligatureAnim.stop();\r\n    //                 }\r\n    //             );\r\n    //         }\r\n    //         this._ligatureAnim.reset();\r\n    //     }\r\n\r\n    //     /**\r\n    //      * 播放获取金币动画\r\n    //      */\r\n    //     playGetGoldAnim() {\r\n    //         const atlas = GameIns.loadManager.getRes(\"package_mainPlane\", SpriteAtlas);\r\n    //         if (atlas) {\r\n    //             let anim = this._goldAnimArr.pop();\r\n    //             if (!anim) {\r\n    //                 const animNode = instantiate(GameConst.frameAnim);\r\n    //                 this.skin.getChildByName(\"goldAnim\").addChild(animNode);\r\n    //                 animNode.y = this.point.y;\r\n    //                 anim = animNode.getComponent(PfFrameAnim);\r\n    //                 anim.init(atlas, \"ag_\", 6, GameConfig.ActionFrameTime, () => {\r\n    //                     anim.stop();\r\n    //                     this._goldAnimArr.push(anim);\r\n    //                 });\r\n    //             }\r\n    //             anim.reset();\r\n    //         } else {\r\n    //             GameIns.loadManager.loadAtlas(\"package_mainPlane\");\r\n    //         }\r\n    //     }\r\n\r\n    //     /**\r\n    //      * 设置皮肤颜色\r\n    //      * @param {Color} color 颜色值\r\n    //      */\r\n    //     set skinColor(color) {\r\n    //         this.skinImg.node.color = color;\r\n    //     }\r\n\r\n    //     /**\r\n    //      * 播放皮肤光效动画\r\n    //      */\r\n    //     playSkinLightAnim() {\r\n    //         tween(this.skinImg.node)\r\n    //             .to(16 * GameConfig.ActionFrameTime, { color: color(255, 255, 255) })\r\n    //             .start();\r\n    //     }\r\n\r\n    //     /**\r\n    //      * 飞机变形\r\n    //      * @param {Object} data 变形数据\r\n    //      * @param {number} index 当前变形阶段\r\n    //      */\r\n    //     planeTransform(data, index) {\r\n    //         const transformData = this.m_config[`trans${data.mainTrans}`];\r\n    //         const src = this.m_config.transSrc;\r\n    //         const ext = this.m_config.transExt;\r\n\r\n    //         this.skinAnim.node.getComponent(UIOpacity).opacity = 0;\r\n    //         this.skinImg.node.active = true;\r\n    //         this.avatarNodeActive = this.m_config.type !== 710 || BattleManager.gameType !== GameType.Gold;\r\n\r\n    //         if (index >= transformData.length) return;\r\n\r\n    //         switch (transformData[index]) {\r\n    //             case 0:\r\n    //                 this.playTrans0(data, index + 1);\r\n    //                 break;\r\n    //             case 1:\r\n    //                 this.setTrans1Img(this.mainTransLevel.get(1) || 2, src, ext);\r\n    //                 this.playTrans1(data, index + 1);\r\n    //                 break;\r\n    //             case 2:\r\n    //                 this.setTrans2Img(src);\r\n    //                 this.planeTransform(data, index + 1);\r\n    //                 break;\r\n    //             case 3:\r\n    //                 this.setTrans3Img(this.mainTransLevel.get(3) || 2, src, ext);\r\n    //                 this.playTrans3(data, index + 1);\r\n    //                 break;\r\n    //             case 4:\r\n    //                 this.setTrans4Img(this.mainTransLevel.get(4) || 2, src, ext);\r\n    //                 this.playTrans4(data, index + 1);\r\n    //                 break;\r\n    //         }\r\n    //     }\r\n\r\n    //     /**\r\n    //      * 播放变形阶段 0 的动画\r\n    //      * @param {Object} data 变形数据\r\n    //      * @param {number} nextIndex 下一阶段索引\r\n    //      */\r\n    //     playTrans0(data, nextIndex) {\r\n    //         this.avatarNodeActive = false;\r\n    //         this.skinImg.node.active = false;\r\n    //         this.skinAnim.node.getComponent(UIOpacity).opacity = 255;\r\n    //         this.skinAnim.animation = \"levelup\";\r\n\r\n    //         this.skinAnim.setCompleteListener(() => {\r\n    //             this.planeTransform(data, nextIndex);\r\n    //         });\r\n    //     }\r\n\r\n    //     /**\r\n    //      * 设置变形阶段 1 的图片\r\n    //      * @param {number} level 当前阶段等级\r\n    //      * @param {Array} src 图片资源\r\n    //      * @param {Array} ext 图片扩展信息\r\n    //      */\r\n    //     setTrans1Img(level, src, ext) {\r\n    //         const imgName = `${src[1]}${level}`;\r\n    //         const whiteImgName = `${imgName}-white`;\r\n    //         const position = ext[0].split(\",\");\r\n\r\n    //         const sprite = this.attspeAnim.node.getChildByName(\"attspe\").getComponent(HDSprite);\r\n    //         const whiteSprite = this.attspeAnim.node.getChildByName(\"attspe\").getChildByName(\"white\").getComponent(HDSprite);\r\n\r\n    //         sprite.spriteFrame = GameIns.loadManager.getImage(imgName, `package_mainPlane_trans_${this.m_config.type}`);\r\n    //         whiteSprite.spriteFrame = GameIns.loadManager.getImage(whiteImgName, `package_mainPlane_trans_${this.m_config.type}`);\r\n    //         this.attspeAnim.node.position = new Vec2(Number(position[0]), Number(position[1]));\r\n    //     }\r\n\r\n    //     /**\r\n    //      * 播放变形阶段 1 的动画\r\n    //      * @param {Object} data 变形数据\r\n    //      * @param {number} nextIndex 下一阶段索引\r\n    //      */\r\n    //     playTrans1(data, nextIndex) {\r\n    //         this.attspeAnim.stop(\"atspeTrans701\");\r\n    //         this.attspeAnim.node.active = true;\r\n    //         this.attspeAnim.play(\"atspeTrans701\");\r\n    //         this.attspeAnim.once(Animation.EventType.FINISHED, () => {\r\n    //             this.planeTransform(data, nextIndex);\r\n    //         });\r\n    //     }\r\n    //     /**\r\n    //      * 设置变形阶段 2 的图片\r\n    //      * @param {Array} src 图片资源\r\n    //      */\r\n    //     setTrans2Img(src) {\r\n    //         this.skinImg.spriteFrame = GameIns.loadManager.getImage(\r\n    //             src[0],\r\n    //             `package_mainPlane_trans_${this.m_config.type}`\r\n    //         );\r\n    //         this.skinImg.node.width = this.skinImg.spriteFrame.getOriginalSize().width;\r\n    //         this.skinImg.node.height = this.skinImg.spriteFrame.getOriginalSize().height;\r\n    //     }\r\n\r\n    //     /**\r\n    //      * 设置变形阶段 3 的图片\r\n    //      * @param {number} level 当前阶段等级\r\n    //      * @param {Array} src 图片资源\r\n    //      * @param {Array} ext 图片扩展信息\r\n    //      */\r\n    //     setTrans3Img(level, src, ext) {\r\n    //         --level;\r\n    //         this.scrAnim.node.children.forEach((child, index) => {\r\n    //             const scr = child.getChildByName(\"scr\");\r\n    //             const white = scr.getChildByName(\"white\");\r\n    //             const imgName = `${src[2]}${level}`;\r\n    //             const whiteImgName = `${imgName}-white`;\r\n    //             const position = ext[1].split(\"|\")[level].split(\",\");\r\n\r\n    //             scr.getComponent(HDSprite).spriteFrame = GameIns.loadManager.getImage(\r\n    //                 imgName,\r\n    //                 `package_mainPlane_trans_${this.m_config.type}`\r\n    //             );\r\n    //             white.getComponent(HDSprite).spriteFrame = GameIns.loadManager.getImage(\r\n    //                 whiteImgName,\r\n    //                 `package_mainPlane_trans_${this.m_config.type}`\r\n    //             );\r\n    //             child.position = new Vec2(\r\n    //                 Number(position[0]) * Math.pow(-1, index),\r\n    //                 Number(position[1])\r\n    //             );\r\n    //         });\r\n    //     }\r\n\r\n    //     /**\r\n    //      * 播放变形阶段 3 的动画\r\n    //      * @param {Object} data 变形数据\r\n    //      * @param {number} nextIndex 下一阶段索引\r\n    //      */\r\n    //     playTrans3(data, nextIndex) {\r\n    //         this.scrAnim.stop(\"scrpar701\");\r\n    //         this.scrAnim.node.active = true;\r\n    //         this.scrAnim.play(\"scrpar701\");\r\n    //         this.scrAnim.once(Animation.EventType.FINISHED, () => {\r\n    //             this.planeTransform(data, nextIndex);\r\n    //         });\r\n    //     }\r\n\r\n    //     /**\r\n    //      * 设置变形阶段 4 的图片\r\n    //      * @param {number} level 当前阶段等级\r\n    //      * @param {Array} src 图片资源\r\n    //      * @param {Array} ext 图片扩展信息\r\n    //      */\r\n    //     setTrans4Img(level, src, ext) {\r\n    //         --level;\r\n    //         this.auxAnim.node.children.forEach((child, index) => {\r\n    //             const aux = child.getChildByName(\"aux\");\r\n    //             const white = aux.getChildByName(\"white\");\r\n    //             const imgName = `${src[3]}${level}`;\r\n    //             const whiteImgName = `${imgName}-white`;\r\n    //             const position = ext[2].split(\"|\")[level - 1].split(\",\");\r\n\r\n    //             aux.getComponent(HDSprite).spriteFrame = GameIns.loadManager.getImage(\r\n    //                 imgName,\r\n    //                 `package_mainPlane_trans_${this.m_config.type}`\r\n    //             );\r\n    //             white.getComponent(HDSprite).spriteFrame = GameIns.loadManager.getImage(\r\n    //                 whiteImgName,\r\n    //                 `package_mainPlane_trans_${this.m_config.type}`\r\n    //             );\r\n    //             child.position = new Vec2(\r\n    //                 Number(position[0]) * Math.pow(-1, index),\r\n    //                 Number(position[1])\r\n    //             );\r\n    //         });\r\n    //     }\r\n\r\n    //     /**\r\n    //      * 播放变形阶段 4 的动画\r\n    //      * @param {Object} data 变形数据\r\n    //      * @param {number} nextIndex 下一阶段索引\r\n    //      */\r\n    //     playTrans4(data, nextIndex) {\r\n    //         this.auxAnim.stop(\"auxpar701\");\r\n    //         this.auxAnim.node.active = true;\r\n    //         this.auxAnim.play(\"auxpar701\");\r\n    //         this.auxAnim.once(Animation.EventType.FINISHED, () => {\r\n    //             this.planeTransform(data, nextIndex);\r\n    //         });\r\n    //     }\r\n\r\n    /**\r\n     * 初始化变形图片\r\n     */\r\n    initPic() {\r\n        this.mainTransLevel = new Map();\r\n\r\n        const attspe = this.attspeAnim.node.getChildByName(\"attspe\");\r\n        attspe.getComponent(Sprite).spriteFrame = (GameIns.loadManager.getImage(\r\n            `${this.m_config.transSrc[1]}1`,\r\n            `mainPlane/package_mainPlane_trans_${this.m_config.type}`\r\n        )) as SpriteFrame;\r\n\r\n        const position = this.m_config.transExt[0].split(\",\");\r\n        attspe.setPosition(Number(position[0]), Number(position[1]))\r\n\r\n        this.scrAnim.node.children.forEach((child) => {\r\n            const scr = child.getChildByName(\"scr\");\r\n            scr.getComponent(Sprite).spriteFrame = (GameIns.loadManager.getImage(\r\n                `${this.m_config.transSrc[2]}1`,\r\n                `mainPlane/package_mainPlane_trans_${this.m_config.type}`\r\n            )) as SpriteFrame;;\r\n            const pos = this.m_config.transExt[1].split(\",\");\r\n            child.setPosition(Number(pos[0]), Number(pos[1]))\r\n        });\r\n\r\n        this.auxAnim.node.children.forEach((child) => {\r\n            const aux = child.getChildByName(\"aux\");\r\n            aux.getComponent(Sprite).spriteFrame = (GameIns.loadManager.getImage(\r\n                `${this.m_config.transSrc[3]}1`,\r\n                `mainPlane/package_mainPlane_trans_${this.m_config.type}`\r\n            )) as SpriteFrame;;\r\n            const pos = this.m_config.transExt[2].split(\",\");\r\n            child.setPosition(Number(pos[0]), Number(pos[1]))\r\n        });\r\n    }\r\n    //     /**\r\n    //      * 播放充能动画\r\n    //      * @param {Vec2} position 动画位置\r\n    //      * @param {number} index 动画索引\r\n    //      */\r\n    //     playChargeAnim(position, index) {\r\n    //         return new Promise((resolve) => {\r\n    //             let spine = this.m_spines[index];\r\n    //             if (!spine) {\r\n    //                 const node = new Node();\r\n    //                 spine = node.addComponent(sp.Skeleton);\r\n    //                 this.m_spines[index] = spine;\r\n\r\n    //                 GameIns.loadManager.loadSpine(\"skel_charge\").then((skeletonData) => {\r\n    //                     spine.skeletonData = skeletonData;\r\n    //                     this._playChargeAnimInternal(spine, position, resolve);\r\n    //                 });\r\n    //             } else {\r\n    //                 this._playChargeAnimInternal(spine, position, resolve);\r\n    //             }\r\n    //         });\r\n    //     }\r\n\r\n    //     /**\r\n    //      * 内部方法：播放充能动画\r\n    //      * @param {sp.Skeleton} spine 动画组件\r\n    //      * @param {Vec2} position 动画位置\r\n    //      * @param {Function} resolve Promise 的 resolve 方法\r\n    //      */\r\n    //     _playChargeAnimInternal(spine, position, resolve) {\r\n    //         spine.node.parent = this.node;\r\n    //         spine.node.position = position;\r\n    //         spine.node.active = true;\r\n    //         spine.premultipliedAlpha = false;\r\n    //         spine.animation = \"play1\";\r\n    //         spine.paused = false;\r\n\r\n    //         spine.setCompleteListener(() => {\r\n    //             spine.node.active = false;\r\n    //             spine.setCompleteListener(null);\r\n    //             resolve();\r\n    //         });\r\n    //     }\r\n\r\n    //     /**\r\n    //      * 停止充能动画\r\n    //      * @param {number} index 动画索引\r\n    //      */\r\n    //     stopChargeAnim(index) {\r\n    //         const spine = this.m_spines[index];\r\n    //         if (spine) {\r\n    //             spine.paused = true;\r\n    //             spine.node.active = false;\r\n    //             spine.setCompleteListener(null);\r\n    //         }\r\n    //     }\r\n\r\n    //     /**\r\n    //      * 播放火力动画\r\n    //      * @param {Function} callback 动画完成后的回调\r\n    //      * @param {number} index 动画索引\r\n    //      */\r\n    //     playFireAnim(callback, index) {\r\n    //         const spine = this.m_spines[index];\r\n    //         if (spine) {\r\n    //             spine.animation = \"play2\";\r\n    //             spine.paused = false;\r\n\r\n    //             spine.setEventListener(() => {\r\n    //                 callback();\r\n    //                 spine.setEventListener(null);\r\n    //             });\r\n\r\n    //             spine.setCompleteListener(() => {\r\n    //                 spine.node.active = false;\r\n    //                 spine.setCompleteListener(null);\r\n    //             });\r\n    //         }\r\n    //     }\r\n\r\n    /**\r\n     * 播放挑战动画\r\n     * @param {string} animationName 动画名称\r\n     */\r\n    playChallengeAnim(animationName) {\r\n        if (this.challengeAnim) {\r\n            this.challengeAnim.node.active = true;\r\n            this.challengeAnim.animation = animationName;\r\n\r\n            this.challengeAnim.setCompleteListener(() => {\r\n                this.challengeAnim.node.active = false;\r\n                this.challengeAnim.setCompleteListener(null);\r\n            });\r\n\r\n            this.challengeAnimName = null;\r\n        } else {\r\n            this.challengeAnimName = animationName;\r\n        }\r\n    }\r\n\r\n    //     /**\r\n    //      * 播放套装技能特效\r\n    //      */\r\n    //     playSuitSkillEffect() {\r\n    //         this.suitEffect.stopAllActions();\r\n    //         tween(this.suitEffect)\r\n    //             .to(0, { scale: 0.75, opacity: 177 })\r\n    //             .to(3 * GameConfig.ActionFrameTime, { scale: 1.6, opacity: 230 })\r\n    //             .to(14 * GameConfig.ActionFrameTime, { scale: 2, opacity: 0 })\r\n    //             .start();\r\n    //     }\r\n\r\n    //     /**\r\n    //      * 初始化套装特效\r\n    //      */\r\n    //     initSuitEffect() {\r\n    //         const suitType = EquipManager.getFullSuitType();\r\n    //         if (suitType) {\r\n    //             this.suitEffect.active = true;\r\n    //             this.suitEffect.getComponent(UIOpacity).opacity = 0;\r\n\r\n    //             const colors = [color(255, 66, 255), color(255, 222, 66), color(255, 222, 66)];\r\n    //             const quality = EquipManager.getSuitQuality(suitType);\r\n    //             this.suitEffect.color = colors[quality - 1];\r\n    //         }\r\n    //     }\r\n\r\n    //     /**\r\n    //      * 释放技能\r\n    //      */\r\n    //     fireSkill() {\r\n    //         if (this.m_skill) {\r\n    //             this.m_skill.fire();\r\n    //         }\r\n    //     }\r\n\r\n    //     /**\r\n    //      * 获取技能进度\r\n    //      * @returns {number} 技能进度\r\n    //      */\r\n    //     getSkillProgress() {\r\n    //         return this.m_skill ? this.m_skill.getProgress() : 0;\r\n    //     }\r\n\r\n    //     /**\r\n    //      * 获取技能冷却时间\r\n    //      * @returns {number} 技能冷却时间\r\n    //      */\r\n    //     getSkillCD() {\r\n    //         return this.m_skill ? this.m_skill.cdTime : 0;\r\n    //     }\r\n\r\n    //     /**\r\n    //      * 获取技能对象\r\n    //      * @returns {Object|null} 技能对象\r\n    //      */\r\n    //     getSkill() {\r\n    //         return this.m_skill;\r\n    //     }\r\n    //     /**\r\n    //      * 设置皮肤是否激活\r\n    //      * @param {boolean} isActive 是否激活\r\n    //      */\r\n    //     setSkinAcitive(isActive) {\r\n    //         this.skinImg.node.active = !isActive;\r\n    //         this.avatarNodeActive = !isActive;\r\n    //         this.skinAnim.node.getComponent(UIOpacity).opacity = isActive ? 255 : 0;\r\n    //     }\r\n\r\n    //     /**\r\n    //      * 播放机甲动画\r\n    //      */\r\n    //     playMechaAnim() {\r\n    //         const type = this.m_config.type;\r\n    //         m.default.audioManager.playEffect(`turret_fit${type}`);\r\n    //         this.setSkinAcitive(true);\r\n    //         this.skinAnim.animation = \"tocrazy\";\r\n\r\n    //         if (this.m_skinShield) {\r\n    //             this.m_skinShield.setAnimation(0, \"tocrazy\", false);\r\n    //         }\r\n    //         if (this.m_skinCircle) {\r\n    //             this.m_skinCircle.setAnimation(0, \"tocrazy\", false);\r\n    //         }\r\n\r\n    //         switch (type) {\r\n    //             case 708:\r\n    //                 this.planeNode.scale = this.mechaScale[0];\r\n    //                 break;\r\n    //             case 709:\r\n    //                 this.planeNode.scale = this.mechaScale[1];\r\n    //                 break;\r\n    //             case 710:\r\n    //                 this.planeNode.scale = this.mechaScale[2];\r\n    //                 break;\r\n    //             case 711:\r\n    //                 this.planeNode.scale = this.mechaScale[3];\r\n    //                 break;\r\n    //             default:\r\n    //                 this.planeNode.scale = 1;\r\n    //         }\r\n\r\n    //         this.mechaOver();\r\n    //         this.skinAnim.setCompleteListener(() => {\r\n    //             this.skinAnim.setCompleteListener(null);\r\n    //             this.skinAnim.setAnimation(0, \"crazyidle\", true);\r\n    //             if (this.m_skinShield) {\r\n    //                 this.m_skinShield.setAnimation(0, \"crazyidle\", true);\r\n    //             }\r\n    //             if (this.m_skinCircle) {\r\n    //                 this.m_skinCircle.setAnimation(0, \"crazyidle\", true);\r\n    //             }\r\n    //             u.MainPlaneMgr.isEndChange = true;\r\n    //             N.default.me.mapEndChange();\r\n    //         });\r\n    //     }\r\n\r\n    //     /**\r\n    //      * 获取单位的起始位置\r\n    //      * @param {number} index 单位索引\r\n    //      * @returns {Vec2} 起始位置\r\n    //      */\r\n    //     getUnitStartPosByindex(index) {\r\n    //         const worldPos = C.default.me.winePlanes[index].node.convertToWorldSpaceAR(Vec2.ZERO);\r\n    //         return this.node.convertToNodeSpaceAR(worldPos);\r\n    //     }\r\n\r\n    //     /**\r\n    //      * 加载单位预制体\r\n    //      */\r\n    //     loadUnitPrefab() {\r\n    //         return new Promise((resolve) => {\r\n    //             m.default.loadManager.loadPrefab(\"mechaUnit\")\r\n    //                 .then((prefab) => {\r\n    //                     this.pfb = prefab;\r\n    //                     resolve();\r\n    //                 })\r\n    //                 .catch(() => {\r\n    //                     at.GFunc.wxLoadErr();\r\n    //                 });\r\n    //         });\r\n    //     }\r\n\r\n    //     /**\r\n    //      * 播放单位动画\r\n    //      * @param {number} index 单位索引\r\n    //      * @param {string} skillName 技能名称\r\n    //      */\r\n    //     playUnitAnim(index, skillName) {\r\n    //         const prefabInstance = instantiate(this.pfb);\r\n    //         prefabInstance.parent = this.node;\r\n    //         prefabInstance.scale = 0.85;\r\n\r\n    //         const signNode = prefabInstance.getChildByName(\"sign\");\r\n    //         signNode.getComponent(L.default).spriteFrame = m.default.loadManager.getImage(`tu_${skillName}`, \"mecha_commonImg\");\r\n\r\n    //         // 动画逻辑根据索引处理\r\n    //         switch (index) {\r\n    //             case 0:\r\n    //                 // 动画逻辑 0\r\n    //                 break;\r\n    //             case 1:\r\n    //                 // 动画逻辑 1\r\n    //                 break;\r\n    //             case 2:\r\n    //                 // 动画逻辑 2\r\n    //                 break;\r\n    //             case 3:\r\n    //                 // 动画逻辑 3\r\n    //                 break;\r\n    //             default:\r\n    //                 break;\r\n    //         }\r\n    //     }\r\n\r\n    //     /**\r\n    //      * 播放单位结束动画\r\n    //      */\r\n    //     unitOverAnim() {\r\n    //         this.scheduleOnce(() => {\r\n    //             const scale = this.m_config.type === 708 ? 0.75 : 0.65;\r\n    //             const overLight = instantiate(this.mechaUnitOverLight);\r\n    //             overLight.parent = this.node;\r\n    //             overLight.scale = 2 * scale;\r\n\r\n    //             const anim = overLight.getComponent(Animation);\r\n    //             if (anim) {\r\n    //                 anim.play();\r\n    //                 anim.once(Animation.EventType.FINISHED, () => {\r\n    //                     overLight.destroy();\r\n    //                 });\r\n    //             } else {\r\n    //                 overLight.destroy();\r\n    //             }\r\n    //         }, 0.1);\r\n    //     }\r\n\r\n    //     /**\r\n    //      * 机甲结束逻辑\r\n    //      */\r\n    //     mechaOver() {\r\n    //         return new Promise((resolve) => {\r\n    //             if (u.MainPlaneMgr.isPlane(710)) {\r\n    //                 resolve();\r\n    //                 return;\r\n    //             }\r\n\r\n    //             setTimeout(() => {\r\n    //                 E.GData.isMechaOver = true;\r\n    //                 if (this.m_config.type === 712) {\r\n    //                     this.hpBar.y = 90;\r\n    //                 }\r\n\r\n    //                 const dialog = m.default.uiManager.getDialog(et.default) || m.default.uiManager.createDialog(et.default);\r\n    //                 dialog.showFont(() => {\r\n    //                     this.skinAnim.animation = \"ingametoidle\";\r\n    //                     if (this.m_skinShield) {\r\n    //                         this.m_skinShield.setAnimation(0, \"ingametoidle\", false);\r\n    //                     }\r\n    //                     if (this.m_skinCircle) {\r\n    //                         this.m_skinCircle.setAnimation(0, \"ingametoidle\", false);\r\n    //                     }\r\n\r\n    //                     this.skinAnim.setCompleteListener(() => {\r\n    //                         this.skinAnim.setCompleteListener(null);\r\n    //                         this.setMechaOverPic();\r\n    //                     });\r\n\r\n    //                     tween(this.skinAnim.node)\r\n    //                         .to(0.5, { y: this.initPosSkinAnim - 100 })\r\n    //                         .start();\r\n\r\n    //                     tween(this.mechaAnimNode)\r\n    //                         .to(0.5, { y: this.initPosMechaAnim - 100 })\r\n    //                         .start();\r\n\r\n    //                     resolve();\r\n    //                 });\r\n    //             }, 1750);\r\n    //         });\r\n    //     }\r\n    //     /**\r\n    //      * 设置技能可以释放\r\n    //      */\r\n    //     setCanFireSkill() {\r\n    //         if (this.m_skill) {\r\n    //             this.m_skill.setCanFire();\r\n    //         }\r\n    //     }\r\n\r\n    //     /**\r\n    //      * 设置机甲结束后的图片\r\n    //      * @param {boolean} isInit 是否初始化\r\n    //      */\r\n    //     setMechaOverPic(isInit = false) {\r\n    //         if (this.m_config.type !== 710) {\r\n    //             this.skinAnim.node.getComponent(UIOpacity).opacity = 0;\r\n    //             this.avatarNodeActive = false;\r\n    //             this.setUnit(this.mechaAnimNode, isInit);\r\n    //             if (PlaneManager.me.shadowPlane) {\r\n    //                 PlaneManager.me.shadowPlane.mechaShow(true);\r\n    //             }\r\n    //             this.refreshStreak();\r\n    //         }\r\n    //     }\r\n\r\n    //     /**\r\n    //      * 设置单位\r\n    //      * @param {Node} parent 父节点\r\n    //      * @param {boolean} isInit 是否初始化\r\n    //      */\r\n    //     setUnit(parent, isInit = false) {\r\n    //         let shouldInstantiate = true;\r\n\r\n    //         if (this.m_config.type === 710) {\r\n    //             if (parent.childrenCount > 0) {\r\n    //                 shouldInstantiate = false;\r\n    //             }\r\n    //         } else {\r\n    //             parent.destroyAllChildren();\r\n    //         }\r\n\r\n    //         if (shouldInstantiate) {\r\n    //             for (const prefab of this.unitPrefab) {\r\n    //                 const instance = instantiate(prefab);\r\n    //                 instance.parent = parent;\r\n    //                 instance.getComponent(Animation).play();\r\n    //                 const name = instance.name;\r\n    //                 instance.zIndex = -Number(name[name.length - 1]);\r\n    //                 if (isInit) {\r\n    //                     this.mechaAvatar(instance);\r\n    //                 }\r\n    //             }\r\n\r\n    //             if (this.m_config.type === 711 && BattleManager.isContinue) {\r\n    //                 // Additional logic for type 711\r\n    //             }\r\n    //         }\r\n    //     }\r\n\r\n    //     /**\r\n    //      * 设置机甲头像图片\r\n    //      * @param {Node} node 节点\r\n    //      * @param {string} leftName 左侧图片名称\r\n    //      * @param {string} rightName 右侧图片名称\r\n    //      * @param {number} level 等级\r\n    //      * @param {string} prefix 图片前缀\r\n    //      */\r\n    //     setMechaAvatarImg(node, leftName, rightName, level, prefix) {\r\n    //         return new Promise((resolve) => {\r\n    //             const leftNode = node.getChildByName(leftName);\r\n    //             if (leftNode) {\r\n    //                 GameIns.loadManager.loadAtlas(\"diaochan\").then((atlas) => {\r\n    //                     const leftSprite = leftNode.getComponent(Sprite);\r\n    //                     const rightSprite = node.getChildByName(rightName).getComponent(Sprite);\r\n    //                     const adjustedLevel = this.mainTransLevel.get(level) - 2 || 0;\r\n\r\n    //                     leftSprite.spriteFrame = atlas.getSpriteFrame(`${prefix}${adjustedLevel}`);\r\n    //                     rightSprite.spriteFrame = atlas.getSpriteFrame(`${prefix}${adjustedLevel}`);\r\n    //                     resolve();\r\n    //                 });\r\n    //             } else {\r\n    //                 resolve();\r\n    //             }\r\n    //         });\r\n    //     }\r\n\r\n    //     /**\r\n    //      * 设置机甲头像\r\n    //      * @param {Node} node 节点\r\n    //      */\r\n    //     mechaAvatar(node) {\r\n    //         this.setMechaAvatarImg(node, \"zuojian_zuo\", \"zuojian_you\", 3, \"zuojian_\");\r\n    //         this.setMechaAvatarImg(node, \"zuofa_zuo\", \"zuofa_you\", 1, \"zuofa_\");\r\n    //         this.setMechaAvatarImg(node, \"zuoxiaotui_zuo\", \"zuoxiaotui_you\", 1, \"zuoxiaotui_\");\r\n    //     }\r\n\r\n    /**\r\n     * 退出游戏时设置图片\r\n     */\r\n    quitGameSetPic() {\r\n        // this.skinAnim.node.getComponent(UIOpacity).opacity = 0;\r\n        // this.avatarNodeActive = true;\r\n        // this.mechaAnimNode.destroyAllChildren();\r\n    }\r\n\r\n    //     /**\r\n    //      * 清除机甲结束后的图片\r\n    //      * @param {boolean} isInit 是否初始化\r\n    //      */\r\n    //     clearMechaOverPic(isInit = false) {\r\n    //         if (this.m_skill) {\r\n    //             this.m_skill.removeSkill();\r\n    //         }\r\n    //         this.skinAnim.node.getComponent(UIOpacity).opacity = 0;\r\n    //         this.avatarNodeActive = true;\r\n    //         this.mechaAnimNode.destroyAllChildren();\r\n\r\n    //         for (const prefab of this.unitPrefab) {\r\n    //             GameIns.loadManager.releaseAsset(prefab, false);\r\n    //         }\r\n    //         this.unitPrefab.splice(0);\r\n\r\n    //         GameIns.loadManager.releaseAsset(this.pfb, false);\r\n    //         GameIns.loadManager.releaseAsset(this.mechaAtlas_Anim, true);\r\n    //         GameIns.loadManager.releaseAsset(this.mechaUnitOverLight, false);\r\n\r\n    //         this.pfb = null;\r\n    //         this.mechaAtlas_Anim = null;\r\n    //         this.mechaUnitOverLight = null;\r\n\r\n    //         GameIns.loadManager.releaseRes(\"mecha_commonImg\", SpriteAtlas, true);\r\n    //         GameIns.loadManager.releaseOriginalArr(this.animArr, AnimationClip, false);\r\n    //         GameIns.loadManager.releaseOriginalArr(\r\n    //             [\r\n    //                 \"animation/MainPlane/mecha/batUnitAnim\",\r\n    //                 \"animation/MainPlane/mecha/dragonUnitAnim\",\r\n    //                 \"animation/MainPlane/mecha/sword/swordUnitAnim\",\r\n    //                 \"animation/MainPlane/mecha/unitOverLight\",\r\n    //             ],\r\n    //             AnimationClip,\r\n    //             false\r\n    //         );\r\n    //     }\r\n    //     /**\r\n    //      * 设置蝙蝠技能数据\r\n    //      * @param {number} distanceX X轴距离\r\n    //      * @param {number} distanceY Y轴距离\r\n    //      * @param {number} speed 速度\r\n    //      * @param {Array} targetNodes 目标节点数组\r\n    //      * @param {Vec2} startPoint 起始点\r\n    //      */\r\n    //     batSkillData(distanceX, distanceY, speed, targetNodes, startPoint) {\r\n    //         this.skillBatDis = [distanceX, distanceY, speed];\r\n    //         this.skillTargetNodes = targetNodes;\r\n    //         this.skillBatPoint = startPoint;\r\n    //     }\r\n\r\n    //     /**\r\n    //      * 播放机甲进入动画1\r\n    //      */\r\n    //     mechaInAnim1() {\r\n    //         if (!E.GData.isMechaOver) {\r\n    //             const unlocks = this.m_config.unlock;\r\n    //             const winePlanes = C.default.me.winePlanes;\r\n    //             unlocks.forEach((unlock, index) => {\r\n    //                 if (winePlanes[index]) {\r\n    //                     winePlanes[index].playBotAnim();\r\n    //                 }\r\n    //             });\r\n    //         }\r\n    //     }\r\n\r\n    //     /**\r\n    //      * 播放机甲进入动画2\r\n    //      */\r\n    //     mechaInAnim2() {\r\n    //         if (E.GData.isMechaOver) {\r\n    //             this.begine();\r\n    //         } else {\r\n    //             const unlocks = this.m_config.unlock;\r\n    //             const winePlanes = C.default.me.winePlanes;\r\n\r\n    //             if (winePlanes[0]) {\r\n    //                 unlocks.forEach((unlock, index) => {\r\n    //                     this.playUnitAnim(index, winePlanes[index].m_config.Onskill);\r\n    //                 });\r\n    //             } else {\r\n    //                 this.playMechaAnim();\r\n    //                 this.unitOverAnim();\r\n    //             }\r\n    //         }\r\n    //     }\r\n\r\n    //     /**\r\n    //      * 进入Roguelike模式动画\r\n    //      */\r\n    //     roguelikeInAnim() {\r\n    //         this.mechaAnimNode.active = false;\r\n    //         this.skinAnim.node.getComponent(UIOpacity).opacity = 255;\r\n    //         this.skinAnim.animation = \"land\";\r\n\r\n    //         if (this.m_skinShield) {\r\n    //             this.m_skinShield.setAnimation(0, \"land\", false);\r\n    //         }\r\n    //         if (this.m_skinCircle) {\r\n    //             this.m_skinCircle.setAnimation(0, \"land\", false);\r\n    //         }\r\n\r\n    //         if (U.default.me.shadowPlane) {\r\n    //             U.default.me.shadowPlane.node.active = false;\r\n    //         }\r\n\r\n    //         this.skinAnim.setCompleteListener(() => {\r\n    //             this.skinAnim.setCompleteListener(null);\r\n    //             this.avatarNodeActive = true;\r\n    //             this.skinAnim.node.getComponent(UIOpacity).opacity = 0;\r\n    //         });\r\n\r\n    //         switch (this.m_config.type) {\r\n    //             case 712:\r\n    //             case 713:\r\n    //                 this.skinAnim.node.y = 0;\r\n    //                 break;\r\n    //             case 714:\r\n    //             case 715:\r\n    //                 this.skinAnim.node.y = -16;\r\n    //                 break;\r\n    //             default:\r\n    //                 this.skinAnim.node.y = this.initPosSkinAnim;\r\n    //         }\r\n\r\n    //         this.mechaAnimNode.y = this.initPosMechaAnim;\r\n    //         this.suitAnimBot.y = this.initPosSuitBot;\r\n    //         this.suitAnimTop.y = this.initPosSuitTop;\r\n    //         this.planeNode.scale = 1;\r\n    //     }\r\n\r\n    //     /**\r\n    //      * 退出Roguelike模式动画\r\n    //      */\r\n    //     roguelikeOutAnim() {\r\n    //         this.skinAnim.node.getComponent(UIOpacity).opacity = 255;\r\n    //         this.avatarNodeActive = false;\r\n    //         this.skinAnim.animation = \"launch\";\r\n\r\n    //         if (this.m_skinShield) {\r\n    //             this.m_skinShield.setAnimation(0, \"launch\", false);\r\n    //         }\r\n    //         if (this.m_skinCircle) {\r\n    //             this.m_skinCircle.setAnimation(0, \"launch\", false);\r\n    //         }\r\n\r\n    //         this.skinAnim.setCompleteListener(() => {\r\n    //             if (F.default.me.getMySkillByType(23)) {\r\n    //                 if (U.default.me.shadowPlane) {\r\n    //                     U.default.me.shadowPlane.node.active = true;\r\n    //                     U.default.me.shadowPlane.mechaShow(true);\r\n    //                 }\r\n    //             }\r\n    //             this.skinAnim.setCompleteListener(null);\r\n    //             this.mechaAnimNode.active = true;\r\n    //             this.skinAnim.node.getComponent(UIOpacity).opacity = 0;\r\n    //         });\r\n\r\n    //         if (this.m_config.type !== 710) {\r\n    //             if (this.skinAnim) {\r\n    //                 tween(this.skinAnim.node)\r\n    //                     .to(5 / 30, { y: this.initPosSkinAnim - (this.m_config.type === 711 ? 0 : 100) })\r\n    //                     .start();\r\n    //             }\r\n    //             if (this.mechaAnimNode) {\r\n    //                 tween(this.mechaAnimNode)\r\n    //                     .to(5 / 30, { y: this.initPosMechaAnim - (this.m_config.type === 711 ? 0 : 100) })\r\n    //                     .start();\r\n    //             }\r\n    //             if (this.suitAnimBot) {\r\n    //                 tween(this.suitAnimBot)\r\n    //                     .to(5 / 30, { y: this.initPosSuitBot - (this.m_config.type === 712 || this.m_config.type === 714 || this.m_config.type === 715 ? 0 : 100) })\r\n    //                     .start();\r\n    //             }\r\n    //             if (this.suitAnimTop) {\r\n    //                 tween(this.suitAnimTop)\r\n    //                     .to(5 / 30, { y: this.initPosSuitTop - (this.m_config.type === 712 || this.m_config.type === 714 || this.m_config.type === 715 ? 0 : 100) })\r\n    //                     .start();\r\n    //             }\r\n    //         }\r\n\r\n    //         if (this.planeNode) {\r\n    //             if (E.GData.isMechaOver) {\r\n    //                 switch (this.m_config.type) {\r\n    //                     case 708:\r\n    //                         tween(this.planeNode).to(5 / 30, { scale: this.mechaScale[0] }).start();\r\n    //                         break;\r\n    //                     case 709:\r\n    //                         tween(this.planeNode).to(5 / 30, { scale: this.mechaScale[1] }).start();\r\n    //                         break;\r\n    //                     case 710:\r\n    //                         tween(this.planeNode).to(5 / 30, { scale: this.mechaScale[2] }).start();\r\n    //                         break;\r\n    //                     case 711:\r\n    //                         tween(this.planeNode).to(5 / 30, { scale: this.mechaScale[3] }).start();\r\n    //                         break;\r\n    //                     default:\r\n    //                         this.planeNode.scale = 1;\r\n    //                 }\r\n    //             } else {\r\n    //                 this.planeNode.scale = 1;\r\n    //             }\r\n    //         }\r\n    //     }\r\n}"]}