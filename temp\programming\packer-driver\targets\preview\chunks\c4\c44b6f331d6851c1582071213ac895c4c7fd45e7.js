System.register(["__unresolved_0", "cc", "__unresolved_1"], function (_export, _context) {
  "use strict";

  var _reporterNs, _cclegacy, __checkObsolete__, __checkObsoleteInNamespace__, _decorator, Component, Node, GameIns, _dec, _dec2, _dec3, _dec4, _dec5, _dec6, _dec7, _dec8, _dec9, _dec10, _dec11, _dec12, _dec13, _dec14, _dec15, _class, _class2, _descriptor, _descriptor2, _descriptor3, _descriptor4, _descriptor5, _descriptor6, _descriptor7, _descriptor8, _descriptor9, _descriptor10, _descriptor11, _descriptor12, _descriptor13, _descriptor14, _class3, _crd, ccclass, property, BattleLayer;

  function _initializerDefineProperty(target, property, descriptor, context) { if (!descriptor) return; Object.defineProperty(target, property, { enumerable: descriptor.enumerable, configurable: descriptor.configurable, writable: descriptor.writable, value: descriptor.initializer ? descriptor.initializer.call(context) : void 0 }); }

  function _applyDecoratedDescriptor(target, property, decorators, descriptor, context) { var desc = {}; Object.keys(descriptor).forEach(function (key) { desc[key] = descriptor[key]; }); desc.enumerable = !!desc.enumerable; desc.configurable = !!desc.configurable; if ('value' in desc || desc.initializer) { desc.writable = true; } desc = decorators.slice().reverse().reduce(function (desc, decorator) { return decorator(target, property, desc) || desc; }, desc); if (context && desc.initializer !== void 0) { desc.value = desc.initializer ? desc.initializer.call(context) : void 0; desc.initializer = undefined; } if (desc.initializer === void 0) { Object.defineProperty(target, property, desc); desc = null; } return desc; }

  function _initializerWarningHelper(descriptor, context) { throw new Error('Decorating class property failed. Please ensure that ' + 'transform-class-properties is enabled and runs after the decorators transform.'); }

  function _reportPossibleCrUseOfGameIns(extras) {
    _reporterNs.report("GameIns", "../../GameIns", _context.meta, extras);
  }

  return {
    setters: [function (_unresolved_) {
      _reporterNs = _unresolved_;
    }, function (_cc) {
      _cclegacy = _cc.cclegacy;
      __checkObsolete__ = _cc.__checkObsolete__;
      __checkObsoleteInNamespace__ = _cc.__checkObsoleteInNamespace__;
      _decorator = _cc._decorator;
      Component = _cc.Component;
      Node = _cc.Node;
    }, function (_unresolved_2) {
      GameIns = _unresolved_2.GameIns;
    }],
    execute: function () {
      _crd = true;

      _cclegacy._RF.push({}, "36677HBzmxCaqKfwtl9xX+S", "BattleLayer", undefined);

      __checkObsolete__(['_decorator', 'Component', 'Node']);

      ({
        ccclass,
        property
      } = _decorator);

      _export("default", BattleLayer = (_dec = ccclass('BattleLayer'), _dec2 = property(Node), _dec3 = property(Node), _dec4 = property(Node), _dec5 = property(Node), _dec6 = property(Node), _dec7 = property(Node), _dec8 = property(Node), _dec9 = property(Node), _dec10 = property(Node), _dec11 = property(Node), _dec12 = property(Node), _dec13 = property(Node), _dec14 = property(Node), _dec15 = property(Node), _dec(_class = (_class2 = (_class3 = class BattleLayer extends Component {
        constructor() {
          super(...arguments);

          _initializerDefineProperty(this, "shadowLayer", _descriptor, this);

          _initializerDefineProperty(this, "enemyPlaneLayer", _descriptor2, this);

          _initializerDefineProperty(this, "enemyBulletLayer", _descriptor3, this);

          _initializerDefineProperty(this, "enemyEffectLayer", _descriptor4, this);

          _initializerDefineProperty(this, "selfPlaneLayer", _descriptor5, this);

          _initializerDefineProperty(this, "dunLayer", _descriptor6, this);

          _initializerDefineProperty(this, "selfBulletLayer", _descriptor7, this);

          _initializerDefineProperty(this, "selfBulletEffect", _descriptor8, this);

          _initializerDefineProperty(this, "lootLayer", _descriptor9, this);

          _initializerDefineProperty(this, "lootGoldLayer", _descriptor10, this);

          _initializerDefineProperty(this, "lootBoxLayer", _descriptor11, this);

          _initializerDefineProperty(this, "streak", _descriptor12, this);

          _initializerDefineProperty(this, "laserPoint", _descriptor13, this);

          _initializerDefineProperty(this, "enemyBulletLight", _descriptor14, this);
        }

        onLoad() {
          BattleLayer.me = this;
        }

        addShadow(node) {
          this.shadowLayer.addChild(node);
        }

        addBullet(bullet) {
          if (bullet.enemy) {
            bullet.node.parent = this.enemyBulletLayer;
          } else {
            bullet.node.parent = this.selfBulletLayer;
          }
        }

        addBulletNode(node, isEnemy) {
          if (isEnemy === void 0) {
            isEnemy = false;
          }

          node.parent = isEnemy ? this.enemyBulletLayer : this.selfBulletLayer;
        }

        addCopyPlane(plane) {
          plane.node.parent = this.selfPlaneLayer;
        }

        addMainPlane() {
          (_crd && GameIns === void 0 ? (_reportPossibleCrUseOfGameIns({
            error: Error()
          }), GameIns) : GameIns).mainPlaneManager.plane.node.parent = this.selfPlaneLayer;
        }

        addWinePlane(plane) {
          plane.node.parent = this.selfPlaneLayer;
        }

        addFriendPlane(plane) {
          plane.node.parent = this.dunLayer;
        }

        addEnemy(node, zIndex) {
          if (zIndex === void 0) {
            zIndex = 0;
          }

          node.parent = this.enemyPlaneLayer;
          node.setSiblingIndex(zIndex);
        }

        addMissile(node) {
          node.parent = this.enemyBulletLayer;
        }

        addMissileWarn(node) {
          node.parent = this.enemyPlaneLayer;
        }

        addLootGold(node) {
          node.parent = this.lootGoldLayer;
        }

        addLootBox(node) {
          node.parent = this.lootBoxLayer;
        }

      }, _class3.me = void 0, _class3), (_descriptor = _applyDecoratedDescriptor(_class2.prototype, "shadowLayer", [_dec2], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function initializer() {
          return null;
        }
      }), _descriptor2 = _applyDecoratedDescriptor(_class2.prototype, "enemyPlaneLayer", [_dec3], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function initializer() {
          return null;
        }
      }), _descriptor3 = _applyDecoratedDescriptor(_class2.prototype, "enemyBulletLayer", [_dec4], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function initializer() {
          return null;
        }
      }), _descriptor4 = _applyDecoratedDescriptor(_class2.prototype, "enemyEffectLayer", [_dec5], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function initializer() {
          return null;
        }
      }), _descriptor5 = _applyDecoratedDescriptor(_class2.prototype, "selfPlaneLayer", [_dec6], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function initializer() {
          return null;
        }
      }), _descriptor6 = _applyDecoratedDescriptor(_class2.prototype, "dunLayer", [_dec7], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function initializer() {
          return null;
        }
      }), _descriptor7 = _applyDecoratedDescriptor(_class2.prototype, "selfBulletLayer", [_dec8], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function initializer() {
          return null;
        }
      }), _descriptor8 = _applyDecoratedDescriptor(_class2.prototype, "selfBulletEffect", [_dec9], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function initializer() {
          return null;
        }
      }), _descriptor9 = _applyDecoratedDescriptor(_class2.prototype, "lootLayer", [_dec10], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function initializer() {
          return null;
        }
      }), _descriptor10 = _applyDecoratedDescriptor(_class2.prototype, "lootGoldLayer", [_dec11], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function initializer() {
          return null;
        }
      }), _descriptor11 = _applyDecoratedDescriptor(_class2.prototype, "lootBoxLayer", [_dec12], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function initializer() {
          return null;
        }
      }), _descriptor12 = _applyDecoratedDescriptor(_class2.prototype, "streak", [_dec13], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function initializer() {
          return null;
        }
      }), _descriptor13 = _applyDecoratedDescriptor(_class2.prototype, "laserPoint", [_dec14], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function initializer() {
          return null;
        }
      }), _descriptor14 = _applyDecoratedDescriptor(_class2.prototype, "enemyBulletLight", [_dec15], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function initializer() {
          return null;
        }
      })), _class2)) || _class));

      _cclegacy._RF.pop();

      _crd = false;
    }
  };
});
//# sourceMappingURL=c44b6f331d6851c1582071213ac895c4c7fd45e7.js.map