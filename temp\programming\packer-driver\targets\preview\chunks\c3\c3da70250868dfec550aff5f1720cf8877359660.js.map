{"version": 3, "sources": ["file:///E:/M2Game/Client/assets/scripts/Game/ui/plane/mainPlane/MainRelifeComp.ts"], "names": ["_decorator", "v2", "Entity", "ColliderComp", "GameIns", "GameConst", "HDSpine", "ccclass", "property", "MainRelifeComp", "m_callBack", "m_time", "m_radius", "m_changeTime", "collider", "onLoad", "addComp", "init", "onEnable", "setData", "mainPlaneManager", "plane", "node", "position", "x", "y", "relifeAnim", "setAnimation", "setCompleteListener", "callback", "updateSize", "deltaTime", "size", "setSize", "onCollide", "other", "update", "GameAble", "clampedDeltaTime"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAASA,MAAAA,U,OAAAA,U;AAA6BC,MAAAA,E,OAAAA,E;;AAC/BC,MAAAA,M;;AACEC,MAAAA,Y,iBAAAA,Y;;AACAC,MAAAA,O,iBAAAA,O;;AACAC,MAAAA,S,iBAAAA,S;;AACFC,MAAAA,O;;;;;;;;;OAGD;AAAEC,QAAAA,OAAF;AAAWC,QAAAA;AAAX,O,GAAwBR,U;;yBAGTS,c,WADpBF,OAAO,CAAC,gBAAD,C,UAEHC,QAAQ;AAAA;AAAA,6B,2BAFb,MACqBC,cADrB;AAAA;AAAA,4BACmD;AAAA;AAAA;;AAAA;;AAAA,eAI/CC,UAJ+C,GAIjB,IAJiB;AAAA,eAK/CC,MAL+C,GAK7B,KAAK,EAAN,GAAY,IALkB;AAKZ;AALY,eAM/CC,QAN+C,GAM5B,GAN4B;AAMvB;AANuB,eAO/CC,YAP+C,GAOxB,CAPwB;AAOrB;AAPqB,eAQ/CC,QAR+C,GAQf,IARe;AAAA;;AAU/CC,QAAAA,MAAM,GAAG;AACL,eAAKD,QAAL,GAAgB,KAAKE,OAAL;AAAA;AAAA,4CAA2B;AAAA;AAAA,6CAA3B,CAAhB;AACA,eAAKF,QAAL,CAAcG,IAAd,CAAmB,IAAnB;AACH;;AAEDC,QAAAA,QAAQ,GAAG;AAAA;;AACP,iCAAKJ,QAAL,4BAAeK,OAAf,CAAuB,CAAC,CAAD,EAAI,CAAJ,EAAO,CAAP,EAAU,CAAV,EAAa,CAAb,CAAvB,EAAwC;AAAA;AAAA,kCAAQC,gBAAR,CAAyBC,KAAjE,EAAwEpB,EAAE,CAAC,KAAKqB,IAAL,CAAUC,QAAV,CAAmBC,CAApB,EAAsB,KAAKF,IAAL,CAAUC,QAAV,CAAmBE,CAAzC,CAA1E;AAEA,mCAAKC,UAAL,8BAAiBC,YAAjB,CAA8B,CAA9B,EAAiC,MAAjC,EAAyC,KAAzC;AACA,oCAAKD,UAAL,+BAAiBE,mBAAjB,CAAqC,MAAM;AACvC,gBAAI,KAAKlB,UAAT,EAAqB;AACjB,mBAAKA,UAAL;AACH;AACJ,WAJD;AAKH;;AAEDkB,QAAAA,mBAAmB,CAACC,QAAD,EAAqB;AACpC,eAAKnB,UAAL,GAAkBmB,QAAlB;AACH;;AAEDC,QAAAA,UAAU,CAACC,SAAD,EAAoB;AAAA;;AAC1B,eAAKlB,YAAL,IAAqB,OAAOkB,SAA5B;;AACA,cAAI,KAAKlB,YAAL,IAAqB,KAAKF,MAA9B,EAAsC;AAClC,iBAAKE,YAAL,GAAoB,KAAKF,MAAzB;AACH;;AAED,cAAMqB,IAAI,GAAG,KAAKpB,QAAL,IAAiB,KAAKC,YAAL,GAAoB,KAAKF,MAA1C,CAAb;AACA,kCAAKG,QAAL,6BAAemB,OAAf,CAAuBD,IAAvB;AACH;;AAEDE,QAAAA,SAAS,CAACC,KAAD,EAAa,CAClB;AACH;;AAEDC,QAAAA,MAAM,CAACL,SAAD,EAAoB;AACtB,cAAI;AAAA;AAAA,sCAAUM,QAAd,EAAwB;AAAA;;AACpB,gBAAMC,gBAAgB,GAAGP,SAAS,GAAG,GAAZ,GAAkB,iBAAlB,GAAsCA,SAA/D;AACA,iBAAKD,UAAL,CAAgBQ,gBAAhB;AACA,oCAAKxB,QAAL,6BAAesB,MAAf;AACH;AACJ;;AAlD8C,O;;;;;iBAElB,I", "sourcesContent": ["import { _decorator, Component, Node, v2 } from 'cc';\r\nimport Entity from '../../base/Entity';\r\nimport { ColliderComp } from '../../base/ColliderComp';\r\nimport { GameIns } from '../../../GameIns';\r\nimport { GameConst } from '../../../const/GameConst';\r\nimport HDSpine from '../../base/HDSpine';\r\n\r\n\r\nconst { ccclass, property } = _decorator;\r\n\r\n@ccclass('MainRelifeComp')\r\nexport default class MainRelifeComp extends Entity {\r\n    @property(HDSpine)\r\n    relifeAnim: HDSpine | null = null;\r\n\r\n    m_callBack: Function | null = null;\r\n    m_time: number = (19 / 30) * 1000; // 动画时间\r\n    m_radius: number = 290; // 半径\r\n    m_changeTime: number = 0; // 当前变化时间\r\n    collider: ColliderComp | null = null;\r\n\r\n    onLoad() {\r\n        this.collider = this.addComp(ColliderComp, new ColliderComp());\r\n        this.collider.init(this);\r\n    }\r\n\r\n    onEnable() {\r\n        this.collider?.setData([0, 0, 0, 0, 0], GameIns.mainPlaneManager.plane, v2(this.node.position.x,this.node.position.y));\r\n\r\n        this.relifeAnim?.setAnimation(0, 'play', false);\r\n        this.relifeAnim?.setCompleteListener(() => {\r\n            if (this.m_callBack) {\r\n                this.m_callBack();\r\n            }\r\n        });\r\n    }\r\n\r\n    setCompleteListener(callback: Function) {\r\n        this.m_callBack = callback;\r\n    }\r\n\r\n    updateSize(deltaTime: number) {\r\n        this.m_changeTime += 1000 * deltaTime;\r\n        if (this.m_changeTime >= this.m_time) {\r\n            this.m_changeTime = this.m_time;\r\n        }\r\n\r\n        const size = this.m_radius * (this.m_changeTime / this.m_time);\r\n        this.collider?.setSize(size);\r\n    }\r\n\r\n    onCollide(other: any) {\r\n        // 碰撞逻辑可以在这里实现\r\n    }\r\n\r\n    update(deltaTime: number) {\r\n        if (GameConst.GameAble) {\r\n            const clampedDeltaTime = deltaTime > 0.2 ? 0.016666666666667 : deltaTime;\r\n            this.updateSize(clampedDeltaTime);\r\n            this.collider?.update();\r\n        }\r\n    }\r\n}"]}