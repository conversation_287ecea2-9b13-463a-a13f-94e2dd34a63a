System.register(["__unresolved_0", "cc", "__unresolved_1", "__unresolved_2"], function (_export, _context) {
  "use strict";

  var _reporterNs, _cclegacy, __checkObsolete__, __checkObsoleteInNamespace__, _decorator, Component, Enum, SpriteFrame, BackgroundLayer, Message, ChangeBgSpeedMsg, _dec, _dec2, _dec3, _dec4, _class2, _class3, _descriptor, _descriptor2, _descriptor3, _dec5, _dec6, _dec7, _class5, _class6, _descriptor4, _descriptor5, _descriptor6, _crd, ccclass, property, eLayer, BackgroundLayerData, Background;

  function _initializerDefineProperty(target, property, descriptor, context) { if (!descriptor) return; Object.defineProperty(target, property, { enumerable: descriptor.enumerable, configurable: descriptor.configurable, writable: descriptor.writable, value: descriptor.initializer ? descriptor.initializer.call(context) : void 0 }); }

  function _applyDecoratedDescriptor(target, property, decorators, descriptor, context) { var desc = {}; Object.keys(descriptor).forEach(function (key) { desc[key] = descriptor[key]; }); desc.enumerable = !!desc.enumerable; desc.configurable = !!desc.configurable; if ('value' in desc || desc.initializer) { desc.writable = true; } desc = decorators.slice().reverse().reduce(function (desc, decorator) { return decorator(target, property, desc) || desc; }, desc); if (context && desc.initializer !== void 0) { desc.value = desc.initializer ? desc.initializer.call(context) : void 0; desc.initializer = undefined; } if (desc.initializer === void 0) { Object.defineProperty(target, property, desc); desc = null; } return desc; }

  function _initializerWarningHelper(descriptor, context) { throw new Error('Decorating class property failed. Please ensure that ' + 'transform-class-properties is enabled and runs after the decorators transform.'); }

  function _reportPossibleCrUseOfBackgroundLayer(extras) {
    _reporterNs.report("BackgroundLayer", "./BackgroundLayer", _context.meta, extras);
  }

  function _reportPossibleCrUseOfIMessage(extras) {
    _reporterNs.report("IMessage", "../../base/Messaging", _context.meta, extras);
  }

  function _reportPossibleCrUseOfMessage(extras) {
    _reporterNs.report("Message", "../../base/Messaging", _context.meta, extras);
  }

  _export("ChangeBgSpeedMsg", void 0);

  return {
    setters: [function (_unresolved_) {
      _reporterNs = _unresolved_;
    }, function (_cc) {
      _cclegacy = _cc.cclegacy;
      __checkObsolete__ = _cc.__checkObsolete__;
      __checkObsoleteInNamespace__ = _cc.__checkObsoleteInNamespace__;
      _decorator = _cc._decorator;
      Component = _cc.Component;
      Enum = _cc.Enum;
      SpriteFrame = _cc.SpriteFrame;
    }, function (_unresolved_2) {
      BackgroundLayer = _unresolved_2.BackgroundLayer;
    }, function (_unresolved_3) {
      Message = _unresolved_3.Message;
    }],
    execute: function () {
      _crd = true;

      _cclegacy._RF.push({}, "55929qCSzBFca2GLIyw3Tee", "Background", undefined);

      __checkObsolete__(['_decorator', 'Component', 'Enum', 'SpriteFrame', 'Node']);

      ({
        ccclass,
        property
      } = _decorator);

      _export("ChangeBgSpeedMsg", ChangeBgSpeedMsg = class ChangeBgSpeedMsg {
        constructor() {
          this.speedModifier = 1.0;
        }

      });

      _export("eLayer", eLayer = /*#__PURE__*/function (eLayer) {
        eLayer[eLayer["BG_VeryFar"] = -4] = "BG_VeryFar";
        eLayer[eLayer["BG_Far"] = -3] = "BG_Far";
        eLayer[eLayer["BG_Close"] = -2] = "BG_Close";
        eLayer[eLayer["BG_VeryClose"] = -1] = "BG_VeryClose";
        eLayer[eLayer["Player"] = 0] = "Player";
        eLayer[eLayer["FG_Close"] = 1] = "FG_Close";
        eLayer[eLayer["FG_VeryClose"] = 2] = "FG_VeryClose";
        return eLayer;
      }({}));

      _export("BackgroundLayerData", BackgroundLayerData = (_dec = ccclass('BackgroundLayerData'), _dec2 = property({
        displayName: "Scroll Duration(s)",
        tooltip: "Scroll duration in seconds"
      }), _dec3 = property({
        type: Enum(eLayer)
      }), _dec4 = property({
        type: [SpriteFrame]
      }), _dec(_class2 = (_class3 = class BackgroundLayerData {
        constructor() {
          _initializerDefineProperty(this, "scrollDuration", _descriptor, this);

          _initializerDefineProperty(this, "sortingLayer", _descriptor2, this);

          // for changing background
          _initializerDefineProperty(this, "spriteFrames", _descriptor3, this);
        }

      }, (_descriptor = _applyDecoratedDescriptor(_class3.prototype, "scrollDuration", [_dec2], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function initializer() {
          return 1.0;
        }
      }), _descriptor2 = _applyDecoratedDescriptor(_class3.prototype, "sortingLayer", [_dec3], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function initializer() {
          return eLayer.BG_Far;
        }
      }), _descriptor3 = _applyDecoratedDescriptor(_class3.prototype, "spriteFrames", [_dec4], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function initializer() {
          return [];
        }
      })), _class3)) || _class2));

      _export("Background", Background = (_dec5 = ccclass('Background'), _dec6 = property({
        type: [_crd && BackgroundLayer === void 0 ? (_reportPossibleCrUseOfBackgroundLayer({
          error: Error()
        }), BackgroundLayer) : BackgroundLayer]
      }), _dec7 = property({
        type: BackgroundLayerData
      }), _dec5(_class5 = (_class6 = class Background extends Component {
        constructor() {
          super(...arguments);

          _initializerDefineProperty(this, "speedModifier", _descriptor4, this);

          _initializerDefineProperty(this, "layers", _descriptor5, this);

          _initializerDefineProperty(this, "layerDatas", _descriptor6, this);

          this.onChangeBgSpeedMsgHandler = e => {
            this.speedModifier = e.speedModifier;
            console.log("changing speed to " + this.speedModifier);
          };
        }

        onEnable() {
          (_crd && Message === void 0 ? (_reportPossibleCrUseOfMessage({
            error: Error()
          }), Message) : Message).on(ChangeBgSpeedMsg, this.onChangeBgSpeedMsgHandler);
        }

        onDisable() {
          (_crd && Message === void 0 ? (_reportPossibleCrUseOfMessage({
            error: Error()
          }), Message) : Message).off(ChangeBgSpeedMsg, this.onChangeBgSpeedMsgHandler);
        }

        update(deltaTime) {
          this.layers.forEach(layer => {
            layer.tick(deltaTime * this.speedModifier);
          });
        }

      }, (_descriptor4 = _applyDecoratedDescriptor(_class6.prototype, "speedModifier", [property], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function initializer() {
          return 1.0;
        }
      }), _descriptor5 = _applyDecoratedDescriptor(_class6.prototype, "layers", [_dec6], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function initializer() {
          return [];
        }
      }), _descriptor6 = _applyDecoratedDescriptor(_class6.prototype, "layerDatas", [_dec7], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function initializer() {
          return [];
        }
      })), _class6)) || _class5));

      _cclegacy._RF.pop();

      _crd = false;
    }
  };
});
//# sourceMappingURL=6959084f40fe466415c521f4ebf995530a824060.js.map